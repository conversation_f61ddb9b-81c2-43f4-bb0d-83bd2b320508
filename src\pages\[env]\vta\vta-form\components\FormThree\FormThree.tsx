import React, { useState } from 'react';
import pc from 'prefix-classnames';
import './FormThree.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Space } from 'antd';

const px = pc('form-three');

export interface FormThreeProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {

}

const FormThree = (props: FormThreeProps) => {
  const { className = '', ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
  );
};

export default FormThree;
