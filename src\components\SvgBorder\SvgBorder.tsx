import { useSize } from 'ahooks';
import React, { useRef } from 'react';
import Color from 'color';
import styles from './SvgBorder.less';

export interface SvgBorderProps extends React.HTMLAttributes<SVGElement> {
  borderPath?: string | ((width: number, height: number) => string);
  color?: string;
  fill?: string;
  strokeWidth?: string | number;
  borderProps?: React.HTMLAttributes<SVGPathElement>;
  backgroundOpacity?: number;
}

const SvgBorder: React.FC<SvgBorderProps> = (props: SvgBorderProps) => {
  const {
    className = '',
    color = '#1199FF',
    children,
    borderPath,
    borderProps,
    backgroundOpacity = 0.1,
    fill,
    ...otherProps
  } = props;
  const ref = useRef(null);
  const { width = 100, height = 100 } = useSize((ref as unknown) as HTMLElement);

  const path = typeof borderPath === 'function' ? borderPath(width, height) : borderPath;

  const bgColor = fill ?? Color(color).alpha(backgroundOpacity)
    .string();

  return (
    <svg
      ref={ref}
      className={`${styles.root} ${className}`}
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      {...otherProps}
    >
      {path && (
        <>
          <path id="svg-main-path" stroke="transparent" fill={bgColor} d={path} />
          <path className="svg-border-edge" stroke={color} fill="none" d={path} {...borderProps} />
        </>
      )}
      {children}
    </svg>
  );
};

export default SvgBorder;
