import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import { history, Location, useIntl, useLocation } from 'umi';

import FormBase from './components/FormBase';
import FooterButton from '@/components/FooterButton';

import FormBaseMobile from './components/Mobile/FormBase';
import FooterButtonMobile from '@/components/FooterButtonMobile';

import GuideButton from '@/components/GuideButton';

import Success from './components/Success';
import SuccessMobile from './components/Mobile/Success';

import { cancelTxnPay, completePayment } from '@/services/0028F';
import { completeOvsapTxn, saveVehRegTaxTxn, validateTxnForCompleteVehReg } from '@/services/0033D-ii';
import { isMobile } from '@/utils';
import { Page } from '@gov-mo/mpaas-js-bridge';
import { getResponseMessage } from '@/locales/lang';

const classPrefix = isMobile() ? 'page-33d-ii-mobile' : 'page-33d-ii';

const px = pc(classPrefix);

type QueryLocation = {
  query: {
    txnId: string;
    reserved1: string;
    requestId: string;
    ID: string;
  };
};

const NewCarSignPage = () => {
  const ComFormBase = isMobile() ? FormBaseMobile : FormBase;
  const ComFooterButton = isMobile() ? FooterButtonMobile : FooterButton;

  const [step, setStep] = useState(1);
  const intl = useIntl();
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));

  const [showPrevStep, setShowPrevStep] = useState(true);
  const [showNextStep, setShowNextStep] = useState(true);
  const [showClose, setShowClose] = useState(true);
  const [showTempStore, setShowTempStore] = useState(true);
  const [showCloseAndTempStore, setShowCloseAndTempStore] = useState(false);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);
  const [disableTempStore, setDisableTempStore] = useState(false);
  const [disableCloseAndTempStore, setDisableCloseAndTempStore] = useState(false);
  const [tipMsg, setTipMsg] = useState<string>('');
  const [paySelected, setPaySelected] = useState<string>('');
  const [speedUp, setSpeedUp] = useState('N');
  const [_, setIsNeedPay] = useState<boolean>(true);

  const location = useLocation() as Location & QueryLocation;

  const txnId = location.query.txnId! || location.query.reserved1!;
  const requestId = location.query?.requestId || ''; // BOC 帶這個參數
  const ID = location.query?.ID || ''; // BNU 帶這個參數

  const baseUrl = window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const handlePaySelected = (payMethod: string) => {
    setPaySelected(payMethod);
  };

  const handleSpeedUp = (isSpeedUp: string) => {
    setSpeedUp(isSpeedUp);
  };

  const handleIsNeedPay = (isNeedPay: boolean) => {
    setIsNeedPay(isNeedPay);
  };

  const prevStep = () => {
    if (step <= 1) {
      isMobile() && history.push('/web/vem');
      return;
    }

    setStep(step - 1);
  };

  const [isCancelPay, setIsCancelPay] = useState<boolean>(false);

  const onSetIsCancelPay = (state: boolean) => {
    setIsCancelPay(state);
  };

  const handleCancelTxnPay = async () => {
    if (!txnId) return;
    const res = await cancelTxnPay([txnId]);
    if (res && res.code !== '0') {
      setTipMsg(getResponseMessage(res));
      return;
    }
    // 刷新页面
    window.location.reload();
  };

  const submit = async () => {
    setTipMsg('');

    // 是否是取消支付
    if (isCancelPay) {
      await handleCancelTxnPay();
      return;
    }

    let pageUrl = `ovsap/web/vem/0033D-ii/newcar-reg-pay?page=success`;
    if (paySelected === 'BOCP' || paySelected === 'BOC' || paySelected === 'BNU') {
      pageUrl = baseUrl + '/' + pageUrl;
    }
    const data: completePaymentParams = {
      txnId: txnId!,
      payMethodCodeKey: paySelected,
      lang: 'zh_TW',
      ua: isMobile() ? 'MobileWeb' : 'PCWeb',
      remark: '',
      pageUrl,
    };
    try {
      const ret = await completePayment(data);
      if (!ret) return;
      if (ret.code === '0') {
        const { data } = ret;
        if (data.payUrl) {
          window.location.href = data.payUrl;
        } else if (data.returnHTML) {
          document.write(data.returnHTML?.replace(/\\/, ''));
        }
      } else {
        setTipMsg(getResponseMessage(ret));
      }
    } catch (e: any) {
      setTipMsg(e.message);
    }
  };

  const nextStep = async () => {
    if (step === 1) {
      try {
        if (!txnId) return;
        setTipMsg('');
        setDisableNextStep(true);

        const ret = await validateTxnForCompleteVehReg(txnId, speedUp);
        if (!ret) return;

        if (ret.code === '0') {
          if (ret.data?.isNeedToPay) {
            setStep(step + 1);
          } else {
            const res = await completeOvsapTxn(txnId);
            if (res && res.code === '0') {
              history.push(`/web/vem/0033D-ii/newcar-reg-pay?page=success&txnId=${txnId}`);
            }
          }
        } else {
          setTipMsg(getResponseMessage(ret));
        }

      } catch (e: any) {
        setTipMsg(e.message);
      } finally {
        setDisableNextStep(false);
      }
    }

    if (step === 2) {
      try {
        setDisableNextStep(true);
        await submit();
      } finally {
        setDisableNextStep(false);
      }
      // setStep(step + 1);
    }

    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const handleClose = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const handleTempStore = async () => {
    setDisableTempStore(true);
    try {
      await saveVehRegTaxTxn({ txnId, speedUp });
    } finally {
      setDisableTempStore(false);
    }
  };

  const handleCloseAndTempStore = () => {
    saveVehRegTaxTxn({ txnId, speedUp });
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  useEffect(() => {
    setDisablePrevStep(step <= 1);
  }, [step]);

  const isSuccessPage = () => location.query.page === 'success' || location.search?.includes('page=success');

  if (isSuccessPage()) {
    return isMobile() ? (
      <SuccessMobile txnId={txnId} requestId={requestId} ID={ID} />
    ) : (
      <Success txnId={txnId} requestId={requestId} ID={ID} />
    );
  }

  return (
    <>
      <ComFormBase
        step={step}
        classPrefix={classPrefix}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        nextStep={nextStep}
        setNextStepText={(text) => {
          setNextStepText(text);
        }}
        setShowClose={(flag) => {
          setShowClose(flag);
        }}
        setShowTempStore={(flag) => {
          setShowTempStore(flag);
        }}
        setShowCloseAndTempStore={(flag) => {
          setShowCloseAndTempStore(flag);
        }}
        setShowPrevStep={setShowPrevStep}
        txnId={txnId ?? ''}
        tipMsg={tipMsg}
        onPaySelected={handlePaySelected}
        onSpeedUp={handleSpeedUp}
        onIsNeedPay={handleIsNeedPay}
        handlePrevStep={() => {
          prevStep();
        }}
        onSetIsCancelPay={onSetIsCancelPay}
      />
      <GuideButton itemId="ApplicationNotice33DII" />
      <ComFooterButton
        // handleClose={handleClose}
        className={px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        // showTempStore={showTempStore} 暫時屏蔽暫存按鈕
        showTempStore={false}
        showCloseAndTempStore={showCloseAndTempStore}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        setShowTempStore={() => {
          setShowTempStore;
        }}
        setShowCloseAndTempStore={() => {
          setShowCloseAndTempStore;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        disableTempStore={disableTempStore}
        disableCloseAndTempStore={disableCloseAndTempStore}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        setDisableTempStore={() => {
          setDisableTempStore;
        }}
        setDisableCloseAndTempStore={() => {
          setDisableCloseAndTempStore;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
        handleTempStore={() => {
          handleTempStore();
        }}
        handleCloseAndTempStore={() => {
          handleCloseAndTempStore();
        }}
      />
    </>
  );
};

export default NewCarSignPage;
