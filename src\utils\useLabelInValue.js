import { useCallback, useEffect, useMemo } from 'react';
import { parseValue } from './selectRadio';

/**
 * 下拉框数据源字段映射关系
 * @property {string} LABEL 标签
 * @property {string} VALUE 值
 * @property {string} CHILDREN 子元素
 */
const OPTIONS_FIELDS_MAP = {
  LABEL: 'label',
  VALUE: 'value',
  CHILDREN: 'children',
};

/**
 * 是否需transformValue转换
 * @param {*} v 值
 * @return {boolean}
 */
const shouldTransform = (v) => {
  const type = Object.prototype.toString.call(v);
  if (type === '[object Array]') {
    return v.some(shouldTransform);
  }
  return type === '[object String]' || type === '[object Number]';
};

/**
 * 在开发表单组件-下拉框时，PC端和移动端有很多重复的逻辑，useLabelInValue 就是帮你抽离这些重复逻辑的 Hook。
 * @param {object} props
 * @param {*} props.value 表单项的值
 * @param {function} props.onChange FormItem自动添加的方法
 * @param {array} optionDataSource 下拉框数据源
 * @param {object} options 可选项
 * @param {object} options.optionDataSourceFields 下拉框数据源字段映射关系
 * @return {function} transformValue 提交时格式化value的方法
 * @return {*} value 格式化后的value
 */
export default function useLabelInValue({ value, onChange }, optionDataSource, options = {}) {
  const optionDataSourceFields = useMemo(
    () => options?.optionDataSourceFields ?? OPTIONS_FIELDS_MAP,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  /**
   * 获取数据字典对应key
   * @param {*} v 值
   * @return {string} label
   */
  const getLabel = useCallback(
    (v) => {
      let queue = [];
      queue = queue.concat(optionDataSource);
      while (queue.length) {
        const temp = queue.shift();
        if (temp[optionDataSourceFields.CHILDREN]) {
          queue = queue.concat(temp[optionDataSourceFields.CHILDREN]);
        }

        /* 判断条件 */
        if (temp[optionDataSourceFields.VALUE] === v) {
          return temp[optionDataSourceFields.LABEL];
        }
      }
      return '';
    },
    [optionDataSource, optionDataSourceFields],
  );

  /**
   * 格式化value
   * @param {*} v 值
   * undefined -> undefined
   * string -> { value: string, label: label}
   * [string] -> [{ value: string, label: label}]
   */
  const transformValue = useCallback(
    (v) => {
      switch (Object.prototype.toString.call(v)) {
        case '[object Array]':
          /* 应该仅有一层数组包裹 */
          return v.map(transformValue);
        case '[object Number]':
        case '[object String]':
          return {
            value: v,
            label: getLabel(v),
          };
        default:
          return v;
      }
    },
    [getLabel],
  );

  useEffect(() => {
    if (shouldTransform(value)) {
      onChange(transformValue(value));
    }
  }, [onChange, value, transformValue]);

  const formattedValue = useMemo(() => parseValue(value), [value]);

  return {
    transformValue,
    value: formattedValue,
  };
}
