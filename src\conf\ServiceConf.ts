/**
 * 默认面包屑路径,一个服务两个事项的情况根据itemCode区分
 */
import { useIntl } from 'umi';
const intl = useIntl();
const getDefaultBreadcrumbItems = (serviceName?: string) => [
  { title: intl.formatMessage({ id: 'home' }) },
  { title: '身份证明局(DSI)' },
  { title: serviceName ?? '' },
];

const ServiceInfo = {
  ServiceList: [
    {
      name: intl.formatMessage({ id: 'demo_matters' }),
      itemId: '1',
      itemCode: '1-035-2-1-1065-07',
      stepInfo: [
        {
          sort: 1,
          stepType: '2',
          stepName: '申请资料',
          itemResultReceiveMobieRespList: [
            {
              receiveType: '2,1',
              resultType: '2,1',
              resultName: intl.formatMessage({ id: 'demo_matters' }),
            },
          ],
          itemMaterialRespList: [],
        },
        {
          sort: 2,
          stepType: '3',
          stepName: '资料确认',
          itemResultReceiveMobieRespList: [],
          itemMaterialRespList: [],
          needSubmit: true,
        },
        {
          sort: 3,
          stepType: '4',
          stepName: '身份识别',
          itemResultReceiveMobieRespList: [],
          itemMaterialRespList: [],
        },
        {
          sort: 4,
          stepType: '5',
          stepName: '支付信息',
          itemResultReceiveMobieRespList: [],
          itemMaterialRespList: [],
        },
      ],
    },
  ],
};
export { ServiceInfo, getDefaultBreadcrumbItems };
