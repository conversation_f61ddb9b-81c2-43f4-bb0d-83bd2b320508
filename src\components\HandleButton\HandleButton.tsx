import React from 'react';
import { Button } from 'antd-mobile';
import pc from 'prefix-classnames';
import './HandleButton.less';

const px = pc('handle-button');

type ButtonType = {
  text: string;
  onClick?: () => void;
  disabled?: boolean;
};
export interface HandleButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  leftButton?: ButtonType;
  rightButton?: ButtonType;
}

const handleClick = (disable: boolean | undefined, onCLick: (() => void) | undefined) => {
  if (!disable && onCLick) {
    onCLick();
  }
};

function HandleButton(props: HandleButtonProps) {
  const { leftButton, rightButton, className = '', ...otherProps } = props;

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      {leftButton && (
        <Button
          type="ghost"
          className={px('btn')}
          disabled={leftButton.disabled}
          size="large"
          onClick={() => {
            handleClick(leftButton.disabled, leftButton.onClick);
          }}
        >
          {leftButton.text}
        </Button>
      )}
      {rightButton && (
        <Button
          className={px('btn')}
          disabled={rightButton.disabled}
          type="primary"
          size="large"
          onClick={() => {
            handleClick(rightButton.disabled, rightButton.onClick);
          }}
        >
          {rightButton.text}
        </Button>
      )}
    </div>
  );
}

export default HandleButton;
