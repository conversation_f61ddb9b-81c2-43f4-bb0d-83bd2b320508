/* eslint-disable max-lines */
import React, { useRef, useEffect, useState } from 'react';
import HandleButton from '@/components/HandleButton';
import { usePersistFn, useMount } from 'ahooks';
import { Toast, Modal } from 'antd-mobile';
import type { FormInstance } from 'antd';
import {
  StepModelsState,
  ApplicantInformationsModelsState,
  FormModelsState,
  receiveResultModelsState,
  UploadModelsState,
  PaymentModelsState,
  getLocale,
  useIntl,
  history,
  useSelector,
  useDispatch,
} from 'umi';
import { CloseOutlined } from '@ant-design/icons';
// import serviceInfoPng from '@/assets/serviceInfo.png';
import { MoBaseContainer as BaseContainer } from '@gov-mo/components';
import HeaderProgress from '@/components/HeaderProgress';
import { ServiceInfo } from '@/conf/ServiceConf';
import { Page, Pay, Infos, Utils } from '@gov-mo/mpaas-js-bridge';
import GuideButton from '@/components/GuideButton';
import type { FetchResponse } from '@/utils/fetch';
import { transform2HantFormat } from '@/locales/lang';
import scrollToFirstError from '@/utils/formUtils';
import { getLoginUserInfo } from '@/services/UserApi';
import { savePersonalCert, isPaySuccess } from './services/serviceStepApi';
import DataCheck from './components/DataCheck';
import StepsPopover from './components/StepsPopover';
import type {
  StepInfoListType,
  ReceiveResultType,
  // FormDetailType,
  MaterialsListType,
  StepInfoType,
} from './types/defs';
import FormInfo from './components/FormInfo';
import { stepType, modelsType, resultTypeEnum } from './config';
import ReceiveResult from './components/ReceiveResult';
import PaymentInformation from './components/PaymentInformation';
import styles from './ServiceStep.less';
import IdentityAuth, { handleAuth } from './components/IdentityAuth';
import UploadFile from './components/UploadFile';
import { getParams } from './utils';
import useStep from './useStep';

type updataModelsType = (
  payload:
    | Partial<StepModelsState>
    | Partial<ApplicantInformationsModelsState>
    | Partial<FormModelsState>
    | Partial<receiveResultModelsState>
    | Partial<UploadModelsState>
    | Partial<PaymentModelsState>,
  type: modelsType,
) => void;

function ServiceStep() {
  const intl = useIntl();
  // 从models里取状态
  const allModels = useSelector(
    ({
      stepModels,
      applicantInformationsModels,
      formModels,
      receiveResultModels,
      uploadModels,
      paymentModels,
    }: {
      stepModels: StepModelsState;
      applicantInformationsModels: ApplicantInformationsModelsState;
      formModels: FormModelsState;
      receiveResultModels: receiveResultModelsState;
      uploadModels: UploadModelsState;
      paymentModels: PaymentModelsState;
    }) => ({
      stepModels,
      applicantInformationsModels,
      formModels,
      receiveResultModels,
      uploadModels,
      paymentModels,
    }),
  );
  const {
    stepModels: { step, maxStep, itemSecurityLevel, workNo },
    applicantInformationsModels: { applicantInformations },
    formModels: { formData },
    receiveResultModels: { receiveResult, isShowOnly },
    paymentModels: { commodityList, total },
  } = allModels;
  const dispatch = useDispatch();
  const _this = useRef<{
    // 是否已经通过身份识别
    isAuth: boolean;
    // 是否可以触发返回，主要与表单交互，表单有打开一个容器的操作，当他打开时返回的控制权交予表单
    canGoback: boolean;
    // 表单，结果物表单实例及起初始值，初始值用于判断用户是否更改过表单内容，作为是否需要保存草稿的依据
    formRenderInstance: FormInstance | null;
    receiveResultInstance: FormInstance<{ receiveResult?: ReceiveResultType }> | null;
    uploadInstance: FormInstance<Record<string, MaterialsListType>> | null;
    initFormValue?: string;
    initResultValue?: string;
    initUploadValue?: string;
    isFirstFace: boolean;
    secondId: string;
  }>({
    isAuth: false,
    canGoback: true,
    formRenderInstance: null,
    receiveResultInstance: null,
    uploadInstance: null,
    isFirstFace: true,
    secondId: '',
  }).current;

  const {
    location: { query = {} },
  } = history;
  const { itemCode = '' } = query as Record<string, string>;

  // 步骤信息状态
  const [stepInfoList, setStepInfoList] = useState<StepInfoListType>([]);
  // 过滤纯展示情况的结果物步骤;
  const stepInfoToShowList = isShowOnly
    ? stepInfoList.filter((i) => i.stepType !== stepType.resultReceive)
    : [...stepInfoList];
  const currentStep = stepInfoToShowList[step - 1] || {};

  // 用于步骤切换时回到顶部操作
  const scrollRef = useRef<HTMLDivElement>(null);

  // 点击右上角"x"的时候，提示框的控制
  const goBackModal = useRef<{ close: () => void } | null>(null);

  // 更新/初始化models
  const updataModels = usePersistFn<updataModelsType>((payload, type) => {
    dispatch({
      type: `${type}/update`,
      payload,
    });
  });
  const initModels = usePersistFn((models: modelsType[]) => {
    models.forEach((type) => {
      dispatch({ type: `${type}/init` });
    });
  });

  function createReceiveResult(stepInfo: StepInfoType): ReceiveResultType {
    return stepInfo.itemResultReceiveMobieRespList.map((item) => ({
      // cartType: item.cartType,
      efficetiveTimeInterval: item.efficetiveTimeInterval,
      normalTimeInterval: item.normalTimeInterval,
      premiumAble: -1,
      premiumTimeInterval: item.premiumTimeInterval,
      receiveType: [Number(item.receiveType?.split(',')[0])],
      resultLanguage: (item.resultLanguage ?? '').split(',')[0],
      resultNameZhMO: item.resultName,
      resultReceiveId: item.resultReceiveId,
      resultType: Number(item.resultType?.split(',')[0]),
      surrogateAble: item.surrogateAble,
      timeUnit: item.timeUnit,
    }));
  }

  function createShowOnly(stepInfo: StepInfoType): boolean {
    return stepInfo.itemResultReceiveMobieRespList.every((item) => {
      const { resultType, premiumAble, resultLanguage } = item;
      const resultLanguageList = (resultLanguage ?? '').split(',');
      return (
        resultType === String(resultTypeEnum.electronic) &&
        premiumAble === 0 &&
        resultLanguageList.length <= 1
      );
    });
  }

  // 存储用户信息
  const storageUserInfo = async (userInfo: FetchResponse<Record<string, string>>) => {
    const nameCn = userInfo.data.nameCn || userInfo.data.firstName || '';
    const [lastnameCn, fistnameCn] = nameCn.split(',');
    const [lastnamePt, fistnamePt] = (userInfo.data.namePt || '').split(',');
    updataModels(
      {
        applicantInformations: {
          ...applicantInformations,
          applicantLastnameZh: lastnameCn,
          applicantNameZh: fistnameCn,
          applicantLastnameFl: lastnamePt,
          applicantNameFl: fistnamePt,
          applicantCertType: Number(userInfo.data.identityType),
          applicantCertTypeDesc: userInfo.data.identityTypeDesc,
          applicantCertNo: userInfo.data.identityNo,
          mobilePhone: userInfo.data.mobile,
          acceptInformType: intl.formatMessage({ id: '短讯' }),
          euid: userInfo.data.euid,
        },
      },
      modelsType.applicantInformationsModels,
    );
  };

  const { checkQuaFn } = useStep();

  const checkQuality = async () => {
    let userInfo: FetchResponse<Record<string, string>>;
    if (Utils.isGovMoApp()) {
      userInfo = await Infos.requestProfile() as any;
    } else {
      userInfo = await getLoginUserInfo();
    }
    if (userInfo.success) {
      storageUserInfo(userInfo);
      const res = await checkQuaFn({
        idNumber: userInfo.data.identityNo,
        idType: userInfo.data.identityType,
        serviceCode: itemCode,
      });
      if (!res.success) {
        Modal.alert(intl.formatMessage({ id: '提示' }), res.message, [
          {
            text: intl.formatMessage({ id: '确认' }),
            onPress: () => {
              Page.close();
            },
          },
        ]);
      }
    }
  };

  // 初始化获取步骤信息
  useMount(async () => {
    Toast.loading(intl.formatMessage({ id: '请稍候' }), 0);
    await checkQuality();
    Toast.hide();
    const resList: StepInfoListType = [];
    const service = ServiceInfo.ServiceList.find((item) => item.itemCode === itemCode);
    updataModels(
      {
        itemName: intl.formatMessage({ id: service?.name }),
        itemCode: service?.itemCode,
      },
      modelsType.stepModels,
    );
    service?.stepInfo.forEach((stepInfo: StepInfoType) => {
      if (stepInfo.stepType === stepType.resultReceive || stepInfo.stepType === stepType.form) {
        // 用户无可填操作时不展示
        const currentIsShowOnly = createShowOnly(stepInfo);
        updataModels(
          {
            resultReceiveConfig: stepInfo.itemResultReceiveMobieRespList,
            // hasForm: currentHasForm,
            isShowOnly: currentIsShowOnly,
            receiveResult: receiveResult.length ? receiveResult : createReceiveResult(stepInfo),
          },
          modelsType.receiveResultModels,
        );
        resList.push(stepInfo);
      } else {
        if (stepInfo.stepType === stepType.upload) {
          updataModels(
            {
              materialConfig: stepInfo.itemMaterialRespList ?? [],
            },
            modelsType.upload,
          );
        }
        resList.push(stepInfo);
      }
    });
    setStepInfoList(resList);
  });

  useEffect(() => {
    // 记录当前到过的最大步骤数，用于步骤间跳转
    if (maxStep < step) {
      updataModels({ maxStep: step }, modelsType.stepModels);
    }
  }, [step]);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo(0, 0);
    }
  }, [step]);

  // 保存或提交
  const handleSaveOrSubmit = usePersistFn<(status: 0 | 1) => Promise<boolean>>(async (status) => {
    if (status === 1) {
      initModels([
        modelsType.applicantInformationsModels,
        modelsType.formModels,
        modelsType.receiveResultModels,
        modelsType.upload,
      ]);
      history.push('/complete-submission');
      return true;
    }
    Toast.loading(intl.formatMessage({ id: '請稍候' }), 0);
    const curFormData: Record<string, unknown> = _this.formRenderInstance
      ? _this.formRenderInstance.getFieldsValue()
      : formData;
    const params = {
      ...getParams({ applicantInformations, receiveResult, formData: curFormData }),
      itemCode,
    };
    const res = await savePersonalCert(params).finally(() => {
      Toast.hide();
    });
    if (res.success) {
      // 保存办件编码，清空models以及草稿缓存
      updataModels(
        {
          step: 1,
          workNo: res.data.applyNo,
          forecastTime: res.data.deliveryTime,
        },
        modelsType.stepModels,
      );
    } else {
      Toast.fail(res.message);
    }
    return res.success;
  });

  // 返回并清空models方法，判断是否需要暂存草稿
  const handleGoBack = usePersistFn(() => {
    initModels([
      modelsType.stepModels,
      modelsType.applicantInformationsModels,
      modelsType.formModels,
      modelsType.receiveResultModels,
      modelsType.upload,
      modelsType.paymentModels,
    ]);
    Page.close();
  });

  // 退出时弹出的对话框方法
  const handleGoBackModal = usePersistFn(() => {
    if (goBackModal.current) return;
    goBackModal.current = Modal.alert(
      intl.formatMessage({ id: '温馨提示' }),
      intl.formatMessage({ id: '确认放弃当前操作，并退出' }),
      [
        {
          text: intl.formatMessage({ id: '取消' }),
          onPress: () => {
            goBackModal.current?.close();
            goBackModal.current = null;
          },
        },
        {
          text: intl.formatMessage({ id: '确认' }),
          onPress: () => {
            handleGoBack();
          },
        },
      ],
    );
  });

  const handleNext = () => {
    // 最后一步，提交，后续也需根据需求区分是否有提交不在最后一步的情况情况
    if (step === stepInfoToShowList.length) {
      handleSaveOrSubmit(1);
    } else {
      updataModels({ step: step + 1 }, modelsType.stepModels);
    }
  };

  const handlePrev = () => {
    updataModels({ step: step - 1 }, modelsType.stepModels);
  };

  // 返回事件
  const handleBackOrStepChange = usePersistFn(() => {
    if (_this.canGoback) {
      // 第一步為返回并清空models
      if (step === 1) {
        handleGoBackModal();
      } else if (currentStep.stepType === stepType.payment) {
        // 事项账单页面，返回操作，添加申请支付状态查询（逻辑同“付款”按钮），若是已支付，不允许返回，直接进入下一步
        Toast.loading(intl.formatMessage({ id: '請稍候' }), 0);
        isPaySuccess(commodityList.billId).then((res) => {
          Toast.hide();
          if (res.data) {
            handleNext();
          } else {
            handlePrev();
          }
        });
      } else {
        // 其余为返回上一步
        handlePrev();
      }
    }
  });

  // 底部右按鈕點擊
  const handleNextStep = usePersistFn(async () => {
    if (currentStep.stepType === stepType.faceRecognition /* && !_this.isAuth */) {
      handleAuth({
        applyNo: workNo,
        itemSecurityLevel,
        isFirstFace: _this.isFirstFace,
        secondId: _this.secondId,
        setSecondId: (id: string) => {
          _this.secondId = id;
        },
      });
      return;
    } else if (
      currentStep.stepType === stepType.form ||
      currentStep.stepType === stepType.resultReceive
    ) {
      // 表单类步骤触发校验并保存
      const formValidateFields = () =>
        scrollToFirstError(_this.formRenderInstance?.validateFields, _this.formRenderInstance);
      const resultValidateFields = () =>
        scrollToFirstError(
          _this.receiveResultInstance?.validateFields,
          _this.receiveResultInstance,
        );
      const [formValue, receiveResultValue] = await Promise.all([
        formValidateFields(),
        resultValidateFields(),
      ]);
      if (formValue) updataModels({ formData: formValue }, modelsType.formModels);
      if (receiveResultValue.receiveResult) {
        updataModels(
          { receiveResult: receiveResultValue.receiveResult },
          modelsType.receiveResultModels,
        );
      }
    } else if (currentStep.stepType === stepType.upload && _this.uploadInstance?.validateFields) {
      const uploadValue = await _this.uploadInstance.validateFields();
      const uploadList: MaterialsListType = [];
      Object.keys(uploadValue).forEach((key) => {
        uploadValue[key].forEach((file) => {
          uploadList.push(file);
        });
      });
      updataModels({ applicantMaterials: uploadList }, modelsType.upload);
    } else if (currentStep.stepType === stepType.payment) {
      const res = await isPaySuccess(commodityList.billId);
      if (!res.data) {
        await Pay.start(
          {
            title: intl.formatMessage({ id: 'Demo事项' }),
            amount: total.toFixed(2),
            billId: commodityList.billId,
            cashierLanguage: transform2HantFormat(getLocale()),
            currency: commodityList.unit,
            terminalType: 'MOBILE',
            tarUrl: window.location.origin,
            // 各事项根据后端接口是否满足自行定制化修改
            orderServer: `${API_HOST}/payment/order`,
            stateServer: `${API_HOST}/payment/bill/success`,
            payChannels: ['BOCPAY', 'MPGS', 'BOCCARDPAY', 'MPAY'],
            // 部門自定義支付服務參數
            isProxyOrderServer: false,
            mpayRenderUrl: `${location.origin}/${window.routerBase.replace('/', '')}mpay.html`,
            redirectRelUri: `${window.routerBase.replace('/', '')}pay-passed`,
            channelServer: `${API_HOST}/pay-channel`,
          },
          { path: `${location.origin}/${window.routerBase.replace('/', '')}pay-begin` },
        );
        return;
      }
    } else if (currentStep.stepType === stepType.dataCheck) {
      const res: boolean = await handleSaveOrSubmit(0);
      if (!res) return;
    }
    handleNext();
  });

  /**
   * 支付流程成功回调
   * ps:流程成功，不一定是支付成功，有可能会是支付失败
   */
  const handlePaySuccess = (e) => {
    // TODO: if success
    // eslint-disable-next-line no-console
    console.log('支付成功', e);
    if (e?.data?.success && currentStep.stepType === stepType.payment) {
      handleNext();
    }
  };

  /**
   * 支付流程中断回调
   */
  const handlePayCancel = () => {
    // eslint-disable-next-line no-console
    console.log('支付被取消');
  };

  // 渲染對應步驟組件
  const renderStep = usePersistFn(() => {
    let res: React.ReactNode;
    switch (currentStep.stepType) {
      case stepType.form:
        res = (
          <FormInfo
            title={currentStep.stepName}
            canGoBack={_this.canGoback}
            setCanGoback={(v: boolean) => {
              _this.canGoback = v;
            }}
            onInstance={(
              instance: FormInstance | null,
              receiveResultInstance: FormInstance | null,
            ) => {
              _this.formRenderInstance = instance;
              if (receiveResultInstance) {
                _this.receiveResultInstance = receiveResultInstance;
              }
              if (!_this.initFormValue && instance) {
                _this.initFormValue = JSON.stringify(
                  Object.keys(formData).length ? formData : instance.getFieldsValue(),
                );
              }
            }}
          />
        );
        break;
      case stepType.dataCheck:
        res = <DataCheck mobile needShowStepList={stepInfoList} />;
        break;
      case stepType.resultReceive:
        res = <ReceiveResult title={currentStep.stepName} />;
        break;
      case stepType.faceRecognition:
        res = (
          <IdentityAuth
            itemSecurityLevel={itemSecurityLevel}
            onSuccess={() => {
              handleNext();
              _this.isAuth = true;
            }}
            isFirstFace={_this.isFirstFace}
            secondId={_this.secondId}
            setSecondId={(id: string) => {
              _this.secondId = id;
            }}
            onFail={() => {
              _this.isFirstFace = false;
            }}
          />
        );
        break;
      case stepType.payment:
        res = <PaymentInformation onSuccess={handlePaySuccess} onCancel={handlePayCancel} />;
        break;
      case stepType.upload:
        res = (
          <UploadFile
            onInstance={(instance: FormInstance | null) => {
              _this.uploadInstance = instance;
              if (!_this.initUploadValue && instance) {
                _this.initUploadValue = JSON.stringify(instance.getFieldsValue());
              }
            }}
          />
        );
        break;
      default:
        res = null;
    }
    return res;
  });
  const renderButtonText = usePersistFn(() => {
    let text = '';
    switch (currentStep.stepType) {
      case stepType.dataCheck:
        text = intl.formatMessage({ id: '確認' });
        break;
      case stepType.payment:
        text = intl.formatMessage({ id: '立即付款' });
        break;
      default:
        text = intl.formatMessage({ id: '下一步' });
    }
    return text;
  });

  return (
    // TODO: BaseContainer可配置化性较差，后续有更新的话可以减少baseRoot这一层嵌套
    <div className={styles.baseRoot}>
      <BaseContainer
        type="custom"
        bodyContentClass={styles.bodyContent}
        titleContent=""
        onLeftClick={handleBackOrStepChange}
        rightContent={
          <div className={styles.headerRightContent}>
            <StepsPopover
              maxStep={maxStep}
              stepList={stepInfoToShowList.map((i) => i.stepName)}
              current={step}
            />
            {/* <div className={styles.divider} /> */}
            <div onClick={handleGoBackModal} className={styles.iconArea}>
              <CloseOutlined
                className={styles.icon}
                role="button"
                aria-label={intl.formatMessage({ id: '關閉' })}
              />
            </div>
          </div>
        }
      >
        <div className={styles.root}>
          <div className={styles.main} ref={scrollRef}>
            <HeaderProgress
              title={intl.formatMessage({ id: 'Demo事项' })}
              progressArr={stepInfoToShowList.map((i) => i.stepName)}
              current={step}
            />
            {renderStep()}
          </div>
          <GuideButton itemId="" />

          <HandleButton
            rightButton={{
              text: renderButtonText(),
              onClick: handleNextStep,
            }}
          />

          {/* <div
            className={styles.serviceIntroduction}
            onClick={() => {
              history.push(`/service-detail?itemId=${itemId}`);
            }}
          >
            <img src={serviceInfoPng} />
            <span>{intl.formatMessage({ id: '事项指南' })}</span>
          </div> */}
        </div>
      </BaseContainer>
    </div>
  );
}

export default ServiceStep;
