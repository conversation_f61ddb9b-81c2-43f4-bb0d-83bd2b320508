import { Infos } from '@gov-mo/mpaas-js-bridge';

export interface PublicModelsState {
  dictionary?: Record<
    string,
    | undefined
    | Array<{
        comment: string;
        dicId: number;
        levelValue: number;
        parentId: number;
        parentPropertyCode: string;
        propertyCode: string;
        propertyName: string;
      }>
  >;
  isDark: boolean;
}

export default {
  namespace: 'public',
  state: {
    dictionary: undefined,
    isDark: false,
  },
  effects: {
    *getDictionary({ forceUpdate = false }, { put, select }) {
      const { dictionary } = yield select((x) => x.public);
      if (dictionary && !forceUpdate) return dictionary;
      const res = yield Infos.getDictionary();
      const { success, data } = res || {};
      let newDictionary = {};
      if (success && data) {
        newDictionary = JSON.parse(data);
        yield put({ type: 'update', dictionary: newDictionary });
      }
      return newDictionary;
    },
  },
  reducers: {
    update: (state, { type, ...newState }) => ({
      ...state,
      ...newState,
    }),
  },
};
