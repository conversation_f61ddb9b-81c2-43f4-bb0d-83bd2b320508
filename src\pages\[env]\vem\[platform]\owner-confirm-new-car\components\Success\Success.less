@prefix: success-one-33d;

.@{prefix} {
  &- {
    &root {
      position: relative;
      padding-top: 30px;
      padding-bottom: 45px;
      min-height: 100%;
    }

    &body {
      margin: 0 auto;
      max-width: 1048px;
      border-radius: 8px;

      .ant-col {
        display: flex;
        gap: 20px;
        padding-bottom: 20px;
      }

      .label {
        min-width: 140px;
        font-size: 16px;
        font-weight: 500;
        color: #6c6c6c;
        line-height: 22px;
      }

      .value {
        font-size: 16px;
        font-weight: 400;
        color: #323232;
        line-height: 22px;
        min-height: 22px;
        height: auto;
      }
    }

    &sectionTitle {
      display: flex;
      padding: 20px;
      text-align: left;
      background-color: rgb(217, 235, 231);
      color: #000;
      font-size: 26px;
      font-weight: bold;
      display: flex;
      gap: 10px;
      align-items: center;
      border-bottom: 1px solid #c4c4c4;
      margin-bottom: 20px;
    }

    &successTitle {
      font-weight: bold;
      font-size: 20px;
      color: #323232;
      height: 60px;
      line-height: 60px;
      border-bottom: 1px solid #c4c4c4;
      margin-bottom: 20px;
    }

    &sectionBody {
      margin: auto;
      font-size: 16px;
      line-height: 22px;
      padding: 24px;
      background-color: rgb(217, 235, 231);
    }

    &footer {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 0.68rem;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10%;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

      gap: 24px;

      .ant-btn {
        background: #fff;
        color: #008378;
        border: 1px solid #008378;

        min-width: 128px;
        height: 48px;
        line-height: 48px;
        border-radius: 24px;
        margin-left: 12px;
        font-size: 16px;
        padding: 0 5px;
      }
    }

    &successBottomTitle {
      font-size: 16px;
      color: #323232;
      border-top: 1px solid #c4c4c4;
      line-height: 30px;
      margin: 20px 0px 10px 0px;
      padding: 20px 0;
    }
  }
}

.footer-container {
  width: 1048px;
  margin: 0 auto;
  display: flex;
  justify-content: flex-end;
}
