export const move = (x: number, y: number) => `M${x} ${y}`;
export const line = (x: number, y: number) => `L${x} ${y}`;
export const lineH = (x: number) => `H${x}`;
export const lineV = (y: number) => `V${y}`;

export type ArrPoint = [number, number];
export const curve = (cp1: ArrPoint, cp2: ArrPoint, endPoint: ArrPoint) =>
  `C${cp1[0]} ${cp1[1]},${cp2[0]} ${cp2[1]},${endPoint[0]} ${endPoint[1]}`;
export const qCurve = (cp: ArrPoint, endPoint: ArrPoint) =>
  `Q${cp[0]} ${cp[1]},${endPoint[0]} ${endPoint[1]}`;
export const sCurve = (cp: ArrPoint, endPoint: ArrPoint) =>
  `S${cp[0]} ${cp[1]},${endPoint[0]} ${endPoint[1]}`;
export const tCurve = (endPoint: ArrPoint) => `T${endPoint[0]} ${endPoint[1]}`;

export const arc = (
  r: ArrPoint,
  rotation: number,
  largeArcFlag: 0 | 1,
  sweepFlag: 0 | 1,
  targetPoint: ArrPoint,
) =>
  `A${r[0]} ${r[1]} ${rotation} ${largeArcFlag} ${sweepFlag} ${targetPoint[0]} ${targetPoint[1]}`;

export type SvgString = string | SvgString[];
export const relative = (s: SvgString): SvgString => {
  if (Array.isArray(s)) {
    return s.map(relative);
  }
  return s.toLowerCase();
};
export const end = () => 'Z';

export const path = (s: SvgString): string => {
  if (Array.isArray(s)) {
    return s.map(path).join('');
  }
  return s;
};
