import React, {
  useRef,
  useState,
  useContext,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useCallback,
} from 'react';
import { useMount } from 'ahooks';
import { Modal, Result, message } from 'antd';
import { useIntl, useSelector } from 'umi';
import authIcon from '@/assets/authIcon.png';
import classPrefix from '@ali-whale/class-prefix';
// import { identificationSignature } from '@/services/publicApi';
import type { StepModelsState } from 'umi';
import { getFirstFaceUrl, getSecondFaceUrl } from '../../services/serviceStepApi';
import type { securityLevel } from '../../config';
import StepContext from '../StepRender/StepContext';
import './WebIdentityAuth.less';

const px = classPrefix('web-auth');
export interface IWebIdentityAuth extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  onSuccess: () => void;
  itemSecurityLevel: securityLevel;
}

function WebIdentityAuth(props: IWebIdentityAuth, ref) {
  const context = useContext(StepContext);
  const { onSuccess } = props;

  const intl = useIntl();
  const _this = useRef<{
    isFirstFace: boolean;
    secondId: string;
  }>({
    isFirstFace: true,
    secondId: '',
  }).current;
  const [visible, setVisible] = useState<boolean>(false);

  const {
    stepModels: { workNo: applyNo },
  } = useSelector(({ stepModels }: { stepModels: StepModelsState }) => ({ stepModels }));

  const handleAuth = () => {
    setVisible(true);
    if (_this.isFirstFace) {
      getFirstFaceUrl({ applyNo, channel: 'web' }).then((res) => {
        if (!res.success) return;
        window.open(res.data);
        const str = decodeURIComponent(res.data);
        const index = str.lastIndexOf('id=');
        let id = str.substring(index + 3);
        if (id.includes('&')) {
          const arr: string[] = id.split('&', 1);
          [id] = arr;
        }
        _this.secondId = id;
      });
    } else {
      getSecondFaceUrl({ id: _this.secondId }).then((res) => {
        window.open(res.data);
      });
    }
  };

  useImperativeHandle(ref, () => ({
    handleAuth,
  }));

  const identityCodeFn = (code: string, id: string) => {
    // identificationSignature({ id, code }).then((res) => {
    //   if (res.success) {
    setVisible(false);
    message.success(intl.formatMessage({ id: '身份校验成功' }));
    onSuccess();
    //   } else {
    //     setVisible(false);
    //     message.warning(res.message || intl.formatMessage({ id: 'code校验失败' }));
    //   }
    // });
  };

  const receiveMessage = useCallback(
    (event: MessageEvent) => {
      // eslint-disable-next-line no-console
      console.log('event', event);
      if (!visible) return; // 用户关闭校验窗口

      if (event.data) {
        const { type, data } = event.data;
        if (type === 'authMessage' && data.success) {
          identityCodeFn(data.code, data.id);
        } else {
          // pass
          setVisible(false);
          _this.isFirstFace = false;
          message.warning(intl.formatMessage({ id: '身份校验失败' }));
        }
      }
    },
    [visible],
  );

  useMount(() => {
    if (!context.isAuth) {
      handleAuth();
    }
  });

  useEffect(() => {
    // 接收postMessage
    window.onmessage = receiveMessage;
    return () => {
      window.onmessage = null;
    };
  }, [receiveMessage]);

  return (
    <>
      {/* <div className={px('title')}>{intl.formatMessage({ id: title })}</div> */}
      {visible ? (
        <Modal
          className={px('modal')}
          visible={visible}
          title={<div />}
          footer={null}
          width="400px"
          onCancel={() => {
            setVisible(false);
            message.warning(intl.formatMessage({ id: '取消校验' }));
          }}
          centered
        >
          <div>{intl.formatMessage({ id: '身份识别中，请勿关闭窗口！' })}</div>
          <img src={authIcon as string} alt="" />
        </Modal>
      ) : (
        context.isAuth && (
          <Result status="success" title={intl.formatMessage({ id: '身份识别成功' })} />
        )
      )}
    </>
  );
}

export default forwardRef(WebIdentityAuth);
