@classprefix: form1-28f;
// @import '../../../。/../styles/listItemMixins.less';

.@{classprefix} {
  &-root {
    margin: 0 0.24rem;
    justify-content: center;

    font-size: 16px;
    .ant-form {
      font-size: 16px;
    }
    .ant-form-item-label > label {
      display: block;
      font-size: 16px;
    }
    .ant-select {
      font-size: 16px;
    }
    .ant-input {
      font-size: 16px;
    }
    .ant-form-item-label {
      display: block !important;
      font-size: 16px !important;
    }
  }

  &-sectionTitle {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 0.18rem;
    color: #232323;
    padding-bottom: 0.1rem;
  }
  &-cardTitle {
    height: 44px;
    padding-left: 40px;
    background: @brand-primary;
    color: #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    border-radius: 12px 12px 0 0;
  }
  &-cardBody {
    border: 1px solid #f0f0f0;
    padding: 20px;
    border-radius: 0 0 12px 12px;
  }

  &-sectionTitle:before {
    display: block;
    content: '';
    width: 0.04rem;
    height: 0.16rem;
    background: @brand-primary;
    margin-right: 0.08rem;
  }

  &-text {
    font-size: 0.16rem;
    line-height: 0.22rem;
    white-space: pre-wrap;
    align-items: baseline;
    height: auto;
  }

  &-form {
    padding-bottom: 0;
  }

  &-row {
    font-size: 0.16rem;
    margin-top: 0.08rem;
    margin-bottom: 0.18rem;
    //  justify-content: center;
    align-items: center;

    .span-t {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    .ant-btn {
      min-width: 0.62rem;
      height: 0.36rem;
      border-radius: 0.24rem;
      margin-left: 0.12rem;
      font-size: 0.16rem;
      line-height: 0.36rem;
      background-color: @brand-primary !important;
      color: #fff;

      &.ant-btn-default {
        background: #ffffff !important;
        color: #fff;
        border: 1px solid @brand-primary;
      }
    }
  }
}

.span-t {
  display: flex;
  align-items: center;
  justify-content: center;
}
.section-title {
  padding: 5px 20px;
  display: flex;
  align-items: center;
  font-size: 0.2rem;
  min-height: 41px;
  color: #ffffff;
  background: #074ab5;
  border-radius: 10px 10px 0px 0px;
  &::before {
    display: none;
  }
}

.section-body {
  padding: 20px 12px;
}
.step-root {
  flex: 1;
  margin: 20px 12px 2px;
  background-color: #ffffff;
  font-size: 16px;
}



