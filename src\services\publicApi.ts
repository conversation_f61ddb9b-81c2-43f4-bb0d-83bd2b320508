import { get, post } from '@/utils/fetch';
import type { FetchResponse } from '@/utils/fetch';

export const getImgByCode = (fileCode) =>
  get('/file/processing/download', { fileCode }, { responseType: 'blob' });

export const identificationSignature = ({ id, code }) =>
  get<FetchResponse<string>>('/identities/signature', { id, code });

export const getSecondPayList = (data) => get('/second-payment', data);

// 獲取服務操作名稱
export const getServiceTitle = (sp: string) =>
  get<FetchResponse<ServiceTitle>>('/oa/sys/getServiceTitle', { spOperationCode: sp });

// 根據txnId，獲取頁面顯示的標題
export const getTxnServiceTitle = (txnId: string) =>
  get<FetchResponse<TxnServiceTitle>>('/oa/txn/getTxnServiceTitle', { txnId: txnId });

// 服务时间查询
export const serviceTimeCheck = (params: serviceTimeCheckParams) =>
  post<FetchResponse<string>>('/oa/sys/serviceTimeCheck', params);

// 系統代碼參數
/**
 * 車輛級別欄位：codetype=31201
 * 車輛用途欄位：codeType = 20001
 * 顏色欄位：codeType = 2
 * 聯絡資料頁面，語言欄位：codeType = 50034
 * 服務申請狀態：code = 20008
 * 車主證件類型：code = 21001
 */
export const getSysCodesByCodeType = (codeType: string, codeStatus: string) =>
  get<FetchResponse<sysCodesByCodeTypeOpt>>('/vta/oa/sys/getSysCodeByTypeRd', {
    codeType: codeType,
    codeStatus: codeStatus,
  });

// VTA
export const getSysCodesByCodeVatType = (codeType: string, codeStatus: string) =>
  get<FetchResponse<sysCodesByCodeTypeOpt>>('/vta/oa/sys/getSysCodeByTypeRd', {
    codeType: codeType,
    codeStatus: codeStatus,
  });

export const checkServiceAvailable = (spOperationCode?: string) =>
  get<FetchResponse<any>>(`/vem/checkServiceAvailable`);

export const getBookingInfo = (spNo: string, vehId: string, language: string) =>
  get<FetchResponse<string>>(`/oa/txn/getBookingInfo`, { spNo, vehId, language });

export const saveBatchCode = (buffer: string, batchId: string) =>
  post<FetchResponse<string>>(`/ignore/saveBatchCode`, { fileBase: buffer, batchId });
