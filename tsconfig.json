{"compilerOptions": {"module": "commonjs", "target": "es2017", "lib": ["es2017", "DOM"], "sourceMap": true, "noImplicitAny": false, "baseUrl": ".", "jsx": "react", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "allowJs": true, "skipLibCheck": true, "experimentalDecorators": true, "strictNullChecks": true, "strict": true, "types": ["node"], "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"]}, "outDir": "./lib", "ignoreDeprecations": "5.0"}, "include": ["src/**/*", "typings.d.ts"], "exclude": ["node_modules", "build", "dist", "scripts", "src/.umi/*", "webpack", "jest"]}