import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';
import { Steps } from 'antd';
import { NavBar } from 'antd-mobile';
import { LeftOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { getTxnServiceTitle } from '@/services/publicApi';
import Form1 from '../Form1';
import Payment from '../Payment';
import Preview from '../Preview';
import { useIntl, history } from 'umi';

import { Page } from '@gov-mo/mpaas-js-bridge';
import { getStatusBarHeight } from '@/utils/hooks';
import { getLangGroup } from '@/locales/lang';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  title: string;
  companyName: string;
  classPrefix: string;
  step: number;
  contentNextStep: number;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (string) => void;
  setCloseText?: (value: string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;

  setShowPrevStep: (boolean) => void;
  setShowNextStep: (boolean) => void;
  contentForNextStep: (number) => void;

  setCreateEXTxnParams: (data) => void;
  createEXTxnParams: createEXTxnParams | undefined;

  setTxnId: (data) => void;
  txnId: string | undefined;
  handlePrevStep: () => void;
}

const FormBase = (props: FormBaseProps) => {
  const intl = useIntl();

  const { step, className = '', ...otherProps } = props;
  const {
    setDisablePrevStep,
    setDisableNextStep,
    nextStep,
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    setShowPrevStep,
    setShowNextStep,
    contentForNextStep,
    contentNextStep,

    setCreateEXTxnParams,
    createEXTxnParams,
    setTxnId,
    txnId,
    handlePrevStep,
  } = otherProps;

  const px = pc(otherProps.classPrefix);

  const [title, setTitle] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [headerOption, showHeaderOption] = useState(false);
  const [entranceInfo, setEntranceInfo] = useState<getRivmByDSFM4Res>();
  const [applied, setApplied] = useState<boolean>(false);
  const [txnStatus, setTxnStatus] = useState<string>('');

  useEffect(() => {
    console.log('[FormBase] step', step);
  }, [step]);

  useEffect(() => {
    if (txnId) {
      getTxnServiceTitle(txnId)
        .then((res) => {
          setTitle(getLangGroup(res?.data?.serviceTitleCn, res?.data?.serviceTitlePt, res?.data?.serviceTitleEn))
          setCompanyName(getLangGroup(res?.data?.companyCname, res?.data?.companyPname, res?.data?.companyEname))
        })
        .catch((e) => {
          console.log('error-- ', e);
        });
    }
  }, [txnId]);

  const handleApplied = (isApplied: boolean) => {
    setApplied(isApplied);
  };

  const initStepOptions: StepOptionsItem[] = [
    {
      sort: 1,
      title: intl.formatMessage({ id: 'application_materials' }),
      steps: [1],
    },
    {
      sort: 2,
      title: intl.formatMessage({ id: 'data_confirmation' }),
      steps: [2],
    },
    {
      sort: 3,
      title: intl.formatMessage({ id: 'owner_data_confirmation' }),
      steps: [2],
    },
    {
      sort: 4,
      title: intl.formatMessage({ id: 'payment_information' }),
      steps: [3],
    },
  ];
  const [stepOptions, setStepOptions] = useState<StepOptionsItem[]>(initStepOptions);

  // 生成右侧表单内容
  const renderRightContent = () => {
    if (step === 3) {
      return (
        <Payment
          txnId={txnId}
          step={step}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          setNextStepText={setNextStepText}
          contentNextStep={contentNextStep}
          contentForNextStep={contentForNextStep}
          setShowClose={setShowClose}
          setShowTempStore={setShowTempStore}
          setShowCloseAndTempStore={setShowCloseAndTempStore}
        />
      );
    }

    // 確認資料
    if (step === 2) {
      return (
        <Preview
          setShowClose={setShowClose}
          setShowTempStore={setShowTempStore}
          setShowCloseAndTempStore={setShowCloseAndTempStore}
          setNextStepText={setNextStepText}
          setCloseText={props.setCloseText}
          step={step}
          contentNextStep={contentNextStep}
          nextStep={nextStep}
          contentForNextStep={contentForNextStep}
          setDisableNextStep={setDisableNextStep}
          setDisablePrevStep={setDisablePrevStep}
          setShowPrevStep={setShowPrevStep}
          setShowNextStep={setShowNextStep}
          txnId={txnId}
          onApplied={handleApplied}
          onTxnStatus={(t) => {
            setTxnStatus(t);
          }}
          setStepOptions={setStepOptions}
          initStepOptions={initStepOptions}
        />
      );
    }
    if (step === 1) {
      return (
        <Form1
          setEntranceInfo={(data) => {
            setEntranceInfo(data);
          }}
          entranceInfo={entranceInfo}
          setCreateEXTxnParams={(data) => {
            setCreateEXTxnParams(data);
          }}
          createEXTxnParams={createEXTxnParams}
          setTxnId={setTxnId}
          txnId={txnId}
          contentNextStep={contentNextStep}
          step={step}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          setShowTempStore={setShowTempStore}
          setShowPrevStep={setShowPrevStep}
          contentForNextStep={contentForNextStep}
        />
      );
    }
    return <></>;
  };

  const findCurrentStep = (step: number) => {
    let currentStep = 1;
    if (step === 2) {
      if (txnStatus === 'P') {
        currentStep = 3;
      } else {
        currentStep = 2;
      }
    } else {
      stepOptions.forEach((item) => {
        if (item.steps.includes(step)) {
          currentStep = item.sort;
        }
      });
    }
    return currentStep - 1;
  };

  const renderTitle = () => {
    if (title) {
      const [before, after] = title?.split(':');
      return (
        <h2 className={px('title')}>
          <div>{companyName}</div>
          <div style={{ marginTop: 10 }}>{before}</div>
          <div style={{ marginTop: 10 }}>{after}</div>
        </h2>
      );
    }
    return (
      <h2 className={px('title')}>
        <div>{otherProps.companyName}</div>
        <div style={{ marginTop: 10 }}>{otherProps.title}</div>
      </h2>
    );
  };

  return (
    <>
      <NavBar
        style={{ paddingTop: getStatusBarHeight() }}
        mode="light"
        leftContent={
          <LeftOutlined
            onClick={() => {
              if (step === 2) {
                history.push('/web/vem');
                return;
              }
              handlePrevStep();
            }}
          />
        }
        rightContent={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {applied ? null : (
              <div className="select">
                <span
                  onClick={() => {
                    showHeaderOption(!headerOption);
                  }}
                >
                  {intl.formatMessage({ id: 'application_steps' })}
                </span>
                <div
                  className="opt-wrap"
                  style={{ display: headerOption ? 'block' : 'none' }}
                  onClick={() => {
                    showHeaderOption(false);
                  }}
                >
                  <div className={px('step')}>
                    <Steps direction="vertical" size="small" current={findCurrentStep(step)}>
                      {stepOptions.map((item) => {
                        return <Steps.Step key={item.sort} title={item.title} icon={item.sort} />;
                      })}
                    </Steps>
                  </div>
                </div>
              </div>
            )}
            <CloseCircleOutlined
              onClick={() => {
                process.env.NODE_ENV === 'production' ? Page.close() : history.push('/web/vem');
              }}
            />
          </div>
        }
      ></NavBar>
      <div
        style={{ paddingTop: 45 + getStatusBarHeight() }}
        className={`${px('root')} ${className}`}
        {...otherProps}
      >
        {renderTitle()}

        <div className={px('stepinfo')}>
          {applied ? null : (
            <div className={`${px('step')} step-h`}>
              <Steps direction="horizontal" size="small" current={findCurrentStep(step)}>
                {stepOptions.map((item) => {
                  return <Steps.Step key={item.sort} title={''} icon={item.sort} />;
                })}
              </Steps>
            </div>
          )}

          <div className={px('form')}>{renderRightContent()}</div>
        </div>
      </div>
    </>
  );
};

export default FormBase;
