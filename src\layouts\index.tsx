import React, { useState } from 'react';
import { Toast } from 'antd-mobile';
import { Alert } from 'antd';
import { isMobile } from '@/utils';
import useTheme from '@/themes/useTheme';
import {
  redirectToGetCode,
  deleteUrlCode,
  getAuthCodeFromUrl,
  checkAuthCodeIsRepeat,
  authorize,
  getFormUrlByKey,
  syncYiWangHeader,
  jsApiSignature,
} from '../services/UserApi';
import MergedLocaleProvider from './components/LocaleProvider/MergedLocaleProvider';
import { CODE_VERSION, generateCodeChallenge, generateCodeVerifier } from '@/utils/auth';
import { getLocale, setIntl, setLocale } from 'umi';
import { getResponseMessage, Lang } from '@/locales/lang';
import { FetchResponse } from '@/utils/fetch';
import { PLATFORM_BUSINESS } from '@/utils/const';
import { SpinProvider } from '@/components/SpinContext';

const NoSignUrl: string[] = [
  // "/web/vem/batch-get",
];

let globleSign: FetchResponse<AuthorizeToken> | boolean | undefined = false;
let inited = false;

async function init(): Promise<FetchResponse<AuthorizeToken> | boolean | undefined> {
  if (inited) {
    return globleSign
  }

  inited = true;

  if (!isMobile() || process.env.NODE_ENV !== 'production' 
      || PLATFORM == PLATFORM_BUSINESS
      || (`${window.location.pathname}${window.location.search}`.startsWith(`/ovsap/web/vem/personal/owner-confirm?page=success`))
      || (`${window.location.pathname}${window.location.search}`.startsWith(`/ovsap/web/vem/personal/owner-confirm-new-car?page=success`))
      || `${window.location.pathname}${window.location.search}`.startsWith(`/ovsap/web/vem/personal/preview-pdf`)
    ) {
    const auth = await syncYiWangHeader();
    if (auth) {
      deleteUrlCode();
      globleSign = true;
    }
  }

  if (!globleSign) {
    const codeVersion = getFormUrlByKey(CODE_VERSION) as string

    const codeVerifier = generateCodeVerifier(128, codeVersion) as string
    const codeChallenge = generateCodeChallenge(codeVerifier, codeVersion)

    let authCode = getAuthCodeFromUrl() as string;
    if (!authCode || checkAuthCodeIsRepeat(authCode)) {
      redirectToGetCode(codeVerifier , codeChallenge);
      return false;
    }

    globleSign = await authorize(authCode, codeVerifier, codeVersion as string);
  }

  await syncYiWangHeader();
  deleteUrlCode();

  if (isMobile()) {
    await jsApiSignature()
  }
  
  return globleSign;
}

export default function (props: any) {
  const {
    location: { query },
  } = props;

  const [showLayout, setShowLayout] = useState<boolean>(false);
  const [tigMsg, setTipMsg] = useState<string>('')
  
  const Layout = isMobile() ? require('./BaseLayout').default : require('./WebLayout').default;
  const [, realPathname] = window.location.pathname.split(window.routerBase);
  const noNeedSign = NoSignUrl.includes(`/${realPathname}`);

  const locate = getLocale()
  if (locate === Lang.简体中文) {
    setIntl(Lang.繁体中文)
    setLocale(Lang.繁体中文, true)
  }

  useTheme(query);

  if (noNeedSign) {
    return <MergedLocaleProvider>{props?.children}</MergedLocaleProvider>;
  }

  React.useEffect(() => {
    init().then((globleSign: FetchResponse<AuthorizeToken> | boolean | undefined) => {
      Toast.hide();
      if (typeof globleSign === 'boolean') {
        setShowLayout(globleSign)
        return
      }

      if (globleSign?.code !== '0') {
        setShowLayout(false)
        setTipMsg(getResponseMessage(globleSign))
        return
      }

      setShowLayout(true)
    });
  }, []);
  return (
    <>

      {
        tigMsg && (
          <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
            <Alert message={tigMsg} type="error" showIcon />
          </div>
        )
      }
      {
        showLayout &&  <SpinProvider><Layout {...props} /></SpinProvider>
      }
    </>
  );
}
