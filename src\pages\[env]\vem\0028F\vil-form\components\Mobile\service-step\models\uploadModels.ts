import type { MaterialConfigType, MaterialsListType } from '../types/defs';

export interface UploadModelsState {
  applicantMaterials?: MaterialsListType;
  materialConfig: MaterialConfigType;
}

export const initUploadModelsState = {
  applicantMaterials: [],
  materialConfig: [],
};

export default {
  namespace: 'uploadModels',
  state: initUploadModelsState,

  /* 更新状态（通用） */
  reducers: {
    update: (state: UploadModelsState, { payload }: { payload: Partial<UploadModelsState> }) => ({
      ...state,
      ...payload,
    }),
    init: () => initUploadModelsState,
  },
};
