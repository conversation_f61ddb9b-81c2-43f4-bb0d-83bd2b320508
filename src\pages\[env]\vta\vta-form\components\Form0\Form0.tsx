import { useIntl } from '@@/plugin-locale/localeExports';
import { Button, Col, Form, Input, InputNumber, Modal, Radio, Row } from 'antd';
import { keys } from 'lodash';
import pc from 'prefix-classnames';
import React, { useEffect, useMemo, useState } from 'react';
import { CarLevel, CarType, useGetEmu } from '../../hooks/useGetEmu';
import { addTxnId } from '../../utils/addTxnId';
import './Form0.less';
import {
  getVTAVehBrandByTxnId,
  updateVTAVehBrandByTxnId,
  createVTATxn,
  selectVehModel,
  checkAddBrandModel,
} from '@/services/vta';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';

const px = pc('vta-form-form-0');

export interface FormZeroProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  txnId?: string;
  setDisablePrevStep: (status: boolean) => void;
  setDisableNextStep: (status: boolean) => void;
  nextStep: () => void;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

const FormZero = (props: FormZeroProps) => {
  const { className = '', txnId, ...otherProps } = props;
  const { setDisableNextStep, nextStepTrigger, handleNextStep } = otherProps;
  let { emuObj } = useGetEmu({
    vehTypeOp: '31201',
    steeringWheelPositionOp: '50118',
    gearboxTypeOp: '31223',
    chandongfangshi1: '22011',
    chandongfangshi2: '22012',
    varSpeedQtyOp: '22013',
    vehBrandCodeOp: '31204',
  });
  const intl = useIntl();
  const [form] = Form.useForm();

  const [isModalOpen, setModalOpen] = useState(false);

  const [modelForm] = Form.useForm();
  async function handleOk() {
    let res = await modelForm.validateFields();
    setVehModelOp((pre) => {
      if (pre) {
        return [
          { label: `${res.addonVal} ${addonAfter}`, value: `${res.addonVal} ${addonAfter}` },
          ...pre,
        ];
      }
      return [{ label: `${res.addonVal} ${addonAfter}`, value: `${res.addonVal} ${addonAfter}` }];
    });
    form.setFieldsValue({ vehModel: `${res.addonVal} ${addonAfter}` });
    setModalOpen(false);
    selectVehModelFn()
  }

  function handleCancel() {
    setModalOpen(false);
  }
  const [financeBureauNum, setFinanceBureauNum] = useState('');
  const [vehBrandCode, setVehBrandCode] = useState();
  const [canEditable, setCanEditable] = useState({});
  let [vehType, setVehType] = useState<CarLevel>();
  useEffect(() => {
    setDisableNextStep(false);
    txnId &&
      getVTAVehBrandByTxnId({ txnId: txnId }).then((res) => {
        let obj: any = {};
        let obj2 = {};
        keys(res?.data).forEach((item) => {
          obj[item] = res?.data[item].value;
          obj2[item] = res?.data[item].editable !== 'Y';
        }, {});
        setCanEditable(obj2);
        selectVehModelFn(obj.vehBrandCode);
        if (obj['steeringWheelPosition'] && obj['gearboxType']) {
          setFlag(true);
        }
        setVehType(obj.vehType);
        setFinanceBureauNum(obj.dsfRefNo);
        form.setFieldsValue(obj);
      });
  }, []);

  let txTypeOp = useMemo(() => {
    return !vehType
      ? []
      : ['L', 'P', 'I', 'S'].includes(vehType + '')
      ? emuObj.chandongfangshi1
      : emuObj.chandongfangshi2;
  }, [vehType, emuObj.chandongfangshi1, emuObj.chandongfangshi2]);
  let [flag, setFlag] = useState(false);
  const onValuesChange = (changedValues: any, allValues: any) => {
    if ('vehType' in changedValues) {
      setVehType(changedValues.vehType);
      form.setFieldsValue({ txType: undefined });
    }
    if ('hasDsfRefNo' in changedValues) {
      setFinanceBureauNum(changedValues.hasDsfRefNo);
    }
    if ('vehBrandCode' in changedValues) {
      setVehBrandCode(changedValues.vehBrandCode);
      form.setFieldsValue({ vehModel: undefined });
    }
    if (allValues['steeringWheelPosition'] && allValues['gearboxType']) {
      setFlag(true);
    } else {
      setFlag(false);
    }
  };
  const postData = async () => {
    try {
      const data = await form.validateFields();
      if (data.txnId) {
        let res = await updateVTAVehBrandByTxnId(data);
        if (res.error) {
          return false;
        }
      } else {
        let res = await createVTATxn(data);
        console.log(res,'res')
        if (res.error) {
          return false;
        }
        addTxnId(res?.data?.txnId);
      }
      return true;
    } catch (error) {
      return false;
    }
  };
  useEffect(() => {
    if (!nextStepTrigger || nextStepTrigger === 0) {
      return;
    }
    // 下一步觸發
    handleNextStep(postData);
  }, [nextStepTrigger]);
  const [addonAfter, setAddonAfter] = useState('M/T');
  const [vehModelOp, setVehModelOp] = useState<any[]>([]);
  const selectVehModelFn = async (code?: any) => {
    let res = await selectVehModel({
      vehBrandCode: code || vehBrandCode,
    });
    setVehModelOp(res?.dataArr?.map((item) => ({ label: item, value: item })) || []);
  };
  useEffect(() => {
    vehBrandCode && selectVehModelFn();
  }, [vehBrandCode]);
  return (
    <div
      className={`${px('root')} ${className} multi-card-container`}
      {...otherProps}
      style={{
        border: '1px solid #f0f0f0',
        padding: '20px',
        marginLeft: 0,
      }}
    >
      <Form form={form} className={px('form')} layout="vertical" onValuesChange={onValuesChange}>
        <Form.Item name="txnId" hidden initialValue={txnId}>
          <Input />
        </Form.Item>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={intl.formatMessage({
                id: 'vehicle_tax_information',
              })}
              name="hasDsfRefNo"
              style={{ marginBottom: 0 }}
                rules={[{ required: true, message: intl.formatMessage({ id: 'please_enter' }) }]}
            >
              <Radio.Group disabled={canEditable['hasDsfRefNo']}>
                <Radio value={'1'}>有</Radio>
                <Radio value={'2'}>沒有</Radio>
              </Radio.Group>
            </Form.Item>
            {financeBureauNum === '1' && (
              <Form.Item
                name="dsfRefNo"
                rules={[{ required: true, message: intl.formatMessage({ id: 'please_enter' }) }]}
              >
                <Input
                  placeholder={intl.formatMessage({ id: 'please_enter' })}
                  disabled={canEditable['dsfRefNo']}
                />
              </Form.Item>
            )}
          </Col>
          <Col span={12}>
            <Form.Item
              label={intl.formatMessage({ id: 'vehType' })}
              name="vehType"
                rules={[{ required: true, message: intl.formatMessage({ id: 'please_enter' }) }]}
            >
              <VtaSelect
                disabled={canEditable['vehType']}
                options={emuObj.vehTypeOp}
                placeholder={intl.formatMessage({ id: 'please_select' })}
              />
            </Form.Item>
          </Col>
          {![CarLevel.輕型摩托車, CarLevel.重型摩托車, CarLevel.半掛車].includes(vehType) ? (
            <Col span={12}>
              <Form.Item
                label={intl.formatMessage({
                  id: 'steeringWheelPosition',
                })}
                rules={[{ required: true }]}
                name="steeringWheelPosition"
              >
                <VtaSelect
                  disabled={canEditable['steeringWheelPosition']}
                  options={emuObj.steeringWheelPositionOp}
                  placeholder={intl.formatMessage({ id: 'please_select' })}
                />
              </Form.Item>
            </Col>
          ) : (
            <></>
          )}
          {![CarLevel.半掛車].includes(vehType) ? (
            <Col span={12}>
              <Form.Item
                label={intl.formatMessage({
                  id: 'gearboxType',
                })}
                rules={[{ required: true }]}
                name="gearboxType"
              >
                <VtaSelect
                  disabled={canEditable['gearboxType']}
                  style={{ width: '100%' }}
                  options={emuObj.gearboxTypeOp}
                  placeholder={intl.formatMessage({ id: 'please_select' })}
                />
              </Form.Item>
            </Col>
          ) : (
            <></>
          )}
          {[CarLevel.半掛車].includes(vehType) ? (
            <></>
          ) : (
            <Col span={12}>
              <Form.Item
                label={intl.formatMessage({
                  id: 'txType',
                })}
                rules={[{ required: true }]}
                name="txType"
              >
                <VtaSelect
                  disabled={canEditable['txType']}
                  placeholder={intl.formatMessage({ id: 'please_select' })}
                  options={txTypeOp}
                />
              </Form.Item>
            </Col>
          )}
          {[CarLevel.半掛車].includes(vehType) ? (
            <></>
          ) : (
            <Col span={12}>
              <Form.Item
                label={intl.formatMessage({
                  id: 'varSpeedQty',
                })}
                rules={[{ required: true }]}
                name="varSpeedQty"
              >
                <VtaSelect
                  disabled={canEditable['varSpeedQty']}
                  placeholder={intl.formatMessage({ id: 'please_select' })}
                  options={emuObj.varSpeedQtyOp}
                />
              </Form.Item>
            </Col>
          )}
          <Col span={12}>
            <Form.Item
              name="vehBrandCode"
              label={`${intl.formatMessage({
                id: 'brand',
              })} （${intl.formatMessage({
                id: 'strOther',
              })}）`}
              rules={[{ required: true }]}
            >
              <VtaSelect
                disabled={canEditable['vehBrandCode']}
                style={{ width: '100%' }}
                options={emuObj.vehBrandCodeOp}
                placeholder={intl.formatMessage({ id: 'please_select' })}
              />
            </Form.Item>
          </Col>
          <Col span={12} style={{position: 'relative'}}>
            <Form.Item
              label={
                <div >
                  {intl.formatMessage({ id: 'style' })} &nbsp;&nbsp;
                  {flag && (
                    <Button
                      type="primary"
                      style={{ position: 'absolute', left: '100%',bottom:'0px' }}
                      size="small"
                      disabled={canEditable['vehModel']}
                      onClick={async () => {
                        const data = await form.validateFields([
                          'steeringWheelPosition',
                          'gearboxType',
                        ]);
                        let res = await checkAddBrandModel(data);
                        setAddonAfter(res?.data || '');
                        setModalOpen(true);
                      }}
                    >
                      {intl.formatMessage({ id: 'add_style' })}
                    </Button>
                  )}
                </div>
              }
              rules={[{ required: true, message: `${intl.formatMessage({ id: 'style' })}` }]}
              name="vehModel"
            >
              <VtaSelect
                disabled={canEditable['vehModel']}
                style={{ width: '100%' }}
                options={vehModelOp}
                placeholder={intl.formatMessage({ id: 'please_select' })}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={intl.formatMessage({ id: 'customModelCode' })}
              rules={[{ required: true }]}
              name="customModelCode"
            >
              <Input
                placeholder={intl.formatMessage({ id: 'please_enter' })}
                disabled={canEditable['customModelCode']}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="vehModelYear"
              label={intl.formatMessage({ id: 'vehModelYear' })}
              rules={[{ required: true }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder={intl.formatMessage({ id: 'please_enter' })}
                disabled={canEditable['vehModelYear']}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Modal
        title={intl.formatMessage({ id: 'add_style' })}
        visible={isModalOpen}
        onOk={handleOk}
        destroyOnClose
        onCancel={handleCancel}
        width={600}
        cancelButtonProps={{ type: 'default' }}
      >
        <Form layout="vertical" form={modelForm}>
          <Form.Item
            label={intl.formatMessage({ id: 'style' })}
            name="addonVal"
            rules={[{ required: true }]}
          >
            <Input addonAfter={addonAfter} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FormZero;
