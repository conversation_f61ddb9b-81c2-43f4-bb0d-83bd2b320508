import React, { useEffect, useMemo } from 'react';
import { Form } from 'antd';
import type { FormInstance } from 'antd';
import type { UploadModelsState } from 'umi';
import { useSelector } from 'react-redux';
import type { MaterialsListType } from '../../types/defs';
import styles from './UploadFile.less';
import UploadForm from './components/UploadForm';
import UploadDetail from './components/UploadDetail';

export interface UploadFileProps {
  isDetail?: boolean;
  title?: string;
  onInstance?: (instance: FormInstance<Record<string, MaterialsListType>>) => void;
}

function UploadFile(props: UploadFileProps) {
  const { isDetail = false, title = '', onInstance } = props;
  const [form] = Form.useForm<Record<string, MaterialsListType>>();
  const { applicantMaterials } = useSelector(
    ({ uploadModels }: { uploadModels: UploadModelsState }) => uploadModels,
  );

  useEffect(() => {
    if (onInstance) {
      onInstance(form);
    }
  }, []);

  const initValue = useMemo(() => {
    const res: Record<string, MaterialsListType> = {};
    applicantMaterials?.forEach((material) => {
      if (!res[material.materialsId]) {
        res[material.materialsId] = [];
      }
      res[material.materialsId].push(material);
    });
    return res;
  }, [applicantMaterials]);

  return (
    <div className={styles.root}>
      {isDetail && <div className={styles.title}>{title}</div>}
      {isDetail ? (
        <UploadDetail />
      ) : (
        <Form initialValues={initValue} form={form}>
          <UploadForm />
        </Form>
      )}
    </div>
  );
}

export default UploadFile;
