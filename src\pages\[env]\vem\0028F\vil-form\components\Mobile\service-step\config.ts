export enum stepType {
  applicantInformations = '1',
  form = '2',
  dataCheck = '3',
  faceRecognition = '4',
  payment = '5',
  upload = '6',
  resultReceive = '7',
}

export enum securityLevel {
  SATISFACTORY = 1,
  HIGH = 2,
  VERY_HIGH = 3,
}

export enum modelsType {
  stepModels = 'stepModels',
  applicantInformationsModels = 'applicantInformationsModels',
  formModels = 'formModels',
  receiveResultModels = 'receiveResultModels',
  upload = 'uploadModels',
  paymentModels = 'paymentModels',
}

export enum resultTypeEnum {
  paper = 1,
  electronic = 2,
}

export enum receiveTypeEnum {
  net = 1,
  personal = 2,
  send = 3,
}
