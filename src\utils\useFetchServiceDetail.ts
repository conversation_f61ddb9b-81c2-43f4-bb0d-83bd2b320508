import { useState } from 'react';
import { useMount } from 'ahooks';
import { getServiceDetail } from '@/services/serviceIntroApi';
import { transform2HantFormat } from '@/locales/lang';
import { getLocale } from 'umi';
import type { ServiceIntroType } from './serviceFetch';

const defaultLocale = 'zh-Hant';

interface Props {
  serviceId: string;
  slug: string;
}

/**
 * 获取常見問題、相關法例、事項指南内容
 * @param {string} serviceId
 * @param {string} slug
 */
export default function useFetchServiceDetail({ serviceId, slug }: Props) {
  const [service, setService] = useState<ServiceIntroType[number]>();
  useMount(async () => {
    // 默认繁体中文
    const language = transform2HantFormat(getLocale()) || defaultLocale;
    getServiceDetail({ language, serviceId }).then((res) => {
      if (res) {
        const target = res.find((item) => item.slug === slug);
        if (target) setService(target);
      }
    });
  });
  return {
    service,
  };
}
