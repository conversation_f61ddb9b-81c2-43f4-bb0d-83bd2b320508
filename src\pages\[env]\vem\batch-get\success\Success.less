@prefix: page-batch-get-success;
@prefix-mobile: page-batch-get-success-mobile;

.@{prefix} {
  &- {
    &root {
      max-width: 1048px;
      margin: 0 auto;
      padding-top: 20px;
      padding-bottom: 0.68rem;
      .my-card-item {
        .my-card-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .ant-btn.ant-btn-dangerous {
            background: #fff;
            color: #f33b40;
            border: 1px solid #f33b40;
          }
        }
      }
      .batch-get-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0.68rem;
        background-color: #fff;
        display: flex;
        align-items: center;
        box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

        .footer-container {
          width: 1048px;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 24px;
          .ant-btn {
            &.ant-btn-default {
              background: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
            }

            min-width: 128px;
            height: 48px;
            line-height: 48px;
            border-radius: 24px;
            margin-left: 12px;
            font-size: 16px;
            padding: 0 5px;
          }
        }
      }
    }
    &body {
      margin: 0 auto;
      max-width: 1048px;
      border-radius: 8px;
      .ant-col {
        display: flex;
        gap: 20px;
        padding-bottom: 20px;
      }
      .label {
        min-width: 140px;
        font-size: 16px;
        font-weight: 500;
        color: #6c6c6c;
        line-height: 22px;
      }
      .value {
        font-size: 16px;
        font-weight: 400;
        color: #323232;
        line-height: 22px;
        min-height: 22px;
        height: auto;
      }
    }
    &sectionTitle {
      display: flex;
      padding: 20px;
      text-align: left;
      background-color: rgb(217, 235, 231);
      color: #000;
      font-size: 26px;
      font-weight: bold;
      display: flex;
      gap: 10px;
      align-items: center;
      border-bottom: 1px solid #c4c4c4;
      margin-bottom: 20px;
    }
    &successTitle {
      font-size: 20px;
      color: #323232;
      height: 60px;
      line-height: 60px;
      border-bottom: 1px solid #c4c4c4;
      margin-bottom: 20px;
    }
    &sectionBody {
      margin: auto;
      font-size: 16px;
      line-height: 22px;
      padding: 24px;
      background-color: rgb(217, 235, 231);
    }
  }
}

.@{prefix-mobile} {
  &- {
    &root {
      position: relative;
      min-height: 100%;
      background-color: #fff;
      h1 {
        font-size: 0.24rem;
        text-align: center;
      }
      .batch-get-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0.68rem;
        background-color: #fff;
        display: flex;
        align-items: center;
        box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

        .footer-container {
          width: 100%;
          padding: 0 20px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          gap: 4%;
          .ant-btn {
            flex: 1;
            height: 48px;
            line-height: 48px;
            border-radius: 24px;
            font-size: 16px;
            padding: 0 5px;
            &.ant-btn-default {
              background: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
            }
          }
        }
      }
    }
    &alert {
      padding: 20px;
    }

    &body {
      margin: 10px auto;
      padding: 20px 0;
      width: 90%;
      border-top: 1px solid #eeeeee;
      border-bottom: 1px solid #eeeeee;
      font-size: 0.14rem;
    }
    .row {
      display: flex;
      margin: 10px 0;
      line-height: 1.5em;
    }
    .label {
      width: 7em;
      color: #aaaaaa;
    }
    .value {
      flex: 1;
      text-align: left;
      word-break: break-all;
    }
  }
}
