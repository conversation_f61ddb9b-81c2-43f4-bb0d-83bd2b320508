@classprefix: form1-28f-mobile;
// @import '../../../。/../styles/listItemMixins.less';

.@{classprefix} {
  &-root {
    margin: 0.2rem 0.12rem 0.2rem;
    background-color: #ffffff;
    font-size: 0.16rem;
    border-radius: 0.12rem;
    overflow: hidden;

    .ant-form {
      font-size: 0.16rem;
    }
    .ant-form-item-label > label {
      font-size: 0.14rem;
      line-height: 0.22rem;
      word-break: break-all;
      flex-direction: row-reverse;
      display: flex;
      justify-content: flex-end;
    }
    .ant-select {
      font-size: 0.16rem;
    }
    .ant-input {
      font-size: 0.16rem;
    }
    .ant-form-item-label {
      display: block !important;
      font-size: 0.16rem !important;
    }
  }

  &-sectionTitle {
    display: flex;
    align-items: center;
    font-weight: 700;
    padding: 0.05rem 0.2rem;
    min-height: 0.41rem;
    font-size: 0.18rem;
    color: #ffffff;
    line-height: 0.21rem;
    background: @brand-primary;
  }

  &-sectionBody {
    padding: 0.2rem 0.12rem;
  }

  &-text {
    font-size: 0.16rem;
    line-height: 0.22rem;
    white-space: pre-wrap;
    align-items: baseline;
    height: auto;
  }

  &-form {
    padding-bottom: 0;
  }

  &-row {
    font-size: 0.16rem;
    margin-top: 0.08rem;
    margin-bottom: 0.18rem;
    //  justify-content: center;
    align-items: center;


    .ant-btn {
      min-width: 0.62rem;
      height: 0.36rem;
      border-radius: 0.24rem;
      margin-left: 0.12rem;
      font-size: 0.16rem;
      line-height: 0.36rem;
      background-color: @brand-primary !important;
      color: #fff;

      &.ant-btn-default {
        background: #ffffff !important;
        color: #fff;
        border: 1px solid @brand-primary;
      }
    }
  }
}

.span-t {
  display: flex;
  align-items: center;
  justify-content: center;
}
.section-title {
  padding: 0.05rem 0.2rem;
  display: flex;
  align-items: center;
  font-size: 0.2rem;
  min-height: 0.41rem;
  color: #ffffff;
  background: @brand-primary;
  border-radius: 0.1rem 0.1rem 0 0;
  &::before {
    display: none;
  }
}


.span-t {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  margin-top: 0.05rem;
}