import React, { useState, useEffect, useContext } from 'react';
import pc from 'prefix-classnames';
import './UploadFile.less';

import { Form, Button, Upload, message, Divider, Alert } from 'antd';
import type { UploadFile, FormProps } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

import {
  getServiceDocs,
  serviceItemFileUpload,
  checkServiceItemFileUpload,
  deleteServiceItemFile,
  getServiceItemFile,
} from '@/services/0028F';
import { useIntl } from 'umi';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import SpinContext from '../../../../../../../components/SpinContext';

const px = pc('uploadFile-28f');

export interface FormSevenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;
  setShowTempStore: (value: boolean) => void;

  txnId: string | undefined;
}

const FormSeven = (props: FormSevenProps) => {
  const intl = useIntl();
  const { className = '', setDisableNextStep, ...otherProps } = props;
  const { contentNextStep, contentForNextStep, setShowTempStore, txnId } = otherProps;

  const [tipMsg, setTipMsg] = useState('');

  const [form] = Form.useForm();

  const [list, setList] = useState<getServiceDocsRes[]>([]);
  const [titleInfo, setTitleInfo] = useState<string>('');
  const [uploadfileList, setUploadfileList] = useState<Record<string, UploadFile[]>>();
  const [fileList, setFileList] = useState<{ [key: string]: File }>();
  const { setLoading } = useContext(SpinContext);
  const getUploadfileList = () => {
    return uploadfileList;
  };

  const getFileList = () => {
    return fileList;
  };

  const onFinish: FormProps['onFinish'] = async (values) => {
    setTipMsg('');
    if (!txnId) {
      setDisableNextStep(false);
      return;
    }
    for (const key in list) {
      const item = (list || [])[key];
      const file = (uploadfileList || [])[`${item?.serviceDocumentID}`];
      if (item?.category === 0 && (!file || file.length === 0)) {
        setTipMsg(`${item.nameZh}`);
        setDisableNextStep(false);
        return;
      }
    }
    const ret = await checkServiceItemFileUpload(txnId).catch((e) => setTipMsg(e.message));
    if (ret.code === '0') {
      contentForNextStep(2);
    } else {
      setTipMsg(getResponseMessage(ret));
      setDisableNextStep(false);
    }
  };

  const onFinishFailed: FormProps['onFinishFailed'] = (errorInfo) => {
    console.log('onFinishFailed', errorInfo);
    setDisableNextStep(false);
  };

  useEffect(() => {
    if (txnId) {
      getServiceDocs(txnId)
        .then((ret) => {
          if (ret.code === '0') {
            const { dataArr, data } = ret;
            setTitleInfo(getLangGroup(data?.docTitleCn, data?.docTitlePt, data?.docTitleEn));
            setList(dataArr);

            const uploadList = (dataArr || []).reduce((acc: any, current) => {
              const key = `${current.serviceDocumentID}`;
              if (!acc[key]) {
                acc[key] = [];
              }

              current?.originalFileName &&
                acc[key].push({
                  name: current?.originalFileName,
                  category: current?.category,
                  uid: `${current?.serviceDocumentID}`,
                });
              return acc;
            }, {});

            console.log('uploadList', uploadList);
            setUploadfileList(uploadList);
            return;
          }
          setTipMsg(getResponseMessage(ret));
        })
        .catch((e) => setTipMsg(e.message))
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [txnId]);

  useEffect(() => {
    contentForNextStep(1);
    setShowTempStore(false);
    setDisableNextStep(false);
    setLoading(true);
    return () => {
      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      form.submit();
    }
  }, [contentNextStep]);

  const handlePreview = async (file: UploadFile) => {
    if (!txnId) return;
    const res = await getServiceItemFile(txnId, Number(file.uid));
    const url = window.URL.createObjectURL(res);
    // 创建一个临时的 <a> 元素来触发下载
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name; // 你可以根据需要更改文件名
    document.body.appendChild(a);
    a.click();
    // 释放 URL 对象
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  return (
    <>
      {tipMsg && (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <Form
          form={form}
          className={px('form')}
          layout="vertical"
          preserve={false}
          scrollToFirstError={true}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
        >
          <div className={px('sectionTitle')}>{titleInfo}</div>
          <div className={px('sectionBody')}>
            {list.map((item, index) => (
              <Form.Item
                label={`${index + 1}. ${item.nameZh}`}
                name={`${item.serviceDocumentID}`}
                required={item?.category === 0}
              >
                <Upload
                  showUploadList={{
                    showPreviewIcon: true,
                    showRemoveIcon: true,
                  }}
                  onDownload={(file) => {
                    console.log('onDownload', file);
                  }}
                  onPreview={handlePreview}
                  // defaultFileList={((item?.originalFileName && [{name: item?.originalFileName }]) || []) as UploadFile[]}
                  fileList={(uploadfileList || [])[`${item.serviceDocumentID}`]}
                  beforeUpload={(file) => {
                    const uploadfileList = getUploadfileList();
                    const fileList = getFileList();

                    let data = { ...uploadfileList };
                    data[String(item.serviceDocumentID)] = [
                      {
                        uid: String(item.serviceDocumentID),
                        name: file.name,
                        status: 'uploading',
                      },
                    ];
                    setUploadfileList(data);

                    let files = { ...fileList };
                    files[String(item.serviceDocumentID)] = file;
                    setFileList(files);

                    return false;
                  }}
                  onRemove={async (file) => {
                    deleteServiceItemFile(txnId as string, item?.serviceDocumentID);
                  }}
                  onChange={(e) => {
                    const uploadfileList = getUploadfileList();
                    const fileList = getFileList();
                    if ('removed' === e.file.status) {
                      let data = { ...uploadfileList };
                      data[e.file.uid] = [];
                      setUploadfileList(data);

                      let files = { ...fileList };
                      delete files[e.file.uid];
                      setFileList(files);
                      return;
                    }

                    if (!txnId) {
                      return;
                    }

                    const file = e.fileList[0];

                    if (!(fileList && fileList[file.uid])) {
                      return;
                    }
                    const formData = new FormData();
                    formData.append('txnId', txnId);
                    formData.append('code', item.code ?? '');
                    formData.append('spServiceDocId', `${item.serviceDocumentID}`);
                    formData.append('nameZh', item.nameZh);
                    formData.append('namePt', item.namePt);

                    formData.append('file', fileList[file.uid]);

                    serviceItemFileUpload(
                      {
                        txnId,
                        code: item.code,
                        spServiceDocId: item.serviceDocumentID,
                        nameZh: item.nameZh,
                        namePt: item.namePt,
                      },
                      formData,
                    )
                      .then((ret) => {
                        const uid = String(item.serviceDocumentID);
                        const uploadfileList = getUploadfileList();
                        let data = { ...uploadfileList };

                        if (!(data[uid] && data[uid].length)) {
                          return;
                        }
                        let f = data[uid][0];

                        if (ret.code !== '0') {
                          f.status = 'error';
                          setTipMsg(getResponseMessage(ret));
                        } else {
                          f.status = 'done';
                          setTipMsg('');
                          //f.url = ret.data.url
                        }
                        data[uid] = [f];
                        setUploadfileList(data);
                      })
                      .catch((e) => {
                        message.error(e.message);

                        const uid = String(item.serviceDocumentID);
                        const uploadfileList = getUploadfileList();
                        let data = { ...uploadfileList };

                        if (!(data[uid] && data[uid].length)) {
                          return;
                        }
                        let f = data[uid][0];

                        f.status = 'error';
                        data[uid] = [f];
                        setUploadfileList(data);
                      });
                  }}
                >
                  <Button type="primary" icon={<UploadOutlined />}>
                    {intl.formatMessage({ id: 'upload_files' })}
                  </Button>
                </Upload>
                {/* <div style={{ color: '#333', marginTop: 10 }}>{titleInfo}</div> */}
                <Divider />
              </Form.Item>
            ))}
          </div>
        </Form>
      </div>
    </>
  );
};

export default FormSeven;
