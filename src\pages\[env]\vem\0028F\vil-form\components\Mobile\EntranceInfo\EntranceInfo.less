@classprefix: batchSuccess-28f-mobile;

.@{classprefix} {
  &-entranceInfoRoot {
    margin: 0.2rem 0.12rem;
    display: flex;
    flex-wrap: wrap;
    font-size: 0.16rem;
    line-height: 0.22rem;
    white-space: pre-wrap;
    align-items: baseline;
    height: auto;
    .ant-descriptions {
      padding: 0.2rem 0.12rem;
      background-color: #fff;
    }
    .ant-descriptions-item-label:after{
      display: none;
    }
    .ant-collapse{
      background-color: #fff;
    }
    .ant-descriptions-item-label {
      width: 50%;
      font-size: 0.16rem;
    }
    .ant-descriptions-item-content {
      width: 50%;
      font-size: 0.16rem;
    }
  }
  &-formItem{
    margin-bottom: 0.2rem;
    border-radius: 0.12rem;
    overflow: hidden;
  }
  &-formTitle {
    display: flex;
    width: 100%;
    align-items: center;
    font-weight: bold;
    font-size: 0.18rem;
    color: #232323;
    padding-bottom: 0.1rem;
    padding: 0.05rem 0.2rem;
    min-height: 0.41rem;
    font-size: 0.18rem;
    color: #ffffff;
    line-height: 0.21rem;
    background: @brand-primary;
  }
}

