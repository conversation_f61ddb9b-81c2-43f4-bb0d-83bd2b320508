export function setLang(lang) {
  window.localStorage.setItem('lang', lang);
}

export function getLang() {
  return window.localStorage.getItem('lang');
}

export function getStorageObject<T>(key: string) {
  const res = localStorage.getItem(key)
  if (!res) {
    return null
  }

  return JSON.parse(res) as T
}

export function setStorageObject<T>(key: string, value: T) {
  if (typeof value === 'string') {
    localStorage.setItem(key, value)
    return
  }

  localStorage.setItem(key, JSON.stringify(value))
}

export function removeStorage(key: string) {
  localStorage.removeItem(key)
}