import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';

import { Steps } from 'antd';
import Form1 from '../Form1';
import Form2, { FormTwoOwnerRef } from '../Form2';
import Form3, { FormThreeVehicleRef } from '../Form3';
import Form4, { FormFourAgentCompRef } from '../Form4';
import Form5, { FormFiveContactRef } from '../Form5';
import Preview from '../Preview';
import Payment from '../Payment';

import { getTxnServiceTitle } from '@/services/publicApi';
import { useIntl } from 'umi';
import { getLangGroup } from '@/locales/lang';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  title: string;
  companyName: string;
  classPrefix: string;
  step: number;
  contentNextStep: number;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (string) => void;
  setCloseText?: (value: string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setDisableTempStore: (status: boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;

  setShowPrevStep: (boolean) => void;
  setShowNextStep: (boolean) => void;

  contentForNextStep: (number) => void;

  setCreateEXTxnParams: (data) => void;
  createEXTxnParams: createEXTxnParams | undefined;
  setTxnId: (data) => void;
  txnId: string | undefined;
}

export interface StepOptionsItem {
  sort: number;
  title: string;
  steps: number[];
}

export type FormBaseRef = {
  tempStore: () => Promise<void>;
};

const FormBase = forwardRef((props: FormBaseProps, ref: React.ForwardedRef<FormBaseRef>) => {
  const intl = useIntl();
  const form2OwnerRef = React.createRef<FormTwoOwnerRef>();
  const form3VehicleRef = React.createRef<FormThreeVehicleRef>();
  const form4AgentCompRef = React.createRef<FormFourAgentCompRef>();
  const form5ContactRef = React.createRef<FormFiveContactRef>();
  const [txnStatus, setTxnStatus] = useState<string>('');

  useImperativeHandle(ref, () => ({
    // 暫存
    async tempStore() {
      console.log(`TempStore Step[${step}]`);

      try {
        props.setDisableTempStore(true);
        step === 3 && (await form2OwnerRef.current?.tempStore());
        step === 4 && (await form3VehicleRef.current?.tempStore());
        step === 5 && (await form4AgentCompRef.current?.tempStore());
        step === 6 && (await form5ContactRef.current?.tempStore());
      } finally {
        props.setDisableTempStore(false);
      }
    },
  }));

  const { step, className = '', ...otherProps } = props;
  const {
    setDisablePrevStep,
    setDisableNextStep,
    nextStep,
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    setShowPrevStep,
    setShowNextStep,
    contentForNextStep,
    contentNextStep,

    setCreateEXTxnParams,
    createEXTxnParams,
    setTxnId,
    txnId,
  } = otherProps;
  const px = pc(otherProps.classPrefix);

  const [title, setTitle] = useState('');
  const [companyName, setCompanyName] = useState('');

  const [entranceInfo, setEntranceInfo] = useState<getRivmByDSFM4Res>();
  const [applied, setApplied] = useState<boolean>(false);

  useEffect(() => {
    if (txnId) {
      getTxnServiceTitle(txnId)
        .then((res) => {
          setTitle(
            getLangGroup(
              res?.data?.serviceTitleCn,
              res?.data?.serviceTitlePt,
              res?.data?.serviceTitleEn,
            ),
          );
          setCompanyName(
            getLangGroup(res?.data?.companyCname, res?.data?.companyPname, res?.data?.companyEname),
          );
        })
        .catch((e) => console.log('error-- ', e));
    }
  }, [txnId]);

  const handleApplied = (isApplied: boolean) => {
    setApplied(isApplied);
  };

  const initStepOptions: StepOptionsItem[] = [
    {
      sort: 1,
      title: intl.formatMessage({ id: 'application_materials' }),
      steps: [1, 2, 3, 4, 5, 6],
    },
    {
      sort: 2,
      title: intl.formatMessage({ id: 'data_confirmation' }),
      steps: [7],
    },
    {
      sort: 3,
      title: intl.formatMessage({ id: 'owner_data_confirmation' }),
      steps: [7],
    },
    {
      sort: 4,
      title: intl.formatMessage({ id: 'payment_information' }),
      steps: [8],
    },
  ];
  const [stepOptions, setStepOptions] = useState<StepOptionsItem[]>(initStepOptions);

  // 生成右侧表单内容
  const renderRightContent = () => {
    // Pay 界面
    if (step === 8) {
      return (
        <Payment
          setNextStepText={setNextStepText}
          setShowClose={setShowClose}
          setShowTempStore={setShowTempStore}
          setShowCloseAndTempStore={setShowCloseAndTempStore}
          step={step}
          contentNextStep={contentNextStep}
          nextStep={nextStep}
          contentForNextStep={contentForNextStep}
          setDisableNextStep={setDisableNextStep}
          txnId={txnId}
        />
      );
    }

    // 確定資料
    if (step === 7) {
      return (
        <Preview
          setShowClose={setShowClose}
          setShowTempStore={setShowTempStore}
          setShowCloseAndTempStore={setShowCloseAndTempStore}
          setNextStepText={setNextStepText}
          setCloseText={props.setCloseText}
          step={step}
          contentNextStep={contentNextStep}
          nextStep={nextStep}
          contentForNextStep={contentForNextStep}
          setDisableNextStep={setDisableNextStep}
          setDisablePrevStep={setDisablePrevStep}
          setShowPrevStep={setShowPrevStep}
          setShowNextStep={setShowNextStep}
          txnId={txnId}
          onApplied={handleApplied}
          onTxnStatus={(t) => {
            setTxnStatus(t);
          }}
          setStepOptions={setStepOptions}
          initStepOptions={initStepOptions}
        />
      );
    }

    // 聯絡資料
    if (step === 6) {
      return (
        <Form5
          ref={form5ContactRef}
          step={step}
          contentNextStep={contentNextStep}
          nextStep={nextStep}
          contentForNextStep={contentForNextStep}
          setDisableNextStep={setDisableNextStep}
          txnId={txnId}
        />
      );
    }

    // 進口商資料
    if (step === 5) {
      return (
        <Form4
          ref={form4AgentCompRef}
          step={step}
          contentNextStep={contentNextStep}
          nextStep={nextStep}
          contentForNextStep={contentForNextStep}
          setDisableNextStep={setDisableNextStep}
          txnId={txnId}
        />
      );
    }

    // 車輛資料
    if (step === 4) {
      return (
        <Form3
          ref={form3VehicleRef}
          step={step}
          contentNextStep={contentNextStep}
          nextStep={nextStep}
          contentForNextStep={contentForNextStep}
          setDisableNextStep={setDisableNextStep}
          txnId={txnId}
          entranceInfo={entranceInfo}
        />
      );
    }

    // 車主資料
    if (step === 3) {
      return (
        <Form2
          ref={form2OwnerRef}
          setEntranceInfo={(data) => setEntranceInfo(data)}
          setDisablePrevStep={setDisablePrevStep}
          step={step}
          contentNextStep={contentNextStep}
          nextStep={nextStep}
          contentForNextStep={contentForNextStep}
          setDisableNextStep={setDisableNextStep}
          entranceInfo={entranceInfo}
          txnId={txnId}
        />
      );
    }

    // 查詢總資料
    if (step === 1 || step === 2) {
      return (
        <Form1
          txnId={txnId || ''}
          setEntranceInfo={(data) => setEntranceInfo(data)}
          entranceInfo={entranceInfo}
          setCreateEXTxnParams={(data) => setCreateEXTxnParams(data)}
          createEXTxnParams={createEXTxnParams}
          setTxnId={setTxnId}
          contentNextStep={contentNextStep}
          step={step}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          setShowTempStore={setShowTempStore}
          setShowPrevStep={setShowPrevStep}
          contentForNextStep={contentForNextStep}
        />
      );
    }
    return <></>;
  };

  const findCurrentStep = (step: number) => {
    let currentStep = 1;
    if (step === 7) {
      if (txnStatus === 'P') {
        currentStep = 4;
      } else {
        currentStep = 3;
      }
    } else {
      stepOptions.forEach((item) => {
        if (item.steps.includes(step)) {
          currentStep = item.sort;
        }
      });
    }
    return currentStep - 1;
  };

  return (
    <div className={`${px('root')} ${className}`}>
      <h2 className={px('title')}>
        <span style={{ flex: 1 }}>{title || otherProps.title}</span>
        <span>{companyName || otherProps.companyName}</span>
      </h2>
      <div className={px('stepinfo')}>
        {applied ? null : (
          <div className={px('step')}>
            <h3>{intl.formatMessage({ id: 'application_steps' })}</h3>
            <Steps direction="vertical" size="small" current={findCurrentStep(step)}>
              {stepOptions.map((item) => {
                return <Steps.Step key={item.sort} title={item.title} icon={item.sort} />;
              })}
            </Steps>
          </div>
        )}
        <div className={px('form')}>{renderRightContent()}</div>
      </div>
    </div>
  );
});

export default FormBase;
