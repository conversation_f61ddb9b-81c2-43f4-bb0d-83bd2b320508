@prefix: preview-28f-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 0.2rem 0.12rem;
      padding-bottom: 0.2rem;
      font-size: 0.16rem;
      // .my-card-container-mobile {
      //   .my-card-item-mobile:nth-child(1) {
      //     background-color: #eee;
      //     .my-card-title-mobile {
      //       border-radius: 0.12rem;
      //       background-color: @brand-primary;
      //     }
      //   }
      // }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 0.18rem;
      color: #232323;
      padding-bottom: 0.1rem;
    }
  }
}

.owner-card-form-28f-mobile {
  background-color: #fff;

  .ant-form-item {
    font-size: 0.16rem;

    .ant-form-item-label {
      label {
        font-size: 0.16rem;
        color: #6c6c6c;
      }
    }

    .statusBg1 {
      padding: 0 2px;
      color: #f33b40;
    }

    .statusBg2 {
      padding: 0 2px;
      color: @brand-primary;
    }
  }
}

.btn-mobile-height {
  display: flex;
  height: auto !important;
  white-space: pre-wrap !important;
}

.owner-card-container-28f-mobile {
  background-color: #eee;
  padding-top: 0.2rem;

  .owner-card-2 {
    background: #fdf8f0;
  }

  .owner-card-3 {
    background: #E5F1EF;
  }

  .owner-card-item-28f-mobile {
    margin-bottom: 0.2rem;
    border-radius: 0.12rem;
    border: 1px solid #f0f0f0;
    overflow: hidden;

    // &:nth-child(4n + 1) {
    //   background: #d9ebe7;
    // }
    // &:nth-child(4n + 2) {
    //   background: #dfeaf5;
    // }
    // &:nth-child(4n + 3) {
    //   background: #faedd9;
    // }
    // &:nth-child(4n + 4) {
    //   background: #f7e4da;
    // }

    .owner-card-title-mobile {
      font-size: 0.16rem;
      color: rgba(0, 0, 0, 0.85);
      height: 0.44rem;
      display: flex;
      align-items: center;
      padding: 0 0.2rem;
    }

    .owner-card-form-28f-mobile {
      background-color: #fff;
      padding: 0.2rem;
    }
  }
}

.disAgree {
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  font-size: 0.16rem;
  color: #f33b40;
}