import React from 'react';
import { RadioGroupContextProvider } from './context';

export interface RadioGroupProps {
  onChange?: (v: string | number) => void;
  value?: string | number;
  children?: React.ReactNode;
}

function RadioGroup(props: RadioGroupProps) {
  const { onChange = () => {}, value = '', children } = props;

  return (
    <RadioGroupContextProvider
      value={{
        onChange,
        value,
      }}
    >
      {children}
    </RadioGroupContextProvider>
  );
}

export default RadioGroup;
