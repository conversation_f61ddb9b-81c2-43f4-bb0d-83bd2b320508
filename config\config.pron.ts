import { defineConfig } from 'umi';
import theme from '@gov-mo/components/lib/theme';
import px2rem from 'postcss-plugin-px2rem';
import px2vm from 'postcss-px-to-viewport';

export default defineConfig({
  locale: {
    default: 'zh-MO',
    antd: false,
  },
  hash: true,
  ignoreMomentLocale: true,
  antd: false,
  lessLoader: {
    modifyVars: {
      hack: `true; @import "~@/mixins.less";`,
    },
  },
  theme: {
    ...theme,
    '@hd': '2px',
    '@color-primary': '#15AADA',
    '@brand-success': '#084ab8', // 13A07B
    '@brand-gradient': 'linear-gradient(52deg, #1382E4 0%, #33A3FE 48%, #1EF5CB 100%)',
    '@brand-gradient-90': 'linear-gradient(90deg, #1382E4 0%, #33A3FE 48%, #1EF5CB 100%)',

    '@mobile-primary-prefix': 'cbd-mobile-form',
    '@mobile-baseColor': '#595959',
    '@mobile-subColor': '#8c8c8c',

    // business
    '@web-primary-color': '#084ab8',
    '@brand-primary':'#084ab8',

  },
  publicPath: '/',
  externals: {
    '@gov-mo/mpaas-js-bridge': 'JsApi',
  },
  mPaaS: {
    production: {
      virtualDomain: 'https://mds-mpaas-uat.mo.gov.mo',
      packagePath: 'A0DD45C221421_sit',
      devTool: true,
    },
    development: {
      virtualDomain: 'https://mds-mpaas-uat.mo.gov.mo',
      packagePath: 'A0DD45C221421_sit',
      devTool: true,
    },
  },
  extraPostCSSPlugins: [
    px2rem({
      propWhiteList: ['font-size'],
      selectorBlackList: [
        'no2rem',
        /^html$/,
        'web',
        /^(\.ant-){1}(?!form)/,
        'rmc-picker',
        'am-toast',
      ],
      exclude: /node_modules/i,
    }),
  ],
  define: {
    ENV: 'prod',
    PLATFORM: 'business',
    COMMON_ACCESS_TOKEN: 'ZHNhdHVzZXI6SjdOTFlTOEZua0ZWanhOUg==',
    API_HOST: '/ovsap/api',
    APP_ID: '********',
    BUCKET_ID: '********',
    AUTH_SERVER: 'https://entity-account.gov.mo/o/authorize/',
    CLIENT_ID: 'Eli5VfkOIgYSwe98QD0hKz7PfCPpfe5DVW7i3XSg',
    CLIENT_SECRET: '9PvWkJoDJndxZDdSAPrJpT4ld4FhPDFeqcEfL123cJHzuqQvodiN2ClrcfyjUePoiM1xxIISTsoD8YMl3Zq8cacpKLmjUVrLy3J8juYiFMa6ylQqXCmhVhYo9FwcdQzl',
    DEV_REDIRECT_URL: 'http://localhost:8000/index',
    eruda: true,
  },
  chainWebpack(config, { webpack }) {
    process.env.NODE_ENV === 'production' && config.output
      .filename('ovsap/[name].[contenthash].js')
      .chunkFilename('ovsap/[name].[contenthash].chunk.js');
    
    process.env.NODE_ENV === 'production' && config.plugin('extract-css').tap(args => {
      args[0].filename = 'ovsap/[name].[contenthash].css';
      args[0].chunkFilename = 'ovsap/[name].[contenthash].chunk.css';
      return args;
    });
  },
  proxy: {
    '/ovsap/api': {
      target: 'https://appdev.dsat.gov.mo/',
      changeOrigin: true,
    },
  },
});
