import React from 'react';
import { LocaleProvider } from 'antd-mobile';
import { zhHant as mobileZhHant } from '@gov-mo/components/esm/locales/mobile';
import { zhHant as pcZhHant } from '@gov-mo/components/esm/locales/pc';
import { getLocale } from 'umi';
import { ConfigProvider } from 'antd';
import { getLocale2ComponentLocaleMap } from '@/locales/lang';
const MergedLocaleProvider = ({ children }) => {
  const AntdLocale = {
    ...(require('@gov-mo/components/esm/locales/pc')[getLocale2ComponentLocaleMap[getLocale()]] ??
      pcZhHant),
  };

  const AntdMobileLocale = {
    ...(require('@gov-mo/components/esm/locales/mobile')[
      getLocale2ComponentLocaleMap[getLocale()]
    ] ?? mobileZhHant),
  };

  return (
    <ConfigProvider locale={AntdLocale}>
      <LocaleProvider locale={AntdMobileLocale}>{children}</LocaleProvider>
    </ConfigProvider>
  );
};

MergedLocaleProvider.propTypes = {};

export default MergedLocaleProvider;
