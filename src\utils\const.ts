import { isMobile } from ".";

export const LANGUAGE_TYPE: {[key: string]: string} = {
  'zh-MO': 'C',
  'pt-PT': 'P',
  'en-US': 'E'
}

export const PLATFORM_BUSINESS = 'business'
export const PLATFORM_PERSONAL = 'personal'
export const PROPS_MATCH_PARAMS_WEB = 'web'
export const PROPS_MATCH_PARAMS_APP = 'app'
export const URL_HEADER_HIDE_SUBFIX = isMobile() ? 'hideNavigationBar=true' : ''

export const EMIT_KEY_MESSAGE_GLOBLE_ERROR = 'emit_key_message_globle_error'
export const MESSAGE_GLOBLE_ERROR = 'globle_error_message'
export const BASE_ORIGIN = window.location.hostname === 'localhost' ? 'https://appdev.dsat.gov.mo' : window.location.origin;

export const PREVIEW_PDF_MAPPING = {
  'A': 'getReceiptFile',
  'B': 'printExReport',
  'C': 'printSubRegReport'
}