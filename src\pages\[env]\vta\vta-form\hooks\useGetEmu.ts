import { getLangGroup } from '@/locales/lang';
import { getSysCodesByCodeType } from '@/services/publicApi';
import { keys } from 'lodash';
import { useEffect, useState } from 'react';

export enum CarLevel {
  '輕型汽車' = 'L',
  '重型汽車' = 'P',
  '輕型摩托車' = 'C',
  '重型摩托車' = 'M',
  '半掛車' = 'S',
  '工業機器' = 'I',
}
export enum CarType {
  '貨車' = 'M',
  '其它' = 'O',
  '客車' = 'P',
  '客貨車' = 'X',
}
export enum CarFuelSupplyType {
  '增程式' = 'B',
  '電力' = 'E',
  '燃油氣' = 'G',
  '油電混合' = 'H',
}
export const useGetEmu = (typeRmu: any = {}, type = 'A') => {
  let [emuObj, setEmuObj] = useState<any>([]);
  let getAllEmu = async () => {
    let typeKeys = keys(typeRmu);
    let res = await Promise.all(
      typeKeys.map((item) => {
        return getSysCodesByCodeType(typeRmu[item], type);
      }),
    );
    let result = typeKeys.reduce((pre, item: any, index) => {
      pre[item] = res[index]?.dataArr.map((ite) => {
        return {
          label:
            'vehBrandCodeOp' === item
              ? ite.codePname
              : getLangGroup(ite.codeCname, ite.codePname, ite.codeEname),
          value: ite.codeKey,
        };
      });
      return pre;
    }, {});
    console.log('result', result);
    setEmuObj(result);
  };
  useEffect(() => {
    getAllEmu();
  }, []);
  return { emuObj };
};
