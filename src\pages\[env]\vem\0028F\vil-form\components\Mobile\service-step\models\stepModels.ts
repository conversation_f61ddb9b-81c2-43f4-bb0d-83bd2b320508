export interface StepModelsState {
  step: number;
  maxStep: number;
  workNo: string;
  serviceId: string;
  serviceCode: string;
  itemId: string;
  itemCode: string;
  itemName: string;
  itemSecurityLevel: number;
  forecastTime: string | null;
}

export const initStepModelsState = {
  step: 1,
  maxStep: 1,
  workNo: '',
  serviceId: 0,
  serviceCode: '',
  itemId: '',
  itemCode: '',
  itemName: '',
  itemSecurityLevel: 2,
  forecastTime: null,
};

export default {
  namespace: 'stepModels',
  state: initStepModelsState,

  reducers: {
    update: (state: StepModelsState, { payload }: { payload: Partial<StepModelsState> }) => ({
      ...state,
      ...payload,
    }),
    init: () => initStepModelsState,
  },
};
