import React, { useRef, useState } from 'react';
import cn from 'classnames';
import { useClickAway } from 'ahooks';
import { useIntl } from 'umi';
import styles from './WebDraggableFloat.less';

interface Props {
  itemList: {
    title: string;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
  }[];
}

const WebDraggableFloat = ({ itemList = [] }: Props) => {
  const intl = useIntl();
  const rootDomRef = useRef<any>(null);
  const listDomRef = useRef<any>(null);
  // 展开收缩状态
  const [isExpand, setIsExpand] = useState(false);

  const mineBtnPos = useRef({
    x: 50,
    // 注意：bottom 该值必须与DraggableFloat.less中.web-root的bottom一致，否则首次拖拽可能会错位。
    y: 300,
    startX: 0,
    startY: 0,
    endX: 50,
    endY: 300,
  });

  useClickAway(() => {
    isExpand &&
      setIsExpand(() => {
        const $mineBtn = rootDomRef.current;
        $mineBtn.style.right = `${mineBtnPos.current.x}px`;
        return false;
      });
  }, rootDomRef);

  /**
   * 展开/收起事件
   */
  const handleExpandChange = () => {
    setIsExpand((temp) => {
      const $mineBtn = rootDomRef.current;
      if (temp) {
        // 将要收缩
        // float2Right();
        $mineBtn.style.right = `${mineBtnPos.current.x}px`;
      } else {
        // 将要展开
        $mineBtn.style.right = `${
          listDomRef.current?.getBoundingClientRect().width + mineBtnPos.current.x
        }px`;
      }
      return !temp;
    });
  };

  const handleTouchStart = (e) => {
    if (isExpand) {
      return;
    }
    mineBtnPos.current.startX = e.pageX;
    mineBtnPos.current.startY = e.pageY;
  };

  const handleTouchEnd = (e) => {
    if (isExpand) {
      return;
    }
    mineBtnPos.current.x = mineBtnPos.current.endX;
    mineBtnPos.current.y = mineBtnPos.current.endY;
  };

  const handleTouchMove = (e) => {
    if (isExpand) {
      return;
    }
    // TODO:不清楚为什么最后会触发一次clientX 0 clientY 0的情况
    if (!(e.clientX && e.clientY)) {
      return;
    }
    const $mineBtn = rootDomRef.current;
    // e.preventDefault();
    const offsetX = e.pageX - mineBtnPos.current.startX;
    const offsetY = e.pageY - mineBtnPos.current.startY;
    let x = mineBtnPos.current.x - offsetX;
    let y = mineBtnPos.current.y - offsetY; // check edge

    if (x + $mineBtn.offsetWidth > document.documentElement.offsetWidth) {
      x = document.documentElement.offsetWidth - $mineBtn.offsetWidth;
    }

    if (y + $mineBtn.offsetHeight > document.documentElement.offsetHeight) {
      y = document.documentElement.offsetHeight - $mineBtn.offsetHeight;
    }

    if (x < 0) {
      x = 0;
    }

    if (y < 0) {
      y = 0;
    }

    $mineBtn.style.right = `${x}px`;
    $mineBtn.style.bottom = `${y}px`;
    mineBtnPos.current.endX = x;
    mineBtnPos.current.endY = y;
  };

  return (
    <div
      className={cn({
        [styles['web-root']]: true,
        [styles['web-root-isFold']]: !isExpand,
      })}
      ref={rootDomRef}
      onDragStart={handleTouchStart}
      onDragEnd={handleTouchEnd}
      onDrag={handleTouchMove}
    >
      <div className={styles['web-icon']} onClick={handleExpandChange}>
        <img src={isExpand ? `/ovsap/svg/reverseIcon.svg` : `/ovsap/svg/icon.svg`} alt="" />
      </div>
      <div className={styles['web-list']} ref={listDomRef}>
        {itemList.map(({ title, onClick }) => (
          <div className={styles['norem-list-item']} onClick={onClick} key={title}>
            {intl.formatMessage({ id: title })}
          </div>
        ))}
      </div>
    </div>
  );
};

export default WebDraggableFloat;
