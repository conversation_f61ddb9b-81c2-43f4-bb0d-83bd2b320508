.valueArea {
  border: 1px solid #d9d9d9;
  border-radius: @radius-md;
  padding: 32px 24px 0 24px;
  font-size: 28px;
  .detialInfo {
    margin-bottom: 16px;
    color: #8c8c8c;
  }
  .modify {
    border-top: 1px solid #d9d9d9;
    color: @brand-primary;
    text-align: center;
    height: 90px;
    line-height: 90px;
  }
}
.title {
  font-weight: bold;
  margin-bottom: 16px;
}

.addBtn {
  background: #f1f8fe;
  padding: 0.1rem;
  border-radius: 0.1rem;
  text-align: center;
  font-size: 0.14rem;
  color: #15aada;
}

.portalRoot {
  transform: translateX(100%);
  transition: transform 0.5s ease;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  .bodyContent {
    width: 100%;
    height: 100%;
    background: #f2f3f5;
    overflow-y: auto;
    padding: 32px 24px;
    .addressArea {
      background: #fff;
      border-radius: @radius-md;
      padding: 32px 24px;
      margin-top: 24px;
      .detailArea {
        background: rgba(0, 0, 0, 0.02);
        padding: 32px 24px;
        border-radius: @radius-md;
        .key {
          color: #8c8c8c;
        }
      }
    }
  }
}
