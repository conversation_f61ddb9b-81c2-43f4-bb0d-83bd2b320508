import { useDispatch, useSelector } from 'react-redux';
import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import type { PublicModelsState } from 'umi';

export function useDictionary(
  names: string[],
  prefix: string = 'root',
  forceUpdate: boolean = false,
) {
  const dispatch = useDispatch();
  const { dictionary } = useSelector(
    ({ public: insidePublic }: { public: PublicModelsState }) => insidePublic,
  );
  useEffect(() => {
    if (!dictionary || forceUpdate) {
      dispatch({ type: 'public/getDictionary', forceUpdate });
    }
  }, []);

  const dictFromName = useMemo(() => {
    const res: Record<string, string>[] = new Array(names.length).fill(1).map(() => ({}));
    if (dictionary) {
      names.forEach((name, index) => {
        const current = dictionary[`${prefix}#${name}`] ?? [];
        current.forEach((item) => {
          const { propertyCode, propertyName } = item;
          res[index][propertyCode] = propertyName;
        });
      });
    }
    return res;
  }, [dictionary, names, prefix]);
  return dictFromName;
}

export const useCountDown = ({ expiryTime = 60, onExpire = () => {} }) => {
  const [seconds, setSeconds] = useState(expiryTime);
  const [isRunning, setIsRunning] = useState(false);
  let timer;
  useEffect(() => {
    if (isRunning && seconds > 0) {
      timer = setInterval(() => {
        setSeconds((prevCount) => prevCount - 1);
      }, 1000);
    } else if (seconds === 0) {
      setIsRunning(false); // Stop the timer when countdown reaches 0
      onExpire();
    }

    return () => clearInterval(timer);
  }, [isRunning, seconds]);

  const start = () => {
    if (!isRunning) {
      setSeconds(expiryTime);
      setIsRunning(!isRunning);
    }
  };

  const reset = () => {
    setIsRunning(false);
    setSeconds(expiryTime);
  };

  return { seconds, start, reset, isRunning };
};

export interface UseRefParams {
  fn: (_args: any) => void;
  timer: ReturnType<typeof setTimeout> | null;
}
// React anti shake function
export const useDebounce = (fn: any, delay = 500) => {
  const { current } = useRef<UseRefParams>({ fn, timer: null });
  useEffect(() => {
    current.fn = fn;
  }, [current, fn]);
  return useCallback(
    (args: any) => {
      if (current.timer) {
        clearTimeout(current.timer);
      }
      current.timer = setTimeout(() => {
        current.fn(args);
      }, delay);
    },
    [current, delay],
  );
};

export function throttle(fn, delay = 500) {
  let flag = true;
  return function () {
    if (flag) {
      setTimeout(() => {
        fn.call();
        flag = true;
      }, delay);
    }
    flag = false;
  };
}

export function debounce(fn: any, delay: number = 100): () => void {
  let timer: NodeJS.Timeout | null = null;

  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn();
    }, delay);
  };
}

export function getStatusBarHeight() {
  //获取当前手机状态栏高度
  let immersed = 0;
  let ms = /Html5Plus\/.+\s\(.*(Immersed\/(\d+\.?\d*).*)\)/gi.exec(navigator.userAgent);
  if (ms && ms.length >= 3) {
    // 当前环境为沉浸式状态栏模式
    immersed = parseInt(ms[2]); // 获取状态栏的高度
  }

  if(window.location.hostname === 'localhost'){
    return 10;
  }
  return immersed || 40;
}
