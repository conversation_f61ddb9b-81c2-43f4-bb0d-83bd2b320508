import React, { useRef, useEffect } from 'react';
// import { useSize } from 'ahooks';
import cn from 'classnames';
// import { useDispatch, useIntl } from 'umi';
// import { Toast } from 'antd-mobile';
// import { dividerLine } from '../SvgBorder';
import styles from './HeaderProgress.less';
import { useIntl } from 'umi';

export interface HeaderProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  current: number;
  title: string;
  progressArr: string[];
}

type ThisType = { maxStep: number };

function HeaderProgress(props: HeaderProgressProps) {
  const intl = useIntl();
  const { progressArr, current, title, className = '', ...otherProps } = props;
  // const svgRef = useRef(null);
  // const { width = 100, height = 10 } = useSize(svgRef);
  const _this = useRef<ThisType>({ maxStep: 0 }).current;
  // const dispatch = useDispatch();

  useEffect(() => {
    // 记录当前到过的最大步骤数
    if (_this.maxStep < current) {
      _this.maxStep = current;
    }
  }, [current]);

  // const handleStepChange = (target: number) => {
  //   if (target > _this.maxStep) {
  //     Toast.info(intl.formatMessage({ id: '步骤未完成' }, { step: _this.maxStep }));
  //     return;
  //   }
  //   dispatch({
  //     type: 'stepModels/update',
  //     payload: { step: target },
  //   });
  // };

  const getAriaLabel = (index) => {
    if (index + 1 < current) {
      return `步骤${index + 1}, 已完成`;
    } else if (index + 1 === current) {
      return `步骤${index + 1}, 进行中`;
    }
    return  intl.formatMessage({ id: 'steps_not_completed'},{step:index+1});
  };

  return (
    <div className={`${styles.root} ${className}`} {...otherProps}>
      <h2 className={styles.title}>{title}</h2>
      {/*  <div className={styles.divider}>
        <svg ref={svgRef}>
          <path d={dividerLine(height, width - 2 * height, 1)} />
        </svg>
      </div> */}

      <div className={styles.procressArea}>
        {progressArr.map((stepName, index) => (
          <div className={styles.procressItem} key={stepName}>
            <div className={styles.procressIndexArea}>
              <div
                className={cn(styles.procressIndex, {
                  [styles.active]: index + 1 < current,
                  [styles.current]: index + 1 === current,
                })}
                // onClick={() => {
                //   handleStepChange(index + 1);
                // }}
              >
                <div aria-label={getAriaLabel(index)} className={styles.progressIndexContent}>
                  {index + 1}
                </div>
              </div>
              {index + 1 < progressArr.length && (
                <div
                  className={`${styles.procressDashLine} ${
                    index + 1 < current ? styles.activeLine : ''
                  } ${styles[`line${progressArr.length}`]}`}
                />
              )}
            </div>
            {/* <div className={`${styles.stepName} ${index + 1 === current ? styles.currentStep : ''}`}>{stepName}</div> */}
          </div>
        ))}
      </div>
    </div>
  );
}

export default HeaderProgress;
