/**
 * @Description: 定开：checkBox勾选——接收审批结果方式
 * @Author: 听风
 * @Date: 2021-04-16 10:01:14
 **
 * @property {Array} data 信息展示键值对 例： [{label: '載於社會保障基金的地址', value: '澳門沙梨頭海邊街24號福興大廈4樓D'}]
 * @property {string} footer 底部提示 例：'如需修改現載於社會保障極盡的電話或通訊地址，請透過社會保障基金網上服務辦理。'
 */

import React, { useContext } from 'react';
import { Divider } from 'antd';
import classPrefix from '@ali-whale/class-prefix';
import FormContext from '../../FormContext';
import './CheckBoxMainTips.less';

const CheckBoxMainTips = (props) => {
  const { data = [], footer = '' } = props;
  const { mobile } = useContext(FormContext);
  const px = classPrefix(
    mobile ? 'cbd-mobile-base-form-checkbox-main-tips' : 'cbd-web-base-form-checkbox-main-tips',
  );

  return (
    <div className={px()}>
      {data.map(({ label, value }, index) => (
        <div className={px('item')} key={index}>
          <div className={px('label')}>{`${label}：`}</div>
          <div className={px('value')}>{value}</div>
        </div>
      ))}
      <Divider className={px('divider')} />
      <div className={px('footer')}>{footer}</div>
    </div>
  );
};

export default CheckBoxMainTips;
