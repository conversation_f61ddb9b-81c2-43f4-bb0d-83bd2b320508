@prefix: newcar-apply-record;
@prefix-mobile: newcar-apply-record-mobile;

.@{prefix} {
  &- {
    &root {
      padding: 0 10% 88px;
      overflow-y: auto;
      background-color: #fff;

      .mophone-user .mo-phone {
        width: 100%;
      }

      .mophone-user .ant-form-item-label {
        display: none;
      }

      // .ant-form {
      // }

      // .ant-form-item-label > label {
      // }

      // .ant-select {
      // }
    }

    &title {
      font-size: 20px;
      font-weight: 700;
      padding: 20px 24px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    }

    &search-query-btn {
      width: 100%;
      display: flex;
      margin-bottom: 20px;
      justify-content: space-between;
    }

    &batch-payment-btn {
      width: 100%;
      margin-bottom: 10px;
    }

    &pagination-box {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }

    &owner-time {
      font-size: 16px;
      width: 100%;
      margin-bottom: 12px;
      height: auto;

      padding: 10px 12px;
      color: #13A07B;
      background-color: rgba(19, 160, 123, 0.2);

    }

    &owner-info {
      background-color: #eee;
      width: 100%;
      padding: 20px;

      .item {
        .label {
          width: 160px;
          font-size: 18px;
        }
      }
    }

    &stepinfo {
      display: flex;
    }

    &form {
      flex: 1 1;
      margin-top: 20px;
    }

    &form h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 24px;
    }

    &form > .pc-pay-ui-root {
      margin-left: 24px;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 65px;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 16px;
      color: #666;
      top: -2px;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 24px;
      top: -2px;
    }

    &footer-button {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10%;
      background: #fff;
      box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.1);
    }

    &footer-button .ant-btn {
      min-width: 128px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
      margin-left: 12px;
      font-size: 16px;
      padding: 0 5px;
    }

    &footer-button .ant-btn.ant-btn-default {
      background: #fff;
      color: @brand-primary;
      border: 1px solid @brand-primary;
    }

    &sectionBody {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .ant-form-item {
        width: 30%;
      }

      .ant-form-item-label label {
        font-size: 16px;
      }

      .ant-btn.ant-btn-primary {
        &.pure {
          border-radius: 2px;
          border: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }

      .ant-btn.ant-btn-default {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;

        &.pure {
          border-radius: 2px;
          color: rgba(0, 0, 0, 0.85);
          border: 1px solid #f0f0f0;
          font-size: 14px;
        }

        &.btn-active {
          background-color: @brand-primary;
          color: #fff;
        }
      }

      .card {
        box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.12);
        margin-bottom: 24px;
        border-radius: 8px;
        overflow: hidden;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 58px;
          padding: 0 20px;
          color: #ffffff;

          .card-title {
            flex: 1;
            padding: 0 12px;
            font-size: 18px;
            font-weight: 600;
          }

          .status {
            background: #fff;
            border-radius: 15px;
            line-height: 30px;
            font-size: 14px;
            text-align: center;
            padding: 0 10px;
          }

          .status-paying {
            color: #e9b745;
          }

          .status-paied {
            color: #084ab8;
          }

          .status-passed {
            color: #13a07b;
          }
        }

        .card-body {
          min-height: 58px;
          padding: 18px 20px 0;
          display: flex;
          flex-wrap: wrap;

          .item {
            width: 50%;
            display: flex;
            margin-bottom: 16px;

            .label {
              width: 30%;
              font-size: 16px;
              color: #6c6c6c;
              overflow-wrap: anywhere;
            }

            .value {
              flex: 1 1;
              color: #232323;
              font-size: 16px;
              font-family: 'PingFang SC', 'Microsoft YaHei', 'Ming-MSCS-DSI';
              overflow-wrap: anywhere;
              word-break: break-all;
            }
          }
        }

        .card-footer {
          height: 62px;
          padding: 13px 0;
          display: flex;
          justify-content: space-between;
          margin: 0 24px;
          border-top: 1px solid #f0f0f0;

          .right {
            .ant-btn {
              margin-right: 8px;
            }
          }
        }
      }

      .data-empty {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }


    &batch-veh-reg-btn {
      width: 100%;
      margin-bottom: 10px;
    }

    &batch-veh-reg-modal {
      padding: 0 12px;

      &-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
      }

      &-list {
        margin-bottom: 20px;

        &-title {
          font-size: 16px;
        }

        &-content {

        }

        &-item {
          font-weight: bold;
        }
      }

      &-tip {
        color: red;
        font-weight: bold;
      }
    }
  }
}

.@{prefix-mobile} {
  &- {
    &root {
      display: flex;
      justify-content: center;
      padding: 45px 12px;
      background-color: #fff;

      .mophone-user .mo-phone {
        width: 100%;
      }

      .mophone-user .ant-form-item-label {
        display: none;
      }
    }

    &pagination-box {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    &search-query-btn {
      display: flex;
      flex-direction: column;
    }

    &search-query-btn-box {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;

      .ant-btn-default {
        margin-bottom: 10px;
      }
    }

    &batch-payment-btn {
      margin-bottom: 10px;
    }

    &owner-time {
      font-size: 16px;
      width: 100%;
      margin-bottom: 12px;
      height: auto;
      padding: 10px 12px;
      color: #13A07B;
      overflow-wrap: anywhere;
      word-break: break-all;
      background-color: rgba(19, 160, 123, 0.2);

    }

    &owner-info {
      background-color: #eee;
      width: 100%;
      padding: 20px;

      .item {
        .label {
          width: 100px !important;
        }
      }
    }

    &stepinfo {
      display: flex;
    }

    &form {
      flex: 1 1;
      margin-top: 20px;
    }

    &form h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 24px;
    }

    &form > .pc-pay-ui-root {
      margin-left: 24px;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 65px;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 16px;
      color: #666;
      top: -2px;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 24px;
      top: -2px;
    }

    &sectionBody {
      padding-bottom: 50px;

      .search-reset-btn {
        position: absolute;
        bottom: 0;
        width: 90%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: 50px;
      }

      .ant-btn.ant-btn-primary {
        &.pure {
          border-radius: 2px;
          border: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }

      .ant-btn.ant-btn-default {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;

        &.pure {
          border-radius: 2px;
          color: rgba(0, 0, 0, 0.85);
          border: 1px solid #f0f0f0;
          font-size: 14px;
        }

        &.btn-active {
          background-color: @brand-primary;
          color: #fff;
        }
      }

      .card {
        box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.12);
        margin-bottom: 24px;
        border-radius: 8px;
        overflow: hidden;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          min-height: 58px;
          padding: 0 10px;
          color: #ffffff;

          .card-title {
            flex: 1;
            padding: 0 12px;
            font-size: 12px;
            font-weight: 600;
          }

          .status {
            background: #fff;
            border-radius: 15px;
            padding: 0 10px;
            line-height: 30px;
            font-size: 14px;
            text-align: center;
          }

          .status-paying {
            color: #e9b745;
          }

          .status-paied {
            color: #084ab8;
          }

          .status-passed {
            color: #13a07b;
          }
        }

        .card-body {
          min-height: 58px;
          padding: 18px 20px 0;

          .item {
            display: flex;
            margin-bottom: 16px;

            .label {
              width: 40%;
              font-size: 14px;
              color: #6c6c6c;
              overflow-wrap: anywhere;
            }

            .value {
              flex: 1 1;
              color: #232323;
              font-size: 16px;
              font-family: 'PingFang SC', 'Microsoft YaHei', 'Ming-MSCS-DSI';
              overflow-wrap: anywhere;
              word-break: break-all;
            }
          }
        }

        .card-footer {
          padding: 13px 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin: 0 24px;
          border-top: 1px solid #f0f0f0;

          &-checkbox {
            margin-bottom: 0.1rem;
          }

          .right {
            display: flex;
            flex-direction: column;
            flex: 1;

            .ant-btn {
              width: 100%;
              margin-bottom: 10px;
            }
          }
        }
      }

      .data-empty {
        position: fixed;
        inset: 0;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &batch-veh-reg-modal {
      padding: 0 12px;

      &-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
      }

      &-list {
        margin-bottom: 20px;

        &-title {
          font-size: 16px;
        }

        &-content {

        }

        &-item {
          font-weight: bold;
          font-size: 14px;
        }
      }

      &-tip {
        color: red;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }
}

.mobile-title {
  font-size: 16px;
  text-align: center;
  color: #333;
}

.ant-checkbox-disabled {
  .ant-checkbox-input {
    cursor: not-allowed !important;
  }

  .ant-checkbox-inner {
    background-color: #c6bdbd !important;
    border-color: #c6bdbd !important;
  }
}
