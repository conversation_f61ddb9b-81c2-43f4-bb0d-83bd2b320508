import React, {
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import pc from 'prefix-classnames';
import './Form5.less';
import { MoPhone } from '@gov-mo/components';
import type { FormProps } from 'antd';
import { Alert, Col, Form, message, Radio, Row, Select } from 'antd';
// import { ValidateStatus } from 'node_modules/antd/lib/form/FormItem/index';

import { getSysCodesByCodeType } from '@/services/publicApi';
import { getContactList, getTxnContact, saveContact } from '@/services/0028F';

import { useIntl } from 'umi';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import SpinContext from '../../../../../../../components/SpinContext';

const px = pc('form5-28f');

declare type formParams = saveContactParams & {
  name: string;
};

export interface FormFiveProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;

  txnId: string | undefined;
}

export type FormFiveContactRef = {
  tempStore: () => Promise<void>;
};

const FormFive = forwardRef((props: FormFiveProps, ref: React.ForwardedRef<FormFiveContactRef>) => {
  useImperativeHandle(ref, () => ({
    // 暫存
    async tempStore() {
      try {
        await form.validateFields();
        await submit(form.getFieldsValue(), false);
        if (!txnId) return;
        await refreshData(txnId);
      } catch {}
    },
  }));

  const { className = '', setDisableNextStep, ...otherProps } = props;
  const { contentNextStep, contentForNextStep, txnId } = otherProps;

  const intl = useIntl();
  const [tipMsg, setTipMsg] = useState('');
  const [form] = Form.useForm();

  const [contactOpt, setContactOpt] = useState<getContactListRes[]>([]);
  const [contactId, setContactId] = useState('');
  const [checked, setChecked] = useState<boolean | undefined>(false);
  const [langOpts, setLangOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
  // const [validateRadioStatus, setValidateRadiotatus] = useState<ValidateStatus>('');
  // const [radioHelp, setRadioHelp] = useState<string>('');
  const { setLoading } = useContext(SpinContext);
  const contactResultRef = useRef<getTxnContactRes | null>(null);

  const onFinish: FormProps<formParams>['onFinish'] = async (values) => {
    await submit(values);
  };

  const submit = async (values: formParams, next: boolean = true) => {
    setTipMsg('');

    if (!txnId) {
      return;
    }
    if (checked === undefined) {
      // setValidateRadiotatus('error');
      // setRadioHelp(intl.formatMessage({ id: 'please_select' }));
      return;
    }
    // setValidateRadiotatus('');
    // setRadioHelp('');

    const params: saveContactParams = {
      txnId,
      contactId, // PK
      txnOwnerId: '',
      contactMobilePhone: '',
      contactLanguage: '',
      contactAgreest: '', // 是否同意 (Y: 同意, N: 不同意)
    };

    params.txnOwnerId = values.name;
    params.contactMobilePhone = values.contactMobilePhone;
    params.contactLanguage = values.contactLanguage;
    params.contactAgreest = checked ? 'Y' : 'N';

    const ret = await saveContact(params).catch((e) => {
      setTipMsg(e.message);
    });
    if (!ret) {
      return;
    }

    if (ret.code !== '0') {
      setTipMsg(getResponseMessage(ret));
      return;
    } else {
      !next && message.success(intl.formatMessage({ id: 'cp_save_success' }));
    }

    next && contentForNextStep(2);
  };

  const onFinishFailed: FormProps<formParams>['onFinishFailed'] = (errorInfo) => {
    setDisableNextStep(false);
  };

  const getContactListFetch = async (id: string) => {
    try {
      const contactList = await getContactList(id);
      if (contactList?.code === '0') {
        setContactOpt(contactList.dataArr || []);
      }
    } catch (e: any) {
      setTipMsg(e.message);
    }
  };
  const getTxnContactFetch = async (id: string) => {
    try {
      const txnContact = await getTxnContact(id);
      if (txnContact?.code !== '0') return;

      if (!txnContact.data) {
        setChecked(undefined);
        return;
      }

      const { data } = txnContact;
      contactResultRef.current = data;
      setContactId(data.contactId);

      switch (data.contactAgreest) {
        case 'Y':
          setChecked(true);
          break;
        case 'N':
          setChecked(false);
          break;
        default:
          setChecked(undefined);
      }

      form.setFieldsValue({
        name: data.txnOwnerId,
        contactMobilePhone: data.contactMobilePhone,
        contactLanguage: data.contactLanguage,
        phone: { area: '+853', number: data.contactMobilePhone },
        contactAgreest:
          data.contactAgreest === 'Y' ? true : data.contactAgreest === 'N' ? false : undefined,
      });
    } catch (e: any) {
      setTipMsg(e.message);
    }
  };
  const refreshData = async (id: string) => {
    await Promise.all([getContactListFetch(id), getTxnContactFetch(id)]).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    if (txnId) {
      refreshData(txnId).then();
    } else {
      setLoading(false);
    }
  }, [txnId]);

  useEffect(() => {
    setDisableNextStep(false);
    getSysCodesByCodeType('50034', 'A')
      .then((ret) => {
        if (ret?.code === '0') {
          setLangOpts(ret.dataArr);
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });

    contentForNextStep(1);
    return () => {
      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      form.submit();
    }
  }, [contentNextStep]);

  const contactOptions = useMemo(
    () =>
      contactOpt.map((item) => {
        let labelName = '';
        if (item.ownerLastNameCn && item.ownerFirstNameCn) {
          labelName = `${item.ownerLastNameCn}${item.ownerFirstNameCn}`;
        }
        if (item.ownerFirstNamePt && item.ownerLastNamePt) {
          labelName += labelName ? '-' : '';
          labelName += `${item.ownerLastNamePt} ${item.ownerFirstNamePt}`;
        }
        return { label: labelName, value: item.txnOwnerId };
      }),
    [contactOpt],
  );

  return (
    <>
      {tipMsg && (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={`${px('sectionTitle')}`}>
          {intl.formatMessage({ id: 'apply_information' })}
        </div>
        <div className="my-card-container">
          <div className="my-card-item">
            <div className="my-card-title">{intl.formatMessage({ id: 'contact_information' })}</div>
            <div className="my-card-body">
              <Form
                form={form}
                className={px('form')}
                layout="vertical"
                preserve={false}
                scrollToFirstError
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
              >
                <Row>
                  <Col span={10}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'car_owner' })}
                      name="name"
                      rules={[
                        { required: true, message: intl.formatMessage({ id: 'please_select' }) },
                      ]}
                    >
                      <Select options={contactOptions} />
                    </Form.Item>
                  </Col>
                  <Col offset={2} span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'local_mobile_phone' })}
                      name="contactMobilePhone"
                      rules={[
                        { required: true, message: intl.formatMessage({ id: 'please_enter' }) },
                        {
                          pattern: /^6\d{7}$/,
                          message: intl.formatMessage({ id: 'phone_tips' }),
                        },
                      ]}
                    >
                      <div className="mophone-user">
                        <MoPhone
                          onlyAreaGroup={['+853']}
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                          areaProps={{
                            disabled: true,
                            placeholder: 'please_select',
                            formItemProps: {
                              initialValue: '+853',
                            },
                          }}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={10}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'language' })}
                      name="contactLanguage"
                      rules={[
                        { required: true, message: intl.formatMessage({ id: 'please_select' }) },
                      ]}
                    >
                      <Select>
                        {langOpts.map((item, index) => (
                          <Select.Option value={item.codeKey}>
                            {getLangGroup(item.codeCname, item.codePname, item.codeEname)}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label={
                    <div>
                      {getLangGroup(
                        contactResultRef.current?.agreeDefaultCodeDto?.codeCname,
                        contactResultRef.current?.agreeDefaultCodeDto?.codePname,
                        contactResultRef.current?.agreeDefaultCodeDto?.codeEname,
                      )}
                    </div>
                  }
                  name={'contactAgreest'}
                  rules={[{ required: true, message: intl.formatMessage({ id: 'please_select' }) }]}
                  // validateStatus={validateRadioStatus}
                  // help={radioHelp}
                  // hasFeedback={!!radioHelp}
                >
                  <Radio.Group
                    value={checked}
                    onChange={(e) => {
                      setChecked(e.target.value);
                    }}
                  >
                    <Radio value>{intl.formatMessage({ id: 'agree' })}</Radio>
                    <Radio value={false}>{intl.formatMessage({ id: 'no_agree' })}</Radio>
                  </Radio.Group>
                </Form.Item>
                {checked === false && (
                  <div style={{ color: 'red' }}>
                    {getLangGroup(
                      contactResultRef.current?.disAgreeCodeDto?.codeCname,
                      contactResultRef.current?.disAgreeCodeDto?.codePname,
                      contactResultRef.current?.disAgreeCodeDto?.codeEname,
                    )}
                  </div>
                )}
              </Form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
});

export default FormFive;
