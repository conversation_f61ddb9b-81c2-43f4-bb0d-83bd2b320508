import React, { useEffect, useState, useRef, useMemo } from 'react';
import pc from 'prefix-classnames';
import dayjs from 'dayjs';
import './index.less';

import { history, Location, useIntl, useLocation } from 'umi';
import { Col, Button, Alert } from 'antd';
import { getCompleteTxnsInBatch } from '@/services/batch';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { checkTxnJumpOrder, completeTxnForPay, getTxnPendingApproval } from '@/services/0028F';
import { isMobile } from '@/utils';
import { getStatusBarHeight } from '@/utils/hooks';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import { Page } from '@gov-mo/mpaas-js-bridge';
import { NavBar } from 'antd-mobile';
import { Toast } from 'antd-mobile-v5';

const px = pc(isMobile() ? 'page-batch-success-mobile' : 'page-batch-success');

type BatchSuccessLocation = {
  query: {
    reserved1: string;
    requestId: string;
    ID: string;
  };
};

export interface BatchSuccessProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {}

const BatchSuccess = (props: BatchSuccessProps) => {
  const intl = useIntl();
  const { className = '' } = props;

  const location = useLocation() as Location & BatchSuccessLocation;
  const batchId = location.query?.reserved1 ?? '';
  const requestId = location.query?.requestId ?? '';
  const ID = location.query?.ID ?? '';
  const [transId, setTransId] = useState(ID);
  const [tipMsg, setTipMsg] = useState('');
  const [completeData, setCompleteData] = useState<getExTxnCompleteDataRes[]>([]);
  const [completeMobileData, setCompleteMobileData] = useState<getExTxnCompleteDataRes[]>([]);
  const requestTimes = useRef<number>(0);

  function errorMessage(msg: string, duration?: number) {
    if (duration) {
      Toast.show({
        content: msg,
        icon: 'fail',
        duration,
      });
      return;
    }

    Toast.show({
      content: msg,
      icon: 'fail',
    });
  }

  const handleBNU = async (transId: string) => {
    // BNU
    const res = await getTxnPendingApproval(transId);
    if (!res) return;
    if (res.code === '0') {
      setCompleteData(res.dataArr ?? []);
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const handleBOC = async () => {
    // BOC
    const res = await completeTxnForPay(requestId);
    if (!res) return;
    if (res.code === '0') {
      await func();
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const fetchCompleteTxnsInBatch = async () => {

    try {
      if (batchId) {
        await func();
        return;
      }
      if (requestId) {
        const checkRes = await checkTxnJumpOrder(requestId);
        if (!checkRes) return;
        if (checkRes.code !== '0') {
          setTipMsg(getResponseMessage(checkRes));
          return;
        }
        if (checkRes.data.isShowCompletePage) {
          await handleBOC();
        }
        if (checkRes.data.isShowPendingPage) {
          setTransId(checkRes.data.transId);
          await handleBNU(checkRes.data.transId);
        }
        return;
      }
      if (transId) {
        // BNU
        await handleBNU(transId);
      }
    } catch (e: any) {
      setTipMsg(e.message);
    }
  };

  const func = async () => {
    const res = await getCompleteTxnsInBatch(batchId || requestId);
    if (!res) return;
    if (res && res.code === '0') {
      let result = (res.dataArr ?? []).reduce((pre, item) => {
        if (pre[item.txnStatus]) {
          pre[item.txnStatus].push(item);
        } else {
          pre[item.txnStatus] = [item];
        }
        return pre;
      }, {});
      setCompleteMobileData(result);
      setCompleteData(res.dataArr ?? []);
      requestTimes.current = 0;
    } else if (requestTimes.current < 21) {
      requestTimes.current += 1;

      setTimeout(() => {
        fetchCompleteTxnsInBatch();
      }, 1000);
    } else if (requestTimes.current >= 21) {
      isMobile() && errorMessage(getResponseMessage(res), 0)
      setTipMsg(getResponseMessage(res));
    }
  };

  useEffect(() => {
    fetchCompleteTxnsInBatch();
  }, [batchId, requestId, transId]);

  function gotoIndex() {
    if (process.env.NODE_ENV !== 'production') {
      history.push('/web/vem');
    }

    isMobile() ? Page.close() : window.close();
  }

  let mobileData = useMemo(() => {
    let f = []
      .concat(completeMobileData['F'], completeMobileData['A'])
      .filter((item) => !!item) as getExTxnCompleteDataRes[];
    let e = []
      .concat(completeMobileData['E'])
      .filter((item) => !!item) as getExTxnCompleteDataRes[];
    let s = []
      .concat(completeMobileData['S'])
      .filter((item) => !!item) as getExTxnCompleteDataRes[];
    return [
      {
        label: intl.formatMessage({ id: 'successfully_submitted_application' }),
        list: f,
        style: {
          backgroundColor: '#084ab8',
        },
      },
      {
        label: intl.formatMessage({ id: 'failed_submitted_application' }),
        list: e,
        style: {
          backgroundColor: '#e9b745',
        },
      },
      {
        label: intl.formatMessage({ id: 'bnu_pay_submitted_application' }),
        list: s,
        style: {
          backgroundColor: '#13a07b',
        },
      },
    ];
  }, [completeMobileData]);

  return isMobile() ? (
    <>
      <NavBar style={{ paddingTop: getStatusBarHeight() }} mode="light">
        <div className="mobile-title">{intl.formatMessage({ id: 'payment_results' })}</div>
      </NavBar>
      <div
        style={{ paddingTop: 45 + getStatusBarHeight(), backgroundColor: '#eee' }}
        className={`${px('root')} ${className}`}
      >
        <div className={`${px('body')} ${className}`}>
          {mobileData.map(({ label, list = [], style }) => {
            return !list.length ? null : (
              <div className="mobile-batch-payment-pay-way-mobile" style={{ paddingTop: 0 }}>
                <div style={style} className="mobile-batch-payment-formTitle">
                  {label}
                </div>
                {list?.map((item, index) => {
                  return (
                    <div className={px('body')} key={index}>
                      <div className={px('sectionBody')}>
                        {transId ? (
                          <>
                            <div className={px('row')}>
                              <div className={px('label')}>
                                {intl.formatMessage({ id: 'import_license_number' })}
                              </div>
                              <div className={px('value')}>{item?.importNoFull}</div>
                            </div>
                            <div className={px('row')}>
                              <div className={px('label')}>
                                {intl.formatMessage({ id: 'vehicle_level' })}
                              </div>
                              <div className={px('value')}>
                                {getLangGroup(
                                  item.vehTypeDescCn,
                                  item.vehTypeDescPt,
                                  item.vehTypeDescEn,
                                )}
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className={px('row')}>
                              <div className={px('label')}>
                                {intl.formatMessage({ id: 'query_number' })}
                              </div>
                              <div className={px('value')}>{item?.spNo}</div>
                            </div>
                            <div className={px('row')}>
                              <div className={px('label')}>
                                {intl.formatMessage({ id: 'establishment_time' })}
                              </div>
                              <div className={px('value')}>
                                {item?.txnStatusDate
                                  ? dayjs(item?.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                                  : ''}
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          })}
          <div className={px('successTitlleMobile')} style={{textAlign:'left'}}>
            {intl.formatMessage(
              { id: 'success_bottom_title_sst' },
              {
                platform: (
                  <span className={px('successBottomTitleSpan')}>
                    {intl.formatMessage({ id: 'platform_sst' })}
                  </span>
                ),
              },
            )}
            。
          </div>
        </div>
      </div>

      <div className={px('footer')}>
        <Button
          type="default"
          onClick={() => {
            gotoIndex();
          }}
        >
          {intl.formatMessage({ id: 'complete' })}
        </Button>
      </div>
    </>
  ) : (
    <div className={`${px('root')} ${className}`}>
      <div style={{ maxWidth: '1048px', margin: 'auto' }}>
        {tipMsg ? (
          <div style={{ paddingBottom: '0.24rem' }}>
            <Alert message={tipMsg} type="error" showIcon />
          </div>
        ) : null}
      </div>
      {completeData.map((item, index) => (
        <div className={px('body')} key={index}>
          <div className={px('sectionTitle')}>
            {item?.txnStatus === 'E' && (
              <>
                <CloseCircleFilled style={{ color: '#084ab8' }} />
                <span>{intl.formatMessage({ id: 'failed_submitted_application' })}</span>
              </>
            )}
            {['A', 'F'].includes(item?.txnStatus || '') && (
              <>
                <CheckCircleFilled style={{ color: '#084ab8' }} />
                <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
              </>
            )}
            {item?.txnStatus === 'S' && (
              <>
                <CheckCircleFilled style={{ color: '#084ab8' }} />
                <span>{intl.formatMessage({ id: 'bnu_pay_submitted_application' })}</span>
              </>
            )}
          </div>
          <div className={px('sectionBody')}>
            {transId ? (
              <>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'import_license_number' })}</div>
                  <div className="value">{item.importNoFull}</div>
                </Col>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                  <div className="value">
                    {getLangGroup(item.vehTypeDescCn, item.vehTypeDescPt, item.vehTypeDescEn)}
                  </div>
                </Col>
              </>
            ) : (
              <>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'query_number' })}</div>
                  <div className="value">{item.spNo}</div>
                </Col>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'establishment_time' })}</div>
                  <div className="value">
                    {item.txnStatusDate
                      ? dayjs(item.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                      : ''}
                  </div>
                </Col>
              </>
            )}
          </div>
        </div>
      ))}
      <div className={px('successTitlleMobile')}>
        {intl.formatMessage(
          { id: 'success_bottom_title_sst' },
          {
            platform: (
              <span className={px('successBottomTitleSpan')}>
                {intl.formatMessage({ id: 'platform_sst' })}
              </span>
            ),
          },
        )}
        。
      </div>
      <div className={px('footer')}>
        <div className="footer-container">
          <Button
            type="default"
            onClick={() => {
              gotoIndex();
            }}
          >
            {intl.formatMessage({ id: 'complete' })}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BatchSuccess;
