import { useDispatch } from 'react-redux';
import { checkQua } from './services/serviceStepApi';

const useStep = () => {
  const dispatch = useDispatch();

  const checkQuaFn = async (...args) => {
    const res = await checkQua(...args);
    if (res.success && res.data) {
      const data = res.data as { postItems: Record<string, string>[] };
      const projectDict =
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        data?.postItems?.map(({ id, name }) => ({
          label: name,
          value: id,
          disabled: id === '1',
          required: id === '1',
        })) ?? [];
      dispatch({ type: 'personalFormModels/update', payload: { projectDict } });
    }
    return res;
  };

  return {
    checkQuaFn,
  };
};

export default useStep;
