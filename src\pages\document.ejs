<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <!-- index.html不設置緩存 -->
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <link rel="shortcut icon" type="image/x-icon" href="./favicon.ico" />
    <meta content="telephone=no" name="format-detection" />
    <meta content="email=no" name="format-detection" />
    <title></title>
    <script>
      // 禁止双击放大
      const disabledScale = () => {
        var lastTouchEnd = 0;
        document.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        });
        document.addEventListener(
          'touchend',
          function (event) {
            var now = new Date().getTime();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false,
        );
      };
      disabledScale();
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script>
      // 解决部署到ip为根目录，但经nginx部署到域名后不为根目录的问题
      // https://portal-uat.mo.gov.mo/criminal/ -> https://ip/
      window.routerBase =
        window.location.pathname.split('/')[1] === 'ovsap' ? '/ovsap/' : '/';
    </script>
  </body>
</html>
