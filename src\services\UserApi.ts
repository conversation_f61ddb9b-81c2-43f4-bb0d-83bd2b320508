import { ACCESS_TOKEN_KEY, FetchResponse, post, setToken, TOKEN_CACHE_KEY } from '@/utils/fetch';
import { Toast } from 'antd-mobile';
import * as queryString from 'query-string';
import { delUrlParams } from '@/utils';
import { Login } from '@gov-mo/mpaas-js-bridge';
import { ACCOUNT_TYPE, AUTHORIZE_TOKEN_KEY, CODE_VERSION, generateRedirectServerUrl, ORIGIN_PLAT_FORM, removeAuthVersion } from '@/utils/auth';
import { getStorageObject, removeStorage, setStorageObject } from '@/utils/storage';
import { initialize } from '@gov-mo/mpaas-js-bridge';
import { omit } from 'lodash'

let userProfile = {} as UserProfile

interface JsApiSignature {
  appId: string;
  signature: string;
  timestamp: number;
  nonce: string;
}

export type IdentityAuthorizeParams = {
  ownerId?: string
  language?: string
  redirectUri: string
  isShowHeader?: boolean
  reason?: string
  txnId?: string
  lastModifyDate?: string
}

export type CheckFaceRecognitionParams = {
  code: string
  redirectUri: string
  ownerId: string
  verficationStatus: string
}

function getFullUrl(): string {
  return `${window.location.origin}${window.location.pathname}`
}

function sort(obj: {[key: string]: any}): {[key: string]: any} {
  const sortedKeys = Object.keys(obj).sort();
  
  return sortedKeys.reduce((acc, key) => {
    acc[key as keyof {[key: string]: any}] = obj[key];
    return acc;
  }, {} as {[key: string]: any});
}

function handleUrlParams(query?: {[key: string]: any}, version?: string): string {
  const params = query || queryString.parse(decodeURIComponent(decodeURIComponent(window.location.search))) || {};

  const searchParams = new URLSearchParams({ ...omit(sort(params || {}), 'code', CODE_VERSION) }).toString()

  return searchParams ? `?${searchParams}&${CODE_VERSION}=${version}` : `?${CODE_VERSION}=${version}`
}

export async function jsApiSignature() {
  const data = await signature();
  data && await initialize({ ...data, env: ENV, platform: ORIGIN_PLAT_FORM, signatureValidCallback: () => {} });
}

// JsApi Signature
export async function signature(): Promise<JsApiSignature> {
  const res = await post('/oa/account/jsApiSignature', {
    url: window.location.href,
  });
  return res.data;
}

export function redirectToGetCode(codeVerifier: string, codeChallenge: string) {
  const version = `${Date.now()}`
  const query = queryString.parse(decodeURIComponent(decodeURIComponent(window.location.search)));

  // 避免一直encode的情况
  // const current = decodeURIComponent(`${getFullUrl()}${query?.txnId ? `?txnId=${query.txnId}&${CODE_VERSION}=${version}` : `?${CODE_VERSION}=${version}`}`);
  const current = `${getFullUrl()}${handleUrlParams(query, version)}`;

  setStorageObject("dev_current_link_get_code", current)

  // 授权失败，不进行重定向
  if (query.error) {
    console.error(`授权失败：${location.href}`);
    return;
  }

  // 用于解决apph5授权页面Title未翻译的问题。
  // const { language } = query;
  // const state = query.state ? query.state : encodeURIComponent(current);

  // const url = `${AUTH_SERVER}/auth.html?client_id=${CLIENT_ID}&scope=profile&redirectUri=${encodeURIComponent(
  //   current,
  // )}&grantType=authorization_code${language ? `&language=${language}` : ''}&state=${state}&code_challenge=${codeChallenge}&code_challenge_method=S256`

  window.location.href = generateRedirectServerUrl(current, codeVerifier, codeChallenge, version)
}

export async function getAuthCodeByJsApi() {
  const query = queryString.parse(window.location.search);
  const current = decodeURIComponent(`${getFullUrl()}${query?.txnId ? `?txnId=${query.txnId}` : ``}`);


  Toast.loading('');
  const authInfo = await Login.grantAuth({
    clientId: CLIENT_ID,
    appId: APP_ID,
    grantType: 'authorization_code',
    scope: 'profile',
    redirectUri: current
  });
  const { success, message, data } = authInfo || {};
  if (success && data?.code) {
    return data?.code;
  } else if (message) {
    Toast.fail(message);
    return '';
  }
  return '';
}

export function getFormUrlByKey(key: string) {
  const query = queryString.parse(window.location.search);
  if (query) {
    return query[key];
  }

  return ''
}

export function getAuthCodeFromUrl() {
  const query = queryString.parse(window.location.search);
  if (query.code && query.faceAuth) {
    return undefined
  }

  return query.code;
}

export function checkAuthCodeIsRepeat(code) {
  // 把code暂存，每次进来与上一次的对比，若一致的需重新去获取新的code
  const res = code === localStorage.getItem('authorizationCode');
  localStorage.setItem('authorizationCode', code);
  return res;
}

export function deleteUrlCode() {
  const query = queryString.parse(window.location.search)
  if (query.code && query.faceAuth) {
    const url = delUrlParams([CODE_VERSION]);
    window.history.pushState(null, '', url);
    return
  }

  const url = delUrlParams(['code', CODE_VERSION]);
  window.history.pushState(null, '', url);
}

// for pc
export async function getLoginUserInfo() {
  if (userProfile && userProfile.euid) {
    console.log('userProfile', userProfile)
    return userProfile
  }

  const res = await post<FetchResponse<UserProfile>>('/oa/account/getProfile')

  userProfile = res?.data || {} as UserProfile
  return res?.data || {} as UserProfile
}

export async function authorize(code: string, codeVerifier: string, version: string) {
  const query = queryString.parse(decodeURIComponent(decodeURIComponent(window.location.search))) || {};
  // const current = decodeURIComponent(`${getFullUrl()}${query?.txnId ? `?txnId=${query.txnId}&${CODE_VERSION}=${version}` : `?${CODE_VERSION}=${version}`}`);
  const current = `${getFullUrl()}${handleUrlParams(query, version)}`;
  
  const params = {
    code: code,
    codeVerifier: codeVerifier,
    redirectUri: current,
    accountType: ACCOUNT_TYPE,
    originPlatForm: ORIGIN_PLAT_FORM
  }

  const res = await post<FetchResponse<AuthorizeToken>>('/access/auth/authorize', params, {
    canSendWithoutToken: true
  })

  if (res?.code !== '0') {
    console.log('Token Error Code: ', res?.code)
    removeStorage(AUTHORIZE_TOKEN_KEY)
    return res
  }

  await setAuthorizeToken(res.data)
  removeAuthVersion()
  return res
}

export async function syncYiWangHeader() {
  const tokenInfo = await getAuthorizeToken()
  if (!tokenInfo) {
    return false
  }

  if (PLATFORM !== tokenInfo?.platform) {
    return false
  }

  const iframe = document.getElementById('iframeHeader');
  if (iframe) {
    iframe.addEventListener('load', () => {
      console.log('syncYiWangHeader Iframe loaded');
      const token = tokenInfo?.jwtToken || '';
      if (token) {
        (iframe as any).contentWindow?.postMessage(
          { type: 'headerFrameOriginChangeToken', token },
          '*',
        );
        console.log('syncYiWangHeader Message posted');
      } else {
        console.warn('syncYiWangHeaderToken is missing');
      }
    });
  } else {
    console.error('syncYiWangHeader Iframe not found');
  }

  tokenInfo?.jwtToken && setToken(tokenInfo?.jwtToken, tokenInfo?.accessToken)
  return true
}

export async function getAuthorizeToken() {
  const tokenInfo = getStorageObject<AuthorizeToken>(AUTHORIZE_TOKEN_KEY)
  if (!tokenInfo) {
    return false
  }

  if ((tokenInfo?.timestamp || 0) + ((tokenInfo?.expiresIn || 0) * 1000) < Date.now()) {
    return false
  }

  return tokenInfo
}

export async function setAuthorizeToken(tokenInfo: AuthorizeToken) {
  setStorageObject(AUTHORIZE_TOKEN_KEY, {...tokenInfo, timestamp: Date.now(), platform: PLATFORM});
  tokenInfo?.jwtToken && setToken(tokenInfo?.jwtToken, tokenInfo?.accessToken)
}

export function logout() {
  revokeToken()
  removeStorage(AUTHORIZE_TOKEN_KEY)
  removeStorage(ACCESS_TOKEN_KEY)
  removeStorage(TOKEN_CACHE_KEY)
}

// 登出
export const revokeToken = () => post<FetchResponse<string>>('/access/auth/revokeToken');

// 調用一戶通人面識別
export const identityAuthorize = (params: IdentityAuthorizeParams) => post<FetchResponse<string>>('/oa/face/identityAuthorize', params);

// 校驗人臉識別是否通過
export const checkFaceRecognition = (params: CheckFaceRecognitionParams) => post<FetchResponse<string>>('/oa/face/checkFaceRecognition', params);