import React, { useEffect } from 'react';
import pc from 'prefix-classnames';
import './FormEight.less';

import { useIntl } from 'umi';
import { Checkbox, Form } from 'antd';

const px = pc('form-eight1');

export interface FormEightProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setDisableNextStep: (boolean) => void;
  setNextStepText: (string) => void;
  txnId?: string
}

const FormEight = (props: FormEightProps) => {
  const { className = '', ...otherProps } = props;
  const { setNextStepText } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  useEffect(() => {
    setNextStepText('繳費');
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }));
    };
  }, []);

  let arr = [
    '1.零件供應聲明書：茲聲明，本實體承諾遵守現行第 45/2019 號行政長官批示“規範機動車輛及重型客車之進口”的相關規定，進口全新車輛為上述機動車輛辦理商標及型號核准，以及作銷售或自用，並承諾會為本實體進口的車輛提供零件供應、保養及維修服務。倘有不實，本實體願意承擔一切法律責任。',
    '2.車輛稅務價格聲明書；茲聲明，本進口實體根據現行第 19/2013 號行政法規《機動車輛、掛車及半掛車的商標及型號的核准》的規定，聲明進口機動車輛稅務資料屬實，並附上財政局相關評稅資料。倘有不實，本實體願意承擔一切法律責任。',
    '3.符合尾氣排放標準及OBD的聲明書：能源是汽油/柴油/天然氣/汽油及電力/柴油及電力，須上傳符合排放要求及OBD的聲明書 (參考附件 “涉及電子型號核准之聲明書範本”)',
    '4.摩托車四衝程的聲明：能源是汽油/柴油/天然氣/汽油及電力/柴油及電力，須彈出聲明內容(參考附件 “涉及電子型號核准之聲明書範本”)',
  ];
  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <div className={px('sectionTitle')}>確定資料</div>
      <div className={px('check-container')}>
        {arr.map((item, index) => (
          <div className={'check-item'}>
            <Checkbox value={index}>{item}</Checkbox>
          </div>
        ))}
      </div>
      <div className={px('sectionBody')}>
        <div className="multi-card-container">
          <div className="multi-card-item">
            <div className="multi-card-title">
              <span>{intl.formatMessage({ id: 'model_approval_documents' })}</span>
            </div>
            <div className="multi-card-body">
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                  <div>B.M.W</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'style' })}>
                  <div>NEW Model</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'style_year' })}>
                  <div>2023</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                  <div>{intl.formatMessage({ id: 'light_vehicles' })}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                  <div>載客</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'dsf_ref_no' })}>
                  <div>xxxxxxx</div>
                </Form.Item>
                <Form.Item label="IVM">
                  <div>{intl.formatMessage({ id: 'yes' })}</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                  <div>特斯特</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'style' })}>
                  <div>Model X</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'style_year' })}>
                  <div>2023</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                  <div>私人</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                  <div>VIN1234</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                  <div>Engine1234</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                  <div></div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                  <div></div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                  <div></div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                  <div>2024</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'ma_tyre_f_r_s' })}>
                  <div>235/45-18 - 235/45-18 -</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'color' })}>
                  <div>白色</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'car_body' })}>
                  <div>{intl.formatMessage({ id: 'length' })} 5600 mm</div>
                </Form.Item>
              </Form>
            </div>
          </div>

          <div className="multi-card-item">
            <div className="multi-card-title">
              <span>馬達資料</span>
            </div>
            <div className="multi-card-item-sub">
              <div className="multi-card-title">
                <span>馬達資料 1：</span>
              </div>
              <div className="multi-card-body">
                <Form form={form} className={px('form')} layout="vertical">
                  <Form.Item label={intl.formatMessage({ id: 'engine_no_before' })}>
                    <div>1234</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'engine_no_position' })}>
                    <div>xxxx</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'notes' })}>
                    <div></div>
                  </Form.Item>
                </Form>
              </div>
            </div>
            <div className="multi-card-item-sub">
              <div className="multi-card-title">
                <span>馬達資料 2：</span>
              </div>
              <div className="multi-card-body">
                <Form form={form} className={px('form')} layout="vertical">
                  <Form.Item label={intl.formatMessage({ id: 'engine_no_before' })}>
                    <div>2345</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'engine_no_position' })}>
                    <div>xxxx</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'notes' })}>
                    <div></div>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormEight;
