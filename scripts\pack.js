const fs = require('fs');
const path = require('path');
const tar = require('tar');
const pkg = require('../package');

const per = process.argv.length >= 3 ? `-${process.argv[2]}` : '';

const distName = `${pkg.name}${per}.tar.gz`;
const outputPath = path.resolve(process.cwd(), './dist');

tar
  .c({ gzip: true, cwd: outputPath }, fs.readdirSync(outputPath))
  .pipe(fs.createWriteStream(distName))
  .on('finish', () => {
    // eslint-disable-next-line no-console
    console.log(`打包完成，请查看项目目录下的 ${distName}`);
  });
