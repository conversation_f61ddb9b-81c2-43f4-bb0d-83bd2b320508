import React, { useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import { history, Location, useIntl, useLocation } from 'umi';
import './BatchPaymentPage.less';
import './MobileBatchPayment.less';
import { NavBar } from 'antd-mobile';

import FooterButton from '@/components/FooterButton';
import GuideButton from '@/components/GuideButton';

import { Alert, Radio, Table } from 'antd';
import { getServiceFeesInBatch } from '@/services/batch';
import { cancelTxnPay, completePayment } from '@/services/0028F';
import { isMobile } from '@/utils';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import { getStatusBarHeight } from '@/utils/hooks';
import { LeftOutlined } from '@ant-design/icons';
import { ServiceFeeType } from '../0033D/newcar-reg-apply/components/Payment';

const classPrefix = isMobile() ? 'mobile-batch-payment' : 'page-batch-payment';
// const classPrefix = 'page-batch-payment';
const px = pc(classPrefix);

type BatchPaymentPageLocation = {
  query: {
    txnList: string;
  };
} & Location;

const BatchPaymentPage = () => {
  const [step] = useState(1);
  const intl = useIntl();
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));

  const [showPrevStep, setShowPrevStep] = useState(false);
  const [showNextStep, setShowNextStep] = useState(true);
  const [showClose, setShowClose] = useState(!isMobile());

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);

  const [tipMsg, setTipMsg] = useState('');
  const [serviceFeeData, setServiceFeeData] = useState<getServiceFeesInBatchRes>(
    {} as getServiceFeesInBatchRes,
  );
  const [paySelected, setPaySelected] = useState<string>('');

  const location = useLocation() as BatchPaymentPageLocation;
  const txnList: string[] = JSON.parse(location.query.txnList ?? '');

  const baseUrl =
    window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const [cancelPayDto, setCancelPayDto] = useState<getServiceFeesInBatchRes['cancelPayDto']>();
  const handleCancelTxnPay = async () => {
    if (!txnList?.length) return;
    try {
      setDisableNextStep(true);
      const res = await cancelTxnPay(txnList);
      if (!res) return;
      if (res.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }
      // 刷新页面
      window.location.reload();
    } finally {
      setDisableNextStep(false);
    }
  };

  useEffect(() => {
    const fetchServiceFeesInBatch = async () => {
      if (txnList?.length) {
        try {
          const res = await getServiceFeesInBatch(txnList);

          if (!res) return;
          if (res.code === '0') {
            const { data } = res;
            // 设置取消支付状态
            setCancelPayDto(data?.cancelPayDto);
            // 如果需要取消支付
            if (data?.cancelPayDto?.isShowCancelPayButton === true) {
              // 禁用支付选择
              data.payCodes.forEach((item) => {
                item.available = false;
              });
              // 设置取消支付按钮文案
              setNextStepText(intl.formatMessage({ id: 'cancel_payment' }));
            }

            setServiceFeeData(res.data ?? {});
            if (res.data.payCodes && res.data.payCodes.length) {
              const codeKey =
                data?.cancelPayDto?.isShowCancelPayButton === true
                  ? data.cancelPayDto.payCodeKey
                  : data.payCodes[0].codeKey;
              setPaySelected(codeKey ?? '');
            }
          } else {
            setTipMsg(getResponseMessage(res));
          }
        } catch (e: any) {
          setTipMsg(e.message);
        }
      }
    };
    fetchServiceFeesInBatch();
  }, [txnList?.length]);

  const tableData = useMemo(() => {
    const dataSource: ServiceFeeType[] = [];
    if (serviceFeeData.txnFeeItemList) {
      (serviceFeeData.txnFeeItemList ?? []).forEach((txnFeeItem, index) => {
        txnFeeItem?.feeItemDTOList?.forEach((item, i) => {
          dataSource.push({
            key: `${index}_${i}`,
            title: getLangGroup(item.zh, item.pt),
            amount: item.priceTotal?.toLocaleString(),
            tax: item.taxPriceTotal?.toLocaleString(),
            subtotal: item.subtotal?.toLocaleString(),
          });
        });
      });
      dataSource.push({
        key: (serviceFeeData.txnFeeItemList.length + 1).toString(),
        title: intl.formatMessage({ id: 'total' }),
        amount: '',
        tax: <span style={{ color: '#084ab8' }}>MOP</span>,
        subtotal: <span style={{ color: '#084ab8' }}>{serviceFeeData.total.toLocaleString()}</span>,
      });
    }
    return dataSource;
  }, [serviceFeeData]);

  const prevStep = () => {
    history.goBack();
  };

  const submit = async () => {
    setTipMsg('');
    // 是否是取消支付
    if (cancelPayDto?.isShowCancelPayButton) {
      await handleCancelTxnPay();
      return;
    }

    try {
      setDisableNextStep(true);

      let pageUrl = 'ovsap/web/vem/batch-success';
      if (paySelected === 'BOCP' || paySelected === 'BOC' || paySelected === 'BNU') {
        pageUrl = `${baseUrl}/${pageUrl}`;
      }
      const data: completePaymentParams = {
        txnIds: txnList,
        payMethodCodeKey: paySelected,
        lang: 'zh_TW',
        ua: isMobile() ? 'MobileWeb' : 'PCWeb',
        remark: '',
        pageUrl,
      };

      const ret = await completePayment(data).catch((e) => {
        setTipMsg(e.message);
      });
      console.log(ret);
      if (!ret) {
        return;
      }
      if (ret.code === '0') {
        const { data } = ret;

        if (data.payUrl) {
          window.location.href = data.payUrl;
        } else if (data.returnHTML) {
          document.write(data.returnHTML?.replace(/\\/, ''));
        }
      } else {
        setTipMsg(getResponseMessage(ret));
      }
    } finally {
      setDisableNextStep(false);
    }
  };

  const nextStep = () => {
    submit();
    // history.push('/web/vem/0028F/vil-form?pay=success')
  };

  const handleClose = () => {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  useEffect(() => {
    setDisablePrevStep(false);
  }, []);

  useEffect(() => {
    setDisableNextStep(false);
    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }));
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }));
    };
  }, []);

  const mobileColumns = [
    {
      title: intl.formatMessage({ id: 'cost_details' }),
      dataIndex: 'title',
      key: 'title',
      width: '120px',
    },
    {
      title: intl.formatMessage({ id: 'amount' }),
      dataIndex: 'amount',
      key: 'amount',
      minWidth: '70px',
      // width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'tax_payment' }),
      dataIndex: 'tax',
      key: 'tax',
      minWidth: '70px',
      // width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'subtotal' }),
      key: 'subtotal',
      dataIndex: 'subtotal',
      minWidth: '70px',
      // width: '100px',
    },
  ];

  const columns = [
    {
      title: intl.formatMessage({ id: 'cost_details' }),
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: intl.formatMessage({ id: 'amount' }),
      dataIndex: 'amount',
      key: 'amount',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'tax_payment' }),
      dataIndex: 'tax',
      key: 'tax',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'subtotal' }),
      key: 'subtotal',
      dataIndex: 'subtotal',
      width: '100px',
    },
  ];

  return (
    <>
      {tipMsg ? (
        <div
          style={
            isMobile()
              ? { padding: '0.2rem 0.24rem 0' }
              : { paddingLeft: '0.24rem', paddingBottom: '0.24rem' }
          }
        >
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      ) : null}
      <div className={`${px('root')}`}>
        {isMobile() ? (
          <NavBar
            style={{ paddingTop: getStatusBarHeight() }}
            mode="light"
            leftContent={<LeftOutlined onClick={handleClose} style={{ fontSize: '0.16rem' }} />}
            rightContent={
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }} />
            }
          >
            {/* title */}
            <span style={{ color: '#333', fontSize: '0.16rem' }}>
              {intl.formatMessage({ id: 'batch_payment' })}
            </span>
          </NavBar>
        ) : (
          <h2 className={px('title')}>{intl.formatMessage({ id: 'batch_payment' })}</h2>
        )}

        <div className={px('stepinfo')} style={{ paddingTop: isMobile() ? '50px' : '' }}>
          <div className={px('form')}>
            <div className={`${px('body-root')}`}>
              {isMobile() ? (
                <div className="pay-way-mobile">
                  <p className={px('cost-title')}>{intl.formatMessage({ id: 'cost_details' })}</p>
                  {tableData.map((item) => {
                    if (tableData.length === Number(item.key)) {
                      return <></>;
                    }
                    return (
                      <div className={px('cost-container')}>
                        <div key={item.key} className={px('cost-content')}>
                          {/*缴费项*/}
                          <div className={px('cost-content-item')}>
                            <div className={px('cost-content-item-label')}>
                              {intl.formatMessage({ id: 'fee_item' })}
                            </div>
                            <div className={px('cost-content-item-text-black')}>{item.title}</div>
                          </div>
                          {/*金额*/}
                          <div className={px('cost-content-item')}>
                            <div className={px('cost-content-item-label')}>
                              {intl.formatMessage({ id: 'amount' })}
                            </div>
                            <div className={px('cost-content-item-text-black')}>{item.amount}</div>
                          </div>
                          {/*税款*/}
                          <div className={px('cost-content-item')}>
                            <div className={px('cost-content-item-label')}>
                              {intl.formatMessage({ id: 'tax_payment' })}
                            </div>
                            <div className={px('cost-content-item-text-black')}>{item.tax}</div>
                          </div>
                          {/*小计*/}
                          <div className={`${px('cost-content-item')} ${px('cost-divide')}`}>
                            <div className={px('cost-content-item-label')}>
                              {intl.formatMessage({ id: 'subtotal' })}
                            </div>
                            <div className={px('cost-content-item-text-primary')}>
                              {item.subtotal}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  <div className={px('cost-divide')} style={{ marginTop: '0.12rem' }}>
                    <div className={px('cost-content-item')} style={{ padding: '0 0.28rem' }}>
                      <div className={px('cost-content-item-label')}>
                        {intl.formatMessage({ id: 'total' })}
                      </div>
                      <div className={px('cost-content-item-text-primary')}>
                        <span style={{ marginRight: '0.1rem' }}>MOP</span>
                        {serviceFeeData.total?.toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <Table
                  columns={isMobile() ? mobileColumns : columns}
                  dataSource={tableData}
                  pagination={false}
                />
              )}

              {/* <div style={{ height: '50px' }} /> */}
              <div className={`${px('pay-way-mobile')}`}>
                <p className={px('formTitle')}>
                  {intl.formatMessage({ id: 'payment_channels' })}
                  {cancelPayDto?.isShowCancelPayButton && (
                    <span style={{ marginLeft: '10px', color: 'red' }}>
                      (
                      {getLangGroup(
                        cancelPayDto?.payMsgCodeDto?.codeCname,
                        cancelPayDto?.payMsgCodeDto?.codePname,
                        cancelPayDto?.payMsgCodeDto?.codeEname,
                      )}
                      )
                    </span>
                  )}
                </p>
                {(serviceFeeData.payCodes ?? []).map((item) => (
                  <div style={{ paddingLeft: '30px', marginTop: '5px' }} key={item.codeKey}>
                    <Radio
                      disabled={!item.available}
                      checked={item.codeKey === paySelected}
                      onChange={(e) => {
                        setPaySelected(item.codeKey);
                      }}
                    >
                      {item.codeKey === 'GOV' && (
                        <img style={{ width: '150px' }} src="/ovsap/image/pay/GOVpay.png" alt="" />
                      )}
                      {item.codeKey === 'BOC' && (
                        <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCEPAY.jpg" alt="" />
                      )}
                      {item.codeKey === 'BOCP' && (
                        <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCPPAY.jpg" alt="" />
                      )}
                      {item.codeKey === 'BNU' && (
                        <img style={{ width: '150px' }} src="/ovsap/image/pay/BNU.png" alt="" />
                      )}
                    </Radio>
                    {!item.available && (
                      <div style={{ color: 'red', fontSize: '16px' }}>
                        {getLangGroup(
                          item.payMaintenanceCodeDto?.codeCname,
                          item.payMaintenanceCodeDto?.codePname,
                          item.payMaintenanceCodeDto?.codeEname,
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      <GuideButton itemId="" />
      <FooterButton
        // handleClose={handleClose}
        className={px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
      />
    </>
  );
};

export default BatchPaymentPage;
