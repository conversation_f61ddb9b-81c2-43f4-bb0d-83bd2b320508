import React, {useMemo, useState} from 'react';
import {useIntl} from 'umi';
import {Modal} from 'antd';
// import AffairLaws from '@/pages/affair-laws';
// import AffairFaq from '@/pages/affair-faq';
// import AffairIntro from '@/pages/affair-intro';
// import ServiceIntroduction from '@/pages/service-introduction/ServiceIntroduction';
import WebDraggableFloat from '../DraggableFloat/WebDraggableFloat';
import styles from './WebGuideButton.less';
import {ApplicationNoticeItemId, GetApplicationNotice} from "@/components/ApplicationNotice";

const INITIAL_TITLE = '';

interface GuideButtonProps {
  itemId: string;
}

const GuideButton = (props: GuideButtonProps) => {
  const intl = useIntl();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState(INITIAL_TITLE);

  const itemList = useMemo(() => {
    const list = [`${intl.formatMessage({id: 'application_notice'})}`];
    return list.map((item = '') => ({
      title: item,
      onClick: () => {
        if (['ApplicationNotice28F', 'ApplicationNotice33D', 'ApplicationNotice33DII'].includes(props.itemId)) {
          setIsModalOpen(true);
          return;
        }
        setTitle(item);
        setVisible(true);
      },
    }));
  }, []);

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleTCancel = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setVisible(false);
    setTitle(INITIAL_TITLE);
  };

  const getRenderContent = () => {
    // 各事项自己实现组件
    switch (title) {
      // case '服务协议':
      //   return <ServiceIntroduction />;
      // case '事项指南':
      //   return <AffairIntro />;
      // case '常见问题':
      //   return <AffairFaq />;
      case '相关法例':
        // return <AffairLaws />;
        return <></>;

      default:
        return <div/>;
    }
  };

  return (
    <>
      <Modal
        className={styles['web-guideModal']}
        width="70%"
        title={title && intl.formatMessage({id: title})}
        visible={visible}
        onCancel={handleCancel}
        cancelText={intl.formatMessage({id: 'turn_off'})}
        centered
      >
        {getRenderContent()}
      </Modal>

      <Modal
        title={intl.formatMessage({id: 'application_notice'})}
        cancelText={intl.formatMessage({id: 'exit'})}
        okText={intl.formatMessage({id: 'continue'})}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleTCancel}
      >
        {GetApplicationNotice({itemId: props.itemId as ApplicationNoticeItemId})}
      </Modal>
      <WebDraggableFloat itemList={itemList}/>
    </>
  );
};

export default GuideButton;
