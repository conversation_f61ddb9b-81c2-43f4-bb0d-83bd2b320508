export type FormDetailType = {
  name: string;
  instanceId: string;
  formModelId: string;
  version: string;
  modelEditorJson: string;
};
export type ReceiveConfigType = {
  cartType?: string;
  efficetiveTimeInterval?: number;
  materialVos?: {
    absolveEnable: number;
    form: string;
    formTemp: string;
    materialId: number;
    name: string;
    viewProfileEnable: number;
  }[];
  normalTimeInterval?: number;
  premiumAble?: number;
  premiumTimeInterval?: number;
  receiveType?: string;
  remark?: string;
  resultName?: string;
  resultReceiveId?: number;
  resultType?: string;
  supportLanguage?: string;
  resultLanguage?: string;
  surrogateAble?: number;
  timeUnit?: number;
  tips?: string;
}[];
export type MaterialConfigType = {
  itemMaterialId: number;
  material: {
    code: string;
    version: number;
    materialId: string;
    nameZhMo: string;
    nameZhCn: string;
    namePt: string;
    nameEn: string;
  };
  noticeZhMo: string;
  noticeZhCn: string;
  noticePt: string;
  noticeEn: string;
}[];
export type StepInfoType = {
  sort: number;
  stepType: string;
  title?: string;
  stepName: string;
  itemResultReceiveMobieRespList: ReceiveConfigType;
  itemMaterialRespList?: MaterialConfigType;
};

export type ReceiveResultType = {
  // cartType: string;
  efficetiveTimeInterval?: number;
  normalTimeInterval?: number;
  premiumTimeInterval?: number;
  premiumAble?: number;
  receiveType: Record<string, string>[] | number[];
  resultNameZhMO?: string;
  resultReceiveId?: number;
  resultType: number;
  serviceResultCount?: number;
  person?: AddressType;
  send?: AddressType;
  resultLanguage?: string;
  surrogateAble?: number;
  timeUnit?: number;
  receiveRegion?: string;
  recipientName?: string;
  recipientPhone?: string;
  receiveAddress?: Record<string, string>[];
}[];
export type AddressType = {
  title: string;
  address: string;
  contactPerson?: string;
  phone?: string;
};

export type StepInfoListType = StepInfoType[];
export type MaterialsListType = {
  uploadFileCode: string;
  uploadFileName: string;
  uploadFileType: string;
  uploadFileUrl: string;
  materialsCode: string;
  materialsName: string;
  materialsId: string;
}[];

export type PayItemType = {
  // 收费项目编码
  code: string;
  // 收费项目名称
  payItemName: string;
  // 收款单号
  billId: string;
  // 单价
  price: number;
  // 数量
  quantity: number;
  // 总价
  total: number;
  // 币种
  unit: string;
};

export type PersonalFormType = {
  useCode: string;
  items: string;
  otherUse?: string;
  // englishTranslation: number;
};
