@prefix: form5-28f;

.@{prefix} {
  &- {
    &root {
      margin-left: 24px;

      .mophone-user .ant-form-item-label {
        display: none;
      }

      .ant-form label {
        font-size: 16px;
        align-items: start;
      }

      .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional):before {
        padding-top: 6px;
      }

      .mophone-user .mo-phone {
        width: 100%;
      }

      .ant-form {
        font-size: 16px;
      }

      .ant-form-item-label > label {
        font-size: 16px;
      }

      .ant-select {
        font-size: 16px;
      }

      .ant-input {
        font-size: 16px;
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px
    }

    &sectionTitle:before {
      display: block;
      content: "";
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px
    }

    &sectionBody {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .ant-form-item {
        width: 48%;
      }
    }

  }
}

.ant-radio-inner {
  border-color: #333;
}

.mophone-user {
  .mo-phone {
    margin-bottom: 0 !important;
  }
}
