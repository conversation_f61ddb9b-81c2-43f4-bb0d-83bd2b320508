import React from 'react';
import { DatePicker } from 'antd';
import moment from 'moment';

const { RangePicker: RangePickerAntd } = DatePicker;

const DatePickerWeb = (props) => {
  const {
    value,
    onChange,
    placeholder = '',
    format,
    type = 'single',
    enableDateSetting = {},
    ...otherProps
  } = props;
  const { start, end } = enableDateSetting;
  const extra = {};
  if (format) {
    extra.format = format;
  }
  if (format === 'YYYY-MM-DD HH:mm:ss') {
    extra.showTime = true;
  }
  const dateVal = {};
  if (value) {
    dateVal.value = moment(value);
  }

  const triggerChange = (_, dateString) => {
    onChange(dateString);
  };

  const singleDisableDate = (current) => {
    let minDisable = false;
    let maxDisable = false;
    if ('start' in enableDateSetting) {
      minDisable = current < moment().add(+start - 1, 'days');
    }
    if ('end' in enableDateSetting) {
      maxDisable = current > moment().add(+end, 'days');
    }
    return minDisable || maxDisable;
  };

  if (type === 'single') {
    return (
      <DatePicker
        placeholder={placeholder}
        disabledDate={singleDisableDate}
        onChange={triggerChange}
        {...dateVal}
        {...otherProps}
        {...extra}
      />
    );
  }
  if (type === 'startAndEnd') {
    return <RangePickerAntd disabledDate={singleDisableDate} {...otherProps} {...extra} />;
  }
  return null;
};

export default DatePickerWeb;
