import React, { useMemo } from 'react';
import pc from 'prefix-classnames';
import './Form5.less';

import { useIntl } from 'umi';
import { Col, Form, Input, InputNumber, Radio, Row, Select } from 'antd';
import { groupBy, keys } from 'lodash';
import { CarFuelSupplyType, CarLevel, useGetEmu } from '../../hooks';
import { useFormAction } from '../../hooks/useFormAction';
import { getVTAVehGasByTxnId, saveVTAVehGas } from '@/services/vta';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';
import { VtaRadioGroup } from '@/components/VtaRadioGroup/VtaRadioGroup';

const px = pc('vta-form-form-5');

export interface FormFiveProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId?: string;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

const FormFive = (props: FormFiveProps) => {
  const { className = '', txnId, ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();
  const [fuelEfficiency, setFuelEfficiency] = React.useState();
  const handleValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {
    setFuelEfficiency(allValues['fuelEfficiency']);
  };
  let { emuObj } = useGetEmu({
    noxDirectInjectEngine: 22019,
    efApplied: 22020,
  });
  let { vehType, vtaCategory, powerSource, canEditable } = useFormAction(
    txnId,
    form,
    getVTAVehGasByTxnId,
    saveVTAVehGas,
    props,
  );
  let gasFormItemList = useMemo(()=>[
    {
      type: 'select',
      category: '尾氣排放項目',
      label: '尾氣排放報告所載標準',
      name: 'exhaustStd',
      required: true,
      span: 24,
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <VtaSelect
          disabled={canEditable['exhaustStd']}
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={[]}
        />
      ),
    },
    {
      type: 'checkbox',
      category: '尾氣排放項目',
      label: '使用 NOX 儲存還原催化淨化技術的汽油直噴發動機',
      name: 'noxDirectInjectEngine',
      hide:
        [CarFuelSupplyType.電力].includes(powerSource) ||
        [CarLevel.半掛車, CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
      component: (
        <VtaRadioGroup
          disabled={canEditable['noxDirectInjectEngine']}
          options={emuObj.noxDirectInjectEngine}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '排放測試程序',
      name: 'exhaustTestProg',
      required: true,
      span: 24,
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <Input
          disabled={canEditable['exhaustTestProg']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '燃料效率測試程序',
      name: 'fuelEffTestProg',
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <Input
          disabled={canEditable['fuelEffTestProg']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '燃料效率 (km/L)',
      name: 'fuelEfficiency',
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEfficiency']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '車重 (kg)',
      name: 'fuelEffTestVehWeight',
      required: !!fuelEfficiency,
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEffTestVehWeight']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '測試質量 (kg)',
      name: 'fuelEffTestMass',
      required: true,
      hide:
        ![CarFuelSupplyType.電力].includes(powerSource) && [CarLevel.輕型汽車].includes(vehType),
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEffTestMass']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '最高車速 (km/h) ',
      name: 'fuelEffTestTopSpeed',
      required: true,
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEffTestTopSpeed']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '環保車申請',
      name: 'efApplied',
      hide:
        ![CarFuelSupplyType.電力].includes(powerSource) &&
        [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
      component: (
        <VtaSelect
          disabled={canEditable['efApplied']}
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={emuObj.noxDirectInjectEngine}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '一氧化碳(CO) （mg/km）',
      name: 'coMg',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '一氧化碳(CO) （mg/km）',
      name: 'coMg',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '總碳氫化合物(THC) (mg/kWh)',
      name: 'thc',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '非甲烷碳氫化合物(NMHC) (mg/km)',
      name: 'nmhc',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '氮氧化物(NOx) (mg/km)',
      name: 'noxMg',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '粒子數量(PM) (mg/km)',
      name: 'pm',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '粒子數量(PM) (x 10#/kWh)',
      name: 'pnCoefficient',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '總碳氫化合物+氮氧化物(THC+Nox)  (mg/km)',
      name: 'thcNox',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '氧化亞氮(N2O) (mg/km)',
      name: 'n2o',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHSC',
      label: '一氧化碳(CO) (mg/kWh)',
      name: 'escCoGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHSC',
      label: '總碳氫化合物(THC) (mg/kWh)',
      name: 'escThcGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHSC',
      label: '氮氧化物(NOx) (mg/kWh)',
      name: 'escNoxGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHSC',
      label: '氨(NH3) (ppm)',
      name: 'escNh3Ppm',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHSC',
      label: '顆粒物(PM) (mg/kWh)',
      name: 'escPmGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHSC',
      label: '粒子數量(PN) (x 10#/kWh)',
      name: 'escPnCoefficient',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHSC',
      label: '非甲烷碳氫化合物(NMHC) (mg/kWh)',
      name: 'escNmhcMgKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '一氧化碳(CO) (mg/kWh)',
      name: 'etcCoGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '總碳氫化合物(THC) (mg/kWh)',
      name: 'etcThcMgKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '氮氧化物(NOx) (mg/kWh)',
      name: 'etcNoxGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '氨(NH3) (ppm)',
      name: 'etcNh3Ppm',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '顆粒物(PM) (mg/kWh)',
      name: 'etcPmGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '粒子數量(PN) (x 10#/kWh)',
      name: 'etcPnCoefficient',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '非甲烷碳氫化合物(NMHC) (mg/kWh)',
      name: 'etcNmhcGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '甲烷(CH4) (mg/kWh)',
      name: 'etcCh4GKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '氮氧化物(NOx)  (g/mi)',
      name: 'etcNoxGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '一氧化碳(CO) (g/mi)',
      name: 'etcCoGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '甲醛(HCHO) (g/mi)',
      name: 'etcHchoGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '顆粒物(PM) (g/mi)',
      name: 'etcPmGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '非甲烷碳氫化合物(NMHC) (g/bhp-hr)',
      name: 'etcNmhcGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '氮氧化物(NOx) (g/bhp-hr)',
      name: 'etcNoxGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '一氧化碳(CO) (g/bhp-hr)',
      name: 'etcCoGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '顆粒物(PM) (g/bhp-hr)',
      name: 'etcPmGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: 'WHTC(JE05M)',
      label: '煙度(ELR) (%)',
      name: 'etcElrPercent',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
  ],[powerSource,vehType,emuObj])

  let gasMobileFormItemList = [
    {
      type: 'select',
      category: '尾氣排放項目',
      label: '尾氣排放報告所載標準',
      name: 'exhaustStd',
      required: true,
      span: 24,
      component: (
        <Select
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={['-', '-'].map((item, index) => ({
            label: item,
            value: Number(index),
          }))}
        />
      ),
    },
    {
      type: 'checkbox',
      category: '尾氣排放項目',
      label: '使用 NOX 儲存還原催化淨化技術的汽油直噴發動機',
      name: 'noxDirectInjectEngine',
      required: true,
      component: (
        <Radio.Group
          options={['是', ' 否'].map((item, index) => ({
            label: item,
            value: Number(index),
          }))}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '排放測試程序',
      name: 'exhaustTestProg',
      required: true,
      span: 24,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '燃料效率測試程序',
      name: 'fuelEffTestProg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '燃料效率 (km/L)',
      name: 'fuelEfficiency',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '車重 (kg)',
      name: 'fuelEffTestVehWeight',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '測試質量 (kg)',
      name: 'fuelEffTestMass',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '最高車速 (km/h) ',
      name: 'fuelEffTestTopSpeed',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放項目',
      label: '環保車申請',
      name: 'efApplied',
      required: true,
      component: (
        <Select
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={[
            { codeCname: '是', codeKey: '0' },
            { codeCname: '否', codeKey: '1' },
          ].map((item, index) => ({
            label: item.codeCname,
            value: item.codeKey,
          }))}
        />
      ),
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '一氧化碳(CO) （mg/km）',
      name: 'coMg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '一氧化碳(CO) （mg/km）',
      name: 'coMg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '總碳氫化合物(THC) (mg/kWh)',
      name: 'thc',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '非甲烷碳氫化合物(NMHC) (mg/km)',
      name: 'nmhc',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '氮氧化物(NOx) (mg/km)',
      name: 'noxMg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '粒子數量(PM) (mg/km)',
      name: 'pm',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '粒子數量(PM) (x 10#/kWh)',
      name: 'pnCoefficient',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '總碳氫化合物+氮氧化物(THC+Nox)  (mg/km)',
      name: 'thcNox',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: '尾氣排放結果值',
      label: '氧化亞氮(N2O) (mg/km)',
      name: 'n2o',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
  ];

  return (
    <div className={`${px('root')} ${className} multi-card-container`} {...otherProps}>
      <Form
        form={form}
        className={px('form')}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        {' '}
        <Form.Item name="txnId" hidden initialValue={txnId}>
          <Input />
        </Form.Item>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>尾氣排放及車載自我診新系統（ODB）證明或報告之聲明書</span>
          </div>
          {keys(groupBy(gasFormItemList, 'category')).map((item, index) => (
            <div className="multi-card-item-sub" key={index}>
              <div className="multi-card-title">
                <span>{item}</span>
              </div>
              <div className="multi-card-body">
                <Row gutter={16}>
                  {groupBy(gasFormItemList, 'category')[item].map((item, index) => (
                    <Col span={12} key={index}>
                      <Form.Item label={item.label} name={item.name} required={item.required}>
                        {item.component}
                      </Form.Item>
                    </Col>
                  ))}
                </Row>
              </div>
            </div>
          ))}
        </div>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>尾氣排放及車載自我診新系統（ODB）證明或報告之聲明書（摩托車）</span>
          </div>
          {keys(groupBy(gasMobileFormItemList, 'category')).map((item, index) => (
            <div className="multi-card-item-sub" key={index}>
              <div className="multi-card-title">
                <span>{item}</span>
              </div>
              <div className="multi-card-body">
                <Row gutter={16}>
                  {groupBy(gasMobileFormItemList, 'category')[item].map((item, index) => (
                    <Col span={12} key={index}>
                      <Form.Item label={item.label} name={item.name} required={item.required}>
                        {item.component}
                      </Form.Item>
                    </Col>
                  ))}
                </Row>
              </div>
            </div>
          ))}
        </div>
      </Form>
    </div>
  );
};

export default FormFive;
