import React, { useMemo } from 'react';
import pc from 'prefix-classnames';
import './Form5.less';

import { useIntl } from 'umi';
import { Col, Form, Input, InputNumber, Radio, Row, Select } from 'antd';
import { groupBy, keys } from 'lodash';
import { CarFuelSupplyType, CarLevel, useGetEmu } from '../../hooks';
import { useFormAction } from '../../hooks/useFormAction';
import { getVTAVehGasByTxnId, saveVTAVehGas } from '@/services/vta';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';
import { VtaRadioGroup } from '@/components/VtaRadioGroup/VtaRadioGroup';

const px = pc('vta-form-form-5');

export interface FormFiveProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId?: string;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

const FormFive = (props: FormFiveProps) => {
  const { className = '', txnId, ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();
  const [fuelEfficiency, setFuelEfficiency] = React.useState();
  const handleValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {
    setFuelEfficiency(allValues['fuelEfficiency']);
  };
  let { emuObj } = useGetEmu({
    noxDirectInjectEngine: 22019,
    efApplied: 22020,
  });
  let { vehType, vtaCategory, powerSource, canEditable } = useFormAction(
    txnId,
    form,
    getVTAVehGasByTxnId,
    saveVTAVehGas,
    props,
  );
  let gasFormItemList = useMemo(()=>[
    {
      type: 'select',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'exhaust_emission_standard' }),
      name: 'exhaustStd',
      required: true,
      span: 24,
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <VtaSelect
          disabled={canEditable['exhaustStd']}
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={[]}
        />
      ),
    },
    {
      type: 'checkbox',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'nox_storage_reduction_catalyst' }),
      name: 'noxDirectInjectEngine',
      hide:
        [CarFuelSupplyType.電力].includes(powerSource) ||
        [CarLevel.半掛車, CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
      component: (
        <VtaRadioGroup
          disabled={canEditable['noxDirectInjectEngine']}
          options={emuObj.noxDirectInjectEngine}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'emission_test_procedure' }),
      name: 'exhaustTestProg',
      required: true,
      span: 24,
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <Input
          disabled={canEditable['exhaustTestProg']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'fuel_efficiency_test_procedure' }),
      name: 'fuelEffTestProg',
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <Input
          disabled={canEditable['fuelEffTestProg']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'fuel_efficiency_km_l' }),
      name: 'fuelEfficiency',
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEfficiency']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'vehicle_weight_kg' }),
      name: 'fuelEffTestVehWeight',
      required: !!fuelEfficiency,
      hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEffTestVehWeight']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'test_mass_kg' }),
      name: 'fuelEffTestMass',
      required: true,
      hide:
        ![CarFuelSupplyType.電力].includes(powerSource) && [CarLevel.輕型汽車].includes(vehType),
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEffTestMass']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'max_speed_km_h' }),
      name: 'fuelEffTestTopSpeed',
      required: true,
      component: (
        <InputNumber
          style={{ width: '100%' }}
          disabled={canEditable['fuelEffTestTopSpeed']}
          placeholder={intl.formatMessage({ id: 'please_enter' })}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'eco_vehicle_application' }),
      name: 'efApplied',
      hide:
        ![CarFuelSupplyType.電力].includes(powerSource) &&
        [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
      component: (
        <VtaSelect
          disabled={canEditable['efApplied']}
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={emuObj.noxDirectInjectEngine}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_mg_km' }),
      name: 'coMg',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_mg_km' }),
      name: 'coMg',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'total_hydrocarbon_thc_mg_kwh' }),
      name: 'thc',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'non_methane_hydrocarbon_nmhc_mg_km' }),
      name: 'nmhc',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'nitrogen_oxide_nox_mg_km' }),
      name: 'noxMg',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'particle_matter_pm_mg_km' }),
      name: 'pm',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'particle_number_pn_coefficient' }),
      name: 'pnCoefficient',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'thc_nox_mg_km' }),
      name: 'thcNox',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'nitrous_oxide_n2o_mg_km' }),
      name: 'n2o',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whsc' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_mg_kwh' }),
      name: 'escCoGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whsc' }),
      label: intl.formatMessage({ id: 'total_hydrocarbon_thc_mg_kwh_whsc' }),
      name: 'escThcGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whsc' }),
      label: intl.formatMessage({ id: 'nitrogen_oxide_nox_mg_kwh' }),
      name: 'escNoxGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whsc' }),
      label: intl.formatMessage({ id: 'ammonia_nh3_ppm' }),
      name: 'escNh3Ppm',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whsc' }),
      label: intl.formatMessage({ id: 'particle_matter_pm_mg_kwh' }),
      name: 'escPmGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whsc' }),
      label: intl.formatMessage({ id: 'particle_number_pn_coefficient_whsc' }),
      name: 'escPnCoefficient',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whsc' }),
      label: intl.formatMessage({ id: 'non_methane_hydrocarbon_nmhc_mg_kwh' }),
      name: 'escNmhcMgKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_mg_kwh_whtc' }),
      name: 'etcCoGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'total_hydrocarbon_thc_mg_kwh_whtc' }),
      name: 'etcThcMgKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'nitrogen_oxide_nox_mg_kwh_whtc' }),
      name: 'etcNoxGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'ammonia_nh3_ppm_whtc' }),
      name: 'etcNh3Ppm',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'particle_matter_pm_mg_kwh_whtc' }),
      name: 'etcPmGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'particle_number_pn_coefficient_whtc' }),
      name: 'etcPnCoefficient',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'non_methane_hydrocarbon_nmhc_mg_kwh_whtc' }),
      name: 'etcNmhcGKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'methane_ch4_mg_kwh' }),
      name: 'etcCh4GKwh',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'nitrogen_oxide_nox_g_mi' }),
      name: 'etcNoxGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_g_mi' }),
      name: 'etcCoGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'formaldehyde_hcho_g_mi' }),
      name: 'etcHchoGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'particle_matter_pm_g_mi' }),
      name: 'etcPmGMi',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'non_methane_hydrocarbon_nmhc_g_bhp_hr' }),
      name: 'etcNmhcGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'nitrogen_oxide_nox_g_bhp_hr' }),
      name: 'etcNoxGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_g_bhp_hr' }),
      name: 'etcCoGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'particle_matter_pm_g_bhp_hr' }),
      name: 'etcPmGBhpHr',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'whtc_je05m' }),
      label: intl.formatMessage({ id: 'smoke_density_elr_percent' }),
      name: 'etcElrPercent',
      required: true,
      component: <InputNumber
          style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
  ],[powerSource,vehType,emuObj])

  let gasMobileFormItemList = [
    {
      type: 'select',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'exhaust_emission_standard' }),
      name: 'exhaustStd',
      required: true,
      span: 24,
      component: (
        <Select
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={['-', '-'].map((item, index) => ({
            label: item,
            value: Number(index),
          }))}
        />
      ),
    },
    {
      type: 'checkbox',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'nox_storage_reduction_catalyst' }),
      name: 'noxDirectInjectEngine',
      required: true,
      component: (
        <Radio.Group
          options={[intl.formatMessage({ id: 'yes' }), intl.formatMessage({ id: 'no' })].map((item, index) => ({
            label: item,
            value: Number(index),
          }))}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'emission_test_procedure' }),
      name: 'exhaustTestProg',
      required: true,
      span: 24,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'fuel_efficiency_test_procedure' }),
      name: 'fuelEffTestProg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'fuel_efficiency_km_l' }),
      name: 'fuelEfficiency',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'vehicle_weight_kg' }),
      name: 'fuelEffTestVehWeight',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'test_mass_kg' }),
      name: 'fuelEffTestMass',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'max_speed_km_h' }),
      name: 'fuelEffTestTopSpeed',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_items' }),
      label: intl.formatMessage({ id: 'eco_vehicle_application' }),
      name: 'efApplied',
      required: true,
      component: (
        <Select
          placeholder={intl.formatMessage({ id: 'please_select' })}
          options={[
            { codeCname: intl.formatMessage({ id: 'yes' }), codeKey: '0' },
            { codeCname: intl.formatMessage({ id: 'no' }), codeKey: '1' },
          ].map((item, index) => ({
            label: item.codeCname,
            value: item.codeKey,
          }))}
        />
      ),
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_mg_km' }),
      name: 'coMg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'carbon_monoxide_co_mg_km' }),
      name: 'coMg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'total_hydrocarbon_thc_mg_kwh' }),
      name: 'thc',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'non_methane_hydrocarbon_nmhc_mg_km' }),
      name: 'nmhc',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'nitrogen_oxide_nox_mg_km' }),
      name: 'noxMg',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'particle_matter_pm_mg_km' }),
      name: 'pm',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'particle_number_pn_coefficient' }),
      name: 'pnCoefficient',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'thc_nox_mg_km' }),
      name: 'thcNox',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
    {
      type: 'input',
      category: intl.formatMessage({ id: 'exhaust_emission_results' }),
      label: intl.formatMessage({ id: 'nitrous_oxide_n2o_mg_km' }),
      name: 'n2o',
      required: true,
      component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
    },
  ];

  return (
    <div className={`${px('root')} ${className} multi-card-container`} {...otherProps}>
      <Form
        form={form}
        className={px('form')}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        {' '}
        <Form.Item name="txnId" hidden initialValue={txnId}>
          <Input />
        </Form.Item>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>{intl.formatMessage({ id: 'exhaust_emission_odb_declaration_title' })}</span>
          </div>
          {keys(groupBy(gasFormItemList, 'category')).map((item, index) => (
            <div className="multi-card-item-sub" key={index}>
              <div className="multi-card-title">
                <span>{item}</span>
              </div>
              <div className="multi-card-body">
                <Row gutter={16}>
                  {groupBy(gasFormItemList, 'category')[item].map((item, index) => (
                    <Col span={12} key={index}>
                      <Form.Item label={item.label} name={item.name} required={item.required}>
                        {item.component}
                      </Form.Item>
                    </Col>
                  ))}
                </Row>
              </div>
            </div>
          ))}
        </div>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>{intl.formatMessage({ id: 'exhaust_emission_odb_declaration_motorcycle_title' })}</span>
          </div>
          {keys(groupBy(gasMobileFormItemList, 'category')).map((item, index) => (
            <div className="multi-card-item-sub" key={index}>
              <div className="multi-card-title">
                <span>{item}</span>
              </div>
              <div className="multi-card-body">
                <Row gutter={16}>
                  {groupBy(gasMobileFormItemList, 'category')[item].map((item, index) => (
                    <Col span={12} key={index}>
                      <Form.Item label={item.label} name={item.name} required={item.required}>
                        {item.component}
                      </Form.Item>
                    </Col>
                  ))}
                </Row>
              </div>
            </div>
          ))}
        </div>
      </Form>
    </div>
  );
};

export default FormFive;
