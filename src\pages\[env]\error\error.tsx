import React from 'react';
import './error.less';
import { useIntl, history } from 'umi';
import { Page } from '@gov-mo/mpaas-js-bridge';
import { getStatusBarHeight } from '@/utils/hooks';
import { CloseCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { NavBar } from 'antd-mobile';
import { isMobile } from '@/utils';

const NotFound = () => {
  const intl = useIntl();

  function onClose() {
    process.env.NODE_ENV === 'production' ? Page.closePage() : history.goBack();
  }

  return (
    <div className={isMobile() ? 'page-404-mobile' : 'page-404'}>
      {isMobile() && (
        <NavBar
          style={{
            paddingTop: getStatusBarHeight(),
          }}
          mode="light"
          leftContent={
            <LeftOutlined
              onClick={() => onClose()}
            />
          }
          rightContent={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <CloseCircleOutlined
                onClick={() => {
                  onClose();
                }}
              />
            </div>
          }
        ></NavBar>
      )}

      <div className="server-box">
        <h1 className="server-title">
          <svg
            data-v-bb619ec6=""
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="qrcode"
          >
            <rect data-v-bb619ec6="" width="48" height="48" fill="white" fill-opacity="0.01"></rect>
            <path
              data-v-bb619ec6=""
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M24 5.00018L2 43.0002H46L24 5.00018Z"
              fill="none"
              stroke="#ff4d4f"
              stroke-width="4"
              stroke-linejoin="round"
            ></path>
            <path
              data-v-bb619ec6=""
              d="M24 35.0002V36.0002"
              stroke="#ff4d4f"
              stroke-width="4"
              stroke-linecap="round"
            ></path>
            <path
              data-v-bb619ec6=""
              d="M24 19.0007L24.0083 29.0002"
              stroke="#ff4d4f"
              stroke-width="4"
              stroke-linecap="round"
            ></path>
          </svg>
          <span>{intl.formatMessage({ id: 'error' })}</span>
        </h1>
      </div>
    </div>
  );
};

export default NotFound;
