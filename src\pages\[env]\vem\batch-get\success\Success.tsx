import React, { useState, useEffect, useMemo } from 'react';
import pc from 'prefix-classnames';
import { history, useLocation, useIntl } from 'umi';
import './Success.less';
import { Form, Button, Alert, Image, Col, Modal } from 'antd';
import QRCode from 'qrcode';
import { isMobile } from '@/utils';
import { getTxnList } from '@/services/apply';
import { CheckCircleFilled, LeftOutlined } from '@ant-design/icons';
import { getStatusBarHeight } from '@/utils/hooks';
import { NavBar } from 'antd-mobile';
import { Page, File } from '@gov-mo/mpaas-js-bridge';

const BatchGetSuccessPage = () => {
  const px = pc(isMobile() ? 'page-batch-get-success-mobile' : 'page-batch-get-success');
  const intl = useIntl();
  const [tipMsg, setTipMsg] = useState('');

  const [form] = Form.useForm();
  const [qrcodeStatus, setQrcodeStatus] = useState(false);
  const [qrcodeUrl, setQrcodeUrl] = useState('');

  const getList = async () => {
    const res = await getTxnList({ exStatus: 'T', start: 0, length: 100 });
    if (res.code === '0') {
      // form.setFieldsValue({ list });
    }
  };

  const handleClose = () => {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const openQRCode = async (spNo) => {
    // 弹出二维码
    const url = await QRCode.toDataURL(spNo, { errorCorrectionLevel: 'H' });
    setQrcodeUrl(url);
    setQrcodeStatus(true);
  };

  const handleGet = async () => {
    const values = await form.validateFields();
    console.log('values===', values);
  };

  function gotoIndex() {
    process.env.NODE_ENV === 'production' ? Page.closePage() : history.push('/web/vem');
  }

  return (
    <>
      {tipMsg ? (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      ) : null}

      {isMobile() ? (
        <NavBar
          style={{
            paddingTop: getStatusBarHeight(),
            color: '#ffffff',
            backgroundColor: 'var(--primary-btn-fill)',
          }}
          mode="light"
          leftContent={
            <LeftOutlined
              onClick={() => {
                gotoIndex();
              }}
            />
          }
        >
          <span style={{ color: '#ffffff' }}>{intl.formatMessage({ id: 'get_car_list' })}</span>
        </NavBar>
      ) : null}

      <div
        style={{ paddingTop: isMobile() ? 40 + getStatusBarHeight() : 0 }}
        className={`${px('root')}`}
      >
        {isMobile() && (
          <>
            <Image
              preview={false}
              src={'/ovsap/image/success/success-bg.jpg'}
              style={{ marginTop: '-1px' }}
            />
            <h1>{intl.formatMessage({ id: 'successfully_submitted_application' })}</h1>
          </>
        )}

        <div className={px('body')}>
          {!isMobile() && (
            <div className={px('successTitle')}>{intl.formatMessage({ id: 'get_car_list' })}</div>
          )}
          <div className={px('sectionBody')}>
            {!isMobile() && (
              <div className={px('sectionTitle')}>
                <CheckCircleFilled style={{ color: '#084ab8' }} />
                <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
              </div>
            )}
            <div className="row">
              <div className="label">{intl.formatMessage({ id: 'batch_no' })}</div>
              <div className="value"></div>
            </div>
            <div className="row">
              <div className="label">{intl.formatMessage({ id: 'get_address' })}</div>
              <div className="value"></div>
            </div>
            <div className="row">
              <div className="label">{intl.formatMessage({ id: 'status' })}</div>
              <div className="value"></div>
            </div>
          </div>
        </div>
        <div className="batch-get-footer">
          <div className="footer-container">
            <Button onClick={handleClose} type="default">
              {intl.formatMessage({ id: 'turn_off' })}
            </Button>
            <Button onClick={() => openQRCode('10000000001')} type="default">
              {intl.formatMessage({ id: 'qr_code' })}
            </Button>
          </div>
        </div>
      </div>

      <Modal
        title={intl.formatMessage({ id: 'qr_code' })}
        width="300px"
        cancelText={intl.formatMessage({ id: 'turn_off' })}
        visible={qrcodeStatus}
        onCancel={() => setQrcodeStatus(false)}
        footer={null}
      >
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <img src={qrcodeUrl} style={{ width: 200, height: 200 }} />
        </div>
      </Modal>
    </>
  );
};

export default BatchGetSuccessPage;
