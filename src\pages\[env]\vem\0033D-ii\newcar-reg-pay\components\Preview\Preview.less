@prefix: preview-33d-ii;

.@{prefix} {
  &- {
    &root {
      margin-left: 24px;
      .my-card-item:nth-child(2) {
        background-color: #fff;
        .my-card-title {
          border-radius: 12px;
          background-color: #e9b745;
        }
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px
    }

    &sectionTitle:before {
      display: block;
      content: "";
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px
    }

    &sectionBody {
      .ant-form {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 0 24px;
        .ant-form-item {
          width: 48%;
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
              // color: @brand-primary;
            }
          }
          .bg {
            padding: 0 2px;
            color: #ffffff;
            background-color: @brand-primary;
          }

          .statusBg1
          {
            padding: 0 2px;
            color:#fff;
            background-color:#ffc107;
          }
          .statusBg2
          {
            padding: 0 2px;
            color:#fff;
            background-color:#13a07b;
            
            // background-color: @brand-primary;
          }
        }
      }
    }
  }
}

.owner-card-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: #fff;
  .ant-form-item {
    width: 48%;
    font-size: 16px;
    .ant-form-item-label {
      label {
        font-size: 16px;
        color: #6c6c6c;
      }
    }

    .statusBg1 {
      padding: 0 2px;
      color: #f33b40;
    }
    .statusBg2 {
      padding: 0 2px;
      color: @brand-primary;
    }
  }
  .preview-33d-ii-checkbox{
    font-size: 0.18rem;
    display: flex;
    align-items: center;
    .ant-checkbox-inner{
      width: 20px;
      height: 20px;
      border: 1px solid #ccc;
    }
  }
}

.owner-card-container-33d-ii {
  background-color: #fff;
  padding-top: 20px;
  .owner-card-item-33d-ii {
    margin-bottom: 20px;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    overflow: hidden;
    background: #fdf8f0;
    // &:nth-child(4n + 1) {
    //   background: #d9ebe7;
    // }
    // &:nth-child(4n + 2) {
    //   background: #dfeaf5;
    // }
    // &:nth-child(4n + 3) {
    //   background: #faedd9;
    // }
    // &:nth-child(4n + 4) {
    //   background: #f7e4da;
    // }

    .owner-card-title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .owner-card-form {
      background-color: #fff;
      padding: 20px;
      
    }
  }
}

