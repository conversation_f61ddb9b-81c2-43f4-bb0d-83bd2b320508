import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';
import { history, useIntl } from 'umi';
import { Steps, Alert } from 'antd';
import Preview from '../Preview';

import Payment from '../Payment';
import { getTxnServiceTitle } from '@/services/publicApi';
import { getLangGroup } from '@/locales/lang';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  classPrefix: string;
  step: number;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;
  setShowPrevStep: (boolean) => void;
  txnId: string;
  tipMsg: string;
  onPaySelected: (payMethod: string) => void;
  onSpeedUp: (isSpeedUp: string) => void;
  onIsNeedPay: (isNeedPay: boolean) => void;
  onSetIsCancelPay: (state: boolean) => void;
}

export interface StepOptionsItem {
  sort: number;
  title: string;
  steps: number[];
}

const FormBase = (props: FormBaseProps) => {
  const intl = useIntl();

  const { step, className = '', txnId, ...otherProps } = props;
  const {
    setDisablePrevStep,
    setDisableNextStep,
    nextStep,
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    setShowPrevStep,
    tipMsg,
    onPaySelected,
    onSpeedUp,
    onIsNeedPay,
    onSetIsCancelPay,
  } = otherProps;

  const px = pc(otherProps.classPrefix);

  const initStepOptions: StepOptionsItem[] = [
    {
      sort: 1,
      title: intl.formatMessage({ id: 'data_confirmation' }),
      steps: [1],
    },
    {
      sort: 2,
      title: intl.formatMessage({ id: 'payment_information' }),
      steps: [2],
    },
  ];

  const [stepOptions, setStepOptions] = useState<StepOptionsItem[]>(initStepOptions);
  const [title, setTitle] = useState(intl.formatMessage({ id: 'vehicle_registration_application' }));
  const [companyName, setCompanyName] = useState('');

  useEffect(() => {
    if (txnId) {
      getTxnServiceTitle(txnId)
        .then((res) => {
          setTitle(getLangGroup(res?.data?.serviceTitleCn, res?.data?.serviceTitlePt, res?.data?.serviceTitleEn))
          setCompanyName(getLangGroup(res?.data?.companyCname, res?.data?.companyPname, res?.data?.companyEname))
        })
        .catch((e) => console.log('error-- ', e));
    }
  }, [txnId]);

  // 生成右侧表单内容
  const renderRightContent = () => {
    if (step === 2) {
      if (location.search === '?result=1') {
        history.push('/web/vem/0033D/newcar-reg-apply');
        return false;
      }
      return (
        <Payment
          step={step}
          txnId={txnId}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          setNextStepText={setNextStepText}
          setShowClose={setShowClose}
          setShowTempStore={setShowTempStore}
          setShowCloseAndTempStore={setShowCloseAndTempStore}
          onPaySelected={onPaySelected}
          onSetIsCancelPay={onSetIsCancelPay}
        />
      );
    }

    if (step === 1) {
      return (
        <Preview
          step={step}
          setNextStepText={setNextStepText}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          setShowTempStore={setShowTempStore}
          setShowPrevStep={setShowPrevStep}
          txnId={txnId}
          onSpeedUp={onSpeedUp}
          onIsNeedPay={onIsNeedPay}
          setStepOptions={setStepOptions}
          initStepOptions={initStepOptions}
        />
      );
    }
    return <></>;
  };

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <h2 className={px('title')}>
        <span style={{ flex: 1 }}>{title}</span>
        <span>{companyName}</span>
      </h2>
      <div className={px('stepinfo')}>
        <div className={px('step')}>
          <h3>{intl.formatMessage({ id: 'application_steps' })}</h3>
          <Steps direction="vertical" size="small" current={step - 1}>
            {stepOptions.map((item) => {
              return <Steps.Step key={item.sort} title={item.title} icon={item.sort} />;
            })}
          </Steps>
        </div>
        <div className={px('form')}>
          {tipMsg ? (
            <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
              <Alert message={tipMsg} type="error" showIcon />
            </div>
          ) : null}
          {renderRightContent()}
        </div>
      </div>
    </div>
  );
};

export default FormBase;
