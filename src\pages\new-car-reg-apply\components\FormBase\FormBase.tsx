import React, { useEffect } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';

import { Steps } from 'antd';
import Form1 from '../Form1';
import Form2 from '../Form2';
import Form3 from '../Form3';
import Form4 from '../Form4';
import { useIntl } from 'umi';

export interface FormBaseProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
    classPrefix: string;
    step: number;

    setDisablePrevStep: (boolean) => void;
    setDisableNextStep: (boolean) => void;

    nextStep: () => void;

    setNextStepText: (string) => void;
}

const FormBase = (props: FormBaseProps) => {
  const intl = useIntl();

  const { step, className = '', ...otherProps } = props;
  const { setDisablePrevStep, setDisableNextStep, nextStep, setNextStepText } = otherProps;

  const px = pc(otherProps.classPrefix);

  useEffect(() => {
    console.log('[FormBase] step',step)
  }, [step])

  // 生成右侧表单内容
  const renderRightContent = () => {
    if(step===4) {
      return <Form4
        step={step}
        setDisablePrevStep={setDisablePrevStep}
        setDisableNextStep={setDisableNextStep}
        nextStep={nextStep}
        setNextStepText={setNextStepText}
      />
    }
    if(step===3) {
      return <Form3
        step={step}
        setDisablePrevStep={setDisablePrevStep}
        setDisableNextStep={setDisableNextStep}
        nextStep={nextStep}
      />
    }
    if(step===2) {
      return <Form2
        step={step}
        setDisablePrevStep={setDisablePrevStep}
        setDisableNextStep={setDisableNextStep}
        nextStep={nextStep}
      />
    }
    if(step===1) {
      return <Form1
        step={step}
        setDisablePrevStep={setDisablePrevStep}
        setDisableNextStep={setDisableNextStep}
        nextStep={nextStep}
      />
    }
    return <></>
  };

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <h2 className={px('title')}>{intl.formatMessage({ id: 'vehicle_registration_application' })}</h2>
      <div className={px('stepinfo')}>
          <div className={px('step')}>
            <h3>{intl.formatMessage({ id: 'application_steps' })}</h3>
            <Steps direction="vertical" size="small" current={step}>
              <Steps.Step title={intl.formatMessage({ id: 'application_materials' })} icon={1} />
              <Steps.Step title={intl.formatMessage({ id: 'upload_files' })} icon={2} />
              <Steps.Step title={intl.formatMessage({ id: 'payment_information' })} icon={3} />
            </Steps>
          </div>
          <div className={px('form')}>
            {renderRightContent()}
          </div>
        </div>
    </div>
  );
};

export default FormBase;
