import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';
import { history, useIntl } from 'umi';
import { Steps, Alert } from 'antd';
import Preview from '../Preview';
import { NavBar } from 'antd-mobile';
import { LeftOutlined, CloseCircleOutlined } from '@ant-design/icons';
import Payment from '../Payment';
import { Page } from '@gov-mo/mpaas-js-bridge';
import { getStatusBarHeight } from '@/utils/hooks';
import { getTxnServiceTitle } from '@/services/publicApi';
import { getLangGroup } from '@/locales/lang';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  classPrefix: string;
  step: number;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;
  setShowPrevStep: (boolean) => void;
  txnId: string;
  tipMsg: string;
  onPaySelected: (payMethod: string) => void;
  onSpeedUp: (isSpeedUp: string) => void;
  handlePrevStep: () => void;
  onIsNeedPay: (isNeedPay: boolean) => void;
  onSetIsCancelPay: (state: boolean) => void;
}

export interface StepOptionsItem {
  sort: number;
  title: string;
  steps: number[];
}

const FormBase = (props: FormBaseProps) => {
  const { step, className = '', txnId, ...otherProps } = props;
  const {
    setDisablePrevStep,
    setDisableNextStep,
    nextStep,
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    setShowPrevStep,
    tipMsg,
    onPaySelected,
    onSpeedUp,
    handlePrevStep,
    onIsNeedPay,
    onSetIsCancelPay,
  } = otherProps;

  const px = pc(otherProps.classPrefix);
  const intl = useIntl();
  const [headerOption, showHeaderOption] = useState(false);

  const initStepOptions: StepOptionsItem[] = [
    {
      sort: 1,
      title: intl.formatMessage({ id: 'data_confirmation' }),
      steps: [1],
    },
    {
      sort: 2,
      title: intl.formatMessage({ id: 'payment_information' }),
      steps: [2],
    },
  ];
  const [stepOptions, setStepOptions] = useState<StepOptionsItem[]>(initStepOptions);
  const [title, setTitle] = useState(intl.formatMessage({ id: 'vehicle_registration_application' }));
  const [companyName, setCompanyName] = useState('');

  useEffect(() => {
    if (txnId) {
      getTxnServiceTitle(txnId)
        .then((res) => {
          setTitle(getLangGroup(res?.data?.serviceTitleCn, res?.data?.serviceTitlePt, res?.data?.serviceTitleEn))
          setCompanyName(getLangGroup(res?.data?.companyCname, res?.data?.companyPname, res?.data?.companyEname))
        })
        .catch((e) => console.log('error-- ', e));
    }
  }, [txnId]);

  const renderTitle = () => {
    const [before, after] = title?.split(':');
      return (
        <h2 className={px('title')}>
          <div>{companyName}</div>
          <div style={{ marginTop: 10 }}>{before}</div>
          <div style={{ marginTop: 10 }}>{after}</div>
        </h2>
      );
  };

  // 生成右侧表单内容
  const renderRightContent = () => {
    if (step === 2) {
      if (location.search === '?result=1') {
        history.push('/web/vem/0033D/newcar-reg-apply');
        return false;
      }
      return (
        <Payment
          step={step}
          txnId={txnId}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          setNextStepText={setNextStepText}
          setShowClose={setShowClose}
          setShowTempStore={setShowTempStore}
          setShowCloseAndTempStore={setShowCloseAndTempStore}
          onPaySelected={onPaySelected}
          onSetIsCancelPay={onSetIsCancelPay}
        />
      );
    }

    if (step === 1) {
      return (
        <Preview
          step={step}
          setNextStepText={setNextStepText}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          setShowTempStore={setShowTempStore}
          setShowPrevStep={setShowPrevStep}
          txnId={txnId}
          onSpeedUp={onSpeedUp}
          onIsNeedPay={onIsNeedPay}
          setStepOptions={setStepOptions}
          initStepOptions={initStepOptions}
        />
      );
    }
    return <></>;
  };

  const findCurrentStep = (step: number) => {
    let currentStep = 1;
    stepOptions.forEach((item) => {
      if (item.steps.includes(step)) {
        currentStep = item.sort;
      }
    });
    return currentStep - 1;
  };

  return (
    <>
      <NavBar
        style={{ paddingTop: getStatusBarHeight() }}
        mode="light"
        leftContent={<LeftOutlined onClick={handlePrevStep} />}
        rightContent={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className="select">
              <span
                onClick={() => {
                  showHeaderOption(!headerOption);
                }}
              >
                {intl.formatMessage({ id: 'application_steps' })}
              </span>
              <div
                className="opt-wrap"
                style={{ display: headerOption ? 'block' : 'none' }}
                onClick={() => {
                  showHeaderOption(false);
                }}
              >
                <div className={px('step')}>
                  <Steps direction="vertical" size="small" current={findCurrentStep(step)}>
                    {stepOptions.map((item) => {
                      return <Steps.Step key={item.sort} title={item.title} icon={item.sort} />;
                    })}
                  </Steps>
                </div>
              </div>
            </div>
            <CloseCircleOutlined
              onClick={() => {
                process.env.NODE_ENV === 'production' ? Page.close() : history.push('/web/vem');
              }}
            />
          </div>
        }
      ></NavBar>
      <div
        style={{ paddingTop: 45 + getStatusBarHeight() }}
        className={`${px('root')} ${className}`}
        {...otherProps}
      >
        { renderTitle() }
        <div className={px('stepinfo')}>
          <div className={px('mobile-step')}>
            {stepOptions.map((item, index) => {
              return (
                <>
                  {index > 0 && <div className="line"></div>}
                  <div
                    className={`step-item ${item.steps.includes(step) ? 'progress' : ''}
                          ${step > item.steps.slice(-1)[0] ? 'finish' : ''}
                        `}
                  >
                    <span>{item.sort}</span>
                  </div>
                </>
              );
            })}
          </div>

          {/* <div className={px('step')}>
          <h3>{intl.formatMessage({ id: 'application_steps' })}</h3>
          <Steps direction="vertical" size="small" current={step - 1}>
            <Steps.Step title={intl.formatMessage({ id: 'data_confirmation' })} icon={1} />
            <Steps.Step title={intl.formatMessage({ id: 'payment' })} icon={2} />
          </Steps>
        </div> */}
          <div className={px('form')}>
            {tipMsg && (
              <div style={{ padding: '0.2rem 0.24rem 0' }}>
                <Alert message={tipMsg} type="error" showIcon />
              </div>
            )}
            {renderRightContent()}
          </div>
        </div>
      </div>
    </>
  );
};

export default FormBase;
