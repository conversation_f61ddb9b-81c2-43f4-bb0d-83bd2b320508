@prefix: success-33d-ii-mobile;

.@{prefix} {
  &- {
    &root {
      position: relative;
      min-height: 100%;
      background-color: #fff;

      h1 {
        font-size: 0.24rem;
        text-align: center;
      }
    }

    &alert {
      padding: 20px;
    }

    &body {
      margin: 10px auto;
      padding: 20px 0;
      width: 90%;
      border-top: 1px solid #eeeeee;
      font-size: 0.14rem;
    }

    &row {
      display: flex;
      margin: 10px 0;
      line-height: 1.5em;
    }

    &label {
      width: 7em;
      color: #aaaaaa;
    }

    &value {
      flex: 1;
      text-align: left;
      word-break: break-all;
    }

    &footer {
      width: 100%;
      height: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 20px;
      background: #fff;

      .ant-btn {
        margin-top: 20px;
        flex: 1;
        width: 100%;
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;
        height: 52px;
        line-height: 52px;
        border-radius: 26px;
        font-size: 16px;
        padding: 0 5px;
      }
    }

    &successTitlleMobile {
      color: #aaaaaa;
      text-align: center;
      font-size: 0.12rem;
      height: auto;
      border-top: 1px solid #eeeeee;
      line-height: 30px;
      margin: 20px 0px 10px 0px;
      padding: 10px 0;
    }

    &successBottomTitleSpan {
      color: #084ab8;
    }
  }
}
