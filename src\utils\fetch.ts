/* eslint-disable @typescript-eslint/no-explicit-any */
import { getResponseMessage, transform2HantFormat } from '@/locales/lang';
import { getLocale } from 'umi';
import type { ResponseError, RequestOptionsInit, RequestResponse } from 'umi-request';
import { extend } from 'umi-request';
import { isMobile } from '@/utils';
import {
  ACCOUNT_TYPE,
  generateCodeChallenge,
  generateCodeVerifier,
  ORIGIN_PLAT_FORM,
} from './auth';
import { removeStorage, setStorageObject } from './storage';
import * as queryString from 'query-string';
import Axios, { AxiosRequestConfig } from 'axios';

import { emit } from './customEvent';
import { redirectToGetCode } from '@/services/UserApi';
import { EMIT_KEY_MESSAGE_GLOBLE_ERROR } from './const';
import { error } from 'console';

// 使用routerBase区分不同事项(部署在大禹时同一个域名)
export const TOKEN_CACHE_KEY = `${window.routerBase}_token`;
export const ACCESS_TOKEN_KEY = 'AccessToken';

let authorization: string = localStorage.getItem(TOKEN_CACHE_KEY) || '';
let access_token: string = localStorage.getItem(ACCESS_TOKEN_KEY) || '';

export function setAuthorization(token: string) {
  authorization = token;
}

export function getAuthorization() {
  return authorization;
}

/* 异常处理 */
const errorHandler = function (error: ResponseError) {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.error(error.response.status, error.request);
  } else {
    // The request was made but no response was received or error occurs when setting up the request.
    console.error('Request error:', error.message);
  }
  throw error;
};

let http = Axios.create({
  baseURL: API_HOST,
  headers: {
    'Accept-Language': transform2HantFormat(getLocale()),
    Authorization: access_token || '',
    AccessToken: COMMON_ACCESS_TOKEN,
    accountType: ACCOUNT_TYPE,
    originPlatForm: ORIGIN_PLAT_FORM,
  },
});

const request = extend({
  // 为每个请求添加前缀
  prefix: API_HOST,
  errorHandler,
  headers: {
    'Accept-Language': transform2HantFormat(getLocale()),
    // 先从localStorage中获取token(仅PC端，暂不支持app端，因app有账号切换功能，会导致用户信息不匹配等问题)
    // Authorization: localStorage.getItem(TOKEN_CACHE_KEY) ?? '',
    Authorization: (!isMobile() && access_token) || '',
    AccessToken: COMMON_ACCESS_TOKEN,
    accountType: ACCOUNT_TYPE,
    originPlatForm: ORIGIN_PLAT_FORM,
    // Authorization: 'eyJhbGciOiJSUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bvHDhsv2YbnfZLCGzThOR-cVo2yn_cLi03Se6eu9pPDV38E_iwUlHuOoT31gYwkdjpZrpanmnyV96K8BDttIcjPUOX4-b896m0hkQsErhlpirltmOZrBjjbvI__CEE16vmc7Kso93M6lpObdHGAJx-L_D_EO82Iv6DEyeLCsFKLk1HsefJ2WVZGPfSMZuttZGf7Mz-k4_gAJZBU3hl8tumkhOqmN7P8bXuWy_BekpBykJOW0MYR-9HNej0gLWb0t6C5TasD4D8W7jzJv4xSCLfllBhN-JhYxOPx3fbuJNTnY7Rjlh_j-F9dVGClHHmyH-GZ8xKLFcTHC0oNKAiUwoA'
  },
  getResponse: true,
});

// 未有Token时请求加入队列
const requestTask: any[] = [];
let tempToken = '';

function runRequestTask() {
  while (requestTask.length) {
    requestTask.shift()?.();
  }
}

export function setToken(token: string, access_token?: string) {
  setAuthorization(token);
  localStorage.setItem(TOKEN_CACHE_KEY, token);
  access_token ? setStorageObject(ACCESS_TOKEN_KEY, access_token) : removeStorage(ACCESS_TOKEN_KEY);

  request.extendOptions({
    headers: {
      Authorization: access_token || '',
      AccessToken: COMMON_ACCESS_TOKEN,
      accountType: ACCOUNT_TYPE,
      originPlatForm: ORIGIN_PLAT_FORM,
    },
  });

  http = Axios.create({
    baseURL: API_HOST,
    headers: {
      'Accept-Language': transform2HantFormat(getLocale()),
      Authorization: access_token || '',
      AccessToken: COMMON_ACCESS_TOKEN,
      accountType: ACCOUNT_TYPE,
      originPlatForm: ORIGIN_PLAT_FORM,
    },
  });

  tempToken = token;
  runRequestTask();
}

export function setAcceptLanguage(lang) {
  if (lang) {
    request.extendOptions({
      headers: {
        'Accept-Language': lang,
      },
    });
  }
}

//`${(res as Response)?.status} ${(res as Response)?.statusText}`
/* 处理data */
const handleResponseData = <T>(res: RequestResponse<T> | Response) => {
  if ((res as Response)?.status === 502 || (res as Response)?.status === 504) {
    window.location.href = `${window.location.origin}/ovsap/web/error`;
    return { error: true, data: null, response: res as Response };
  }

  if ((res as Response)?.status === 500) {
    emit(EMIT_KEY_MESSAGE_GLOBLE_ERROR, {
      content: `${(res as Response)?.status} ${(res as Response)?.statusText}`,
    });
    return { error: true, data: null, response: res as Response };
  }

  const { data, response } = res as RequestResponse<T>;

  const refreshToken = response?.headers?.get('authorization');
  if (refreshToken) {
    setToken(refreshToken);
  }

  const query = queryString.parse(window.location.search);
  if ((data as any)?.code === '401' && !query.code_version && !query.code) {
    const codeVerifier = generateCodeVerifier(128, '') as string;
    const codeChallenge = generateCodeChallenge(codeVerifier, '');

    redirectToGetCode(codeVerifier, codeChallenge);
  }

  if ((data as any)?.code && (data as any)?.code !== '0' && (data as any)?.code !== 'W-8888') {
    emit(EMIT_KEY_MESSAGE_GLOBLE_ERROR, {
      content: getResponseMessage(data as any),
    });
  }
  if(data?.code!=0){
    return { error: true, data: null, response: res as Response };
  }
  return data;
};

function getCanUrlSend(options) {
  // 有token或者在声明api时设置了canSendWithoutToken为true的可通过。
  return tempToken || options?.canSendWithoutToken || options?.headers?.Authorization;
}

/**
 * Middleware
 * 有Token或设置了canSendWithoutToken时直接请求
 * 无则加入请求队列
 */
request.use(async (ctx, next) => {
  const { req } = ctx;
  const { options } = req;

  emit('request-start');

  if (!getCanUrlSend(options)) {
    await new Promise((resolve) => {
      requestTask.push(resolve);
    });
    // 处理已经压入队列的request取不到token的问题。
    options.headers = { ...options.headers, Authorization: tempToken };
  }
  try {
    await next();

    emit('request-end');
  } catch (error) {
    emit('request-end');

    console.log(error);
  }
});

const get = <T = any>(
  url: string,
  params?: Record<string, number | string | boolean> | URLSearchParams,
  options?: RequestOptionsInit,
) =>
  request
    .get<T>(url, { ...options, params })
    .then(handleResponseData);

const post = <T = any>(url: string, data?: Record<string, any>, options?: RequestOptionsInit) =>
  request
    .post<T>(url, { ...options, data })
    .then(handleResponseData);

const postform = <T = any>(url: string, data?: FormData, options?: AxiosRequestConfig) =>
  http.postForm<T>(url, data, options).then((res) => res.data);

const put = <T = any>(url: string, data?: Record<string, any>, options?: RequestOptionsInit) =>
  request
    .put<T>(url, { ...options, data })
    .then(handleResponseData);

const del = <T = any>(url: string, data?: Record<string, any>, options?: RequestOptionsInit) =>
  request
    .delete<T>(url, { ...options, data })
    .then(handleResponseData);

const upload = <T = any>(url: string, file: Blob, data?: { [key: string]: string }) => {
  const form = new FormData();
  data &&
    Object.entries(data).forEach(([key, value]) => {
      form.append(key, value);
    });

  form.append('file', file);
  return postform<T>(url, form, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * // How to upload file?
 * const formData = new FormData();
 * formData.append('file', file);
 * fetch('/api/v1/some/api', { method: 'POST', body: formData });
 */

// 导出你所需要使用的方法
export { request, get, post, put, del, upload, postform };

export interface FetchResponse<T> {
  code: string;
  error: boolean;
  externalMessageCn: string;
  externalMessagePt: string;
  externalMessageEn: string;
  internalMessageCn: string;
  internalMessagePt: string;
  internalMessageEn: string;
  data: T;
  dataArr: T[];
}
