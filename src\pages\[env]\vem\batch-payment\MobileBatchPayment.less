@prefix: mobile-batch-payment;

.@{prefix} {
  &- {
    &root {
      display: flex;
      flex-direction: column;
      // background-color: #eee;
      height: 100%;
    }

    &title {
      color: #363636;
      text-align: center;
      line-height: 0.21rem;
      padding: 0.12rem 0.24rem 0.12rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      font-size: 0.16rem;
      font-weight: 400;
      background-color: #fff;
    }

    &stepinfo {
      flex: 1;
      padding-bottom: 1rem;
      overflow-y: auto;
    }

    // &form {
    //     height: 100%;
    // }
    &body-root {
      //   padding: 0 10% 88px;
      //   height: calc(100% - 68px);
      //   overflow-y: auto
      // padding: 0.12rem;
      // padding-bottom: 0.2rem;
      padding: 0.2rem 0.12rem 0.4rem;
      font-size: 0.16rem;

      .ant-table {
        font-size: 0.12rem !important;
        border-radius: 0.12rem;
        border: 1px solid #f0f0f0;
        overflow: hidden;
      }

      .ant-table-thead > tr > th {
        background: @brand-primary;
        color: #fff;
      }
    }

    // &stepinfo {
    //     padding: 0.12rem;
    // }

    &formTitle {
      padding: 0.16rem;
      background: #e9b745;
      color: #fff;
      font-size: 0.12rem;
    }

    &pay-way-mobile {
      border-radius: 0.12rem;
      border: 1px solid #f0f0f0;
      overflow: hidden;
      padding-bottom: 0.2rem;
      margin-top: 0.2rem;
      background-color: #ffffff;

      .ant-radio-checked {
        &:after {
          border-color: #084ab8;
        }

        .ant-radio-inner {
          border-color: #084ab8;

          &::after {
            background-color: #084ab8;
          }
        }
      }
    }

    &footer-button {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 0.2rem;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
      height: 1rem;
      gap: 4%;

      .ant-btn {
        flex: 1;
        height: 0.52rem;
        line-height: 0.52rem;
        border-radius: 0.26rem;
        font-size: 0.16rem;
        padding: 0 0.05rem;

        &-ant-btn-default {
          background: #fff;
          color: #084ab8;
          border: 1px solid #084ab8;
        }
      }
    }


    // &title {
    //   font-size: 20px;
    //   font-weight: 700;
    //   padding: 20px 24px;
    //   border-bottom: 1px solid rgba(0,0,0,.15)
    // }

    // &stepinfo {
    //   display: flex
    // }


    // &form {
    //   flex: 1 1;
    //   margin-top: 20px
    // }

    // &body-root {
    //   margin-left: 24px;
    //   .ant-table-thead>tr>th {
    //     background: @brand-primary;
    //     color: #fff;
    //   }
    // }

    // &formTitle {
    //   padding: 16px;
    //   background: #E9B745;
    //   color: #fff;
    //   font-size: 16px;
    // }


    // &footer-button {
    //   position: absolute;
    //   bottom: 0;
    //   width: 100%;
    //   height: 68px;
    //   display: flex;
    //   align-items: center;
    //   justify-content: flex-end;
    //   padding: 0 10%;
    //   background: #fff;
    //   box-shadow: 0 -2px 8px 0 rgba(0,0,0,.1)
    // }

    // &footer-button .ant-btn {
    //   min-width: 128px;
    //   height: 48px;
    //   line-height: 48px;
    //   border-radius: 24px;
    //   margin-left: 12px;
    //   font-size: 16px;
    //   padding: 0 5px
    // }

    // &footer-button .ant-btn.ant-btn-default {
    //   background: #fff;
    //   color: @brand-primary;
    //   border: 1px solid @brand-primary
    // }
    &cost-title {
      padding: 0.16rem;
      background: #084ab8;
      color: #fff;
      font-size: 0.12rem;
    }

    &cost-container {
      padding: 0.16rem 0.16rem 0 0.16rem;
    }

    &cost-content {
      background: #f1f8fe;
      border-radius: 0.12rem;
      padding: 0.12rem 0.12rem 0 0.12rem;

      &-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.14rem;
        padding-bottom: 0.10rem;

        &-label {
          flex-shrink: 0;
          font-size: 0.14rem;
          color: #ACACAC;
          padding-right: 0.10rem;
        }

        &-text-black {
          color: black;
        }

        &-text-primary {
          color: #084ab8;
        }
      }
    }

    &cost-divide {
      border-top: 1px solid #ACACAC;
      padding-top: 0.10rem;
    }
  }
}

