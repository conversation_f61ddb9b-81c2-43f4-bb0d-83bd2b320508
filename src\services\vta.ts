import { type FetchResponse, get, post } from '@/utils/fetch';
import { CheckAddBrandModelDTO, CreateVTATxnDTO, getTxnListParams, getTxnListRes, OvsapVTABatteryDTO, OvsapVtaFeaturesDTO, OvsapVTAMainTxnParamsDTO, OvsapVTAPowerDeviceDTO, OvsapVTAVehBodyDTO, OvsapVTAVehGasDTO, pageRes, VtaGetSysCodeByTypeRdModel } from 'typings';

type Params = Record<string, string | number | boolean>;
export const getSysCodeByTypeRd = (params:{codeType:number,codeStatus:string}) => {
  return get<FetchResponse<VtaGetSysCodeByTypeRdModel>>('/vta/oa/sys/getSysCodeByTypeRd', params);
}

export const getVTATxnList = (params: getTxnListParams) =>
  post<FetchResponse<pageRes<getTxnListRes[]>>>('/vta/oa/apply/getVTATxnList', params);

export const saveVTAFeatures = (data:OvsapVtaFeaturesDTO) => {
  return post<FetchResponse<string>>('/vta/oa/apply/saveVTAFeatures',data);
}
export const getVTAFeaturesByTxnId = (params:{txnId:string}) => {
  return get<FetchResponse<string>>('/vta/oa/apply/getVTAFeaturesByTxnId',params);
}
export const saveVTASizeWeight = (data:any) => {
  return post<FetchResponse<string>>('/vta/oa/apply/saveVTASizeWeight',data);
}
export const getVTASizeWeightByTxnId = (params:{txnId:string}) => {
  return get<FetchResponse<string>>('/vta/oa/apply/getVTASizeWeightByTxnId',params);
}
export const createVTATxn = (data: CreateVTATxnDTO) =>
  post<FetchResponse<any>>(`/vta/oa/apply/createVTATxn`, data);
export const selectVehModel = (data?: Params) =>
  get<FetchResponse<any>>(`/vta/oa/apply/selectVehModel`, data);
export const checkAddBrandModel = (data?: CheckAddBrandModelDTO) =>
  post<FetchResponse<any>>(`/vta/oa/apply/checkAddBrandModel`, data);
export const updateVTAVehBrandByTxnId = (data?: OvsapVTAMainTxnParamsDTO) =>
  post<FetchResponse<any>>(`/vta/oa/apply/updateVTAVehBrandByTxnId`, data);

export const getVTAVehBrandByTxnId = (data?: Params) =>
  get<FetchResponse<any>>(`/vta/oa/apply/getVTAVehBrandByTxnId`, data);
export const getServiceTitle = (data?: Params) =>
  get<FetchResponse<any>>(`/vta/oa/sys/getServiceTitle`, data);
export const getVTAPowerDeviceByTxnId = (data?: Params) =>
  get<FetchResponse<any>>(`/vta/oa/apply/getVTAPowerDeviceByTxnId`, data);
export const saveVTAPowerDevice = (data?: OvsapVTAPowerDeviceDTO) =>
  post<FetchResponse<any>>(`/vta/oa/apply/saveVTAPowerDevice`, data);
export const getVTAVehBodyByTxnId = (data?: Params) =>
  get<FetchResponse<any>>(`/vta/oa/apply/getVTAVehBodyByTxnId`, data);
export const saveVTAVehBody = (data?: OvsapVTAVehBodyDTO) =>
  post<FetchResponse<any>>(`/vta/oa/apply/saveVTAVehBody`, data);
export const getVTAVehGasByTxnId = (data?: Params) =>
  get<FetchResponse<any>>(`/vta/oa/apply/getVTAVehGasByTxnId`, data);
export const saveVTAVehGas = (data?: OvsapVTAVehGasDTO) =>
  post<FetchResponse<any>>(`/vta/oa/apply/saveVTAVehGas`, data);

export const getVTAHomoBatteryByTxnId = (data?: Params) =>
  get<FetchResponse<any>>(`/vta/oa/apply/getVTAHomoBatteryByTxnId`, data);
export const saveVTAHomoBattery = (data?: OvsapVTABatteryDTO) =>
  post<FetchResponse<any>>(`/vta/oa/apply/saveVTAHomoBattery`, data);
