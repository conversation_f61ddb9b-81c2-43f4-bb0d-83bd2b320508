@prefix: success-one-33d-mobile;

.@{prefix} {
  &- {
    &root {
      position: relative;
      min-height: 100%;
      background-color: #fff;
      padding-top: 38px;

      h1 {
        font-size: 0.24rem;
        text-align: center;
      }
    }

    &alert {
      padding: 20px;
    }

    &body {
      margin: 10px auto;
      padding: 20px 0;
      width: 90%;
      border-top: 1px solid #eeeeee;
      font-size: 0.14rem;
    }

    &row {
      display: flex;
      margin: 10px 0;
      line-height: 1.5em;
    }

    &label {
      width: 7em;
      color: #aaaaaa;
    }

    &value {
      flex: 1;
      text-align: left;
      word-break: break-all;
    }

    &footer {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 20px;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
      gap: 4%;

      .ant-btn {
        flex: 1;
        background: #fff;
        color: #008378;
        border: 1px solid #008378;
        height: 52px;
        line-height: 52px;
        border-radius: 26px;
        font-size: 16px;
        padding: 0 5px;
      }
    }

    &successTitle {
      font-size: 0.16rem;
      color: #6c6c6c;
      height: 0.3rem;
      line-height: 0.3rem;
    }

    &successBottomTitle {
      color: #aaaaaa;
      text-align: center;
      font-size: 0.12rem;
      border-top: 1px solid #eeeeee;
      line-height: 30px;
      margin: 20px 0px 10px 0px;
      padding: 10px 0 100px 0;
    }
  }
}
