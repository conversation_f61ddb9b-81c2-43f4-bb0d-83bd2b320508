import React from 'react';
import pc from 'prefix-classnames';
import './Success.less';

import { history, useIntl } from 'umi';
import { Col, Button } from 'antd';

import { CheckCircleFilled } from '@ant-design/icons';
import { getLangGroup } from '@/locales/lang';
import dayjs from 'dayjs';

const px = pc('success-one-28f');

export interface SuccessProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId: string | undefined;
  detail: getEXTxnDataRes;
  serviceTitle: string;
  currentOwner?: saveTxnOwnersParams;
}

const Success = (props: SuccessProps) => {
  const intl = useIntl();
  const { className = '', txnId, serviceTitle, detail, currentOwner, ...otherProps } = props;

  function gotoIndex() {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  }

  return (
    <>
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('body')}>
          <div className={px('successTitle')}>{serviceTitle}</div>
          <div className={px('sectionBody')}>
            <div className={px('sectionTitle')}>
              <CheckCircleFilled style={{ color: '#13a07b' }} />
              <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
            </div>
            {/* <Col>
              <div className="label">{intl.formatMessage({ id: 'query_number' })}</div>
              <div className="value">{data?.spNo?.split('-')?.pop()}</div>
            </Col> */}
            {/* <Col>
              <div className="label">{intl.formatMessage({ id: 'trading_time' })}</div>
              <div className="value">
                {data?.payDate ? dayjs(data?.payDate).format('YYYY-MM-DD HH:mm:ss') : ''}
              </div>
            </Col> */}
            <Col>
              <div className="label">{intl.formatMessage({ id: 'car_submit_time' })}</div>
              <div className="value">
                {currentOwner?.verficationStatusDate &&
                  dayjs(currentOwner?.verficationStatusDate).format('YYYY-MM-DD HH:mm:ss')}
              </div>
            </Col>

            <Col>
              <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
              <div className="value">
                {getLangGroup(
                  detail.ovsapVehDataDTO?.vehTypeDescCn,
                  detail.ovsapVehDataDTO?.vehTypeDescPt,
                  detail.ovsapVehDataDTO?.vehTypeDescEn,
                )}
              </div>
            </Col>

            <Col>
              <div className="label">{intl.formatMessage({ id: 'vehicle_usage' })}</div>
              <div className="value">
                {getLangGroup(
                  detail.ovsapVehDataDTO?.vehUsageDescCn,
                  detail.ovsapVehDataDTO?.vehUsageDescPt,
                  detail.ovsapVehDataDTO?.vehUsageDescEn,
                )}
              </div>
            </Col>
            <Col>
              <div className="label">{intl.formatMessage({ id: 'brand' })}</div>
              <div className="value">
                {getLangGroup(
                  detail.ovsapVehDataDTO?.vehBrandDescCn,
                  detail.ovsapVehDataDTO?.vehBrandDescPt,
                  detail.ovsapVehDataDTO?.vehBrandDescEn,
                )}
              </div>
            </Col>
            <Col>
              <div className="label">{intl.formatMessage({ id: 'style' })}</div>
              <div className="value">{detail.ovsapVehDataDTO?.vehModel}</div>
            </Col>
            <Col>
              <div className="label">{intl.formatMessage({ id: 'color' })}</div>
              <div className="value">
                {getLangGroup(
                  detail.ovsapVehDataDTO?.colorDescCn,
                  detail.ovsapVehDataDTO?.colorDescPt,
                  detail.ovsapVehDataDTO?.colorDescEn,
                )}
              </div>
            </Col>
            <div className={px('successBottomTitle')} style={{textAlign:'left'}}>
              {intl.formatMessage({ id: 'success_bottom_title_yht' })}
            </div>
          </div>
        </div>
      </div>
      <div className={px('footer')}>
        <div className="footer-container">
          <Button
            type={'primary'}
            style={{background:'#13a07b'}}
            onClick={() => {
              gotoIndex();
            }}
          >
            {intl.formatMessage({ id: 'complete' })}
          </Button>
        </div>
      </div>
    </>
  );
};

export default Success;
