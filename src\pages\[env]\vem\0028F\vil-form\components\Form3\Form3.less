@prefix: form3-28f;

.@{prefix} {
  &- {
    &root {
      margin-left: 24px;

      .ant-form label {
        font-size: 16px;
      }

      .ant-form {
        font-size: 16px;
      }

      .ant-form-item {
        font-size: 16px
      }

      .ant-form-item-label > label {
        font-size: 16px;
      }

      .ant-select {
        font-size: 16px;
      }

      .ant-input {
        font-size: 16px;
      }

      .hideinput .ant-input {
        width: 0;
        padding-left: 0;
        padding-right: 0;
        border-left: none;
        border-right: none;
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px
    }

    &sectionTitle:before {
      display: block;
      content: "";
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px
    }

    &sectionBody {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .ant-form-item {
        width: 48%;
      }
    }

    &dspaFpNoItem {
      flex-direction: row !important;
      display: flex;

      .ant-form-item-label {
        padding: 5px 10px 0 0;
      }
    }

    &dspaFpNoTip {
      color: var(--adm-color-primary);
    }

    &owner-card-title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      background: #E3F0EC;
      border-radius: 12px;
    }

    &owner-card-form {
      background-color: #fff;
      padding: 20px;
    }
  }
}

@media (max-width: 1199px) {
  .@{prefix} {
    &- {
      &sectionBody {
        .ant-form-item {
          width: 100%;
        }
      }
    }
  }
}
