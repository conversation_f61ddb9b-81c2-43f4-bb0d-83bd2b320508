import { post, get } from '@/utils/fetch';
import type { FetchResponse } from '@/utils/fetch';
import type { StepInfoType, FormDetailType, MaterialsListType, PayItemType } from '../types/defs';

export const saveAffair = (data: {
  acceptInformType: string;
  applicantCertNo: string;
  applicantCertType: number;
  applicantLastnameFl: string;
  applicantLastnameZh: string;
  applicantNameFl: string;
  applicantNameZh: string;
  euid?: string;
  formDataJson: string;
  formDetailId?: string;
  formId: string;
  formModelJson: string;
  informLanguage: string;
  itemCode: string;
  itemId: string;
  mobilePhone: string;
  resultReceive: {
    cartType?: string;
    efficetiveTimeInterval?: number;
    normalTimeInterval?: number;
    // 是否加急 1-加急 2-普通
    premiumAble?: number;
    premiumTimeInterval?: number;
    // 领取方式
    receiveType?: number;
    resultNameEn?: string;
    resultNamePt?: string;
    resultNameZhMO?: string;
    resultReceiveId?: number;
    resultType?: number;
    serviceResultCount?: number;
    surrogateAble?: number;
    timeUnit?: number;
    receiveRegion?: string;
    recipientName?: string;
    recipientPhone?: string;
  }[];
  applicantMaterialsInsert?: MaterialsListType;
  serviceCode: string;
  serviceId: string;
  sourceCode: string;
  status: number;
  workNo?: string;
}) =>
  post<FetchResponse<{ workNo: string; forecastTime: string | null }>>('/acceptance/affair', data);

export const getStepInfo = (itemId: string) =>
  get<FetchResponse<StepInfoType[]>>(`/items/${itemId}/steps`);

export const getFormDetail = (data: { parentFormIds: string }) =>
  get<FetchResponse<FormDetailType[]>>('/acceptance/publishedForm', data);

export const getAuthUrl = ({ applyNo, channel = 'app' }) =>
  get<FetchResponse<string>>('/identities/auth-url', { applyNo, channel });

export const getPaymentItem = (data: { payCode: string }) =>
  get<FetchResponse<PayItemType>>('/criminal/pay-item', data);

export const isPaySuccess = (billId: string) =>
  get<FetchResponse<boolean>>('/payment/bill/success', { billId });

export const checkQua = data =>
  get<FetchResponse<Record<string, unknown>>>('/check', data);

export const savePersonalCert = data => post<FetchResponse<Record<string, string>>>('/acceptance', data);

export const getPersonalCertPay = data => get<FetchResponse<PayItemType>>('/bill-item', data);

export const getFirstFaceUrl = data => get<FetchResponse<string>>('/identities/auth-url', data);

export const getSecondFaceUrl = data => get<FetchResponse<string>>('/identities/auth-url/refresh', data);
