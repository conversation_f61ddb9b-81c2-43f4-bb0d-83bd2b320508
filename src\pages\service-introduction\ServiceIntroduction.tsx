import {Location, useLocation} from 'umi';
import {
  ApplicationNoticeItemId,
  GetApplicationNotice
} from "@/components/ApplicationNotice";


interface ServiceIntroductionLocation {
  query: {
    itemId: ApplicationNoticeItemId;
  };
}

const ServiceIntroduction = () => {
  const location = useLocation() as Location<unknown> & ServiceIntroductionLocation;

  return GetApplicationNotice({itemId: location.query.itemId});
};
export default ServiceIntroduction;
