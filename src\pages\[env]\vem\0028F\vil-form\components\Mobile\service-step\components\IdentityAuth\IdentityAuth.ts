import { useEffect } from 'react';
import { useSelector } from 'umi';
import { Auth, Storage } from '@gov-mo/mpaas-js-bridge';
import { Toast } from 'antd-mobile';
import usePersistFunc from '@/layouts/usePersistFunc';
// import { identificationSignature } from '@/services/publicApi';
import type { StepModelsState } from 'umi';
import { getFirstFaceUrl, getSecondFaceUrl } from '../../services/serviceStepApi';
import { securityLevel } from '../../config';

export interface IdentityAuthProps {
  onSuccess?: () => void;
  itemSecurityLevel: securityLevel;
  isFirstFace: boolean;
  secondId: string;
  setSecondId: (id: string) => void;
  onFail?: () => void;
}

// 调用身份识别方法
export const startAuth = (itemSecurityLevel: securityLevel, authUrl: string) => {
  Storage.get('userType').then((res: { success: boolean; data?: string }) => {
    if (res.success && typeof res.data === 'string') {
      Auth.start({
        accountType: res.data.toUpperCase(),
        identityLevel: securityLevel[itemSecurityLevel],
        reason: '',
        authUrl,
      });
    } else {
      Toast.fail('');
    }
  });
};

export const handleAuth = ({
  isFirstFace,
  applyNo,
  secondId,
  setSecondId,
  itemSecurityLevel,
}: {
  isFirstFace: boolean;
  applyNo: string;
  secondId: string;
  setSecondId: (id) => void;
  itemSecurityLevel: securityLevel;
}) => {
  if (isFirstFace) {
    getFirstFaceUrl({ applyNo, channel: 'app' }).then((res) => {
      startAuth(itemSecurityLevel, res.data);
      const str = decodeURIComponent(res.data);
      const index = str.lastIndexOf('id=');
      let id = str.substring(index + 3);
      if (id.includes('&')) {
        const arr: string[] = id.split('&', 1);
        [id] = arr;
      }
      setSecondId(id);
    });
  } else {
    getSecondFaceUrl({ id: secondId }).then((res) => {
      startAuth(itemSecurityLevel, res.data);
    });
  }
};

function IdentityAuth(props: IdentityAuthProps) {
  const {
    onSuccess,
    itemSecurityLevel,
    isFirstFace = true,
    secondId = '',
    setSecondId,
    onFail,
  } = props;

  const {
    stepModels: { workNo },
  } = useSelector(({ stepModels }: { stepModels: StepModelsState }) => ({ stepModels }));

  // 身份识别回调
  const onRecognitionFinish = usePersistFunc(
    (e: { data: { success: boolean; data: { error: string; code: string; id: string } } }) => {
      const { data } = e;
      if (data.success) {
        if (onSuccess) {
          // const { code = '', id = '' } = data.data;
          // identificationSignature({ code, id }).then((res) => {
          //   if (res.success) {
          onSuccess();
          //   } else {
          //     Toast.fail(res.message);
          //   }
          // });
        }
      } else {
        onFail?.();
      }
      // else if (typeof data.data === 'object' && data.data.error) {
      //   Toast.fail('身份識別未通過');
      // } else {
      //   Toast.fail('身份識別未通過');
      // }
    },
  );
  useEffect(() => {
    document.addEventListener('identity-auth', onRecognitionFinish);
    handleAuth({ isFirstFace, applyNo: workNo, secondId, setSecondId, itemSecurityLevel });
    return () => {
      document.removeEventListener('identity-auth', onRecognitionFinish);
    };
  }, []);
  return null;
}

export default IdentityAuth;
