import React, {
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import pc from 'prefix-classnames';
import './Form4.less';
import { MoPhone } from '@gov-mo/components';
import type { FormProps } from 'antd';
import { Alert, Descriptions, Form, message } from 'antd';

import { getAgentComp, saveAgentComp } from '@/services/0028F';
import { useIntl } from 'umi';
import { getResponseMessage } from '@/locales/lang';
import SpinContext from '../../../../../../../components/SpinContext';

const px = pc('form4-28f');

export interface FormFourProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;

  txnId: string | undefined;
}

export type FormFourAgentCompRef = {
  tempStore: () => Promise<void>;
};

const FormFour = forwardRef(
  (props: FormFourProps, ref: React.ForwardedRef<FormFourAgentCompRef>) => {
    const [agentCompResult, setAgentCompResult] = useState<saveAgentCompParams>();

    useImperativeHandle(ref, () => ({
      // 暫存
      async tempStore() {
        try {
          await form.validateFields();
          await submit(
            {
              ...agentCompResult,
              ...form.getFieldsValue(),
            },
            false,
          );
        } catch {}
      },
    }));

    const { className = '', setDisableNextStep, ...otherProps } = props;

    const intl = useIntl();

    const { contentNextStep, contentForNextStep, txnId } = otherProps;

    const [tipMsg, setTipMsg] = useState('');
    const [form] = Form.useForm();
    const { setLoading } = useContext(SpinContext);
    const agentCompResultRef = useRef<getAgentCompRes | null>(null);
    const onFinish: FormProps<saveAgentCompParams>['onFinish'] = async (values) => {
      submit({
        ...agentCompResult,
        ...values,
      });
    };

    const submit = async (values: saveAgentCompParams, next: boolean = true) => {
      setTipMsg('');
      if (!txnId) {
        return;
      }
      const ret = await saveAgentComp({
        txnId,
        // vtaCompIdentType: values.vtaCompIdentType, //進口商類型
        vtaCompIdentType: agentCompResultRef.current?.vtaCompIdentType ?? '', //進口商類型
        vtaCompIdentTypeDescCn: values.vtaCompIdentTypeDescCn,
        vtaCompIdentTypeDescPt: values.vtaCompIdentTypeDescPt,
        vtaCompIdentNo: values.vtaCompIdentNo, //進口商編號
        vtaCompCname: values.vtaCompCname, //進口商中文名
        vtaCompPname: values.vtaCompPname, //進口商葡文名
        // agentCompIdentType: values.agentCompIdentType, //代理商類型
        agentCompIdentType: agentCompResultRef.current?.agentCompIdentType ?? '', //代理商類型
        agentCompIdentTypeDescCn: values.agentCompIdentTypeDescCn,
        agentCompIdentTypeDescPt: values.agentCompIdentTypeDescPt,
        agentCompIdentNo: values.agentCompIdentNo, //代理商編號
        agentCompCname: values.agentCompCname, //代理商中文名
        agentCompPname: values.agentCompPname, //代理商葡文名
        agentContactPhone: values.agentContactPhone, //代理商聯絡電話
      });
      if (ret.code !== '0') {
        setTipMsg(getResponseMessage(ret));
        return;
      }
      !next && message.success(intl.formatMessage({ id: 'cp_save_success' }));
      next && contentForNextStep(2);
    };

    useEffect(() => {
      if (txnId) {
        getAgentComp(txnId)
          .then((ret) => {
            if (ret.code !== '0') {
              return;
            }

            const { data } = ret;
            agentCompResultRef.current = data;
            setAgentCompResult(data);
            let values = {
              vtaCompIdentType: data.vtaCompIdentType, //進口商類型
              vtaCompIdentTypeDescCn: data.vtaCompIdentTypeDescCn,
              vtaCompIdentTypeDescPt: data.vtaCompIdentTypeDescPt,
              vtaCompIdentNo: data.vtaCompIdentNo, //進口商編號
              vtaCompCname: data.vtaCompCname, //進口商中文名
              vtaCompPname: data.vtaCompPname, //進口商葡文名
              agentCompIdentType: data.agentCompIdentType, //代理商類型
              agentCompIdentTypeDescCn: data.agentCompIdentTypeDescCn,
              agentCompIdentTypeDescPt: data.agentCompIdentTypeDescPt,
              agentCompIdentNo: data.agentCompIdentNo, //代理商編號
              agentCompCname: data.agentCompCname, //代理商中文名
              agentCompPname: data.agentCompPname, //代理商葡文名
              agentContactPhone: data.agentContactPhone, //代理商聯絡電話
              phone: {
                area: '+853',
                number: data.agentContactPhone,
              },
            };
            form.setFieldsValue(values);
          })
          .catch((e) => setTipMsg(e.message))
          .finally(() => {
            setLoading(false);
          });
      } else {
        setLoading(false);
      }
    }, [txnId]);

    useEffect(() => {
      setLoading(true);
      contentForNextStep(1);
      return () => {
        contentForNextStep(0);
      };
    }, []);

    useEffect(() => {
      if (contentNextStep > 0) {
        form.submit();
      }
    }, [contentNextStep]);

    return (
      <>
        {tipMsg && (
          <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
            <Alert message={tipMsg} type="error" showIcon />
          </div>
        )}
        <div className={`${px('root')} ${className}`} {...otherProps}>
          <div className={`${px('sectionTitle')}`}>
            {intl.formatMessage({ id: 'apply_information' })}
          </div>
          <Form
            form={form}
            className={px('form')}
            layout="vertical"
            preserve={false}
            scrollToFirstError={true}
            onFinish={onFinish}
            onFinishFailed={() => {
              setDisableNextStep(false);
            }}
          >
            <div className="my-card-container">
              <div className="my-card-item">
                <div className="my-card-title">
                  {intl.formatMessage({ id: 'importer_information' })}
                </div>
                <div className="my-card-body">
                  <Descriptions layout="vertical">
                    <Descriptions.Item
                      label={intl.formatMessage({ id: 'importer_chinese_name' })}
                      span={1}
                    >
                      {agentCompResult?.vtaCompCname}
                    </Descriptions.Item>

                    <Descriptions.Item
                      label={intl.formatMessage({ id: 'importer_portuguese_name' })}
                      span={1}
                    >
                      {agentCompResult?.vtaCompPname}
                    </Descriptions.Item>
                  </Descriptions>
                </div>
              </div>
              <div className="my-card-item">
                <div className="my-card-title">
                  {intl.formatMessage({ id: 'agent_information' })}
                </div>
                <div className="my-card-body">
                  <Form.Item
                    style={{ width: '50%' }}
                    label={intl.formatMessage({ id: 'agent_phone' })}
                    name="agentContactPhone"
                    rules={[
                      { required: true, message: intl.formatMessage({ id: 'please_enter' }) },
                      {
                        pattern: /^6\d{7}$/,
                        message: intl.formatMessage({ id: 'phone_tips' }),
                      },
                    ]}
                  >
                    <div className="mophone-user">
                      <MoPhone
                        onlyAreaGroup={['+853']}
                        areaProps={{
                          disabled: true,
                          placeholder: intl.formatMessage({ id: 'please_select' }),
                          formItemProps: {
                            initialValue: '+853',
                          },
                        }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </div>
                  </Form.Item>
                </div>
              </div>
            </div>
          </Form>
        </div>
      </>
    );
  },
);

export default FormFour;
