import { get, post, del, postform } from '@/utils/fetch';
import type { FetchResponse } from '@/utils/fetch';

// 查詢進口准照資料
export const getRivmByDSFM4 = (params: getRivmByDSFM4Params) =>
  post<FetchResponse<getRivmByDSFM4Res>>('/oa/vem/getRivmByDSFM4', params);

export const deleteExTxn = (params: deleteExTxnParams) =>
  post<FetchResponse<getRivmByDSFM4Res>>('/oa/vem/deleteExTxn', params);

// 根據txnId獲取進口准照資料
export const getRivmByDSFM4WithTxnId = (txnId: string) =>
  get<FetchResponse<getRivmByDSFM4Res>>('/oa/vem/getRivmByDSFM4WithTxnId', { txnId });

// 創建0028F流水
export const createEXTxn = (params: createEXTxnParams) =>
  post<FetchResponse<createEXTxnRes>>('/oa/vem/createExTxn', params);

// 加載車主資料
export const getTxnOwners = (txnId: string) =>
  get<FetchResponse<saveTxnOwnersParams>>('/oa/txn/getTxnOwners', { txnId });

// 批量保存車主資料
export const saveTxnOwners = (params: saveTxnOwnersParams[]) =>
  post<FetchResponse<string>>('/oa/txn/saveTxnOwners', params);

// 刪除車主資料
export const deleteTxnOwners = (ownerId: string) =>
  del<FetchResponse<string>>('/oa/txn/deleteTxnOwners?txnOwnerId=' + ownerId, {});

// 輪胎下拉菜單
export const getVTATyreList = (vtaNo: string, vtaYear: string) =>
  get<FetchResponse<vehTyreOpt>>('/oa/sys/getVTATyreList', { vtaNo: vtaNo, vtaYear: vtaYear });

// 加載車輛資料
export const getTxnVehicle = (txnId: string) =>
  post<FetchResponse<getTxnVehicleRes>>('/oa/txn/getTxnVehicle', { txnId });

// 保存車輛資料
export const saveTxnVehicle = (params: saveTxnVehicleParams) =>
  post<FetchResponse<string>>('/oa/txn/saveTxnVehicle', params);

export const validateDspaFpNo = (params: validateDspaFpNoParams) =>
  get<FetchResponse<ValidateDspaFpNoResultDTO>>('/oa/txn/validateDspaFpNo', params);

// 初次加載進口商代理商資料（已废弃）
export const getFirstDefaultAgentComp = () =>
  post<FetchResponse<string>>('/oa/vem/getFirstDefaultAgentComp');

// 加載進口商代理商資料
export const getAgentComp = (txnId: string) =>
  post<FetchResponse<getAgentCompRes>>('/oa/vem/getAgentComp', { txnId });

// 保存進口商代理商資料
export const saveAgentComp = (params: saveAgentCompParams) =>
  post<FetchResponse<string>>('/oa/vem/saveAgentComp', params);

// 更新進口商代理商資料（已废弃）
export const updateAgentComp = (params: saveAgentCompParams) =>
  post<FetchResponse<string>>('/oa/vem/updateAgentComp', params);

// 聯絡人下拉菜單取值接口
export const getContactList = (txnId: string) =>
  get<FetchResponse<getContactListRes>>('/oa/txn/getContactList', { txnId });

// 加載聯絡人資料
export const getTxnContact = (txnId: string) =>
  get<FetchResponse<getTxnContactRes>>('/oa/txn/getTxnContact', { txnId });

// 保存聯絡人資料
export const saveContact = (params: saveContactParams) =>
  post<FetchResponse<string>>('/oa/txn/saveContact', params);

// 獲取收件列表
export const getServiceDocs = (txnId: string) =>
  get<FetchResponse<getServiceDocsRes>>('/oa/service/getServiceDocs', { txnId });

export const deleteServiceItemFile = (txnId: string, spServiceDocId: number) =>
  get<FetchResponse<string>>('/oa/service/deleteServiceItemFile', { txnId, spServiceDocId });

export const getServiceItemFile = (txnId: string, spServiceDocId: number) =>
  get<FetchResponse<string>>(
    '/oa/service/getServiceItemFile',
    { txnId, spServiceDocId },
    { responseType: 'blob' },
  );

// 收件文件上傳接口
export const serviceItemFileUpload = (
  queryParams: serviceItemFileUploadQueryParams,
  params: FormData,
) =>
  postform<FetchResponse<string>>('/oa/service/serviceItemFileUpload', params, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

// 收件頁面下一步，檢查必提交收件接口
export const checkServiceItemFileUpload = (txnId: string) =>
  get<FetchResponse<string>>('/oa/service/checkServiceItemFileUpload', { txnId });

// 加載資料
export const getEXTxnData = (txnId: string) =>
  get<FetchResponse<getEXTxnDataRes>>('/oa/vem/getExTxnData', { txnId });

// 發送車主認證
export const sendFacialRecognitionECNotice = (params: sendFacialRecognitionECNoticeParams) =>
  post<FetchResponse<string>>('/oa/pushMsg/sendFacialRecognitionECNotice', params);

// 確認資料下一步
export const validateTxnForPayment = (txnId: string) =>
  get<FetchResponse<string>>('/oa/vem/validateTxnForPayment', { txnId });

// 检查当前txn流水是否需要支付
export const validateTxnIsNeedToPay = (txnId: string) =>
  get<FetchResponse<{ isNeedPay: boolean }>>('/oa/vem/validateTxnIsNeedToPay', { txnId });

// 獲取收費項
export const getServiceFee = (txnId: string) =>
  get<FetchResponse<getServiceFeeRes>>('/oa/service/getServiceFee', { txnId });

// 確定支付
export const completePayment = (params: completePaymentParams) =>
  post<FetchResponse<completePaymentRes>>('/oa/service/completePayment', params);

// 確定支付前校验是否支持
export const batchPayValidateData = (params: batchPaymentCheckParams) =>
  post<FetchResponse<batchPaymentCheckRes>>('/oa/service/batchPayValidateData', params);

// 批量領取EX牌：數據校驗
export const validateGetBatchExTxn = (params: { txnIds: string[] }) =>
  post<FetchResponse<completePaymentRes>>('/oa/batchEx/validateGetBatchExTxn', params);

// 加載批次記錄數據
export const getBatchExTxnData = (params: { txnIds: string[] }) =>
  post<FetchResponse<getBatchExTxnDataRes>>('/oa/batchEx/getBatchExTxnData', params);

// 批量領取EX牌(新增)創建批次記錄: 生成batchId
export const createBatchExData = (params: { txnIds: string[]; licAddrCode: string }) =>
  post<FetchResponse<string>>('/oa/batchEx/createBatchExData', params);

// 批量領取EX牌（更新）: 保存
export const updateBatchExData = (params: { batchId: string; licAddrCode: string }) =>
  post<FetchResponse<string>>('/oa/batchEx/updateBatchExData', params);

// 批次内刪除單個記錄（D狀態）
export const deleteBatchExDetail = (params: {
  detailId: string;
  batchId: string;
  vehId: string;
  txnId: string;
}) => post<FetchResponse<string>>('/oa/batchEx/deleteBatchExDetail', params);

// 批量領取EX牌列表
export const getBatchExDataPage = (params) =>
  post<FetchResponse<getBatchExDataPageDataRes>>('/oa/batchEx/getBatchExDataPage', params);

// 通過batchId加載批次記錄數據
export const getBatchExDataById = (params: { batchId: string }) =>
  post<FetchResponse<getBatchExTxnDataRes>>('/oa/batchEx/getBatchExDataById', params);

// 失效整個批次記錄
export const cancelBatchData = (batchId: string) =>
  post<FetchResponse<string>>(`/oa/batchEx/cancelBatchData`, { batchId });

// 服務完成接口
export const completeTxn = (txnId: string) =>
  post<FetchResponse<string>>('/oa/service/completeTxn', { txnId });

// 服务结果
export const getExTxnCompleteData = (txnId: string) =>
  post<FetchResponse<getExTxnCompleteDataRes>>('/oa/vem/getExTxnCompleteData', { txnId });

export const completeTxnForPay = (requestId: string) =>
  get<FetchResponse<getServiceFeeRes>>('/oa/service/completeTxnForPay', { requestId });

export const getTxnPendingApproval = (transId: string) =>
  post<FetchResponse<getExTxnCompleteDataRes>>('/oa/vem/getTxnPendingApproval', { transId });

// 下載收據
export const getReceiptFile = (txnId: string) =>
  get<FetchResponse<string> | BlobPart>(
    '/oa/service/getReceiptFile',
    { txnId },
    {
      responseType: 'blob',
    },
  );

// 下載收據 獲取預覽地址
export const receiptFileUrl = (txnId: string) =>
  post<FetchResponse<string>>(`/oa/service/receiptFileUrl`, { txnId });

// 下載EX牌試驗車牌臨時准照
export const printExReport = (txnId: string) =>
  post<FetchResponse<string> | BlobPart>(
    '/oa/vem/printExReport',
    { txnId },
    {
      responseType: 'blob',
    },
  );

// 下載EX牌試驗車牌臨時准照 獲取預覽地址
export const getExReportPreviewUrl = (txnId: string) =>
  post<FetchResponse<string>>(`/oa/vem/getExReportPreviewUrl`, { txnId });

// 服務操作下拉菜單
export const getServiceOptions = () =>
  get<FetchResponse<getServiceOptions>>('/oa/sys/getServiceOptions');

// 取消支付按鈕
export const cancelTxnPay = (txnIds: string[]) =>
  post<FetchResponse<string>>('/oa/service/cancelTxnPay', { txnIds });

// 中銀網銀支付用於判斷跳轉到哪個頁面
export const checkTxnJumpOrder = (requestId: string) =>
  get<FetchResponse<TxnJumpPageDTO>>('/oa/vem/checkTxnJumpOrder', { requestId });
