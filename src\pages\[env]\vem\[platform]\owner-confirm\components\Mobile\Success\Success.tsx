import React from 'react';
import pc from 'prefix-classnames';
import './Success.less';

import { history, useIntl } from 'umi';
import { Button, Image } from 'antd';

import { Page } from '@gov-mo/mpaas-js-bridge';

import { NavBar } from 'antd-mobile';
import { LeftOutlined } from '@ant-design/icons';
import { getLangGroup } from '@/locales/lang';
import { getStatusBarHeight } from '@/utils/hooks';
import dayjs from 'dayjs';

const px = pc('success-one-28f-mobile');

export interface FormTenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId: string | undefined;
  detail: getEXTxnDataRes;
  serviceTitle: string;
  currentOwner?: saveTxnOwnersParams;
}

const FormTen = (props: FormTenProps) => {
  const intl = useIntl();
  const { className = '', txnId, serviceTitle, currentOwner, detail, ...otherProps } = props;

  function gotoIndex() {
    process.env.NODE_ENV === 'production' ? Page.closePage() : history.push('/web/vem');
  }

  return (
    <>
      <NavBar
        style={{ paddingTop: getStatusBarHeight(), backgroundColor: '#fff' }}
        mode="light"
        leftContent={
          <LeftOutlined
            onClick={() => {
              gotoIndex();
            }}
          />
        }
      >
        <span style={{ color: '#000' }}>{intl.formatMessage({ id: 'complete_submit' })}</span>
      </NavBar>
      <div
        style={{ paddingTop: 40 + getStatusBarHeight() }}
        className={`${px('root')} ${className}`}
        {...otherProps}
      >
        <Image
          preview={false}
          src={'/ovsap/image/success/success-bg-one.jpg'}
          style={{ marginTop: '-1px' }}
        />
        <h1 style={{ color: '#008378' }}>
          {intl.formatMessage({ id: 'successfully_submitted_application' })}
        </h1>
        <div className={px('body')}>
          <div className={px('successTitle')}>{serviceTitle}</div>
          {/* <div className={px('row')}>
            <div className={px('label')}>{intl.formatMessage({ id: 'query_number' })}</div>
            <div className={px('value')}>{data?.spNo?.split('-')?.pop()}</div>
          </div> */}
          {/* <div className={px('row')}>
            <div className={px('label')}>
              {intl.formatMessage({ id: 'service_procedure_name' })}
            </div>
            <div className={px('value')}>{serviceTitle}</div>
          </div> */}

          <div className={px('row')}>
            <div className={px('label')}>{intl.formatMessage({ id: 'car_submit_time' })}</div>
            <div className={px('value')}>
              {currentOwner?.verficationStatusDate &&
                dayjs(currentOwner?.verficationStatusDate).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </div>

          <div className={px('row')}>
            <div className={px('label')}>{intl.formatMessage({ id: 'vehicle_level' })}</div>
            <div className={px('value')}>
              {getLangGroup(
                detail.ovsapVehDataDTO?.vehTypeDescCn,
                detail.ovsapVehDataDTO?.vehTypeDescPt,
                detail.ovsapVehDataDTO?.vehTypeDescEn,
              )}
            </div>
          </div>

          <div className={px('row')}>
            <div className={px('label')}>{intl.formatMessage({ id: 'vehicle_usage' })}</div>
            <div className={px('value')}>
              {getLangGroup(
                detail.ovsapVehDataDTO?.vehUsageDescCn,
                detail.ovsapVehDataDTO?.vehUsageDescPt,
                detail.ovsapVehDataDTO?.vehUsageDescEn,
              )}
            </div>
          </div>
          <div className={px('row')}>
            <div className={px('label')}>{intl.formatMessage({ id: 'brand' })}</div>
            <div className={px('value')}>
              {getLangGroup(
                detail.ovsapVehDataDTO?.vehBrandDescCn,
                detail.ovsapVehDataDTO?.vehBrandDescPt,
                detail.ovsapVehDataDTO?.vehBrandDescEn,
              )}
            </div>
          </div>
          <div className={px('row')}>
            <div className={px('label')}>{intl.formatMessage({ id: 'style' })}</div>
            <div className={px('value')}>{detail.ovsapVehDataDTO?.vehModel}</div>
          </div>
          <div className={px('row')}>
            <div className={px('label')}>{intl.formatMessage({ id: 'color' })}</div>
            <div className={px('value')}>
              {' '}
              {getLangGroup(
                detail.ovsapVehDataDTO?.colorDescCn,
                detail.ovsapVehDataDTO?.colorDescPt,
                detail.ovsapVehDataDTO?.colorDescEn,
              )}
            </div>
          </div>
          <div className={px('successBottomTitle')} style={{textAlign:'left'}}>
            {intl.formatMessage({ id: 'success_bottom_title_yht' })}
          </div>
        </div>
      </div>
      <div className={px('footer')}>
        <Button
          type={'primary'}
            style={{background:'#13a07b'}}
          onClick={() => {
            gotoIndex();
          }}
        >
          {intl.formatMessage({ id: 'complete' })}
        </Button>
      </div>
    </>
  );
};

export default FormTen;
