@import '~@gov-mo/components/cjs/global.less';
@import '~@/themes/themes.less';
// 公共组件库暗黑版
@import '~@/themes/components-dark.less';
@import './components/Modal/Modal.less';
@import './styles/common.less';
@import "./styles/base.css";

@all-font: 'PingFang SC', 'Microsoft YaHei';
html {
  font-size: 100px;
}

@media (max-width: 1199px) {
  html {
    font-size: 62.5px;
  }

  // #iframeHeader {
  //   display: none;
  // }
  // #root {
  //   padding-top:0px !important;
  // }
}

html,
body,
#root {
  height: 100%;
  background-color: var(--firstBgColor);
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

body {
  font-family: @all-font;
  color: @font-color-base;
  font-size: @font-size-md;
}

html,
body,
ul,
li,
ol,
dl,
dd,
dt,
p,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
legend,
img {
  margin: 0;
  padding: 0;
}

fieldset,
img,
input,
button {
  border: none;
  padding: 0;
  margin: 0;
  outline-style: none;
}

// ios系统中元素被触摸时产生的半透明灰色遮罩
a,
button,
input,
textarea {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

ul,
ol {
  list-style: none;
}

input {
  padding-top: 0;
  padding-bottom: 0;
  // font-family: @all-font;
}

select,
input {
  vertical-align: middle;
}

select,
input,
textarea {
  margin: 0;
}

textarea {
  resize: none;
}

img {
  border: 0;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

.clearfix:before,
.clearfix:after {
  content: '';
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
}

a {
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  text-decoration: none;
  font-weight: normal;
  font-size: 100%;
}

s,
i,
em,
u {
  font-style: normal;
  text-decoration: none;
}

input::-webkit-input-placeholder {
  color: @font-color-base !important;
}

input:-moz-placeholder {
  color: @font-color-base;
}

input::-moz-placeholder {
  color: @font-color-base;
}

input:-ms-input-placeholder {
  color: @font-color-base;
}

.hide {
  display: none !important;
}

.unShow {
  opacity: 0 !important;
}

.show {
  opacity: 1 !important;
}

.active {
  border: 1px solid @brand-primary !important;
}

// antd mobile 样式全局覆盖
.am-tabs-default-bar-underline {
  border-radius: 0.25rem;
  height: 1vw;
  border: none !important;
  background-image: @brand-gradient;
  transform: scaleX(0.6) !important;
}

input::-webkit-input-placeholder {
  color: white;
}

.am-accordion .am-accordion-item .am-accordion-header {
  padding: 0 32px;
}

.am-accordion .am-accordion-item .am-accordion-header:after {
  content: none;
}

.am-accordion .am-accordion-item .am-accordion-header i {
  display: none;
}

.am-accordion .am-accordion-item .am-accordion-header[aria-expanded~='true'] svg {
  transform: rotate(180deg);
}

.am-navbar .am-navbar-title .mo-baseContainer-mobile-title {
  font-size: 36px;
  color: var(--firstTextColor);
}

.am-accordion .am-accordion-item .am-accordion-header::after,
.am-accordion .am-accordion-item .am-accordion-content .am-accordion-content-box::after,
.am-accordion::before {
  content: none !important;
}

.mo-baseContainer-mobile-leftIcon {
  width: 38px;
  height: 52px;
  color: var(--firstTextColor);
}

.ant-cascader-menu-item .ant-cascader-menu-item-keyword {
  color: #009688;
}

.am-modal.am-modal-transparent {
  width: 72.5vw;

  .am-modal-content {
    padding: 0;
    background: var(--secondBgColor);

    .am-modal-header {
      padding: 32px 48px 20px;

      .am-modal-title {
        color: var(--primaryColor);
      }
    }

    .am-modal-body {
      padding: 0 48px 48px;
      color: var(--secondTextColor);

      .am-modal-alert-content {
        // text-align: left;
        white-space: pre-wrap;
      }
    }

    .am-modal-footer {
      padding: 0 32px 32px;

      .am-modal-button-group-v {
        display: flex;
        justify-content: center;

        .am-modal-button {
          background-color: var(--primaryColor);
          border-radius: 60px;
          color: #fff;
          width: 47.5%;
          height: 96px;

          &:nth-last-child(1) {
            background-color: var(--primaryColor);
          }
        }
      }

      .am-modal-button-group-h {
        display: flex;
        justify-content: space-around;
        // padding: 20px 0;

        .footerStyle(0);

        .am-modal-button {
          .btnStyle();
          background-color: var(--primaryColor);
          border-radius: 60px;
          color: #fff;
          // flex: 0 0 auto;
          // width: 47.5%;
          height: 96px;
          line-height: 94px;
          display: flex;
        }

        .am-modal-button:first-child {
          background-color: var(--secondBgColor);
          color: var(--primaryColor);
          border: 1px solid var(--primaryColor);
          // margin-right: 16px;
        }

        .am-modal-button:nth-last-child(2) {
          margin-right: 6 * @hd;
        }
      }

      .am-modal-button-group-h::before {
        height: 0;
      }
    }
  }
}

// antd-mobile-picker 确认取消按钮
// TODO:用统一样式替换
.am-picker-popup {
  .am-picker-popup-item {
    &.am-picker-popup-header-left {
      color: var(--thirdTextColor);
    }

    &.am-picker-popup-header-right {
      color: #00785d;
    }

    &-active {
      background: var(--thirdBgColor);
    }
  }

  .am-picker .am-picker-col-item {
    color: var(--firstTextColor);
  }
}

.rmc-picker-popup-header {
  .rmc-picker-popup-item {
    &.rmc-picker-popup-header-left {
      color: #8a8a8a;
    }

    &.rmc-picker-popup-header-right {
      color: #00785d;
    }
  }
}

// rmc-popup园角
.rmc-picker-popup {
  border-radius: 10px 10px 0px 0px;
  box-shadow: 0px 2px 10px 0px #000000;
  background-color: var(--secondBgColor);
}

.am-picker {
  &-popup {
    &-header {
      background-image: none;
    }

    &-header:after {
      display: none !important;
    }

    &-item {
      padding: 16 * @hd;
      height: auto;
    }
  }

  &-col-indicator:before,
  &-col-indicator:after {
    width: 0 !important;
  }
}

// 去掉不同机型偶现的线条
.am-accordion-content-box {
  border-bottom: none !important;
}

.am-accordion {
  border-top: none;
}

.am-modal-button-group-h {
  border-top: none !important;
}

.pc-pay-modal-actions {
  .footerStyle(0);

  .ant-btn {
    .btnStyle();
    display: inline-block;
  }
}

.am-toast {
  .toastMixins();
}

// 導航陰影調整
.mo-baseContainer-mobile-navBar.shadow {
  box-shadow: 0px 6 * @hd 12 * @hd 0 rgba(0, 0, 0, 0.08);
  border-radius: 0;
}

// BaseContainer
.mo-baseContainer-mobile-statusBar,
.mo-baseContainer-mobile-navBar {
  background: var(--secondBgColor);
}

// antd-mobile search
.am-search .am-search-input {
  background: var(--thirdBgColor);
  border-color: var(--borderColor);

  input.am-search-value {
    color: var(--firstTextColor);
  }
}

// antd-mobile modal
.am-modal-mask {
  z-index: 99999;
}

.am-modal-wrap {
  z-index: 99999;
}

.system-maintenance-mobile {
  &.ant-modal {
    max-width: 80%;

    .ant-modal-content {
      border-radius: 20px;
    }

    .ant-modal-confirm-body .ant-modal-confirm-title {
      text-align: center;
    }

    .ant-modal-confirm-content {
      text-align: center;
    }

    .ant-modal-confirm-body-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}

@media (max-width: 768px) {
  #root {
    background-color: #eee;
  }

  .ant-message {
    top: 100px;
  }
}

body {
  .ant-form, .ant-tag-close-icon, .ant-tag {
    font-size: 16px;
  }
}
