import React, { useCallback, useMemo } from 'react';
import moment from 'moment';
import { TimePicker } from 'antd';

const { RangePicker } = TimePicker;

const TimeRange = (props) => {
  const { placeholder = ['开始时间', '结束时间'], format = 'HH:mm', onChange, value } = props;

  /**
   * 格式化value(与移动端保持一致)
   * @param {moment[]} v 值
   * @return {string[]}
   */
  const handleChange = useCallback(
    (v) => {
      onChange?.(v?.map(m => m?.format(format)));
    },
    [format, onChange],
  );

  /**
   * 转换value
   * @param {string[]} value
   * @return {moment[]}
   */
  const transformedValue = useMemo(() => value?.map(v => moment(v, format)), [format, value]);

  return (
    <RangePicker
      placeholder={placeholder}
      format={format}
      onChange={handleChange}
      value={transformedValue}
    />
  );
};

export default TimeRange;
