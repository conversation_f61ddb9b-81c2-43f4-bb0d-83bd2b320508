import React from 'react';
import { Page, Image } from '@gov-mo/mpaas-js-bridge';
import { isMobile, isIOS } from '@/utils';
import styles from './RichText.less';

const RichText = ({ className = '', richText, ...otherProps }) => {
  const handleClick = (e) => {
    let hasEmitted = true;
    if (e.target.nodeName === 'A') {
      const url = e.target.href;
      if (url) {
        if (isMobile()) {
          e.preventDefault();
          // 安卓原生使用Open.Page打开下载链接，会出现白屏/提示网络错误的问题。
          // 非IOS
          if (!isIOS()) {
            // 链接为pdf下载地址
            if (url.toLocaleLowerCase().endsWith('pdf')) {
              window.location.href = url;
              return;
            }
          }
          Page.openPage(url);
          return;
        }
        window.open(url);
        e.preventDefault();
      }
    } else if (e.target.parentElement.nodeName === 'A') {
      e.preventDefault();
    } else if (e.target.nodeName === 'IMG') {
      Image.previewPicture({ url: e.target.src });
    } else {
      hasEmitted = false;
    }

    if (hasEmitted) {
      e.stopPropagation();
    }
  };

  return (
    <div
      className={`${isMobile() ? styles.richText : styles['web-richText']} ${className}`}
      onClick={handleClick}
      // eslint-disable-next-line react/no-danger
      dangerouslySetInnerHTML={{ __html: richText }}
      {...otherProps}
    />
  );
};

export default RichText;
