import { transform2HantFormat, updatelocale } from '@/locales/lang';
import { setAcceptLanguage } from '@/utils/fetch';
import { setMomentLocale } from './setMomentLocale';

/**
 * 用于项目中更新语言环境
 * FAQ:为什么不写在lang.ts?
 * 解决循环引用的问题
 */
export const updateLocale = (locale: string) => {
  if (!locale) {
    return;
  }
  if (locale === 'zh-Hant' || locale === 'zh-Mo') {
    document.body.classList.add('hantfont');
  } else {
    document.body.classList.remove('hantfont');
  }
  const transformedLocale = transform2HantFormat(locale);
  setMomentLocale(transformedLocale);
  setAcceptLanguage(transformedLocale);
  updatelocale(transformedLocale);
};
