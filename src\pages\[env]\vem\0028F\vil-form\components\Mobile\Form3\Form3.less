@prefix: form3-28f-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 0.2rem 0.12rem;
      font-size: 0.16rem;

      .ant-form {
          font-size: 0.16rem ;
        .ant-radio,
        .ant-radio-wrapper {
          white-space: unset;
        }
      }
      .ant-form-item{
        font-size: 0.16rem
      }
      .ant-form label {
        font-size: 0.16rem ;

      }
      .ant-form-item-label > label {
        font-size: 0.16rem;
        flex-direction: row-reverse;
        display: flex;
        justify-content: flex-end;
      }
      .ant-select {
        font-size: 0.16rem;
      }
      .ant-input {
        font-size: 0.16rem;
      }
    }
    &dspaFpNoItem{
    }
    &dspaFpNoTip{
      font-size: 0.16rem;
      color: var(--adm-color-primary);
    }
    &owner-card-title{
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      background: #E3F0EC;
      border-radius: 12px;
    }
    &owner-card-form{
      background-color: #fff;
      padding: 20px;
    }
  }
}
