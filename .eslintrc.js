module.exports = {
  extends: '@ali-whale/eslint-config',
  globals: {
    document: true,
    API_HOST: true,
    APP_ID: true,
    BUCKET_ID: true,
    REDIRECT_URI: true,
    ENV: true,
    AUTH_SERVER: true,
    CLIENT_ID: true,
    DEV_REDIRECT_URL: true,
  },
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
    },
  },
  "rules": {
    // ...
    "max-len": ["error", { "code": 300 }]
    // ...
}
}
