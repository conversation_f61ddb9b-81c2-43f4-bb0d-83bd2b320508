@prefix: success;

.@{prefix} {
  &- {
    &root {
      position: relative;
      padding-top: 76px;
      padding-bottom: 45px;
      min-height: 100%;
      background-color: #f2f3f5;
    }
    &body {
      margin: 0 auto;
      min-height: 450px;
      max-width: 1048px;
      border-radius: 8px;
      background-color: rgb(217, 235, 231);
      .ant-col {
        display: flex;
        gap: 20px;
        padding-bottom: 20px;
      }
      .label {
        min-width: 100px;
        font-size: 16px;
        font-weight: 500;
        color: @brand-primary;
        line-height: 22px;
      }
      .value {
        font-size: 16px;
        font-weight: 400;
        color: #323232;
        line-height: 22px;
        min-height: 22px;
        height: auto;
      }
    }
    &sectionTitle {
      display: flex;
      padding: 20px;
      text-align: left;
      background-color: rgb(217, 235, 231);
      color: #000;
      font-size: 26px;
      font-weight: bold;
      display: flex;
      gap: 10px;
      align-items: center;
      border-bottom: 1px solid #c4c4c4;
    }
    &sectionBody {
      margin: auto;
      font-size: 16px;
      line-height: 22px;
      padding: 24px;
    }
    &footer {
      display: flex;
      justify-content: center;
      gap: 24px;
      margin-top: 20px;

      .ant-btn-default {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;

        min-width: 128px;
        height: 48px;
        line-height: 48px;
        border-radius: 24px;
        margin-left: 12px;
        font-size: 16px;
        padding: 0 5px;
      }
    }
  }
}
