import React, { useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Form6.less';

import { useIntl } from 'umi';
import { Button, Col, Empty, Form, Input, Modal, Radio, Row, Select, Space } from 'antd';
import { groupBy, keys } from 'lodash';
import { CarFuelSupplyType, CarLevel, useGetEmu } from '../../hooks';
import { useFormAction } from '../../hooks/useFormAction';
import { VtaRadioGroup } from '@/components/VtaRadioGroup/VtaRadioGroup';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';
import { getVTAHomoBatteryByTxnId, saveVTAHomoBattery } from '@/services/vta';

const px = pc('vta-form-form-6');

export interface FormSixProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId?: string;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

const FormSix = (props: FormSixProps) => {
  const { className = '', txnId, ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  const handleValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {};
  let { emuObj } = useGetEmu({
    batterySwapMode: 22016,
    acPlugIn1: 22017,
    acPlugIn2: 22018,
    dcPlugIn1: 22023,
    dcPlugIn2: 22024,
  });
  let { vehType, vtaCategory, powerSource, canEditable } = useFormAction(
    txnId,
    form,
    getVTAHomoBatteryByTxnId,
    saveVTAHomoBattery,
    props,
  );
  let acPlugIn = useMemo(() => {
    return [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType)
      ? emuObj.acPlugIn2
      : emuObj.acPlugIn1;
  }, [vehType, emuObj]);
  let dcPlugIn = useMemo(() => {
    return [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType)
      ? emuObj.dcPlugIn2
      : emuObj.dcPlugIn1;
  }, [vehType, emuObj]);
  const carFormItemList = useMemo(
    () => [
      {
        type: 'checkbox',
        category: '電池',
        label: '換電模式',
        name: 'batterySwapMode',
        span: 24,
        required: true,
        hide: ![CarFuelSupplyType.電力].includes(powerSource),
        component: (
          <VtaRadioGroup
            disabled={canEditable['batterySwapMode']}
            options={emuObj.batterySwapMode}
          />
        ),
      },
      {
        type: 'checkbox',
        category: '電池',
        label: '電池型號',
        name: 'batteryModel',
        span: 24,
        required: [CarFuelSupplyType.電力, CarFuelSupplyType.增程式].includes(powerSource)
          ? true
          : false,
        hide: ![
          CarFuelSupplyType.電力,
          CarFuelSupplyType.增程式,
          CarFuelSupplyType.油電混合,
        ].includes(powerSource),
        component: (
          <Input
            disabled={canEditable['batteryModel']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'checkbox',
        category: '電池',
        label: '電池容量',
        name: 'batteryCapacity',
        span: 24,
        required: [CarFuelSupplyType.電力, CarFuelSupplyType.增程式].includes(powerSource)
          ? true
          : false,
        hide: ![
          CarFuelSupplyType.電力,
          CarFuelSupplyType.增程式,
          CarFuelSupplyType.油電混合,
        ].includes(powerSource),
        component: (
          <Input
            disabled={canEditable['batteryModel']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'select',
        category: '充電接口規格',
        label: '交流',
        name: 'acPlugIn',
        required: true,
        hide: [CarFuelSupplyType.燃油氣].includes(powerSource),
        component: (
          <VtaSelect
            disabled={canEditable['acPlugIn']}
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={acPlugIn}
          />
        ),
      },
      {
        type: 'select',
        category: '充電接口規格',
        label: '直流',
        name: 'dcPlugIn',
        required: true,
        hide: [CarFuelSupplyType.燃油氣].includes(powerSource)||[CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            disabled={canEditable['dcPlugIn']}
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={dcPlugIn}
          />
        ),
      },
    ],
    [vehType, vtaCategory, powerSource, emuObj],
  );

  const [show, setShow] = useState(false);

  const [isModalOpen, setModalOpen] = useState(false);

  function handleOk() {
    setModalOpen(false);
    setShow(true);
  }

  function handleCancel() {
    setModalOpen(false);
  }

  return (
    <div className={`${px('root')} ${className} multi-card-container`} {...otherProps}>
      <Form
        form={form}
        className={px('form')}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>電池資料</span>
            <div className="multi-card-title-action">
              <Space>
                <Button onClick={() => setModalOpen(true)}>新增電池資料</Button>
              </Space>
            </div>
          </div>
          {show ? (
            <>
              <div className="multi-card-item-sub">
                <div className="multi-card-title">
                  <span>電池資料1</span>
                  <div className="multi-card-title-action">
                    <Space>
                      <Button>更新</Button>
                      <Button danger>{intl.formatMessage({ id: 'delete' })}</Button>
                    </Space>
                  </div>
                </div>
                <div className="multi-card-body">
                  <Form form={form} className={px('form')} layout="inline">
                    <Form.Item label="型號" style={{ width: '25%' }}>
                      <div>ABCD</div>
                    </Form.Item>
                    <Form.Item label="能量(kWh)" style={{ width: '25%' }}>
                      <div>80</div>
                    </Form.Item>
                    <Form.Item label="材料" style={{ width: '25%' }}>
                      <div>PUA</div>
                    </Form.Item>
                  </Form>
                </div>
              </div>
              <div className="multi-card-item-sub">
                <div className="multi-card-title">
                  <span>電池資料2</span>
                  <div className="multi-card-title-action">
                    <Space>
                      <Button>更新</Button>
                      <Button danger>{intl.formatMessage({ id: 'delete' })}</Button>
                    </Space>
                  </div>
                </div>
                <div className="multi-card-body">
                  <Form form={form} className={px('form')} layout="inline">
                    <Form.Item label="型號" style={{ width: '25%' }}>
                      <div>ABCD</div>
                    </Form.Item>
                    <Form.Item label="能量(kWh)" style={{ width: '25%' }}>
                      <div>80</div>
                    </Form.Item>
                    <Form.Item label="材料" style={{ width: '25%' }}>
                      <div>PUA</div>
                    </Form.Item>
                  </Form>
                </div>
              </div>
            </>
          ) : (
            <Empty
              description={intl.formatMessage({ id: 'no_data' })}
              style={{ margin: '20px 0' }}
            />
          )}
        </div>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>電池及充電接口</span>
          </div>
          {keys(groupBy(carFormItemList, 'category')).map((item, index) => (
            <div className="multi-card-item-sub" key={index}>
              <div className="multi-card-title">
                <span>{item}</span>
              </div>
              <div className="multi-card-body">
                <Row gutter={16}>
                  {groupBy(carFormItemList, 'category')[item].map((item, index) => (
                    <Col span={12} key={index}>
                      <Form.Item label={item.label} name={item.name} required={item.required}>
                        {item.component}
                      </Form.Item>
                    </Col>
                  ))}
                </Row>
              </div>
            </div>
          ))}
        </div>
      </Form>
      <Modal
        title="錄入電池資料"
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="確 認"
        width={600}
        cancelButtonProps={{ type: 'default' }}
      >
        <Form layout="vertical">
          <Form.Item label="型號" required>
            <Input value="" />
          </Form.Item>
          <Form.Item label="能量 (kWh)" required>
            <Input value="" />
          </Form.Item>
          <Form.Item label="材料" required>
            <Input value="" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FormSix;
