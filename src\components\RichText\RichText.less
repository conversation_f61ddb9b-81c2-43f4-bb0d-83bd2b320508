.text(@scale:1px) {
  :global {
    ul {
      list-style: disc inside;
      padding-left: 20 * @scale;
    }
    ol {
      list-style: decimal inside;
      padding-left: 20 * @scale;
    }
    a {
      text-decoration: underline;
    }
    h1 {
      font-size: 28 * @scale !important;
    }
    h2 {
      font-size: 24 * @scale !important;
    }
    h3 {
      font-size: 21 * @scale !important;
    }
    h4 {
      font-size: 20 * @scale !important;
    }
    h5 {
      font-size: 19 * @scale !important;
    }
    h6 {
      font-size: 18 * @scale !important;
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      text-decoration: none;
      font-weight: normal;
      margin: 18 * @scale 0;
      color: var(--firstTextColor);
    }
    s {
      text-decoration: line-through;
    }
    i,
    em {
      font-style: italic;
    }
    u {
      text-decoration: underline;
    }
    table {
      width: 100% !important;
    }
  }
}

.richText {
  .text(@hd);
  background: var(--secondBgColor);
  color: var(--secondTextColor);
}

.web-richText {
  .text();
}
