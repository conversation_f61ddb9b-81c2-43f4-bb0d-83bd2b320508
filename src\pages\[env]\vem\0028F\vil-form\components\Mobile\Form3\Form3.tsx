import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';
import pc from 'prefix-classnames';
import './Form3.less';

import { <PERSON>ert, Button, Col, Form, Input, message, Radio, Row, Select, Space } from 'antd';

import { getSysCodesByCodeType } from '@/services/publicApi';
import { getTxnVehicle, getVTATyreList, saveTxnVehicle, validateDspaFpNo } from '@/services/0028F';
import { useIntl } from 'umi';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import SpinContext from '../../../../../../../../components/SpinContext';

const px = pc('form3-28f-mobile');

export interface FormThreeProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;

  entranceInfo: getRivmByDSFM4Res | undefined;
  txnId: string | undefined;
}

export type FormThreeVehicleRef = {
  tempStore: () => Promise<void>;
};

const FormThree = forwardRef(
  (props: FormThreeProps, ref: React.ForwardedRef<FormThreeVehicleRef>) => {
    useImperativeHandle(ref, () => ({
      // 暫存
      async tempStore() {
        try {
          await form.validateFields();
          await submit(false);
        } catch {}
      },
    }));

    const intl = useIntl();
    const { className = '', setDisableNextStep, ...otherProps } = props;
    const { contentNextStep, contentForNextStep, txnId } = otherProps;

    const [tipMsg, setTipMsg] = useState('');

    const [form] = Form.useForm();
    const [dspaForm] = Form.useForm();

    // 下拉选项
    const [vehUsageOpts, setVehUsageOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
    const [tyreOpts, setTyreOpts] = useState<vehTyreOpt[]>([]);
    const [colorOpts, setColorOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
    const [carUseWayList, setCarUseWayList] = useState<sysCodesByCodeTypeOpt[]>([]);
    const [currentCarUseWay, setCurrentCarUseWay] = useState<string>('');
    const [dspaOptions, setDspaOptions] = useState<string>('');
    const [showDspa, setShowDspa] = useState<boolean>(false);
    const [searchSPLoading, setSearchSPLoading] = useState(false);
    const [dspaData, setDspaData] = useState<Partial<DsatDspaCancelVehAppVwDTO>>({
      appVtaNo: undefined,
      appVehBrandCode: '',
      appVehModel: '',
      applyForWaiveLastDate: '',
      oldPlateNo: '',
      vehTypeDescCn: '',
      vehTypeDescPt: '',
      vehTypeDescEn: '',
    });

    const { setLoading } = useContext(SpinContext);
    useEffect(() => {
      if (txnId) {
        getTxnVehicle(txnId)
          .then((ret) => {
            if (ret.code === '0') {
              const { data, dataArr } = ret;
              setCarUseWayList((dataArr as any[]) || []);
              setCurrentCarUseWay(data.plateAgree);
              setShowDspa(data.showDspa);
              setDspaOptions(data.dspaOptions);
              if (data?.dspaData?.appVtaNo) {
                setDspaData(data.dspaData);
              }

              form.setFieldsValue({
                vehUsageCode: data.vehUsageCode,
                tyreId: data.tyreId,
                colorCode: data.colorCode,
                plateAgree: data.plateAgree,
                relatedPlateNo: data.relatedPlateNo,
                dspaOptions: data.dspaOptions,
                dspaFpNo: data.dspaFpNo,
              });

              getVTATyreList(data.vtaNo, data.vtaYear)
                .then((ret) => {
                  if (ret.code === '0') {
                    setTyreOpts(ret.dataArr);
                  }
                })
                .catch((e) => setTipMsg(e.message));
            }
          })
          .catch((e) => setTipMsg(e.message))
          .finally(() => {
            setLoading(false);
          });
      } else {
        setLoading(false);
      }
    }, [txnId]);

    useEffect(() => {
      getSysCodesByCodeType('20001', 'A')
        .then((ret) => {
          if (ret.code === '0') {
            setVehUsageOpts(ret.dataArr);
          }
        })
        .catch((e) => setTipMsg(e.message));
      getSysCodesByCodeType('20002', 'A')
        .then((ret) => {
          if (ret.code === '0') {
            setColorOpts(ret.dataArr);
          }
        })
        .catch((e) => setTipMsg(e.message));
    }, []);

    useEffect(() => {
      setLoading(true);
      contentForNextStep(1);
      return () => {
        contentForNextStep(0);
      };
    }, []);

    useEffect(() => {
      setDisableNextStep(false);
      if (contentNextStep > 0) {
        submit();
      }
    }, [contentNextStep]);

    const submit = async (next: boolean = true) => {
      setTipMsg('');
      const values = await form.validateFields();
      const ret = await saveTxnVehicle({
        txnId: txnId ?? '',
        vehUsageCode: values.vehUsageCode,
        tyreId: values.tyreId,
        colorCode: values.colorCode,
        plateAgree: values.plateAgree,
        relatedPlateNo: (values.plateAgree === 'A' && values.relatedPlateNo) || undefined,
        dspaOptions: values.dspaOptions,
        dspaFpNo: (values.dspaOptions === 'Y' && values.dspaFpNo) || undefined,
      }).catch((e) => setTipMsg(e.message));

      if (!ret) {
        return;
      }

      if (ret.code !== '0') {
        setTipMsg(getResponseMessage(ret));
        return;
      } else {
        !next && message.success(intl.formatMessage({ id: 'cp_save_success' }));
      }

      next && contentForNextStep(2);
    };

    // 收件登記編號 查询
    const handleSearchSP = async () => {
      if (!txnId) return;

      const values = await form.validateFields(['dspaFpNo']);
      const { dspaFpNo } = values;
      try {
        // 请求接口
        setSearchSPLoading(true);
        const { data, code } = await validateDspaFpNo({ dspaFpNo, txnId });
        if (code !== '0') {
          return;
        }
        setDspaData({
          ...(data.dspaData || {}),
        });
      } catch (e: any) {
        setTipMsg(e.message);
      } finally {
        setSearchSPLoading(false);
      }
    };

    return (
      <>
        {tipMsg && (
          <div style={{ padding: '0.2rem 0.24rem 0' }}>
            <Alert message={tipMsg} type="error" showIcon />
          </div>
        )}
        <div className={`${px('root')} ${className}`} {...otherProps}>
          <Form
            form={form}
            className={px('form')}
            layout="vertical"
            onValuesChange={() => {
              setDisableNextStep(false);
            }}
          >
            <div className="my-card-container-mobile">
              <div className="my-card-item-mobile">
                <div className="my-card-title-mobile">
                  {intl.formatMessage({ id: 'vehicle_information' })}
                </div>
                <div className="my-card-body-mobile">
                  <Form.Item
                    name="vehUsageCode"
                    label={intl.formatMessage({ id: 'vehicle_usage' })}
                    rules={[
                      { required: true, message: intl.formatMessage({ id: 'please_select' }) },
                    ]}
                  >
                    <Select>
                      {vehUsageOpts.map((item, index) => (
                        <Select.Option value={item.codeKey}>
                          {getLangGroup(item.codeCname, item.codePname, item.codeEname)}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="tyreId"
                    label={intl.formatMessage({ id: 'tyre_f_r_s' })}
                    rules={[
                      { required: true, message: intl.formatMessage({ id: 'please_select' }) },
                    ]}
                  >
                    <Select>
                      {tyreOpts.map((item, index) => (
                        <Select.Option value={item.tyreId}>{item.tyreDesc}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="colorCode"
                    label={intl.formatMessage({ id: 'color' })}
                    rules={[
                      { required: true, message: intl.formatMessage({ id: 'please_select' }) },
                    ]}
                  >
                    <Select
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={colorOpts?.map((item) => {
                        return {
                          label: getLangGroup(item.codeCname, item.codePname, item.codeEname),
                          value: item.codeKey,
                        };
                      })}
                    />
                  </Form.Item>
                </div>
              </div>

              <div className="my-card-item-mobile">
                <div className="my-card-title-mobile">
                  {intl.formatMessage({ id: 'usage_license_plates' })}
                </div>
                <div className="my-card-body-mobile">
                  <Form.Item
                    name="plateAgree"
                    rules={[
                      { required: true, message: intl.formatMessage({ id: 'please_select' }) },
                    ]}
                  >
                    <Radio.Group onChange={(e) => setCurrentCarUseWay(e.target.value)}>
                      <Space direction="vertical">
                        {carUseWayList.map((item, index) => (
                          <>
                            <Radio value={item?.codeKey}>
                              {getLangGroup(item?.codeCname, item?.codePname, item?.codeEname)}
                            </Radio>
                            {currentCarUseWay === 'A' && index == 0 && (
                              <Form.Item
                                name="relatedPlateNo"
                                rules={[
                                  {
                                    required: true,
                                    message: intl.formatMessage({ id: 'please_enter' }),
                                  },
                                ]}
                              >
                                <Input />
                              </Form.Item>
                            )}
                          </>
                        ))}
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </div>
              </div>

              {showDspa && (
                <div className="my-card-item-mobile">
                  <div className="my-card-title-mobile" style={{ height: 'auto' }}>
                    {intl.formatMessage({ id: 'old_motorcycle_replace_subsidy' })}
                  </div>
                  <div className="my-card-body-mobile">
                    <Form.Item
                      name="dspaOptions"
                      rules={[
                        { required: true, message: intl.formatMessage({ id: 'please_select' }) },
                      ]}
                    >
                      <Radio.Group
                        style={{ width: '100%' }}
                        onChange={(e) => setDspaOptions(e.target.value)}
                      >
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Radio
                            value="Y"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            {intl.formatMessage({ id: 'yes' })}
                          </Radio>
                          {dspaOptions === 'Y' && (
                            <Row gutter={12}>
                              <Col span={18}>
                                <Form.Item
                                  name="dspaFpNo"
                                  label={intl.formatMessage({ id: 'fp_no' })}
                                  className={px('dspaFpNoItem')}
                                  rules={[
                                    {
                                      required: true,
                                      message: intl.formatMessage({ id: 'please_enter' }),
                                    },
                                  ]}
                                >
                                  <Input defaultValue={'FP'} />
                                </Form.Item>
                              </Col>
                              <Col
                                span={6}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  paddingTop: '0.15rem',
                                }}
                              >
                                <Button
                                  type="primary"
                                  onClick={handleSearchSP}
                                  loading={searchSPLoading}
                                >
                                  {intl.formatMessage({ id: 'search' })}
                                </Button>
                              </Col>
                            </Row>
                          )}

                          {dspaOptions === 'Y' && !dspaData.appVtaNo && (
                            <label className={px('dspaFpNoTip')}>
                              {intl.formatMessage({ id: 'fp_no_tip' })}
                            </label>
                          )}
                          {dspaOptions === 'Y' && dspaData.appVtaNo && (
                            <label className={px('owner-card-item-28f')}>
                              <div className={px('owner-card-title')}>
                                {intl.formatMessage({ id: 'application_materials' })}
                              </div>
                              <Form
                                colon={false}
                                labelAlign="left"
                                form={dspaForm}
                                className={px('owner-card-form')}
                                layout="vertical"
                              >
                                <Form.Item
                                  label={intl.formatMessage({ id: 'model_approval_number' })}
                                >
                                  <div>{dspaData.appVtaNo}</div>
                                </Form.Item>
                                <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                                  <div>{dspaData.appVehBrandCode}</div>
                                </Form.Item>
                                <Form.Item label={intl.formatMessage({ id: 'style' })}>
                                  <div>{dspaData.appVehModel}</div>
                                </Form.Item>
                                <Form.Item
                                  label={intl.formatMessage({ id: 'application_deadline' })}
                                >
                                  <div>{dspaData.applyForWaiveLastDate}</div>
                                </Form.Item>
                                <Form.Item label={intl.formatMessage({ id: 'old_plate_no' })}>
                                  <div>{dspaData.oldPlateNo}</div>
                                </Form.Item>
                                <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                                  <div>
                                    {getLangGroup(
                                      dspaData.vehTypeDescCn,
                                      dspaData.vehTypeDescPt,
                                      dspaData.vehTypeDescEn,
                                    )}
                                  </div>
                                </Form.Item>
                              </Form>
                            </label>
                          )}
                          <Radio
                            value="N"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            {intl.formatMessage({ id: 'no' })}
                          </Radio>
                        </Space>
                      </Radio.Group>
                    </Form.Item>
                  </div>
                </div>
              )}
            </div>
          </Form>
        </div>
      </>
    );
  },
);

export default FormThree;
