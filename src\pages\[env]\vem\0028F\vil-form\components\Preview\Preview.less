@prefix: preview-28f;

.@{prefix} {
  &- {
    &root {
      margin-left: 24px;
      // .my-card-item:nth-child(1) {
      //   background-color: #fff;
      //   .my-card-title {
      //     border-radius: 12px;
      //     background-color: @brand-primary;
      //   }
      // }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }
  }
}

.owner-card-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: #fff;
  .ant-form-item {
    width: 48%;
    font-size: 16px;
    .ant-form-item-label {
      width: 50%;
      // text-align: left;
      label {
        font-size: 16px;
        color: #6c6c6c;
      }
    }

    .statusBg1 {
      padding: 0 2px;
      color: #f33b40;
    }
    .statusBg2 {
      padding: 0 2px;
      color: @brand-primary;
    }
  }
}
.owner-card-container-28f {
  background-color: #fff;
  padding-top: 20px;
  .owner-card-2
    {
      background:#fdf8f0;
    }
    .owner-card-3
    {
      background: #E5F1EF;
    }
  .owner-card-item-28f {
    margin-bottom: 20px;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    overflow: hidden;

    

    // &:nth-child(4n + 1) {
    //   background: #fdf8f0;
    // }
    // &:nth-child(4n + 2) {
    //   background: #dfeaf5;
    // }
    // &:nth-child(4n + 3) {
    //   background: #faedd9;
    // }
    // &:nth-child(4n + 4) {
    //   background: #f7e4da;
    // }

    .owner-card-title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .owner-card-form {
      background-color: #fff;
      padding: 20px;
    }
  }
}

.disAgree {
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 16px;
  color: #f33b40;
}
