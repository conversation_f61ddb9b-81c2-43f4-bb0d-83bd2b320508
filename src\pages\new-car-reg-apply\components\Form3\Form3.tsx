import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Form3.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Upload, message, Divider  } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

const px = pc('form3');

export interface Form3Props extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;
}

const Form3 = (props: Form3Props) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  useEffect(() => {
    setDisablePrevStep(false)
    setDisableNextStep(false)
    return () => {

    }
  }, [])

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <Form form={form} className={px('form')} layout="vertical">
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'submit_size_type_tips' })}</div>
        <div className={px('sectionBody')}>
          <Form.Item label="1. 表格第二號（002/DDAVC）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="2. 輕型重型摩托車規格資料（023/DV）（適用於摩托車）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="3. 輕型重型汽車規格資料（024/DV）（適用於汽車）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="4. 掛車或半掛車規格資料（025/DV）（適用於掛車或半掛車）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="5. 車輛所有人身份證明文件影印本">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="6. 進口准照正本（表格“E”聯或電子報關表格）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="7. 豁免繳付車輛使用牌照稅證明文件（適用於申請豁免繳付車輛使用牌照稅）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="8. 融資租賃合同影印本（適用於車輛以融資租賃方式取得）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default Form3;
