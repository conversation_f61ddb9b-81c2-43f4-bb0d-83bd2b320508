@prefix: owner-record;
@prefix-mobile: owner-record-mobile;

.@{prefix} {
  &- {
    &root {
      padding: 0 10% 88px;
      height: calc(100% - 68px);
      overflow-y: auto;

      .mophone-user .mo-phone {
        width: 100%;
      }
      .mophone-user .ant-form-item-label {
        display: none;
      }
      .ant-form {
      }
      .ant-form-item-label > label {
      }
      .ant-select {
      }
    }

    &title {
      font-size: 20px;
      font-weight: 700;
      padding: 20px 24px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    }

    &stepinfo {
      display: flex;
    }

    &form {
      flex: 1 1;
      margin-top: 20px;
    }

    &form h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 24px;
    }

    &form > .pc-pay-ui-root {
      margin-left: 24px;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 65px;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 16px;
      color: #666;
      top: -2px;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 24px;
      top: -2px;
    }

    &footer-button {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10%;
      background: #fff;
      box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.1);
    }

    &footer-button .ant-btn {
      min-width: 128px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
      margin-left: 12px;
      font-size: 16px;
      padding: 0 5px;
    }

    &footer-button .ant-btn.ant-btn-default {
      background: #fff;
      color: @brand-primary;
      border: 1px solid @brand-primary;
    }

    &sectionBody {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .ant-form-item {
        width: 30%;
      }

      .ant-btn.ant-btn-default {
        background: #fff;
        color: #00785d;
        border: 1px solid #00785d;
        &.pure {
          border-radius: 2px;
          color: rgba(0, 0, 0, 0.85);
          border: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }

      .card {
        box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.12);
        margin-bottom: 24px;
        border-radius: 8px;
        overflow: hidden;
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 58px;
          padding: 0 20px;
          color: #ffffff;
          .card-title {
            flex: 1;
            padding: 0 12px;
            font-size: 18px;
            font-weight: 600;
          }
          .status {
            background: #fff;
            border-radius: 15px;
            padding: 0 10px;
            line-height: 30px;
            font-size: 14px;
            text-align: center;
          }
          .status-paying {
            color: #e9b745;
          }
          .status-paied {
            color: #084ab8;
          }
          .status-passed {
            color: #13a07b;
          }
        }
        .card-body {
          min-height: 58px;
          padding: 18px 20px 0;
          display: flex;
          flex-wrap: wrap;
          .item {
            width: 50%;
            display: flex;
            margin-bottom: 16px;
            .label {
              width: 30%;
              font-size: 16px;
              color: #6c6c6c;
              overflow-wrap: anywhere;
            }
            .value {
              flex: 1 1;
              color: #232323;
              font-size: 16px;
              font-family: 'PingFang SC', 'Microsoft YaHei', 'Ming-MSCS-DSI';
              overflow-wrap: anywhere;
            }
          }
        }
        .card-footer {
          height: 62px;
          padding: 13px 0;
          display: flex;
          justify-content: space-between;
          margin: 0 24px;
          border-top: 1px solid #f0f0f0;
          .right {
            .ant-btn {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}

.@{prefix-mobile} {
  &- {
    &root {
      padding: 12px;
      height: calc(100% - 70px);
      overflow-y: auto;

      .mophone-user .mo-phone {
        width: 100%;
      }
      .mophone-user .ant-form-item-label {
        display: none;
      }
    }

    &title {
      font-size: 20px;
      font-weight: 700;
      padding: 20px 24px;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    }

    &stepinfo {
      display: flex;
    }

    &form {
      flex: 1 1;
      margin-top: 20px;
    }

    &form h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 24px;
    }

    &form > .pc-pay-ui-root {
      margin-left: 24px;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 65px;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 16px;
      color: #666;
      top: -2px;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 24px;
      top: -2px;
    }

    &footer-button {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10%;
      background: #fff;
      box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.1);
    }

    &footer-button .ant-btn {
      min-width: 128px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
      margin-left: 12px;
      font-size: 16px;
      padding: 0 5px;
    }

    &footer-button .ant-btn.ant-btn-default {
      background: #fff;
      color: @brand-primary;
      border: 1px solid @brand-primary;
    }

    &sectionBody {
      .ant-btn.ant-btn-default {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;
        &.pure {
          border-radius: 2px;
          color: rgba(0, 0, 0, 0.85);
          border: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }

      .card {
        box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.12);
        margin-bottom: 24px;
        border-radius: 8px;
        overflow: hidden;
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 58px;
          padding: 0 20px;
          color: #ffffff;
          .card-title {
            flex: 1;
            padding: 0 12px;
            font-size: 12px;
            font-weight: 600;
          }
          .status {
            background: #fff;
            border-radius: 15px;
            padding: 0 10px;
            line-height: 30px;
            font-size: 14px;
            text-align: center;
          }
          .status-paying {
            color: #e9b745;
          }
          .status-paied {
            color: #084ab8;
          }
          .status-passed {
            color: #13a07b;
          }
        }
        .card-body {
          min-height: 58px;
          padding: 18px 20px 0;
          .item {
            display: flex;
            margin-bottom: 16px;
            .label {
              width: 40%;
              font-size: 14px;
              color: #6c6c6c;
              overflow-wrap: anywhere;
            }
            .value {
              flex: 1 1;
              color: #232323;
              font-size: 16px;
              font-family: 'PingFang SC', 'Microsoft YaHei', 'Ming-MSCS-DSI';
              overflow-wrap: anywhere;
            }
          }
        }
        .card-footer {
          padding: 13px 0;
          display: flex;
          justify-content: space-between;
          margin: 0 24px;
          border-top: 1px solid #f0f0f0;
          .right {
            display: flex;
            flex-direction: column;
            flex: 1;
            .ant-btn {
              width: 100%;
              margin-bottom: 10px;
            }
          }
        }
      }
    }
  }
}
