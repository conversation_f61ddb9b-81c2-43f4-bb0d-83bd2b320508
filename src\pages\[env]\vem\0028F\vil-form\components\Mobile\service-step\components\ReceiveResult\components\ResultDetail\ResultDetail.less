@import '../../../../../../styles/listItemMixins.less';

.root {
  margin-bottom: 32px;
  padding: 16 * @hd;
  font-size: 16 * @hd;
  .address {
    margin-top: 24px;
  }
  .content {
    div {
      display: flex;
      span:first-child {
        .itemKeyMixins(@hd);
      }
      span:nth-child(2) {
        .itemValueMixins(@hd);
      }
      margin-bottom: 16 * @hd;
    }
  }
}

.webRoot {
  padding: 16px;
  padding-bottom: 0;
  font-size: 16px;
  .content {
    display: flex;
    flex-wrap: wrap;
    div {
      width: 50%;
      flex: none;
      display: flex;
      span:first-child {
        .itemKeyMixins();
      }
      span:first-child::after {
        // content: ':';
        // margin-left: 2 * @hd;
      }
      span:nth-child(2) {
        .itemValueMixins();
      }
      margin-bottom: 16px;
    }
  }
}
