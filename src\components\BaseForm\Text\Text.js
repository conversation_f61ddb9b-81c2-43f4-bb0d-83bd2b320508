import React, { useContext } from 'react';
import { InputItem, TextareaItem } from 'antd-mobile';
import { Input } from 'antd';
import classPrefix from '@ali-whale/class-prefix';
import FormContext from '../FormContext';
import './Text.less';

const px = classPrefix('cbd-mobile-base-form');

const { TextArea } = Input;

const Text = (props) => {
  const { textType = 'input', placeholder = '', description, ...otherProps } = props;
  const { mobile, isDetail } = useContext(FormContext);

  if (isDetail) {
    return <div>{props.value}</div>;
  }

  if (!mobile) {
    return (
      <div>
        {textType === 'input' ? (
          <Input placeholder={placeholder} {...otherProps} />
        ) : (
          <TextArea placeholder={placeholder} {...otherProps} />
        )}
        {description && <span style={px('text-describe')}>{description}</span>}
      </div>
    );
  }

  return (
    <div className={px('text')}>
      {textType === 'input' ? (
        <InputItem className={px('text-value')} clear placeholder={placeholder} {...otherProps} />
      ) : (
        <TextareaItem
          className={px('text-value')}
          clear
          placeholder={placeholder}
          {...otherProps}
        />
      )}
      {description && <span className={px('text-describe')}>{description}</span>}
    </div>
  );
};

export default Text;
