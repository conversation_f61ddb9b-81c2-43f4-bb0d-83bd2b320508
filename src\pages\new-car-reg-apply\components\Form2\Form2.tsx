import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Form2.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Collapse } from 'antd';

const px = pc('form2');

export interface Form2Props extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;
}

const Form2 = (props: Form2Props) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  useEffect(() => {
    setDisablePrevStep(false)
    setDisableNextStep(false)
    return () => {

    }
  }, [])

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

      <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'application_materials' })}</div>
      <div className={px('sectionBody')}>
        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]} >
          <Collapse.Panel showArrow={false} header={<>車主資料</>} key={1}>
            <div>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                <div>澳門居民身份證</div>
              </Form.Item>
              <Form.Item label="證件編號">
                <div>12345678</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'chinese_sur_name' })}>
                <div>陳大文</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'portuguese_sur_name' })}>
                <div>CHAN DA WEN</div>
              </Form.Item>
              <Form.Item label="聯絡地址">
                <div>澳門xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</div>
              </Form.Item>
              <Form.Item label="語言">
                <div>中文</div>
              </Form.Item>
            </Form>
            </div>

          </Collapse.Panel>
        </Collapse>

        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]} >
          <Collapse.Panel showArrow={false} header={<>{intl.formatMessage({ id: 'vehicle_information' })}</>} key={1}>
            <div>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'temporary_bidding_license_plates' })} style={{width: '100%'}}>
                <div>MA-12-34</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                <div>I/1/2024</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                <div>1/2024</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                <div>{intl.formatMessage({ id: 'light_vehicles' })}</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                <div>載客</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'dsf_ref_no' })}>
                <div>xxxxxxx</div>
              </Form.Item>
              <Form.Item label="IVM">
                <div>{intl.formatMessage({ id: 'yes' })}</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                <div>特斯特</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'style' })}>
                <div>Model X</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'style_year' })}>
                <div>2023</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                <div>私人</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                <div>VIN1234</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                <div>Engine1234</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                <div>2024</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'ma_tyre_f_r_s' })}>
                <div>235/45-18 - 235/45-18 -</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'color' })}>
                <div>白色</div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'size_weight' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'car_body' })}>
                <div>{intl.formatMessage({ id: 'length' })} 5600 mm</div>
              </Form.Item>

            </Form>
            </div>
          </Collapse.Panel>
        </Collapse>
      </div>

    </div>
  );
};

export default Form2;
