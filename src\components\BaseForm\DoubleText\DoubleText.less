.ant-form-item-has-error {
  .cbd-web-base-form-text .cbd-web-base-form-double-text-wrapper {
    .cbd-web-base-form-double-text-Symbol {
      border-color: var(--error) !important;
    }
  }
}

.cbd-mobile-base-form-text {
  // 多行文本框
  .am-list-item.am-textarea-item {
    padding: 12 * @hd !important;
  }
  .am-textarea-control {
    padding: 0;
  }
  .am-textarea-clear {
    margin-top: 0;
  }

  .cbd-mobile-base-form-text-describe {
    color: #bfbfbf;
    font-size: 12 * @hd;
  }
  .cbd-mobile-base-form-double-text-wrapper {
    display: flex;
    .am-list-line {
      border-bottom: none !important;
    }
    .cbd-mobile-base-form-text-value:first-child {
      width: 48%;
      border-right: 0 !important;
    }
    .cbd-mobile-base-form-text-value:last-child {
      width: 48%;
      border-left: 0 !important;
    }
    .cbd-mobile-base-form-double-text-Symbol {
      width: 4%;
      border-top: 1px solid #d9d9d9 !important;
      border-bottom: 1px solid #d9d9d9 !important;
      background: #fff;
      line-height: 36 * @hd;
      text-align: center;
      font-size: 21 * @hd;
      color: #f0f0f0;
    }
  }
}

.cbd-web-base-form-text {
  &-describe {
    color: #bfbfbf;
    font-size: 14 * @hd;
  }
  .cbd-web-base-form-double-text-wrapper {
    display: flex;
    .cbd-web-base-form-text-value:first-child {
      width: 48%;
      border-right: 0;
      border-radius: 2 * @hd 0 0 2 * @hd;
    }
    .cbd-web-base-form-text-value:last-child {
      width: 48%;
      border-left: 0;
      border-radius: 0 2 * @hd 2 * @hd 0;
    }
    .cbd-web-base-form-double-text-Symbol {
      width: 4%;
      border-top: 1px solid #f0f0f0 !important;
      border-bottom: 1px solid #f0f0f0 !important;
      background: #fff;
      text-align: center;
      color: #f0f0f0;
      transition: all 0.3s;
    }
  }
}
