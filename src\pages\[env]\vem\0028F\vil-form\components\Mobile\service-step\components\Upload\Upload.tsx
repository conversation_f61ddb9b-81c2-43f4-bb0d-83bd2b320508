import React from 'react';
import { MoUpload } from '@gov-mo/components';
import { Button } from 'antd';
import { useIntl, useSelector } from 'umi';
import type { PublicModelsState } from 'umi';
import { UploadOutlined } from '@ant-design/icons';
// import filePng from '@/assets/file.png';
import styles from './Upload.less';

type UploadFileStatus = 'error' | 'success' | 'done' | 'uploading' | 'removed';

interface UploadFileType {
  fileCode: string;
  name: string;
  ext: string;
  url?: string;
  thumbUrl?: string;
  uid?: string;
  status?: UploadFileStatus;
  size?: number;
  signature: string;
}

type UploadFileItemType = {
  fileCode: string;
  fileName: string;
  fileType: string;
  fileUrl?: string;
  fileThumbUrl?: string;
  fileUid?: string;
  fileStatus?: UploadFileStatus;
  fileSize?: number;
  signature: string;
  appId: string;
  bucketId: string;
};

type UploadFileListType = UploadFileItemType[];

export interface UploadProps {
  onChange?: (v: UploadFileListType, sn?: number) => void;
  value?: UploadFileListType;
  isFirstClick: boolean;
  setIsFirstClick: (v: boolean) => void;
  maxCount?: number;
  maxSize?: number;
  id?: string;
  mobile: boolean;
  isDetail?: boolean;
}

function Upload(props: UploadProps) {
  // const [visible, setVisible] = useState(false);
  const {
    onChange,
    value,
    isFirstClick,
    setIsFirstClick,
    maxCount = 5,
    maxSize = 10,
    id = 'upload001',
    isDetail = false,
  } = props;
  // 感觉组件不应该直接引models的，但是时间不够不管了
  const { isDark } = useSelector(
    ({ public: publicModels }: { public: PublicModelsState }) => publicModels,
  );

  const intl = useIntl();

  const handleFileChange = (file: UploadFileType, fileList: UploadFileType[]) => {
    if (file.status === 'done' && onChange) {
      window.console.log('upload組件onChange的file', fileList);
      window.console.log('upload組件onChange的fileList', fileList);
      onChange(
        fileList.map((cur) => ({
          fileCode: cur.fileCode,
          fileName: cur.name,
          fileType: cur.ext,
          fileUrl: cur.url,
          fileSize: cur.size,
          fileUid: cur.uid,
          fileStatus: cur.status,
          signature: cur.signature,
          appId: APP_ID,
          bucketId: BUCKET_ID,
        })),
      );
    }
  };
  const handleFileRemove = (file: UploadFileType) => {
    if (onChange) {
      onChange((value ?? []).filter((i: UploadFileItemType) => i.fileCode !== file.fileCode));
    }
  };

  const beforeOpenPanel = (next: () => void) => {
    if (isFirstClick && (value ?? []).length === 0) {
      setIsFirstClick(false);
    } else {
      next();
    }
  };

  return (
    <MoUpload
      id={id}
      className={`${isDetail ? styles.moUpload : ''}`}
      appId={APP_ID}
      bucketId={BUCKET_ID}
      version="v1.0"
      targetType="user"
      accept={['pdf', 'jpeg', 'jpg', 'png', 'tiff', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'zip']}
      sources={['take-photo', 'local-file', 'local-photo']}
      onChange={handleFileChange}
      onRemove={handleFileRemove}
      beforeOpenPanel={beforeOpenPanel}
      fileInvalidDay={0}
      onlyPreview={isDetail}
      albumType="picture"
      // defaultFileList={value.map((item: UploadFileItemType) => item.fileCode)}
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      defaultFileList={value?.map(
        (item: UploadFileItemType) => `${item.fileCode},${item.signature}`,
      )}
      maxCount={maxCount}
      maxSize={maxSize}
      theme={isDark ? 'dark' : 'light'}
      aspect={0}
      token={`Item ${localStorage.getItem(`${window.routerBase}_token`) ?? ''}`}
    >
      <Button type="primary" className={styles.webUploadBtn} icon={<UploadOutlined />}>
        {intl.formatMessage({ id: '文件上传' })}
      </Button>
    </MoUpload>
  );
}

export default Upload;
