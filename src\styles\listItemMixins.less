// 短文本行距
.itemLineHeight(@scale:1px, @fontSize:16) {
  font-size: @fontSize * @scale;
  line-height: (@fontSize+10) * @scale;
}

// 列表Key
.itemKeyMixins(@scale:1px, @fontSize:16) {
  flex: 0 0 28%;
  // 不设置width，设置flex-basis，当flex-basis设置值小于自身内容宽度时，flex-basis不生效
  min-width: 10px;
  color: var(--secondTextColor);
  overflow-wrap: break-word;
  margin-right: 8 * @scale;
  .itemLineHeight(@scale, @fontSize);
}

// 列表Value
.itemValueMixins(@scale:1px, @fontSize:16) {
  color: var(--firstTextColor);
  overflow-wrap: anywhere;
  .itemLineHeight(@scale, @fontSize);
}

// Toast兼容Mobile、PC样式。需在config将am-toast设置到blacklist中
.toastMixins() {
  & > span {
    width: 50%;
    max-width: 200px;
    .am-toast-notice-content .am-toast-text {
      font-size: 16px;
      padding: 16px 24px !important;
      word-break: break-word;
      .am-icon-loading,
      .am-icon-loading ~ .am-toast-text-info::after {
        width: 52px;
        height: 52px;
      }
      &.am-toast-text-icon .am-toast-text-info {
        margin-top: 32px;
      }
    }
  }
}
