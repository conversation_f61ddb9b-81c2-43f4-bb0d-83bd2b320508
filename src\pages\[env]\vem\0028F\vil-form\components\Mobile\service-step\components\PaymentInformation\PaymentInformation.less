.root {
  background: var(--secondBgColor);
  border-radius: @radius-md;
  // flex-grow: 1;
  // margin-bottom: 24px
  margin: 0 32px;
  // padding: 48px 32px;
  .nameArea {
    display: flex;
    align-items: center;
    .cardTitleMixins();
    .paymentName {
      color: var(--firstTextColor);
      font-size: 18 * @hd;
    }
  }
  .paymentArea {
    padding: 4 * @hd 16 * @hd;
    .itemArea {
      font-size: 32px;
      line-height: 84px;
      // color: #8c8c8c;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 8px;
      .itemName {
        color: var(--secondTextColor);
      }
      .itemValue {
        color: var(--firstTextColor);
        display: flex;
        align-items: center;
        .priceUnit {
          font-size: 32px;
        }
      }
    }
    .itemArea:last-child {
      // border-top: 1px solid #e1e1e4;
    }
  }
  .totalContainer {
    padding: 0 16 * @hd 24 * @hd;
  }
  .totalArea {
    border-top: 1px solid #d1d1d6;
    padding-top: 4 * @hd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32px;
    line-height: 84px;
    color: var(--secondTextColor);
    .total {
      // font-size: 48px;
      display: flex;
      align-items: center;
      color: #00785d;
      .totalUnit {
        // font-size: ;
        font-size: 56px;
        font-weight: 500;
      }
      .totalValue {
        font-size: 56px;
        font-weight: 500;
      }
    }
  }
}
