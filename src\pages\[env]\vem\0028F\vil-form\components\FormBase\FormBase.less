@prefix: page-28f;

.@{prefix} {
  &- {
    &root {
      padding: 0 10% 88px;
      height: calc(100% - 68px);
      overflow-y: auto;
    }

    .ant-form-item-label > label {
      font-size: 16px;
    }

    font-size: 16px;

    .ant-form {
      font-size: 16px;
    }

    .ant-form-item-label > label {
      font-size: 16px;
    }

    .ant-select {
      font-size: 16px;
    }

    .ant-input {
      font-size: 16px;
    }

    &title {
      font-size: 0.2rem;
      font-weight: 700;
      padding: 0.2rem 0.24rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      display: flex;
    }

    &stepinfo {
      display: flex;
    }

    &stepinfo-mobile {
      display: flex;
      flex-direction: column;
    }

    // &mobile-step {
    //   display: flex;
    //   justify-content: space-between;
    //   align-items: center;
    //   padding: 20px 25px;
    //   .step-item {
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     width: 33px;
    //     height: 33px;
    //     font-size: 14px;
    //     color: #ffffff;
    //     background-color: rgba(193, 193, 193, 0.25);
    //     border-radius: 50%;
    //     span {
    //       display: flex;
    //       justify-content: center;
    //       align-items: center;
    //       width: 25px;
    //       height: 25px;
    //       background-color: #c1c1c1;
    //       border-radius: 50%;
    //     }
    //     &.finish {
    //       background-color: rgba(8, 74, 184, 0.25);
    //       span {
    //         background-color: @brand-primary;
    //       }
    //     }
    //     &.progress {
    //       background-color: rgba(255, 193, 7, 0.25);
    //       span {
    //         background-color: #ffc107;
    //       }
    //     }
    //   }
    //   .line {
    //     flex: 1;
    //     margin: 0 16px;
    //     min-width: 0.1rem;
    //     max-width: 1rem;
    //     height: 2px;
    //     background-color: #c1c1c1;
    //   }
    // }

    &step {
      margin-top: 0.2rem;
      padding: 0 0.22rem;
      width: 2.4rem;
      min-width: -webkit-fit-content;
      min-width: -moz-fit-content;
      min-width: fit-content;
      border-right: 1px solid rgba(0, 0, 0, 0.15);
    }

    &step h3 {
      font-size: 0.16rem;
      line-height: 0.22rem;
      font-weight: 700;
      padding: 0 0.02rem 0.16rem;
    }

    &step .ant-steps-small {
      font-size: 0.16rem;
    }

    &step .ant-steps-small .ant-steps-item {
      height: 0.7rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon {
      background-color: rgba(8, 74, 184, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-icon {
      background-color: @brand-primary;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-tail:after {
      background-color: @brand-primary;
      height: 0.3rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-icon {
      background-color: rgba(255, 193, 7, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-icon {
      background-color: #ffc107;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 0.3rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-icon {
      background-color: hsla(0, 0%, 76.9%, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-icon {
      background-color: #c4c4c4;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 0.3rem;
    }

    &step
    .ant-steps-small
    .ant-steps-item.ant-steps-item-wait:last-child
    .ant-steps-item-tail:after {
      display: none;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container {
      height: 0.4rem;
      min-height: 0.4rem;
      display: flex;
      align-items: center;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-tail {
      bottom: 0;
      top: 0.4rem;
      left: 0.14rem;
      padding: 0;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon {
      width: 0.28rem;
      height: 0.28rem;
      border-radius: 50%;
      margin-right: 0.06rem;
      cursor: pointer;
      flex-shrink: 0;
    }

    &step
    .ant-steps-small
    .ant-steps-item
    .ant-steps-item-container
    .ant-steps-item-icon
    .ant-steps-icon {
      height: 0.18rem;
      min-height: 0.18rem;
      display: flex;
      align-items: center;
      width: 0.18rem;
      line-height: 0.18rem;
      left: 0.05rem;
      top: 0.05rem;
      font-size: 0.14rem;
      justify-content: center;
      color: #fff;
      border-radius: 50%;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content {
      height: 0.4rem;
      min-height: 0.4rem;
      display: flex;
      align-items: center;
      line-height: 0.16rem;
    }

    &step
    .ant-steps-small
    .ant-steps-item
    .ant-steps-item-container
    .ant-steps-item-content
    .ant-steps-item-title {
      font-size: 0.16rem;
      color: #333;
    }

    &form {
      flex: 1 1;
      margin-top: 0.2rem;
    }

    &form h3 {
      font-size: 0.16rem;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 0.24rem;
    }

    &form > .pc-pay-ui-root {
      margin-left: 0.24rem;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 0.65rem;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 0.16rem;
      color: #666;
      top: -0.02rem;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 0.24rem;
      top: -0.02rem;
    }

    &formTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      margin-left: 0.24rem;
      font-size: 0.18prem;
      color: #232323;
      padding-bottom: 0.1rem;
    }

    &formTitle:before {
      display: block;
      content: '';
      width: 0.04rem;
      height: 0.16rem;
      background: @brand-primary;
      margin-right: 0.08prem;
    }

    &spin {
      width: 100%;
      height: 100%;
      padding: 45%;
    }

    &footer-button {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 0.68rem;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10%;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
    }

    &footer-button .ant-btn {
      min-width: 1.28rem;
      height: 0.48rem;
      line-height: 0.48rem;
      border-radius: 0.24rem;
      margin-left: 0.12rem;
      font-size: 0.16rem;
      padding: 0 0.05rem;
    }

    &footer-button .ant-btn.ant-btn-default {
      background: #fff;
      color: @brand-primary;
      border: 1px solid @brand-primary;
    }
  }
}

@media (max-width: 1199px) {
  .@{prefix} {
    &- {
      &root {
        padding: 0;
      }

      // &step {
      //   display: none;
      // }

      //&footer-button {
      //  justify-content: center;
      //}
    }
  }
}

// web card style
.my-card-container {
  .my-card-item {
    border-radius: 12px;
    margin-bottom: 20px;

    &:nth-of-type(4n + 1) {
      background-color: @brand-primary;
    }

    &:nth-of-type(4n + 2) {
      background-color: #e9b745;
    }

    &:nth-of-type(4n + 3) {
      background-color: #13a07b;
    }

    &:nth-of-type(4n + 4) {
      background-color: #f33b40;
    }

    .my-card-title {
      height: 44px;
      padding: 0 20px;
      color: #fff;
      font-size: 16px;
      display: flex;
      align-items: center;
    }

    .my-card-body {
      background: #fff;
      border: 1px solid #f0f0f0;
      padding: 20px;
      border-radius: 0 0 12px 12px;
    }
  }
}
