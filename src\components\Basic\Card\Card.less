.commonCard {
  :global {
    .am-card {
      border: 0;
      border-radius: @radius-sm;
      padding: 0 24px;
      min-height: unset;
      .am-card-body::before {
        height: 0;
      }
      &:not(.am-card-full) {
        margin: 32px 24px 0 24px;
        margin: 24px 0;
        width: 100%;
        border-radius: @radius-md;
      }
    }
    .am-card-header {
      padding-left: 0;
      padding-right: 0;
      line-height: 45px;
      color: @font-color-title;
      font-size: @font-size-head-sm;
      .am-card-header-content {
        flex: 7;
        font-weight: bold;
      }
      .am-card-header-extra {
        flex: 3;
        font-size: @font-size-sm;
      }
    }
    .am-card-body {
      padding: 10px 0;
      font-size: @font-size-sm;
      color: @font-color-base;
    }
    .am-card-footer {
      position: relative;
      padding: 22px 0;
      color: @font-color-caption;
      line-height: 32px;
      font-size: @font-size-sm;
    }
  }
  &.hasHeaderLine {
    :global {
      .am-card .am-card-body {
        border-top: 1px solid @border-color-base;
      }
    }
  }
  &.hasFooterLine {
    :global {
      .am-card-footer {
        border-top: 1px solid @border-color-base;
      }
    }
  }
}