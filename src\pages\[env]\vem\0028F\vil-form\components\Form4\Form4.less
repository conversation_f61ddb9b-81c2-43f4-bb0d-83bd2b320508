@prefix: form4-28f;

.@{prefix} {
  &- {
    &root {
      margin-left: 24px;
      .ant-descriptions-item-label,
      .ant-descriptions-item-content {
        font-size: 16px;
      }
      .ant-descriptions-item-label:after{
        display: none;
      }

      .mophone-user .ant-form-item-label {
        display: none;
      }

      .mophone-user .ant-form-item {
        width: 100%;
      }
      .ant-form {
        font-size: 16px;
      }
      .ant-form-item-label > label {
        font-size: 16px;
      }
      .ant-select {
        font-size: 16px;
      }
      .ant-input {
        font-size: 16px;
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }

    &sectionBody {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .ant-form-item {
        width: 48%;
      }

      input[type='number']::-webkit-inner-spin-button,
      input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
    }
  }
}
