import React from 'react';
import { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import { history, useIntl } from "umi";

import FormBase from './components/FormBase';
import FooterButton from '@/components/FooterButton';
import GuideButton from '@/components/GuideButton';

import Success from './components/Success';

const classPrefix = 'new-car-reg-apply'
const px = pc(classPrefix);

const NewCarSignPage = () => {
  const intl = useIntl();
  
  const [step, setStep] = useState(1);
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));

  const [showPrevStep, setShowPrevStep] = useState(true);
  const [showNextStep, setShowNextStep] = useState(true);
  const [showClose, setShowClose] = useState(true);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);


  const prevStep = () => {
    setStep(step - 1);
  };

  const nextStep = () => {
    setStep(step + 1);
  };

  const handleClose = () => {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  useEffect(() => {
    setDisablePrevStep(step<=1)
  }, [step])

  if(step===5) {
    return <Success />
  }

  return (
    <>
    <FormBase step={step} classPrefix={classPrefix}
      setDisablePrevStep={(flag) => {setDisablePrevStep(flag)}}
      setDisableNextStep={(flag) => {setDisableNextStep(flag)}}
      nextStep={nextStep}
      setNextStepText={(text) => {setNextStepText(text)}}
    />
    <GuideButton itemId="" />
    <FooterButton
      className={px('footer-button')}
      step={step}
      stepType="progress"

      nextStepText={nextStepText}

      showPrevStep={showPrevStep}
      showNextStep={showNextStep}
      showClose={showClose}
      setShowPrevStep={() => {setShowPrevStep}}
      setShowNextStep={() => {setShowNextStep}}
      setShowClose={() => {setShowClose}}

      disablePrevStep={disablePrevStep}
      disableNextStep={disableNextStep}
      disableClose={disableClose}
      setDisablePrevStep={(flag) => {setDisablePrevStep(flag)}}
      setDisableNextStep={(flag) => {setDisableNextStep(flag)}}
      setDisableClose={() => {setDisableClose}}

      handlePrevStep={() => { prevStep() }}
      handleNextStep={() => { nextStep() }}
      handleClose={() => { handleClose() }}
    />
    </>
  );
};

export default NewCarSignPage;
