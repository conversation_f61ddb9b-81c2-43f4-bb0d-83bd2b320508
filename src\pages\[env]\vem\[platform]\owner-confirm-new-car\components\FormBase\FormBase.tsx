import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';

import { Steps } from 'antd';
import Preview from '../Preview';
import { useIntl } from 'umi';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  classPrefix: string;
  step: number;
  serviceTitle: string;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;
  setDisableConfirmApply: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (string) => void;

  setShowTempStore: (boolean) => void;
  setShowConfirmApply: (boolean) => void;
  detail: getEXTxnDataRes;
  txnId: string | undefined;
  currentOwner?: saveTxnOwnersParams;
  tipMsg?: string;
  ownerId?: string;
}

const FormBase = (props: FormBaseProps) => {
  const intl = useIntl();

  const { step, className = '', ...otherProps } = props;
  const {
    setDisableNextStep,
    setNextStepText,
    setShowConfirmApply,
    setDisableConfirmApply,
    detail,
    txnId,
  } = otherProps;

  const px = pc(otherProps.classPrefix);

  useEffect(() => {
    console.log('[FormBase] step', step);
  }, [step]);

  const [currentOwner, setCurrentOwner] = useState<saveTxnOwnersParams | undefined>();
  const [tipMsg, setTipMsg] = useState(props.tipMsg);
  const [serviceTitle, setServiceTitle] = useState(
    props.serviceTitle || intl.formatMessage({ id: 'vehicle_registration_application' }),
  );

  useEffect(() => {
    setCurrentOwner(props.currentOwner);
    setTipMsg(props.tipMsg);
    setServiceTitle(
      props.serviceTitle || intl.formatMessage({ id: 'vehicle_registration_application' }),
    );
  }, [props.currentOwner, props.tipMsg, props.serviceTitle]);

  // 生成右侧表单内容
  const renderRightContent = () => (
    <Preview
      setShowConfirmApply={setShowConfirmApply}
      setDisableNextStep={setDisableNextStep}
      setNextStepText={setNextStepText}
      detail={detail}
      txnId={txnId}
      currentOwner={currentOwner}
      tipMsg={tipMsg}
      setDisableConfirmApply={setDisableConfirmApply}
    />
  );

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <h2 className={px('title')}>{serviceTitle}</h2>
      <div className={px('stepinfo')}>
        <div className={px('step')}>
          <h3>{intl.formatMessage({ id: 'application_steps' })}</h3>
          <Steps
            direction="vertical"
            size="small"
            current={currentOwner?.verficationStatus === 'Y' ? 1 : 0}
          >
            <Steps.Step title={intl.formatMessage({ id: 'data_confirmation' })} icon={1} />
            <Steps.Step title={intl.formatMessage({ id: 'identity_recognition' })} icon={2} />
          </Steps>
        </div>
        <div className={px('form')}>{renderRightContent()}</div>
      </div>
    </div>
  );
};

export default FormBase;
