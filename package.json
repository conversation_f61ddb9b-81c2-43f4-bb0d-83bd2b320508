{"name": "dsat-ovsap-web", "version": "1.1.0-beta.2", "private": true, "dependencies": {"@ali-whale/class-prefix": "^0.8.42", "@ali-whale/open-box": "^0.8.42", "@ali-whale/portal": "^0.8.43", "@ant-design/icons": "^4.6.2", "@umijs/preset-react": "^1.8.6", "ahooks": "^3.8.4", "antd-mobile-v5": "npm:antd-mobile@5.0.0-beta.18", "axios": "^1.7.9", "core-js": "3.6.5", "crypto": "^1.0.1", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "postcss-plugin-px2rem": "^0.8.1", "qrcode": "^1.5.4", "query-string": "^7.0.1", "rc-tween-one": "^3.0.6", "react-redux": "^7.2.6", "umi": "^3.5.42"}, "scripts": {"start": "cross-env koi dev", "build": "cross-env NODE_ENV=production koi build && npm run pack", "start:per": "cross-env UMI_ENV=per koi dev", "build:per": "cross-env NODE_ENV=production UMI_ENV=per koi build && npm run pack:per", "build:pron-per": "cross-env NODE_ENV=production UMI_ENV=pron-per koi build && npm run pack:pron-per", "build:pron": "cross-env NODE_ENV=production UMI_ENV=pron koi build && npm run pack:pron", "test": "umi-test", "eject": "react-scripts eject", "publish": "koi publish", "eslint": "eslint --fix --ext .js --ext .jsx --ext .ts --ext .tsx ./src", "lint-staged": "lint-staged", "test:coverage": "umi-test --coverage", "analyze": "cross-env NODE_ENV=production ANALYZE=1 npm run build", "pack": "node scripts/pack.js", "pack:per": "node scripts/pack.js per", "pack:pron-per": "node scripts/pack.js per", "pack:pron": "node scripts/pack.js", "cdn": "node scripts/cdn.js", "curl": "node scripts/publish.js", "uat": "npm run build && npm run build:per && npm run cdn && npm run curl", "prod": "npm run build:pron && npm run build:pron-per && npm run cdn && npm run curl"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@ali-dayu/koi": "^1.0.3", "@ali-whale/eslint-config": "^1.1.1", "@gov-mo/framework": "^0.2.8", "@types/react-transition-group": "^4.4.0", "@umijs/test": "^3.2.27", "copy-webpack-plugin": "^12.0.2", "cross-env": "^5.2.1", "cross-spawn": "^7.0.3", "dayjs": "^1.11.13", "eslint": "^7.18.0", "husky": "^4.3.0", "lint-staged": "^10.5.0", "postcss-px-to-viewport": "^1.1.1", "shelljs": "^0.8.4", "tar": "^6.0.5", "typescript": "^4.1.3", "umi-plugin-convention-routes": "^0.2.1", "yorkie": "^2.0.0"}, "lint-staged": {"*.{js,jsx}": ["eslint --fix --quiet"], "*.ts?(x)": ["eslint --fix --quiet"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "volta": {"node": "16.20.2"}}