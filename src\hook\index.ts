import { saveBatchCode } from "@/services/publicApi";
import { isMobile } from "@/utils";
import { BASE_ORIGIN } from "@/utils/const";
import { File } from '@gov-mo/mpaas-js-bridge';
import { Toast } from "antd-mobile-v5";

import html2canvas from "html2canvas";

export const downQRCode = async (id='capture-area', batchId?: string) => {
  const target = document.getElementById(id);
  html2canvas(target as any).then((canvas) => {
    const imageURL = canvas.toDataURL('image/png');

    if (isMobile()) {
      const url = `${BASE_ORIGIN}/ovsap/api/ignore/getBatchCodePic?batchId=${batchId}`
      console.log('downQRCode url', url);
      saveBatchCode(imageURL, `${batchId}`).then(res => {
        startLoading();
        File.downloadFileByType({ downloadType: 'url', downloadParam: `${url}#.png` }).then(res => {
          console.log('downQRCode res', res);
          console.log('downQRCode res', !!res.success);
          stopLoading();
          if (!!res.success) {
            Toast.show({
              icon: 'success',
              content: `${res.data}`
            });
          }

        }).catch(err => {
          stopLoading();
        });

      }).catch(err => {
        stopLoading();
      });
      
      return
    }

    const link = document.createElement('a');
    link.href = imageURL;
    link.download = `QR Code.png`;
    link.click();
  }).catch(err => {
    console.log('html2canvas err', err);
  });
};

const startLoading = () => {
  if (isMobile()) {
    Toast.show({
      icon: 'loading',
      content: 'Loading...',
      duration: 0,
      maskClickable: false
    })
    return
  }
}

const stopLoading = () => {
  if (isMobile()) {
    Toast.clear();
    return
  }
}