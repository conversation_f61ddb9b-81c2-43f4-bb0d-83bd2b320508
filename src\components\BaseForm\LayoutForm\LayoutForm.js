import React, { useContext } from 'react';
import { Form } from 'antd';
import cn from 'classnames';
import classPrefix from '@ali-whale/class-prefix';
import FormContext from '../FormContext';
import './LayoutForm.less';

const LayoutForm = (props) => {
  const { className, children, ...otherProps } = props;
  const { mobile, isDetail } = useContext(FormContext);
  const px = mobile ? classPrefix('cbd-mobile-base-form') : classPrefix('cbd-web-base-form');
  const labelAlign = mobile ? 'right' : 'left';
  const formItemLayout = mobile ? { labelCol: { span: 24 }} : { labelCol: { span: 6 }};
  return (
    <Form
      {...formItemLayout}
      className={cn(px('root'), { [px('detail')]: isDetail }, className)}
      labelAlign={labelAlign}
      {...otherProps}
      colon={false}
    >
      {children}
    </Form>
  );
};

LayoutForm.useForm = Form.useForm;
LayoutForm.Item = Form.Item;
LayoutForm.List = Form.List;

export default LayoutForm;
