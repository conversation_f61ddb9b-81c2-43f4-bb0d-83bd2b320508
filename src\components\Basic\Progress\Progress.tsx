import React from 'react';
import { Steps } from 'antd-mobile';
import styles from './Progress.less';

const { Step } = Steps;

const customIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 42 42" className="am-icon am-icon-md">
    <g fillRule="evenodd" stroke="transparent" strokeWidth="4">
      <path d="M21 0C9.402 0 0 9.402 0 21c0 11.6 9.402 21 21 21s21-9.4 21-21C42 9.402 32.598 0 21 0z" />
    </g>
  </svg>
);

const Progress = (props) => {
  let { steps } = props;
  const { current } = props;
  steps =
    steps &&
    steps.map((s, i) => (
      <Step key={i} title={s.title} description={s.description} icon={customIcon()} />
    ));
  return (
    <div className={styles.step}>
      <Steps current={current} direction="horizontal">
        {steps}
      </Steps>
    </div>
  );
};

export default Progress;
