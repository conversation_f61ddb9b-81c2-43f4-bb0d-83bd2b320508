import { arc, end, line, lineH, lineV, move, path, relative } from './svg';

/* 以下是预设的几种 border */

/* 缺角矩形 */
export function borderStyleRect1(width: number, height: number, cornerSize = 30): string {
  return path([
    move(0, 0),
    relative([lineH(width - 1), lineV(height - 1), lineH(cornerSize - width)]),
    line(0, height - cornerSize),
    end(),
  ]);
}

/* 缺角矩形2 */
export function borderStyleRect2(width: number, height: number, cornerSize = 30): string {
  return path([
    move(5, cornerSize),
    line(5, 5),
    line(cornerSize, 5),
    move(0, 0),
    relative([
      lineH(width - cornerSize),
      line(cornerSize, cornerSize),
      lineV(height - cornerSize),
      lineH(cornerSize - width),
    ]),
    line(0, height - cornerSize),
    end(),
  ]);
}

/* 普通矩形 */
export function borderStyleRect(width: number, height: number): string {
  return path([
    move(0, 0),
    relative([lineH(width - 1), lineV(height - 1), lineH(1 - width)]),
    end(),
  ]);
}

/* 侧边增加圆弧效果 */
export function borderStyleArc(
  height: number,
  startStep: number,
  endStep: number,
  total: number,
  r: number,
) {
  const centerY = (height * total) / 2;
  const radius = Math.max(centerY, r);

  const startY = startStep * height;
  const endY = endStep * height;
  const startDeg = Math.asin((centerY - startY) / radius);
  const startX = radius - radius * Math.cos(startDeg);
  const endDeg = Math.asin((centerY - endY) / radius);
  const endX = radius - radius * Math.cos(endDeg);

  return { radius, startX, startY, endX, endY };
}

/* 左侧圆弧效果 */
export function borderStyleLeftArc(
  width: number,
  height: number,
  // 起始的层级
  startStep: number,
  // 结束的层级
  endStep: number,
  // 总层级
  total: number,
  // 圆弧半径
  r: number,
): string {
  const { startX, endX, radius } = borderStyleArc(height, startStep, endStep, total, r);
  return path([
    move(-startX, 0),
    relative([lineH(width + startX), lineV(height - 1), lineH(-width - endX)]),
    arc([radius, radius], 0, 0, 0, [-startX, 0]),
  ]);
}

/* 右侧圆弧效果 */
export function borderStyleRightArc(
  width: number,
  height: number,
  // 起始的层级
  startStep: number,
  // 结束的层级
  endStep: number,
  // 总层级
  total: number,
  // 圆弧半径
  r: number,
): string {
  const { startX, endX, radius } = borderStyleArc(height, startStep, endStep, total, r);
  return path([
    move(0, 0),
    relative([lineH(width + startX)]),
    arc([radius, radius], 0, 0, 0, [width + endX, height]),
    line(0, height),
    end(),
  ]);
}

/* 梯形效果 */
export function borderStyleLadder(width: number, height: number) {
  return path([
    move(-height * 0.7, 0),
    line(0, height),
    line(width, height),
    line(width + height * 0.7, 0),
  ]);
}

export function borderStyleTest(width: number, height: number): string {
  return path([
    move(0, 0),
    relative([lineH(width - 1)]),
    arc([200, 200], 0, 0, 0, [width * 0.7, height - 1]),
    line(0, height),
    end(),
  ]);
}

export function borderRadiusRect(width: number, height: number, radius: number) {
  return path([
    move(0, radius),
    arc([radius, radius], 0, 0, 1, [radius, 0]),
    line(width - radius, 0),
    arc([radius, radius], 0, 0, 1, [width, radius]),
    line(width, height - radius),
    arc([radius, radius], 0, 0, 1, [width - radius, height]),
    line(radius, height),
    arc([radius, radius], 0, 0, 1, [0, height - radius]),
    end(),
  ]);
}

export function bottomArcRect(width: number, height: number) {
  return path([
    move(0, 0),
    line(width, 0),
    line(width, height / 2),
    arc([width / 2, height / 2], 0, 0, 1, [0, height / 2]),
    end(),
  ]);
}

export function dashLine(width: number) {
  return path([move(0, 0), line(width, 0), end()]);
}

export function percentCircle(r: number, percent: number) {
  const x = Math.sin(Math.PI * 2 * percent) * r + r;
  const y = r - Math.cos(Math.PI * 2 * percent) * r;
  const pathArr = [move(r, 0)];
  if (percent <= 0.5) {
    pathArr.push(arc([r, r], 0, 0, 1, [x, y]));
  } else {
    pathArr.push(arc([r, r], 0, 0, 1, [r, r * 2]));
    pathArr.push(arc([r, r], 0, 0, 1, [x, y]));
  }
  return path(pathArr);
}

export function dividerLine(diagonal: number, lineWidth: number, lineHeight: number) {
  const halfDiagonal = diagonal / 2;
  const halfLineHeight = lineHeight / 2;

  return path([
    move(0, halfDiagonal),
    line(halfDiagonal, 0),
    line(diagonal - halfLineHeight, halfDiagonal - halfLineHeight),
    line(diagonal - halfLineHeight + lineWidth, halfDiagonal - halfLineHeight),
    line(halfDiagonal * 3 - lineHeight + lineWidth, 0),
    line(diagonal * 2 - lineHeight + lineWidth, halfDiagonal),
    line(halfDiagonal * 3 - lineHeight + lineWidth, diagonal),
    line(diagonal - halfLineHeight + lineWidth, halfDiagonal + halfLineHeight),
    line(diagonal - halfLineHeight, halfDiagonal + halfLineHeight),
    line(halfDiagonal, 2 * halfDiagonal),
    end(),
  ]);
}
