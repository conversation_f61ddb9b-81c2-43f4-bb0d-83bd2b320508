import React, { useContext } from 'react';
import { Switch as WSwitch } from 'antd';
import { Switch as MSwitch } from 'antd-mobile';
import classPrefix from '@ali-whale/class-prefix';
import FormContext from '../FormContext';
import './Switch.less';

const Switch = (props) => {
  const { mobile, isDetail } = useContext(FormContext);
  const px = mobile ? classPrefix('cbd-mobile-base-form') : classPrefix('cbd-web-base-form');
  const Component = mobile ? MSwitch : WSwitch;
  return (
    <div className={px('switch')}>
      <Component disabled={isDetail} {...props} />
    </div>
  );
};

export default Switch;
