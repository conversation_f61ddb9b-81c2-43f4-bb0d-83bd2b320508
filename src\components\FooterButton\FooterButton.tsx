import React, { useEffect, useState } from 'react';
import { useIntl } from 'umi';
import { Button } from 'antd';
import useModalMethod from '../Modal/useModalMethod';
import './FooterButton.less';

export interface IFooterButton {
  className?: string;
  step?: number;
  stepType?: string;
  handleNextStep?: () => void;
  handlePrevStep?: () => void;
  handleComplete?: () => void;
  handleClose?: () => void;
  handleTempStore?: () => void;
  handleCloseAndTempStore?: () => void;
  handleConfirmApply?: () => void;

  nextStepText: string;
  closeText?: string;

  showPrevStep?: boolean;
  showNextStep?: boolean;
  showClose?: boolean;
  showTempStore?: boolean;
  showCloseAndTempStore?: boolean;
  showConfirmApply?: boolean;
  setShowPrevStep?: () => void;
  setShowNextStep?: () => void;
  setShowClose?: () => void;
  setShowTempStore?: () => void;
  setShowCloseAndTempStore?: () => void;

  disablePrevStep?: boolean;
  disableNextStep?: boolean;
  disableClose?: boolean;
  disableConfirmApply?: boolean;
  disableTempStore?: boolean;
  disableCloseAndTempStore?: boolean;
  setDisablePrevStep?: (boolean) => void;
  setDisableNextStep?: (boolean) => void;
  setDisableClose?: () => void;
  setDisableConfirmApply?: (boolean) => void;
  setDisableTempStore?: () => void;
  setDisableCloseAndTempStore?: () => void;
}

const FooterButton = (props: IFooterButton) => {
  const intl = useIntl();
  const {
    handlePrevStep,
    handleNextStep,
    className = '',
    handleComplete,
    handleTempStore,
    handleCloseAndTempStore,
    handleConfirmApply,
    showPrevStep,
    showNextStep,
    showClose,
    showTempStore,
    showCloseAndTempStore,
    disablePrevStep,
    disableNextStep,
    disableClose,
    disableConfirmApply,
    disableTempStore,
    disableCloseAndTempStore,
    showConfirmApply,
  } = props;

  const [Modal] = useModalMethod();

  const [closeText, setCloseText] = useState<string>(props.closeText || '');

  let nextStepName = props.nextStepText;

  const handleClose = () => {
    props.handleClose
      ? props.handleClose()
      : handleComplete
      ? (window.top as any).close()
      : Modal.quitConfirm();
  };

  useEffect(() => {
    setCloseText(props.closeText || '');
  }, [props.closeText]);

  return (
    <div className={className}>
      {!handleComplete ? (
        <>
          {showCloseAndTempStore && (
            <Button
              onClick={handleCloseAndTempStore}
              disabled={disableCloseAndTempStore}
              type="default"
            >
              {intl.formatMessage({ id: closeText ? closeText : 'close_save' })}
            </Button>
          )}
          {showClose && (
            <Button onClick={handleClose} disabled={disableClose} type="default">
              {intl.formatMessage({ id: 'turn_off' })}
            </Button>
          )}
          {showConfirmApply && (
            <Button onClick={handleConfirmApply} disabled={disableConfirmApply} type="primary">
              {intl.formatMessage({ id: 'confirm_application' })}
            </Button>
          )}

          {disablePrevStep ||
            (showPrevStep && (
              <Button onClick={handlePrevStep} disabled={disablePrevStep} type="default">
                {intl.formatMessage({ id: 'previous' })}
              </Button>
            ))}
          {showTempStore && (
            <Button onClick={handleTempStore} disabled={disableTempStore} type="primary">
              {intl.formatMessage({ id: 'cp_save' })}
            </Button>
          )}
          {showNextStep && (
            <Button onClick={handleNextStep} disabled={disableNextStep} type="primary">
              {intl.formatMessage({ id: nextStepName })}
            </Button>
          )}
        </>
      ) : (
        <Button onClick={handleComplete} type="primary">
          {intl.formatMessage({ id: 'complete' })}
        </Button>
      )}
    </div>
  );
};

export default FooterButton;
