/**
 * copy from antd-mobile/lib/stepper
 * 不同之处：1. RMCInputNumber引用的是经修改的本地组件
 */
import classnames from 'classnames';
import * as React from 'react';
import Icon from 'antd-mobile/lib/icon';
import type { StepPropsType } from 'antd-mobile/lib/stepper/PropsType';
import classPrefix from '@ali-whale/class-prefix';
import { InputNumber } from 'antd';
import RMCInputNumber from './RMCInputNumber';
import { MOBILE_CLASS_PREFIX, PC_CLASS_PREFIX } from '../constant';
import FormContext from '../FormContext';
import './Stepper.less';

export interface StepProps extends StepPropsType {
  prefixCls?: string;
  showNumber?: boolean;
  className?: string;
}

export default class Stepper extends React.Component<StepProps, unknown> {
  static contextType = FormContext;

  static defaultProps = {
    prefixCls: 'am-stepper',
    step: 1,
    readOnly: false,
    showNumber: false,
    focusOnUpDown: false,
  };

  stepperRef: RMCInputNumber | null | undefined;

  render() {
    const { className, showNumber, ...restProps } = this.props;
    const { mobile } = this.context;
    const px = mobile ? classPrefix(MOBILE_CLASS_PREFIX) : classPrefix(PC_CLASS_PREFIX);
    const stepperClass = classnames(className, px('stepper'), {
      showNumber: !!showNumber,
    });

    return mobile ? (
      <RMCInputNumber
        upHandler={<Icon type="plus" size="xxs" />}
        downHandler={<Icon type="minus" size="xxs" />}
        {...restProps}
        ref={el => (this.stepperRef = el)}
        className={stepperClass}
      />
    ) : (
      <InputNumber
        upHandler={<Icon type="plus" size="xxs" />}
        downHandler={<Icon type="minus" size="xxs" />}
        {...restProps}
        className={stepperClass}
      />
    );
  }
}
