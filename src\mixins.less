@import '~@/styles/publicMixins.less';
@import '~@/styles/listItemMixins.less';
@import '~@/styles/footerBtnMixins.less';

@loopColors: var(--loopColor1), var(--loopColor2), var(--loopColor3), var(--loopColor4);
@color1: extract(@loopColors, 1);
@color2: extract(@loopColors, 2);
@color3: extract(@loopColors, 3);
@color4: extract(@loopColors, 4);

@selected-value-color: var(--firstTextColor);
@divider-border-color: #d1d1d6;

// 表单标题
.cardTitleMixins(@color: @color1) {
  background: @color;
  border-top-left-radius: 10 * @hd;
  border-top-right-radius: 10 * @hd;
  padding: 12 * @hd 16 * @hd;
}

// each(@loopColors, {
//   @n:length(@loopColors);
//   &:nth-of-type(@{n}n + @{index}) {
//       background-color: @value;
//   }
// });
