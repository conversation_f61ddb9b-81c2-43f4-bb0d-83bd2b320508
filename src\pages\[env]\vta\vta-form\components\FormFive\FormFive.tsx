import React from 'react';
import pc from 'prefix-classnames';
import './FormFive.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Space } from 'antd';

const px = pc('form-five');

export interface FormFiveProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {

}

const FormFive = (props: FormFiveProps) => {
  const { className = '', ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
  );
};

export default FormFive;
