import type { PersonalFormType } from '../types/defs';

export interface FormModelsState {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // formData: Record<string, any>;
  formData: PersonalFormType;
  formJson: string;
  // instanceId: string;
  formId: string;
}

export const initFormModelsState = {
  formData: {},
  formJson: '{}',
  // instanceId: '',
  formId: '',
};

export default {
  namespace: 'formModels',
  state: initFormModelsState,

  /* 更新状态（通用） */
  reducers: {
    update: (state: FormModelsState, { payload }: { payload: Partial<FormModelsState> }) => ({
      ...state,
      ...payload,
    }),
    init: () => initFormModelsState,
  },
};
