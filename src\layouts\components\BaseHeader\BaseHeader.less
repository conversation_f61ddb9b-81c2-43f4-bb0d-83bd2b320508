@prefix: web-header;

.@{prefix} {
  &- {
    &main {
      display: flex;
      align-items: center;
    }

    &logo {
      margin-right: 10px;
      cursor: pointer;
      img {
        height: 40px;
        width: 40px;
      }
    }

    &title {
      font-size: 26px;
      color: #ffffff;
      font-weight: 600;
      cursor: pointer;
      max-width: 286px;
      letter-spacing: 2px;
      line-height: 1;
      display: flex;
      align-items: center;
    }

    &menu {
      flex: 1;
    }

    &item {
      display: flex;
      align-items: center;
      flex: 1;
    }

    &search-content {
      padding: 0 12px;
    }

    &text-link {
      background-color: rgba(0, 0, 0, 0);
      color: rgba(255, 255, 255, 0.45);
      padding: 0 12px;
      font-size: 12px;
      cursor: pointer;
      transition: color 300ms, background-color 300ms;

      &:hover {
        background-color: rgba(0, 0, 0, 0.2);
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}
