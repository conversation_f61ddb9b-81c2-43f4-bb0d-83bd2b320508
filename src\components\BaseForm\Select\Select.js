import React, { useMemo, useContext } from 'react';
// import PropTypes from 'prop-types';
import { List, Picker } from 'antd-mobile';
import { Select as SelectAntd } from 'antd';
import cn from 'classnames';
import classPrefix from '@ali-whale/class-prefix';
import { useIntl } from 'umi';
import FormContext from '../FormContext';
import { getTreeDeep } from '../../../utils';
import useLabelInValue from '../../../utils/useLabelInValue';
import { PC_CLASS_PREFIX, MOBILE_CLASS_PREFIX } from '../constant';
import './Select.less';

const Select = (props) => {
  const intl = useIntl();

  const { options = [], onChange, placeholder, disabled, id, ...otherProps } = props;

  const { mobile, isDetail } = useContext(FormContext);
  const px = mobile ? classPrefix(MOBILE_CLASS_PREFIX) : classPrefix(PC_CLASS_PREFIX);

  const colsNum = useMemo(() => Math.max(1, getTreeDeep(options)), [options]);

  const { transformValue, value: formattedValue } = useLabelInValue(props, options);

  if (isDetail) {
    return <div>{props.value?.[0]?.label}</div>;
  }

  if (!mobile) {
    return (
      <div className={cn(px('select'))} id={id}>
        <SelectAntd
          options={options}
          placeholder={placeholder}
          disabled={disabled}
          {...otherProps}
          value={formattedValue}
          onChange={(e) => {
            let newVal = transformValue(e);
            if (!Array.isArray(newVal)) {
              newVal = [newVal];
            }
            onChange(newVal);
          }}
        />
      </div>
    );
  }

  const handleScroll = () => {
    const content = document
      .querySelector('.am-picker-popup-body .am-picker')
      ?.querySelector('.am-picker-col-content');
    const selected = content?.querySelector('.am-picker-col-item-selected');
    const preSelected = content?.querySelector('.cbd-selector-selected-item-scroll');
    if (preSelected) {
      preSelected.classList.remove('cbd-selector-selected-item-scroll');
    }
    if (selected && selected.scrollWidth > selected.clientWidth) {
      selected.classList.add('cbd-selector-selected-item-scroll');
    }
  };

  return (
    <div
      className={cn(px('select'), {
        [px('select-isSelected')]: !!formattedValue,
        [px('select-disabled')]: disabled,
      })}
      id={id}
    >
      <Picker
        data={options}
        cols={colsNum}
        extra={placeholder}
        disabled={disabled}
        okText={intl.formatMessage({ id: '确认' })}
        dismissText={intl.formatMessage({ id: '取消' })}
        {...otherProps}
        value={formattedValue}
        onChange={(e) => {
          onChange(transformValue(e));
        }}
        onPickerChange={handleScroll}
        // 触发默认选中项滚动
        onVisibleChange={visible => visible && setTimeout(() => handleScroll(), 0)}
      >
        <List.Item arrow="down" />
      </Picker>
    </div>
  );
};

// Select.propTypes = {
//   className: PropTypes.string,
//   style: PropTypes.object,
// };

// Select.defaultProps = {
//   className: '',
//   style: {},
// };

export default Select;
