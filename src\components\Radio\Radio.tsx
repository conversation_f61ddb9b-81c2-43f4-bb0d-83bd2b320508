import React from 'react';
import RadioGroupContext from './context';
import styles from './Radio.less';

export interface RadioProps {
  value: string | number;
  children?: React.ReactNode;
  checked?: boolean;
  className?: string;
}

const Radio = (props: RadioProps) => {
  const { value, checked, children, className = '' } = props;
  const context = React.useContext(RadioGroupContext);
  let groupChecked = checked;
  if (context) {
    groupChecked = context.value === value;
  }

  return (
    <span
      onClick={() => context?.onChange(value)}
      className={`${styles.radioWrapper} ${className}`}
    >
      <input type="radio" value={value} checked={groupChecked} className={styles.radio} />
      <span className={styles.label}>{children}</span>
    </span>
  );
};

export default Radio;
