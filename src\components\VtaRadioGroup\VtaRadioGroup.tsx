import React, { forwardRef, useMemo } from 'react';
import { Radio } from 'antd';
import type { RadioGroupProps } from 'antd';

/**
 * 仅封装：完全继承 antd Radio.Group 的所有属性，透传 ref。
 * 不添加 Form.Item、不改动受控/非受控语义、不加默认行为。
 */
export const VtaRadioGroup = forwardRef<HTMLDivElement, RadioGroupProps>((props, ref) => {
  const realProps = useMemo(() => {
    return {
      optionType: 'default' as const,
      ...props,
    };
  }, [props]);
  return <Radio.Group ref={ref} {...realProps} />;
});
