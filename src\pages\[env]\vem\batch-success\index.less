@prefix: page-batch-success;
@prefix-mobile: page-batch-success-mobile;

.@{prefix} {
  &- {
    &root {
      position: relative;
      padding-top: 76px;
      padding-bottom: 45px;
      min-height: 100%;
      background-color: #f2f3f5;
    }

    &body {
      margin: 0 auto 20px;
      max-width: 1048px;
      border-radius: 8px;
      background-color: rgb(217, 235, 231);

      .ant-col {
        display: flex;
        gap: 20px;
        padding-bottom: 20px;
      }

      .label {
        min-width: 100px;
        font-size: 16px;
        font-weight: 500;
        color: @brand-primary;
        line-height: 22px;
      }

      .value {
        font-size: 16px;
        font-weight: 400;
        color: #323232;
        line-height: 22px;
        min-height: 22px;
        height: auto;
      }
    }

    &sectionTitle {
      display: flex;
      padding: 20px;
      text-align: left;
      background-color: rgb(217, 235, 231);
      color: #000;
      font-size: 26px;
      font-weight: bold;
      display: flex;
      gap: 10px;
      align-items: center;
      border-bottom: 1px solid #c4c4c4;
    }

    &sectionBody {
      margin: auto;
      font-size: 16px;
      line-height: 22px;
      padding: 24px;
    }

    &footer {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      height: 0.68rem;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

      gap: 24px;

      .ant-btn {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;

        min-width: 128px;
        height: 48px;
        line-height: 48px;
        border-radius: 24px;
        margin-left: 12px;
        font-size: 16px;
        padding: 0 5px;
      }
    }

    &successTitlleMobile {
      font-size: 16px;
      color: #323232;
      height: 30px;
      border-top: 1px solid #c4c4c4;
      line-height: 30px;
      margin: 20px 0px 10px 0px;
      padding: 20px 0;
    }

    &successBottomTitleSpan {
      color: #084ab8;
    }
  }
}

.footer-container {
  width: 1048px;
  margin: 0 auto;
  display: flex;
  justify-content: flex-end;
}


.@{prefix-mobile} {
  &- {
    &root {
      position: relative;
      min-height: 100%;
      background-color: #fff;
      padding-bottom: 100px;

      h1 {
        font-size: 0.24rem;
        text-align: center;
      }
    }

    &alert {
      padding: 20px;
    }

    &body {
      margin: 10px auto;
      width: 90%;
      border-bottom: 1px solid #eeeeee;
      font-size: 0.14rem;
    }

    &body:last-child {
      border-bottom: 0px solid #eeeeee;
      padding-bottom: 0;
    }

    &row {
      display: flex;
      margin: 10px 0;
      line-height: 1.5em;
    }

    &label {
      width: 7em;
      color: #aaaaaa;
    }

    &value {
      flex: 1;
      text-align: left;
      word-break: break-all;
    }

    &footer {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 20px;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
      gap: 4%;

      .ant-btn {
        flex: 1;
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;
        height: 52px;
        line-height: 52px;
        border-radius: 26px;
        font-size: 16px;
        padding: 0 5px;
      }
    }

    &successTitlleMobile {
      color: #aaaaaa;
      // color: #323232;
      text-align: center;
      font-size: 0.12rem;
      height: 30px;
      border-top: 1px solid #eeeeee;
      line-height: 30px;
      margin: 20px 0px 10px 0px;
      padding: 10px 0;
    }

    &successBottomTitleSpan {
      color: #084ab8;
    }
  }
}
