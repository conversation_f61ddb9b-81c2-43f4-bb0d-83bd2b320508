@font-face {
  font-family: "Ming_MSCS";
  src: url(../fonts/Ming_MSCS/Ming_MSCS.woff2),
  url(../fonts/Ming_MSCS/Ming_MSCS.woff),
  url(../fonts/Ming_MSCS/Ming_MSCS.ttf);
}

@font-face {
  font-family: "EUDC";
  src: url(../fonts/EUDC/EUDC.woff2),
  url(../fonts/EUDC/EUDC.woff)
}

body #root, body #root .web-layout-content {
  font-family: Helvetica, Arial, "EUDC", "Ming_MSCS", "新細明體", "PMingLiU", "細明體", "MingLiU", serif;
}

html, button {
  font-size: 12px;
}

.btn-xs, .btn-sm {
  font-size: 14px;
}

.form-control[disabled] {
  background-color: transparent;
  opacity: 1;
}

.heiti {
  font-family: "SimHei", "Heiti TC", "Heiti SC", "Arial";
}

input[type='checkbox']:hover {
  cursor: pointer;
}

/* .selected { */
/*     background-color: #ffffcc; */
/* } */

.margin-left {
  margin-left: 0.5em;
}

.feedbackPanelERROR {
  color: orange;
}

.feedbackPanelINFO {
  color: green;
}

.diff {
  background-color: #ffffcc;
}

div.wicket-aa {
  font-family: "Lucida Grande", "Lucida Sans Unicode", Tahoma, Verdana;
  font-size: 12px;
  background-color: white;
  border-width: 1px;
  border-color: #cccccc;
  border-style: solid;
  padding: 2px;
  margin: 1px 0 0 0;
  text-align: left;
}

div.wicket-aa ul {
  list-style: none;
  padding: 2px;
  margin: 0;
}

div.wicket-aa ul li {
  cursor: pointer;
}

div.wicket-aa ul li.selected {
  background-color: #FFFF00;
  padding: 2px;
  margin: 0;
}

.screen-id {
  color: silver;
  font-style: italic;
  font-size: 70%;
  font-family: Arial;
  padding-right: 5px;
  margin-top: 20px;
}

.screen-id:hover {
  color: orange;
  cursor: pointer;
}

.dropdown-default.dropdown-menu li a:hover {
  color: #27ae60;
  background-color: #3498db;
}


.select2-container--bootstrap .select2-results__group {
  color: #333;
  display: block;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.428571429;
  white-space: nowrap;
}

.select2-container--bootstrap .select2-results__option[single-group=true] {
  padding: 0;
}

/* .select2-results__options[role="tree"] li.select2-results__option[role="treeitem"]{ */
/* 	color: #333; */
/*     display: block; */
/*     padding: 6px 12px; */
/*     font-size: 16px; */
/*     line-height: 1.428571429; */
/*     white-space: nowrap; */
/* } */
.cursor_pointer {
  cursor: pointer
}
