/**
 * @Description: 定开：checkBox勾选——接收审批结果方式Web
 * @Author: 听风
 * @Date: 2021-04-16 10:01:14
 **
 * @property {string} label 表单项的label 例：'接收审批结果方式'
 * @property {string} content checkbox的内容 例：'本人同意透過以下流動電話號碼接受啟動任意性製度供款申請的審批結果通知短訊'
 */

import React, { useContext } from 'react';
import { Checkbox } from 'antd';
import classPrefix from '@ali-whale/class-prefix';
import FormContext from '../FormContext';
import './CheckBoxTips.less';

const CheckBoxTips = (props) => {
  const { onChange, label = '', content = '' } = props;
  const { mobile, isDetail } = useContext(FormContext);
  const px = classPrefix(
    mobile ? 'cbd-mobile-base-form-checkbox-tips' : 'cbd-web-base-form-checkbox-tips',
  );
  return (
    <div className={px()}>
      <div className={px('header')}>
        <span className={px('label')}>{label}</span>
      </div>
      <div className={px('subtext')}>
        {!isDetail && <Checkbox onChange={e => onChange(e.target.checked)} />}
        {content}
      </div>
    </div>
  );
};

export default CheckBoxTips;
