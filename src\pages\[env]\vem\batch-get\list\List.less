@prefix: page-batch-get-list;
@prefix-mobile: page-batch-get-list-mobile;

.@{prefix} {
  &- {
    &root {
      max-width: 1048px;
      margin: 0 auto;
      padding-bottom: 1rem;
      .my-card-item {
        box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.12);
        border: 0;
        .my-card-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .status {
            background: #fff;
            border-radius: 15px;
            line-height: 30px;
            font-size: 0.14rem;
            text-align: center;
            padding: 0 10px;
            color: @brand-primary;
          }
        }
      }
      .batch-get-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0.68rem;
        background-color: #fff;
        display: flex;
        align-items: center;
        box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

        .footer-container {
          width: 1048px;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 24px;
          .ant-btn {
            &.ant-btn-default {
              background: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
            }

            min-width: 128px;
            height: 48px;
            line-height: 48px;
            border-radius: 24px;
            margin-left: 12px;
            font-size: 16px;
            padding: 0 5px;
          }
        }
      }
      .batch-get-card-form {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: space-between;
        .ant-form-item {
          width: 48%;
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
            }
          }

          .statusBg1 {
            padding: 0 2px;
            color: #f33b40;
          }
          .statusBg2 {
            padding: 0 2px;
            color: @brand-primary;
          }
        }
        .my-card-body {
          display: flex;
          flex-direction: column;
          font-size: 16px;
          padding: 20px;
          .my-card-item-btn {
            margin-top: 13px;
            padding-top: 13px;
            display: flex;
            justify-content: flex-end;
            border-top: 1px solid #f0f0f0;
            .ant-btn {
              background-color: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
              margin-left: 10px;
            }
          }
        }
      }
    }
    &pagination-box {
      text-align: end;
    }
    &title {
      font-size: 0.2rem;
      font-weight: 700;
      padding: 0.2rem 0.24rem 0.2rem 0;
      // border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    }

    &sectionTitle {
      margin-top: 20px;
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }
  }
}

.@{prefix-mobile} {
  &- {
    &root {
      background-color: #eee;
      padding: 40px 20px;
      padding-bottom: 1rem;
      .my-card-item {
        box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.12);

        .my-card-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .status {
            background: #fff;
            border-radius: 15px;
            line-height: 30px;
            font-size: 0.14rem;
            text-align: center;
            padding: 0 10px;
            color: @brand-primary;
          }
        }
      }
      .batch-get-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0.68rem;
        background-color: #fff;
        display: flex;
        align-items: center;
        box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

        .footer-container {
          width: 100%;
          padding: 0 20px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          gap: 4%;
          .ant-btn {
            flex: 1;
            height: 48px;
            line-height: 48px;
            border-radius: 24px;
            font-size: 16px;
            padding: 0 5px;
            &.ant-btn-default {
              background: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
            }
          }
        }
      }
      .batch-get-card-form {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: space-between;
        .ant-form-item {
          width: 100%;
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
            }
          }

          .statusBg1 {
            padding: 0 2px;
            color: #f33b40;
          }
          .statusBg2 {
            padding: 0 2px;
            color: @brand-primary;
          }
        }
        .my-card-body {
          display: flex;
          flex-direction: column;
          font-size: 16px;
          padding: 13px 20px;
          .my-card-item-btn {
            margin-top: 13px;
            padding-top: 13px;
            display: flex;
            justify-content: flex-end;
            border-top: 1px solid #f0f0f0;
            .ant-btn {
              background-color: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
              margin-left: 10px;
            }
          }
        }
      }
    }
    &pagination-box {
      width: 100%;
      display: flex;
      justify-content: center;
    }
    &title {
      font-size: 0.2rem;
      font-weight: 700;
      padding: 0.2rem 0.24rem 0.2rem 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      margin-bottom: 0.2rem;
    }

    &sectionTitle {
      display: none;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }
  }
}
