import React, { useEffect } from 'react';
import pc from 'prefix-classnames';
import './Form9.less';

import { useIntl } from 'umi';
import { Radio, Table } from 'antd';

const px = pc('vta-form-form-9');

export interface FormNineProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setNextStepText: (text: string) => void;
  setShowCloseAndTempStore: (show: boolean) => void;
  setShowClose: (show: boolean) => void;
  setShowTempStore: (show: boolean) => void;
  setShowPrevStep: (show: boolean) => void;
  txnId?: string
}

const FormNine = (props: FormNineProps) => {
  const { className = '', ...otherProps } = props;
  const { setNextStepText } = otherProps;

  const intl = useIntl();

  useEffect(() => {
    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }));
    props.setShowCloseAndTempStore(true);
    props.setShowClose(false);
    props.setShowTempStore(false);
    props.setShowPrevStep(false);
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }));
      props.setShowCloseAndTempStore(false);
      props.setShowClose(true);
      props.setShowTempStore(true);
      props.setShowPrevStep(true);
    };
  }, []);

  const columns = [
    {
      title: intl.formatMessage({ id: 'cost_details' }),
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: intl.formatMessage({ id: 'amount' }),
      dataIndex: 'amount',
      key: 'amount',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'tax_payment' }),
      dataIndex: 'tax',
      key: 'tax',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'subtotal' }),
      key: 'subtotal',
      dataIndex: 'subtotal',
      width: '100px',
    },
  ];
  const data = [
    {
      key: '1',
      title: '機動車輛商標及型號認可費用(輕型、重型汽車、工業機器、掛車、半掛車)',
      amount: '4,800',
      tax: 480,
      subtotal: '5,280',
    },
    {
      key: '2',
      title: intl.formatMessage({ id: 'total' }),
      amount: '',
      tax: <span style={{ color: '#084ab8' }}>MOP</span>,
      subtotal: <span style={{ color: '#084ab8' }}>{'5,280'}</span>,
    },
  ];

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'payment_information' })}</div>
      <Table columns={columns} dataSource={data} pagination={false} />
      <div className={px('pay-way')}>
        <p className={px('formTitle')}>{intl.formatMessage({ id: 'payment_channels' })}</p>
        <div style={{ paddingLeft: '30px', marginTop: '15px' }}>
          <Radio checked={true}>
            <img style={{ width: '150px' }} src="/ovsap/image/pay/GOVpay.png" alt="" />{' '}
          </Radio>
        </div>

        <div style={{ paddingLeft: '30px', marginTop: '5px' }}>
          <Radio checked={false}>
            <img style={{ width: '150px' }} src="/ovsap/image/pay/BOCEPAY.jpg" alt="" />{' '}
          </Radio>
        </div>

        <div style={{ paddingLeft: '30px', marginTop: '5px' }}>
          <Radio checked={false}>
            <img style={{ width: '150px' }} src="/ovsap/image/pay/BOCPPAY.jpg" alt="" />{' '}
          </Radio>
        </div>

        <div style={{ paddingLeft: '30px', marginTop: '5px' }}>
          <Radio checked={false}>
            <img style={{ width: '150px' }} src="/ovsap/image/pay/BNU.png" alt="" />{' '}
          </Radio>
        </div>
      </div>
    </div>
  );
};

export default FormNine;
