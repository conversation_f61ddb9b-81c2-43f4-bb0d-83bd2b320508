import React, { useState, useEffect } from 'react';
import { useIntl } from 'umi';
import { Button } from 'antd';
import './FooterButton.less';

export interface IFooterButton {
  className?: string;
  step?: number;
  stepType?: string;
  handleNextStep?: () => void;
  handlePrevStep?: () => void;
  handleComplete?: () => void;
  handleClose?: () => void;
  handleTempStore?: () => void;
  handleCloseAndTempStore?: () => void;
  handleConfirmApply?: () => void;

  nextStepText: string;
  closeText?: string;

  showPrevStep: boolean;
  showNextStep: boolean;
  showClose: boolean;
  showTempStore: boolean;
  showCloseAndTempStore: boolean;
  showConfirmApply?: boolean;
  setShowPrevStep?: () => void;
  setShowNextStep?: () => void;
  setShowClose?: () => void;
  setShowTempStore?: () => void;
  setShowCloseAndTempStore?: () => void;

  disablePrevStep: boolean;
  disableNextStep: boolean;
  disableClose: boolean;
  disableConfirmApply?: boolean;
  disableTempStore: boolean;
  disableCloseAndTempStore: boolean;
  setDisablePrevStep?: (boolean) => void;
  setDisableNextStep?: (boolean) => void;
  setDisableClose?: () => void;
  setDisableTempStore?: () => void;
  setDisableCloseAndTempStore?: () => void;
}

const FooterButton = (props: IFooterButton) => {
  const intl = useIntl();
  const {
    handleNextStep,
    className = '',
    handleComplete,
    handleTempStore,
    handleCloseAndTempStore,
    showNextStep,
    showTempStore,
    showCloseAndTempStore,
    disableNextStep,
    disableConfirmApply,
    disableTempStore,
    disableCloseAndTempStore,
    showConfirmApply,
    handleConfirmApply,
  } = props;

  const [closeText, setCloseText] = useState<string>(props.closeText || '');

  useEffect(() => {
    setCloseText(props.closeText || '');
  }, [props.closeText]);

  let nextStepName = props.nextStepText;
  
  return handleComplete ||
    showCloseAndTempStore ||
    showConfirmApply ||
    showTempStore ||
    showNextStep ? (
    <div className={`${className} footer-button`}>
      {!handleComplete ? (
        <>
          {showCloseAndTempStore && (
            <Button
              onClick={handleCloseAndTempStore}
              disabled={disableCloseAndTempStore}
              type="default"
            >
              {intl.formatMessage({ id: closeText ? closeText : 'close_save' })}
            </Button>
          )}
          {showConfirmApply && (
            <Button onClick={handleConfirmApply} disabled={disableConfirmApply} type="primary">
              {intl.formatMessage({ id: 'confirm_application' })}
            </Button>
          )}
          {showTempStore && (
            <Button onClick={handleTempStore} disabled={disableTempStore} type="default">
              {intl.formatMessage({ id: 'cp_save' })}
            </Button>
          )}
          {showNextStep && (
            <Button onClick={handleNextStep} disabled={disableNextStep} type="primary">
              {intl.formatMessage({ id: nextStepName })}
            </Button>
          )}
        </>
      ) : (
        <Button onClick={handleComplete} type="primary">
          {intl.formatMessage({ id: 'complete' })}
        </Button>
      )}
    </div>
  ) : null;
};

export default FooterButton;
