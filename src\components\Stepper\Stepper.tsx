import React from 'react';
import { isMobile } from '@/utils';
import styles from './Stepper.less';

export interface StepperProps {
  value?: number;
  onChange?: (v: number) => void;
  max?: number;
  min?: number;
}

function Stepper(props: StepperProps) {
  const { value = 1, onChange, max = 9999, min = -9999 } = props;

  const handleAdd = () => {
    if (value < max && onChange) {
      onChange(value + 1);
    }
  };
  const handleSubtract = () => {
    if (value > min && onChange) {
      onChange(value - 1);
    }
  };
  const handleValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (onChange) {
      if (/^\d+$/.test(newValue)) {
        if (Number(newValue) < min) {
          onChange(min);
        } else if (Number(newValue) > max) {
          onChange(max);
        } else {
          onChange(Number(newValue));
        }
      } else if (newValue === '') {
        onChange(min);
      }
    }
  };

  return (
    <div className={isMobile() ? styles.root : `${styles.root} ${styles.webRoot}`}>
      <div onClick={handleSubtract} className={styles.left}>
        {/* <span className={value === min ? styles.disabled : ''}>——</span> */}
        <div className={`${styles.minus} ${value === min ? styles.disabled : ''}`} />
      </div>
      <input onChange={handleValueChange} className={styles.input} value={value} />
      <div onClick={handleAdd} className={styles.right}>
        <div className={`${value === max ? styles.disabled : ''} ${styles.line}`} />
      </div>
    </div>
  );
}

export default Stepper;
