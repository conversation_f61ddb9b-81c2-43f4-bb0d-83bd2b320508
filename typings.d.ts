declare module '*.css';
declare module '*.png';
declare module '*.jpg';

declare module '*.svg' {
  export function ReactComponent(props: React.SVGProps<SVGSVGElement>): React.ReactElement;

  const url: string;
  export default url;
}

declare module '*.less' {
  type KeyValue = { [key: string]: string };
  const res: KeyValue;
  export default res;
}

interface Window {
  AlipayJSBridge?: any;
}

declare type Obj<T = unknown> = Record<string, T>;

declare const API_HOST: string;
declare const PLATFORM: string;
declare const APP_ID: string;
declare const REDIRECT_URI: string;
declare const AUTH_SERVER: string;
declare const ENV: 'dev' | 'uat' | 'prod' | 'sit';
declare const CLIENT_ID: string;
declare const CLIENT_SECRET: string;
declare const BUCKET_ID: string;
declare const DEV_REDIRECT_URL: string;
declare const COMMON_ACCESS_TOKEN: string;

declare type BaseProps = {
  location: {
    pathname: string;
    search: string;
    hash: string;
    state: any;
    query: { [key: string]: any };
  };
  match: {
    path: string;
    url: string;
    params: { [key: string]: string };
  };
};

interface Window {
  routerBase: string;
}

declare type IdentityAuthorize = {
  client_id: string;
  proof_id: string;
  identity_level: string;
  account_type: string;
  euid: string;
  redirect_uri: string;
  reason: string;
  hash?: string[];
};

declare type AccessToken = {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
};

declare type UserProfile = {
  accountType: string;
  agreeTime: Date;
  euid: string;
  gender: string;
  firstName: string;
  loginName: string;
  namePt: string;
  nameCn: string;
  token: string;
  username: string;
  uid: string;
  identityType: string;
  identityTypeDesc: string;
  identityNo: string;
  mobile: string;
};

declare type AuthorizeToken = {
  accessToken?: string;
  jwtToken?: string;
  expiresIn?: number;
  timestamp?: number;
  platform: string;
};

declare type AuthVersion = {
  codeVerifier?: string;
  codeChallenge?: string;
  version?: string;
};

declare type ServiceTitle = {
  serviceTitleCn: string;
  serviceTitleEn: string;
  serviceTitlePt: string;
  companyCname: string;
  companyEname: string;
  companyPname: string;
};

declare type TxnServiceTitle = {} & ServiceTitle;

declare type getRivmByDSFM4Params = {
  importType: string;
  importNo: string;
  importYear: string;
  vin: string;
  txnId?: string;
};

declare type deleteExTxnParams = Omit<getRivmByDSFM4Params, 'vin' | 'txnId'>;

declare type getRivmByDSFM4ResRequerentes = {
  nomeCN: string;
  nomePT: string;
};
declare type getRivmByDSFM4Res = {
  importNoFull: string;
  vtaNoFull: string;
  dsfRefNo: string;
  licNo: string;
  dataPaga: string;
  dataPagaCn: string;
  dataPagaEn: string;
  dataPagaPt: string;
  motor: string;
  motor2: string;
  motor3: string;
  motor4: string;
  vin: string;
  noContribuinte: string;
  dataEnt: string;
  isencaoLei: string;
  isencaoDateInit: string;
  isencaoDateFim: string;
  requerentes: getRivmByDSFM4ResRequerentes[];
  vehType: string;
  vehTypeDescCN: string;
  vehTypeDescEN: string;
  vehTypeDescPT: string;
  vehCategoryCname: string;
  vehCategoryPname: string;
  vehCategoryEname: string;
  vehCategory: string;
  vehBrandCodeCname: string;
  vehBrandCodePname: string;
  vehBrandCodeEname: string;
  vehModel: string;
  vehModelYear: string;
  vehBuildCtryCodeCname: string;
  vehBuildCtryCodePname: string;
  vehBuildCtryCodeEname: string;
  homoNum: string;
  homoAno: string;
};

declare type createEXTxnParams = {
  importType: string;
  importNo: string;
  importYear: string;
  vinPart: string;
};
declare type createEXTxnRes = {
  txnId: string;
};

declare type sysCodesByCodeTypeOpt = {
  codeCname: string;
  codeEname: string;
  codePname: string;
  codeId: number;
  codeKey: string;
  codeType: number;
  codeStatus: string;
};
declare type vehTyreOpt = {
  tyreFSpec: string;
  tyreRSpec: string;
  tyreId: number;
  tyreDesc: string;
};

declare type saveTxnOwnersParams = {
  ownerId: string; //PK
  txnOwnerId: string; // ownerId 换成ownerId
  txnId: string; //FK of OVSAP_TXN.TXN_ID

  ownerIdentType: string; //車主證件類型
  ownerIdentTypeCn: string;
  ownerIdentTypeEn: string;
  ownerIdentTypePt: string;
  ownerIdentNo: string; //車主證件編號
  ownerCname: string;
  ownerPname: string;
  ownerAddrCn: string;
  ownerLastNameCn: string; //車主中文姓
  ownerLastNamePt: string; //車主葡文姓
  ownerFirstNameCn: string; //車主中文名
  ownerFirstNamePt: string; //車主葡文名

  region: string; //地區
  streetCode: string; //大廈/街道
  bldingNo: string; //門牌
  bldingFloor: string; //樓層
  bldingBlock: string; //座/室
  ownerAddr: string; //中文詳細地址

  addrLang: string; //地址語言（使用一戶通登錄的語言）
  addrLangCn: string;
  addrLangEn: string;
  addrLangPt: string;
  addrType: string;

  quotaNumerator: string; // number //DSAJ業權分子數
  quotaDenominator: string; // number //DSAJ業權分母數
  dsajAddrTicked: string; //選中：以聯絡地址作為汽車登記上的居所/住所 值 Y N用于传接口参数
  dsajAddrTickedChecked: string; // 作为 true false 回显显checkbox

  dsajRegion: string; //所有權地址：地區
  dsajStreetCode: string; //所有權地址：大廈/街道
  dsajBldingNo: string; //所有權地址：門牌
  dsajBldingFloor: string; //所有權地址：樓層
  dsajBldingBlock: string; //所有權地址：座/室
  dsajAddr: string; //所有權地址

  agentVerficationStatusCn: string;
  agentVerficationStatusEn: string;
  agentVerficationStatusPt: string;

  verficationNoticeId: string; // number //若有發送車主人面識別提示信息，記錄其對應的noticeId
  ownerFacialStatus: boolean;
  verficationStatus: string; //是否通過人面識別（Y=是；N=否；W=waiting）
  verficationStatusCn: string;
  verficationStatusPt: string;
  verficationStatusEn: string;
  verficationStatusDate: Date;
  ownerStatus: string; //車主狀態: A=有效; D=删除; H=歷史; U=Undefined
  checkBox: string; // 所佔用份額 checkbox
  vehType: string;
  ownerSeq: string;
  dsajQuotaMsgCname: string;

  isShowFacialButton: string;
  isShowFacialLabel: string;
};

declare type saveTxnVehicleParams = {
  txnId: string;
  vehUsageCode: string;
  colorCode: string;
  tyreId: number;
  plateAgree: string;
  relatedPlateNo: string;
  dspaOptions: string;
  dspaFpNo: string;
};

declare type validateDspaFpNoParams = {
  dspaFpNo: string;
  txnId: string;
};
/**
 * ValidateDspaFpNoResultDTO
 *
 * <description>
 */
declare type ValidateDspaFpNoResultDTO = {
  /**
   * DSPA資料
   */
  dspaData?: DsatDspaCancelVehAppVwDTO;
  /**
   * 校驗結果
   */
  result?: boolean;
};

/**
 * DSPA資料
 *
 * DsatDspaCancelVehAppVwDTO
 */
declare type DsatDspaCancelVehAppVwDTO = {
  draw: number;
  start: number;
  length: number;
  appNo: string;
  vehId: number;
  vehType: string;
  vehRtnDate: string;
  appVtaNo: number;
  appVtaNoFull: string;
  appVtaYear: number;
  appVehBrandCode: string;
  appVehModel: string;
  applyForWaiveLastDate: string;
  appStatus: string;
  oldPlateNo: string;
  vehTypeDescCn: string;
  vehTypeDescPt: string;
  vehTypeDescEn: string;
  oldOwnerInfos: string[];
};

declare type getTxnVehicleRes = {
  vehUsageCode: string;
  tyreId: string;
  colorCode: string;
  vtaYear: string;
  vtaNo: string;
  plateAgree: string;
  relatedPlateNo: string;
  showDspa: boolean;
  dspaOptions: string;
  dspaData: DsatDspaCancelVehAppVwDTO;
  dspaFpNo: string;
};

declare type saveAgentCompParams = {
  txnId?: string;
  vtaCompIdentType: string; //進口商類型
  vtaCompIdentTypeDescCn: string;
  vtaCompIdentTypeDescPt: string;
  vtaCompIdentNo: string; //進口商編號
  vtaCompCname: string; //進口商中文名
  vtaCompPname: string; //進口商葡文名
  agentCompIdentType: string; //代理商類型
  agentCompIdentTypeDescCn: string;
  agentCompIdentTypeDescPt: string;
  agentCompIdentNo: string; //代理商編號
  agentCompCname: string; //代理商中文名
  agentCompPname: string; //代理商葡文名
  agentContactPhone: string; //代理商聯絡電話
};

declare type getAgentCompRes = Omit<saveAgentCompParams, 'txnId'>;

declare type getContactListRes = saveTxnOwnersParams & {
  ownerSeq: number; //車主記錄次序
};

declare type getTxnContactRes = {
  contactId: string; //PK
  ownerId?: string;
  txnOwnerId: string;
  txnId: string;
  identType?: string; //證件類型
  identNo?: string; //證件編號
  contactLastNameCn?: string;
  contactLastNamePt?: string;
  contactFirstNameCn?: string;
  contactFirstNamePt?: string;
  contactMobilePhone: string;
  contactLanguage: string;
  contactLanguageCn?: string;
  contactLanguageEn?: string;
  contactLanguagePt?: string;
  contactAgreest: string; //是否同意 (Y: 同意, N: 不同意)
  agreeCodeDto?: sysCodesByCodeTypeOpt;
  disAgreeCodeDto?: sysCodesByCodeTypeOpt;
  agreeDefaultCodeDto?: sysCodesByCodeTypeOpt;
};

declare type saveContactParams = getTxnContactRes;

declare type getServiceDocsRes = {
  serviceDocumentID: number;
  code: string;
  nameZh: string;
  namePt: string;
  category: number;
  displayOrder: number;
  upLoadable: boolean;
  attachIdentCode: string;
  uploadFileUrl?: string;
  originalFileName?: string;
  docTitleCn?: string;
  docTitlePt?: string;
  docTitleEn?: string;
};

declare type serviceItemFileUploadQueryParams = {
  txnId: string;
  code: string;
  spServiceDocId: number;
  nameZh: string;
  namePt: string;
};

declare type serviceItemFileUploadParams = {
  file: File;
};

declare type ovsapVehDataDTO = {
  vtaNoFull: string;
  importNoFull: string;
  importNo: number;
  vtaNo: number;
  vehType: string;
  vehTypeDescCn: string;
  vehTypeDescEn: string;
  vehTypeDescPt: string;
  vehUsageCode: string;
  vehUsageDescCn: string;
  vehUsageDescEn: string;
  vehUsageDescPt: string;
  brandModelCode: number;
  vehBrandCode: string;
  vehBrandDescCn: string;
  vehBrandDescEn: string;
  vehBrandDescPt: string;
  vehModel: string;
  colorCode: string;
  colorDescCn: string;
  colorDescEn: string;
  colorDescPt: string;
  vehBuildYear: number;
  vehCateGory: string;
  vehCateGoryDescCn: string;
  vehCateGoryDescEn: string;
  vehCateGoryDescPt: string;
  vin: string;
  engineNo: string;
  engineNo2: string;
  engineNo3: string;
  engineNo4: string;
  seatQty: number;
  tyreDesc: string;
  tyreFSpec: string;
  tyreRSpec: string;
  tyreSSpec: string;
  frontTyreQty: number;
  rearTyreQty: number;
  frontAxleQty: number;
  rearAxleQty: number;
  vehWeight: number;
  vehGrossWeight: number;
  vehLoadWeight: number;
  vehLength: number;
  vehWidth: number;
  vehHeight: number;
  cabinType: string;
  cabinTypeDescCn: string;
  cabinTypeDescEn: string;
  cabinTypeDescPt: string;
  cabinLength: number;
  cabinWidth: number;
  cabinHeight: number;
  vehSourceCtryCode: string;
  vehSourceCtryDescCn: string;
  vehSourceCtryDescEn: string;
  vehSourceCtryDescPt: string;
  maxPower: number;
  maxPowerWithUnit: string;
  cylinderQty: number;
  cylinderVol: number;
  fuelType: string;
  fuelTypeDescCn: string;
  fuelTypeDescEn: string;
  fuelTypeDescPt: string;
  plateNoUserCn: string;
  plateNoUserPt: string;
  plateNoUserEn: string;
  relatedPlateNo: string;
  newCheckResultCodeDto: any;
};

declare type CompleteResultDTOType = {
  payAmount: number;
  payDate: string;
  payMethod: string;
  payMethodCn: string;
  payMethodEn: string;
  payMethodPt: string;
  payMode: string;
  payStatus: string;
  payStatusCn: string;
  payStatusEn: string;
  payStatusPt: string;
  spNo: string;
  spOperationCode: string;
  txnStatus: string;
  txnStatusCn: string;
  txnStatusEn: string;
  txnStatusPt: string;
  remark: string;
  remarkCn: string;
  remarkEn: string;
  remarkPt: string;
  invoiceNumber: string;
  docNameListCn: any[];
  docNameListPt: any[];
  serviceTitleRespDTO: {
    serviceTitleCn: string;
    serviceTitleEn: string;
    serviceTitlePt: string;
  };
  verficationStatusDate: string;
  txnStatusDate: string;
  exRegDate: string;
  createDate: string;
};
declare type VehRegTopDataDTOType = {
  canCancelTempMountPlate: string;
  canTempMountPlate: string;
  plateNo: string;
  exRegStartDate: string;
  exRegEndDate: string;
  exRegDate: string;
  lastVehRegDate: string;
  vehTodraw: string;
  vehTodrawCn: string;
  vehTodrawEn: string;
  vehTodrawPt: string;
  drawResultNo: string;
  exRegDuration: string;
  tempMountPlateNo: string;
  isShowDrawResult: string;
  drawMsgCodeDto: sysCodesByCodeTypeOpt;
};

declare type DsiMsgCodeDTO = {
  codeCname: string;
  codeEname: string;
  codeId: number;
  codeKey: string;
  codePname: string;
  codeStatus: string;
  codeType: number;
  createDate: string;
  createUser: string;
  draw: number;
  lastUpdDate: string;
  lastUpdUser: string;
  length: number;
  start: number;
};

declare type getEXTxnDataRes = {
  ovsapTxnContactRequDTO: getTxnContactRes;
  vehRegTopDataDTO: VehRegTopDataDTOType; // 確定資料
  completeResultDTO: CompleteResultDTOType; // 提交申請資料
  ownerDataList: saveTxnOwnersParams[]; //車主資料
  ovsapVehDataDTO: ovsapVehDataDTO; //車輛資料
  ovsapAgentCompData: saveAgentCompParams; //進口商資料，代理商資料
  ovsapTxnContactDTO: getTxnContactRes; //聯絡資料
  txnStatus: string;
  txnId: string;
  vehRegTaxTxnCompleteDataDTO: CompleteResultDTOType;
  lastModifyDate: string;
  dsiMsgCodeDTO: DsiMsgCodeDTO;
  dspaFpDataDTO: dspaFpDataDTO;
};

declare type dspaFpDataDTO = {
  txnId: string;
  dspaFpNo: string;
  dspaFpFeeCodeDto: sysCodesByCodeTypeOpt;
};

declare type sendFacialRecognitionECNoticeParams = {
  txnId: string;
  ownerId: string;
  channelType: string;
  identType: string;
  identNo: string;
};

declare type payCodes = {
  codeId: number;
  codeType: number;
  codeKey: string;
  codeStatus: string;
  codePname: string;
  codeCname: string;
  codeEname: string;
  available: boolean;
  payMaintenanceCodeDto: {
    codeCname: string;
    codeEname: string;
    codePname: string;
  };
};
declare type feeItems = {
  zh: string;
  pt: string;
  code: string;
  price: number;
  priceTotal: number;
  taxPriceTotal: number;
  subtotal: number;
};
declare type PayMsgCodeDto = {
  draw: number;
  start: number;
  length: number;
  createUser: string;
  createDate: string;
  lastUpdUser: string;
  lastUpdDate: string;
  codeId: number;
  codeType: number;
  codeKey: string;
  codePname: string;
  codeCname: string;
  codeEname: string;
  codeStatus: string;
};
declare type CancelPayDto = {
  isShowCancelPayButton: boolean;
  payCodeKey: string;
  payMsgCodeDto: PayMsgCodeDto;
};
declare type getServiceFeeRes = {
  payCodes: payCodes[];
  feeItems: feeItems[];
  total: number;
  cancelPayDto: CancelPayDto;
  codeDto?: CodeDTO;
};

declare type completePaymentFee = {
  nameZh: string;
  namePt: string;
  code: string;
  price: number;
  amount: number;
  taxPrice: number;
  taxAmount: number;
  subTotal: number;
};
declare type completePaymentParams = {
  txnId?: string;
  txnIds?: string[];
  payMethodCodeKey: string;
  lang: string;
  ua: string;
  remark: string;
  //allSubTotal: number,
  pageUrl: string;
  //serviceFeeItems: completePaymentFee[]
};
declare type batchPaymentCheckParams = {
  txnList: {
    txnId: string;
    speedUp: string;
  }[];
};

declare type completePaymentRes = {
  status: number;
  payUrl?: string;
  returnHTML?: string;
};

declare type batchPaymentCheckRes = {
  txnIds: string[];
  isNeedToPay: boolean;
};

declare type getBatchExTxnDataRes = {
  batchId: number;
  companyCname: string;
  companyEname: string;
  companyPname: string;
  batchDetailSize: number;
  batchStatus: string;
  licAddrCode: string;
  batchCode: string;
  lastModifyDate: string;
  ovsapBatchExDataDTOs: [];
  addrLicCodeMsgDto: sysCodesByCodeTypeOpt;
  addrLicCodes: addrLicCodes[];
  vemGroup: vemGroup[];
};

declare type addrLicCodes = {
  codeCname: string;
  codeEname: string;
  codeId: number;
  codeKey: string;
  codeOrder: string;
  codePname: string;
  codeStatus: string;
  codeType: string;
  createDate: string;
  createUser: string;
  draw: number;
  lastUpdDate: string;
  lastUpdUser: string;
  length: number;
  start: number;
};

declare type vemGroup = {
  codeCname: string;
  codeEname: string;
  codePname: string;
  codeKey: string;
  detailId: string;
  vehId: string;
  txnId: string;
  plateNo: string;
};

declare type getExTxnCompleteDataRes = {
  spNo: string;
  serviceTitleRespDTO: {
    serviceTitleCn: string;
    serviceTitlePt: string;
    serviceTitleEn: string;
  };
  remark: string;
  remarkCn: string;
  remarkEn: string;
  remarkPt: string;
  invoiceNumber: string;
  payDate: string;
  payMethod: string;
  payMethodCn: string;
  payMethodPt: string;
  payMethodEn: string;
  payMode: string;
  payAmount: number;
  payStatus: string;
  payStatusCn: string;
  payStatusEn: string;
  payStatusPt: string;
  docNameListCn: string[];
  docNameListPt: string[];
  txnStatus: string;
  txnStatusCn: string;
  txnStatusEn: string;
  txnStatusPt: string;
  txnStatusDate: string;
  spOperationCode: string;
  txnId: string;
  vehId: string;
  importNoFull: string;
  vehTypeDescCn: string;
  vehTypeDescPt: string;
  vehTypeDescEn: string;
  pendingApprovalCodeDto: sysCodesByCodeTypeOpt;
};

declare type getBatchExDataPageDataResItem = {
  batchCode: string;
  batchId: number;
  batchStatus: string;
  batchStatusCn: string;
  batchStatusPt: string;
  batchStatusEn: string;
  batchStatusDate: string;
  createDate: string;
  createUser: string;
  draw: number;
  lastModifyDate: string;
  lastUpdDate: string;
  lastUpdUser: string;
  length: number;
  licAddrCode: string;
  licAddrCodeDescCn: string;
  licAddrCodeDescEn: string;
  licAddrCodeDescPt: string;
  start: number;
  batchDetailSize: number;
  companyCname: string;
  companyEname: string;
  companyPname: string;
  imgUrl: string;
  batchMsgCodeDto: sysCodesByCodeTypeOpt;
};

declare type getBatchExDataPageDataRes = {
  data: getBatchExDataPageDataResItem[];
  pageSize: number;
  total: number;
};

declare type serviceTimeCheckParams = {
  operationCode?: string;
  requestBehavior: 'APPLY' | 'SUBMIT';
  serviceId?: number;
  processId?: number;
  stageId?: number;
};

declare type validateVehRegParams = {
  vehType: string;
  exNo: string;
  vinPart: string;
};

declare type createVehRegTxnParams = {
  vehType: string;
  exNo: string;
  vinPart: string;
};
declare type createVehRegTxnRes = {
  txnId: string;
};

declare type getTxnListParams = {
  start: number;
  length: number;
  vehType?: string;
  plateNo?: string;
  startDate?: string;
  endDate?: string;
  spNo?: string;
  status?: string;
  serviceTitle?: string;
  contactMobilePhone?: string;
  ownerIdentType?: string;
  ownerIdentNo?: string;
  txnStatus?: string;
  payStatus?: string;
  exStatus?: string;
  waitVehRegStatus?: string;
  myTxnApplyStatus?: string;
};
declare type pageRes<T> = {
  data: T;
  pageSize: number;
  recordsFiltered: number;
  recordsTotal: number;
  startForm: number;
  total: number;
};
declare type getTxnListResbuttonStatusDTO = {
  appVehButton: boolean;
  deleteButton: boolean;
  updateButton: boolean;
  detailButton: boolean;
  unknowButton: boolean;
  downloadReceiptButton: boolean;
  download33DiiReceiptButton: boolean;
  downloadExButton: boolean;
  downloadRegDocButton: boolean;
  completeButton: boolean;
  showSpeedUpCheckbox: boolean;
  popWinMsg33DIICn: string;
  popWinMsg33DIIPt: string;
  popWinMsg33DIIEn: string;
};
declare type getTxnListRes = {
  isCanComplete: boolean;
  spNo: string;
  txnId: string;
  nextTxnId: string;
  titleCn: string;
  titlePt: string;
  titleEn: string;
  vin: string;
  spOperationCode: string;
  spOperationCn: string;
  spOperationEn: string;
  spOperationPt: string;
  speedUp?: string;

  plateNo: string;
  vehType: string;
  vehId: string;
  vehTypeDescCn: string;
  vehTypeDescEn: string;
  vehTypeDescPt: string;

  vtaNo: string;
  vtaNoFull: string;
  importNo: string;
  importNoFull: string;

  vehBrandCode: string;
  vehBrandDescCn: string;
  vehBrandDescEn: string;
  vehBrandDescEn: string;
  vehModel: string;

  batchId: number;
  detailStatus: string;
  detailStatusCn: string;
  detailStatusEn: string;
  detailStatusPt: string;

  txnStatus: string;
  txnStatusCn: string;
  txnStatusEn: string;
  txnStatusPt: string;
  payStatus: string;
  payStatusCn: string;
  payStatusEn: string;
  payStatusPt: string;

  ownerDataList: saveTxnOwnersParams[];
  buttonStatusDTO: getTxnListResbuttonStatusDTO;

  serviceTitleNameCn: string;
  serviceTitleNameEn: string;
  serviceTitleNamePt: string;

  promptMsgCn: string;
  promptMsgPt: string;
  promptMsgEn: string;

  noPlateNoMsgDTO: sysCodesByCodeTypeOpt;

  isNeedToPay: boolean;
};

declare type getTxnStatusCountRes = {
  allCount: number;
  activeCount: number;
  waitPayCount: number;
  waitApprovalCount: number;
  finishCount: number;
  waitGetExCount: number;
  alreadyReceivedExCount: number;
  waitVehRegCount: number;
  myTxnApplyCount: number;
};

declare type getServiceOptions = {
  serviceCode: string;
  serviceTitleCn: string;
  serviceTitlePt: string;
  serviceTitleEn: string;
};

declare type NoticeDTOType = {
  spNo: string;
  txnId: string;
  titleCn: string;
  titlePt: string;
  titleEn: string;
  createUser: string;
  createDate: string;
  lastUpdUser: string;
  lastUpdDate: string;
  codeId: number;
  codeType: number;
  codeKey: string;
  codePname: string;
  codeCname: string;
  codeEname: string;
  codeStatus: string;
  codePrintPname: string;
  codePrintCname: string;
  codePrintEname: string;
  codeOrder: number;
  codeDesc: string;
  parentCodeType: number;
  parentCodeKey: string;
};

declare type VehRegTaxTxnResType = {
  exNo: string;
  vehType: string;
  vehTypeDescCn: string;
  vehTypeDescEn: string;
  vehTypeDescPt: string;
  drawResultNo: string;
  tempMountPlateType: string;
  tempMountPlateNo: string;
  vehBrandCode: string | null;
  vehBrandDescCn: string | null;
  vehBrandDescEn: string | null;
  vehBrandDescPt: string | null;
  vehModel: string | null;
  speedUp: string | null;
  ownerDataList: saveTxnOwnersParams[];
  noticeDTO: NoticeDTOType;
  txnStatus: string;
};

declare type ValidateTxnForCompleteVehReg = {
  isNeedToPay: string;
};

declare type CreateVehRegTaxTxn = {
  txnId: string;
};

declare type TxnListParams = {
  txnId: string;
  speedUp: string;
};

declare type FeeItemDTO = {
  code: string;
  en: string;
  price: number;
  priceTotal: number;
  pt: string;
  showFeeRemarkCn: string;
  showFeeRemarkEn: string;
  showFeeRemarkPt: string;
  spFeeRemarkCn: string;
  spFeeRemarkEn: string;
  subtotal: number;
  taxPriceTotal: number;
  zh: string;
};

declare type TxnFeeItems = {
  txnId: string;
  feeItemDTOList: FeeItemDTO[];
};

declare type getServiceFeesInBatchRes = {
  cancelPayDto?: CancelPayButtonDTO;
  payCodes: payCodes[];
  txnFeeItemList: txnFeeItems[];
  total: number;
};
declare type CancelPayButtonDTO = {
  isShowCancelPayButton?: boolean;
  payCodeKey?: string;
  payMsgCodeDto?: CodeDTO;
};
declare type CodeDTO = {
  codeCname?: string;
  codeDesc?: string;
  codeEname?: string;
  codeId?: number;
  codeKey?: string;
  codeOrder?: number;
  codePname?: string;
  codePrintCname?: string;
  codePrintEname?: string;
  codePrintPname?: string;
  codeStatus?: string;
  codeType?: number;
  columns?: Columns[];
  createDate?: string;
  createUser?: string;
  draw?: number;
  lastUpdDate?: string;
  lastUpdUser?: string;
  length?: number;
  order?: Order[];
  parentCodeKey?: string;
  parentCodeType?: number;
  search?: Search;
  spNo?: string;
  start?: number;
  titleCn?: string;
  titleEn?: string;
  titlePt?: string;
  txnId?: string;
};
declare type validateCustomPlateNo = {
  msgCname: string;
  msgEname: string;
  msgPname: string;
};

declare type StepOptionsItem = {
  sort: number;
  title: string;
  steps: number[];
};

/**
 * TxnJumpPageDTO
 *
 * mo.gov.dsat.ovsap.core.dto.vem.TxnJumpPageDTO
 */
declare type TxnJumpPageDTO = {
  /**
   * 是否顯示完成服務頁面：true：顯示，false：不顯示
   */
  isShowCompletePage?: boolean;
  /**
   * 是否顯示等待支付審批頁面：true：顯示，false：不顯示
   */
  isShowPendingPage?: boolean;
  /**
   * 交易編號，儅isShowPendingPage為true時，有值
   */
  transId: string;
};

declare type SaveVehRegTaxTxnParams = {
  txnId: string;
  speedUp: string;
};

declare type CheckBatch33DiiParams = {
  txnId: string;
  speedUp: string;
};

declare type CheckBatch33DiiModel = {
  plateNoDTOList: {
    txnId: string;
    exNo: string;
    plateNo: string;
    payType: boolean;
    speedUp: string;
    vehType: string;
    vehTypeDescCn: string;
    vehTypeDescEn: string;
    vehTypeDescPt: string;
  }[];
  isNeedToPay: boolean;
  txnIds: string[];
};
declare type BatchCompleteOvsapTxnModel = {
  batchId: string;
  txnIds: string[];
};

declare type VtaGetSysCodeByTypeRdModel = {
  codeId: number;
  codeType: number;
  codeKey: string;
  codePname: string;
  codeCname: string;
  codeEname: string;
  codeStatus: string;
  codePrintPname: string;
  codeOrder: number;
  createUser: string;
  createDate: string;
  lastUpdUser: string;
  lastUpdDate: string;
};

/**
 * 數據
 *
 * OvsapVtaFeaturesDTO
 */
declare type OvsapVtaFeaturesDTO = {
  /**
   * 傷殘位數量
   */
  disablilitySeatQty?: number;
  /**
   * 車門數量
   */
  doorQty?: number;
  /**
   * 座位長1(mm)
   */
  driverSeatLength?: number;
  /**
   * 座位寬1(mm)
   */
  driverSeatWidth?: number;
  /**
   * 緊急出口數量
   */
  emergencyExitQty?: number;
  /**
   * 載客量
   */
  passCapacity?: number;
  /**
   * 乘客腳踏型式
   */
  passFootholdType?: string;
  /**
   * 扶手位置
   */
  passHandrailType?: string;
  /**
   * 座位長2(mm)
   */
  passSeatLength?: number;
  /**
   * 座位寬2(mm)
   */
  passSeatWidth?: number;
  /**
   * 座位數量
   */
  seatQty?: number;
  /**
   * 座椅尺寸(mm) 單一 獨立
   * 椅尺寸(mm) 單一 獨立
   */
  seatType?: string;
  /**
   * 座位長3(mm)
   */
  sideSeatLength?: number;
  /**
   * 座位寬3(mm)
   */
  sideSeatWidth?: number;
  /**
   * 站立位數量
   */
  standQty?: number;
  /**
   * 導遊位數量
   */
  touristGuideQty?: number;
  /**
   * 外鍵關聯OVSAP_TXN表
   */
  txnId?: string;
  /**
   * 出產國或地區
   */
  vehBuildCtryCode?: string;
  /**
   * 出廠年份
   */
  vehBuildYear?: number;
  /**
   * 車輛種類
   */
  vehCategory?: string;
  /**
   * 前照燈遠光光束
   */
  vehHeadlamp?: string;
  /**
   * 來源國或地區
   */
  vehSourceCtryCode?: string;
  /**
   * 重型客車類別
   */
  vtaUsageCode?: string;
  [property: string]: any;
};
/**
 * 數據
 *
 * OvsapVTAPowerDeviceDTO
 */
export interface OvsapVTAPowerDeviceDTO {
    /**
     * 進氣方式
     */
    aspiration?: string;
    /**
     * 口徑(mm)
     */
    bore?: number;
    /**
     * 汽缸數量
     */
    cylinderQty?: number;
    /**
     * 汽缸容積 (c.c.)
     * 缸容積 (c.c.)
     */
    cylinderVol?: number;
    /**
     * 內燃機-動力來源
     */
    engineInjection?: string;
    /**
     * 動力裝置-1-序號
     */
    engineNo?: string;
    /**
     * 動力裝置-2-序號
     */
    engineNo2?: string;
    /**
     * 動力裝置-3-序號
     */
    engineNo3?: string;
    /**
     * 動力裝置-4-序號
     */
    engineNo4?: string;
    /**
     * 動力裝置-1-識別碼
     */
    enginePattern1?: string;
    /**
     * 動力裝置-2-識別碼
     */
    enginePattern2?: string;
    /**
     * 動力裝置-3-識別碼
     */
    enginePattern3?: string;
    /**
     * 動力裝置-4-識別碼
     */
    enginePattern4?: string;
    /**
     * 燃料箱容量 (L)
     */
    fuelTankVol?: number;
    /**
     * 能源種類
     */
    fuelType?: string;
    /**
     * 動力裝置-綜合-最大功率
     */
    maxPower?: number;
    /**
     * 動力裝置-1-最大功率
     */
    maxPower1?: number;
    /**
     * 動力裝置-2-最大功率
     */
    maxPower2?: number;
    /**
     * 動力裝置-3-最大功率
     */
    maxPower3?: number;
    /**
     * 動力裝置-4-最大功率
     */
    maxPower4?: number;
    /**
     * 動力裝置-綜合-最大功率-轉速(rpm)
     */
    maxPowerRpm?: number;
    /**
     * 動力裝置-1-最大功率-轉速(rpm)
     */
    maxPowerRpm1?: number;
    /**
     * 動力裝置-2-最大功率-轉速(rpm)
     */
    maxPowerRpm2?: number;
    /**
     * 動力裝置-3-最大功率-轉速(rpm)
     */
    maxPowerRpm3?: number;
    /**
     * 動力裝置-4-最大功率-轉速(rpm)
     */
    maxPowerRpm4?: number;
    /**
     * 動力裝置-綜合-最大功率-單位
     */
    maxPowerUnitType?: string;
    /**
     * 動力裝置-1-最大功率-單位
     */
    maxPowerUnitType1?: string;
    /**
     * 動力裝置-2-最大功率-單位
     */
    maxPowerUnitType2?: string;
    /**
     * 動力裝置-3-最大功率-單位
     */
    maxPowerUnitType3?: string;
    /**
     * 動力裝置-4-最大功率-單位
     */
    maxPowerUnitType4?: string;
    /**
     * 動力裝置-綜合-最大扭力
     */
    maxTorque?: number;
    /**
     * 動力裝置-1-最大扭力
     */
    maxTorque1?: number;
    /**
     * 動力裝置-2-最大扭力
     */
    maxTorque2?: number;
    /**
     * 動力裝置-3-最大扭力
     */
    maxTorque3?: number;
    /**
     * 動力裝置-4-最大扭力
     */
    maxTorque4?: number;
    /**
     * 動力裝置-綜合-最大扭力-轉速(rpm)
     */
    maxTorqueRpm?: number;
    /**
     * 動力裝置-1-最大扭力-轉速(rpm)
     */
    maxTorqueRpm1?: number;
    /**
     * 動力裝置-2-最大扭力-轉速(rpm)
     */
    maxTorqueRpm2?: number;
    /**
     * 動力裝置-3-最大扭力-轉速(rpm)
     */
    maxTorqueRpm3?: number;
    /**
     * 動力裝置-4-最大扭力-轉速(rpm)
     */
    maxTorqueRpm4?: number;
    /**
     * 動力裝置-綜合-最大扭力-單位
     */
    maxTorqueUnitCode?: string;
    /**
     * 動力裝置-1-最大扭力-單位
     */
    maxTorqueUnitCode1?: string;
    /**
     * 動力裝置-2-最大扭力-單位
     */
    maxTorqueUnitCode2?: string;
    /**
     * 動力裝置-3-最大扭力-單位
     */
    maxTorqueUnitCode3?: string;
    /**
     * 動力裝置-4-最大扭力-單位
     */
    maxTorqueUnitCode4?: string;
    /**
     * 能源種類-動力來源
     */
    powerSource?: string;
    /**
     * 衝程(mm)
     */
    stroke?: number;
    /**
     * 外鍵關聯OVSAP_TXN表
     */
    txnId?: string;
    /**
     * 氣閥數量
     */
    valveQty?: number;
    [property: string]: any;
}
/**
 * 數據
 *
 * OvsapVTAVehGasDTO
 */
export interface OvsapVTAVehGasDTO {
    /**
     * 一氧化碳(CO)g/km (摩托車)
     * 尾氣排放結果值(摩托車)-一氧化碳(CO)g/km (摩托車)
     */
    co?: number;
    /**
     * --------------------尾氣排放結果值(非摩托車)--------------------
     * 尾氣排放結果值(非摩托車)-一氧化碳(CO) (mg/km)
     */
    coMg?: number;
    /**
     * 一氧化碳 (CO) %(摩托車)
     * 尾氣排放結果值(摩托車)-一氧化碳 (CO) %(摩托車)
     */
    coPercent?: number;
    /**
     * 環保車申請
     * 尾氣排放項目(非摩托車)-環保車申請
     */
    efApplied?: string;
    /**
     * --------------------WHSC--------------------
     * WHSC-一氧化碳(CO) (mg/km)
     */
    escCoGKwh?: number;
    /**
     * 氨(NH3) (ppm)
     * WHSC-氨(NH3) (ppm)
     */
    escNh3Ppm?: number;
    /**
     * 非甲烷碳氫化合物(NMHC) (mg/kWh)
     * WHSC-非甲烷碳氫化合物(NMHC) (mg/kWh)
     */
    escNmhcMgKwh?: number;
    /**
     * 氮氧化物(NOx) (mg/kWh)
     * WHSC-氮氧化物(NOx) (mg/kWh)
     */
    escNoxGKwh?: number;
    /**
     * 顆粒物(PM) (mg/km)
     * WHSC-顆粒物(PM) (mg/km)
     */
    escPmGKwh?: number;
    /**
     * 粒子數量(PN) (x 10#/kWh)
     * WHSC-粒子數量(PN) (x 10#/kWh)
     */
    escPnCoefficient?: number;
    /**
     * 總碳氫化合物(THC) (mg/kWh)
     * WHSC-總碳氫化合物(THC) (mg/kWh)
     */
    escThcGKwh?: number;
    /**
     * 甲烷(CH4) (mg/kWh)
     * WHTC(JE05M)-甲烷(CH4) (mg/kWh)
     */
    etcCh4GKwh?: number;
    /**
     * 一氧化碳(CO) (g/bhp-hr)
     * WHTC(JE05M)-一氧化碳(CO) (g/bhp-hr)
     */
    etcCoGBhpHr?: number;
    /**
     * --------------------WHTC(JE05M)--------------------
     * WHTC(JE05M)-一氧化碳(CO) (mg/km)
     */
    etcCoGKwh?: number;
    /**
     * 一氧化碳(CO) (g/mi)
     * WHTC(JE05M)-一氧化碳(CO) (g/mi)
     */
    etcCoGMi?: number;
    /**
     * 煙度(ELR) (%)
     * WHTC(JE05M)-煙度(ELR) (%)
     */
    etcElrPercent?: number;
    /**
     * 甲醛(HCHO) (g/mi)
     * WHTC(JE05M)-甲醛(HCHO) (g/mi)
     */
    etcHchoGMi?: number;
    /**
     * 氨(NH3) (ppm)
     * WHTC(JE05M)-氨(NH3) (ppm)
     */
    etcNh3Ppm?: number;
    /**
     * 非甲烷碳氫化合物(NMHC) (g/bhp-hr)
     * WHTC(JE05M)-非甲烷碳氫化合物(NMHC) (g/bhp-hr)
     */
    etcNmhcGBhpHr?: number;
    /**
     * 非甲烷碳氫化合物(NMHC) (mg/kWh)
     * WHTC(JE05M)-非甲烷碳氫化合物(NMHC) (mg/kWh)
     */
    etcNmhcGKwh?: number;
    /**
     * 非甲烷有機氣體(NMOG) (g/mi)
     * WHTC(JE05M)-非甲烷有機氣體(NMOG) (g/mi)
     */
    etcNmog?: number;
    /**
     * 氮氧化物(NOx) (g/bhp-hr)
     * WHTC(JE05M)-氮氧化物(NOx) (g/bhp-hr)
     */
    etcNoxGBhpHr?: number;
    /**
     * 氮氧化物(NOx) (mg/kWh)
     * WHTC(JE05M)-氮氧化物(NOx) (mg/kWh)
     */
    etcNoxGKwh?: number;
    /**
     * 氮氧化物(NOx) (g/mi)
     * WHTC(JE05M)-氮氧化物(NOx) (g/mi)
     */
    etcNoxGMi?: number;
    /**
     * 顆粒物(PM) (g/bhp-hr)
     * WHTC(JE05M)-顆粒物(PM) (g/bhp-hr)
     */
    etcPmGBhpHr?: number;
    /**
     * 顆粒物(PM) (mg/km)
     * WHTC(JE05M)-顆粒物(PM) (mg/km)
     */
    etcPmGKwh?: number;
    /**
     * 顆粒物(PM) (g/mi)
     * WHTC(JE05M)-顆粒物(PM) (g/mi)
     */
    etcPmGMi?: number;
    /**
     * 粒子數量(PN) (x 10#/kWh)
     * WHTC(JE05M)-粒子數量(PN) (x 10#/kWh)
     */
    etcPnCoefficient?: number;
    /**
     * 總碳氫化合物(THC) (mg/kWh)
     * WHTC(JE05M)-總碳氫化合物(THC) (mg/kWh)
     */
    etcThcMgKwh?: number;
    /**
     * --------------------尾氣排放項目(非摩托車)--------------------
     * 尾氣排放項目(非摩托車)-尾氣排放報告所載標準
     */
    exhaustStd?: string;
    /**
     * 排放測試程序
     * 尾氣排放項目(非摩托車)-排放測試程序
     */
    exhaustTestProg?: string;
    /**
     * 燃料效率(km/L)
     * 尾氣排放項目(非摩托車)-燃料效率(km/L)
     */
    fuelEfficiency?: number;
    /**
     * 測試質量(kg)
     * 尾氣排放項目(非摩托車)-測試質量(kg)
     */
    fuelEffTestMass?: number;
    /**
     * 燃料效率測試程序
     * 尾氣排放項目(非摩托車)-燃料效率測試程序
     */
    fuelEffTestProg?: string;
    /**
     * 最高車速(km/h)
     * 尾氣排放項目(非摩托車)-最高車速(km/h)
     */
    fuelEffTestTopSpeed?: number;
    /**
     * 車重(kg)
     * 尾氣排放項目(非摩托車)-車重(kg)
     */
    fuelEffTestVehWeight?: number;
    /**
     * --------------------尾氣排放結果值(摩托車)--------------------
     * 尾氣排放結果值(摩托車)-碳氫化合物(HC) g/km (摩托車)
     */
    hc?: number;
    /**
     * 碳氫化合物+氮氧化物(THC+Nox) g/km (摩托車)
     * 尾氣排放結果值(摩托車)-碳氫化合物+氮氧化物(THC+Nox) g/km (摩托車)
     */
    hcNox?: number;
    /**
     * 碳氫化合物 (HC) ppm (摩托車)
     * 尾氣排放結果值(摩托車)-碳氫化合物 (HC) ppm (摩托車)
     */
    hcPpm?: number;
    /**
     * 氧化亞氮(N2O) (mg/km)
     * 尾氣排放結果值(非摩托車)-氧化亞氮(N2O) (mg/km)
     */
    n2o?: number;
    /**
     * 非甲烷碳氫化合物(NMHC) (mg/km)
     * 尾氣排放結果值(非摩托車)-非甲烷碳氫化合物(NMHC) (mg/km)
     */
    nmhc?: number;
    /**
     * 氮氧化物(NOx) g/km (摩托車)
     * 尾氣排放結果值(摩托車)-氮氧化物(NOx) g/km (摩托車)
     */
    nox?: number;
    /**
     * 使用NOX儲存還原催化淨化技術的汽油直噴發動機
     * 尾氣排放項目(非摩托車)-使用NOX儲存還原催化淨化技術的汽油直噴發動機
     */
    noxDirectInjectEngine?: string;
    /**
     * 氮氧化物(NOx) (mg/km)
     * 尾氣排放結果值(非摩托車)-氮氧化物(NOx) (mg/km)
     */
    noxMg?: number;
    /**
     * 顆粒物(PM) (mg/km)
     * 尾氣排放結果值(非摩托車)-顆粒物(PM) (mg/km)
     */
    pm?: number;
    /**
     * 粒狀污染物(PM)%不透光率 (摩托車)
     * 尾氣排放結果值(摩托車)-粒狀污染物(PM)%不透光率 (摩托車)
     */
    pmPercent?: number;
    /**
     * 粒子數量(PN) (x 10#/kWh)
     * 尾氣排放結果值(非摩托車)-粒子數量(PN) (x 10#/kWh)
     */
    pnCoefficient?: number;
    /**
     * 總碳氫化合物(THC) (mg/kWh)
     * 尾氣排放結果值(非摩托車)-總碳氫化合物(THC) (mg/kWh)
     */
    thc?: number;
    /**
     * 總碳氫化合物+氮氧化物(THC+Nox) (mg/km)
     * 尾氣排放結果值(非摩托車)-總碳氫化合物+氮氧化物(THC+Nox) (mg/km)
     */
    thcNox?: number;
    /**
     * 外鍵關聯OVSAP_TXN表
     */
    txnId?: string;
    [property: string]: any;
}
/**
 * 數據
 *
 * OvsapVTAVehBodyDTO
 */
export interface OvsapVTAVehBodyDTO {
    /**
     * 車軸-軸荷 - 第一軸 (kg)
     */
    axleLoad1?: number;
    /**
     * 車軸-軸荷 - 第二軸 (kg)
     */
    axleLoad2?: number;
    /**
     * 車軸-軸荷 - 第三軸 (kg)
     */
    axleLoad3?: number;
    /**
     * 車軸-軸荷 - 第四軸 (kg)
     */
    axleLoad4?: number;
    /**
     * 車軸-軸荷 - 第五軸 (kg)
     */
    axleLoad5?: number;
    /**
     * 車軸-軸荷 - 牽引軸軸 (kg)
     */
    axleLoadTraction?: number;
    /**
     * 車軸-轉向系統 - 雙轉向軸
     */
    dualFrontAxle?: string;
    /**
     * 車軸-數量 - 前
     */
    frontAxleQty?: number;
    /**
     * 行車制動系統-前
     */
    frontBrakeType?: string;
    /**
     * 前懸掛
     */
    frontSuspension?: string;
    /**
     * 前懸掛數量
     */
    frontSuspensionQty?: number;
    /**
     * 車輪-數量 - 前
     */
    frontTyreQty?: number;
    /**
     * 駐車制動-種類
     */
    handBrake?: string;
    /**
     * 防鎖死煞車系統
     */
    hasAbsBrake?: string;
    /**
     * 前懸掛為懸掛系統
     */
    indepSuspension?: string;
    /**
     * 車軸-軸荷 - 其他軸 (kg)
     */
    otherAxleLoad?: number;
    /**
     * 駐車制動-駐車軸
     */
    parkingAxle?: string;
    /**
     * 車軸-數量 - 後
     */
    rearAxleQty?: number;
    /**
     * 行車制動系統-後
     */
    rearBrakeType?: string;
    /**
     * 後懸掛
     */
    rearSuspension?: string;
    /**
     * 後懸掛數量
     */
    rearSuspensionQty?: number;
    /**
     * 車輪-數量 - 後
     */
    rearTyreQty?: number;
    /**
     * 外鍵關聯OVSAP_TXN表
     */
    txnId?: string;
    /**
     * 識別碼
     */
    vin?: string;
    /**
     * 序號
     */
    vinPattern?: string;
    /**
     * 輪胎資料
     */
    vtaTyreDTOList?: OvsapVtaTyreDTO[];
    [property: string]: any;
}

/**
 * <description>
 *
 * OvsapVtaTyreDTO
 */
export interface OvsapVtaTyreDTO {
    /**
     * 創建日期
     */
    createDate?: string;
    /**
     * 創建用戶
     */
    createUser?: string;
    /**
     * 最後更新日期
     */
    lastUpdDate?: string;
    /**
     * 最後更新用戶
     */
    lastUpdUser?: string;
    /**
     * 外鍵關聯OVSAP_TXN表
     */
    txnId?: string;
    /**
     * 前輪胎規格-寬度
     */
    tyreFSpec1?: string;
    /**
     * 前輪胎規格-平率
     */
    tyreFSpec2?: string;
    /**
     * 前輪胎規格-直徑
     */
    tyreFSpec3?: string;
    /**
     * 後輪胎規格-寬度
     */
    tyreRSpec1?: string;
    /**
     * 後輪胎規格-平率
     */
    tyreRSpec2?: string;
    /**
     * 後輪胎規格-直徑
     */
    tyreRSpec3?: string;
    /**
     * 輪胎次序
     */
    tyreSeq?: number;
    /**
     * 旁卡輪胎規格-寬度
     */
    tyreSSpec1?: string;
    /**
     * 旁卡輪胎規格-平率
     */
    tyreSSpec2?: string;
    /**
     * 旁卡輪胎規格-直徑
     */
    tyreSSpec3?: string;
    /**
     * 輪胎規格單位(22015)：I = 英制 (Imperial), M = 公制 (Metric)
     */
    tyreUnit?: string;
    /**
     * UUID
     */
    vtaTyreId?: string;
    [property: string]: any;
}
/**
 * 數據
 *
 * OvsapVTAHomoBatteryDTO
 */
export interface Request {
    /**
     * 充電接口規格-交流
     * 充電接口規格-直流
     */
    acPlugin?: string;
    /**
     * 換電模式
     */
    batterySwapMode?: string;
    /**
     * 充電接口規格-直流
     */
    dcPlugin?: string;
    /**
     * 外鍵關聯OVSAP_TXN表
     */
    txnId?: string;
    /**
     * 電池資料
     */
    vtaBatteryDTOList?: OvsapVTABatteryDTO[];
    [property: string]: any;
}

/**
 * <description>
 *
 * OvsapVTABatteryDTO
 */
export interface OvsapVTABatteryDTO {
    /**
     * 電池容量
     */
    batteryCapacity?: number;
    /**
     * 電池型號
     */
    batteryModel?: string;
    /**
     * 電池次序
     */
    batterySeq?: number;
    /**
     * 創建日期
     */
    createDate?: string;
    /**
     * 創建用戶
     */
    createUser?: string;
    /**
     * 最後更新日期
     */
    lastUpdDate?: string;
    /**
     * 最後更新用戶
     */
    lastUpdUser?: string;
    /**
     * 外鍵關聯OVSAP_TXN表
     */
    txnId?: string;
    /**
     * UUID
     */
    vtaBatteryId?: string;
    [property: string]: any;
}
/**
 * OvsapVTAMainTxnParamsDTO
 */
export interface OvsapVTAMainTxnParamsDTO {
    /**
     * 型號編碼
     */
    customModelCode?: string;
    /**
     * 評稅編號
     */
    dsfRefNo?: string;
    /**
     * 變速器 codeType:31223
     */
    gearboxType?: string;
    /**
     * 是否有評稅編號 有為Y，沒有為N
     */
    hasDsfRefNo?: string;
    /**
     * 軚盤位置Ｅ＝左盤, D=右盤 codeType:50118
     */
    steeringWheelPosition?: string;
    /**
     * txnId
     */
    txnId?: string;
    /**
     * 傳動方式
     */
    txType?: string;
    /**
     * 檔數
     */
    varSpeedQty?: number;
    /**
     * 商標code codeType:31204
     */
    vehBrandCode?: string;
    /**
     * 型號
     */
    vehModel?: string;
    /**
     * 款式年份
     */
    vehModelYear?: number;
    /**
     * 車輛級別 codeType:31201
     */
    vehType?: string;
    [property: string]: any;
}
/**
 * OvsapVTAMainTxnParamsDTO
 */
export interface CheckAddBrandModelDTO {
    /**
     * 型號編碼
     */
    customModelCode?: string;
    /**
     * 評稅編號
     */
    dsfRefNo?: string;
    /**
     * 變速器 codeType:31223
     */
    gearboxType?: string;
    /**
     * 是否有評稅編號 有為Y，沒有為N
     */
    hasDsfRefNo?: string;
    /**
     * 軚盤位置Ｅ＝左盤, D=右盤 codeType:50118
     */
    steeringWheelPosition?: string;
    /**
     * txnId
     */
    txnId?: string;
    /**
     * 傳動方式
     */
    txType?: string;
    /**
     * 檔數
     */
    varSpeedQty?: number;
    /**
     * 商標code codeType:31204
     */
    vehBrandCode?: string;
    /**
     * 型號
     */
    vehModel?: string;
    /**
     * 款式年份
     */
    vehModelYear?: number;
    /**
     * 車輛級別 codeType:31201
     */
    vehType?: string;
    [property: string]: any;
}
/**
 * OvsapVTAMainTxnParamsDTO
 */
export interface CreateVTATxnDTO {
    /**
     * 型號編碼
     */
    customModelCode?: string;
    /**
     * 評稅編號
     */
    dsfRefNo?: string;
    /**
     * 變速器 codeType:31223
     */
    gearboxType?: string;
    /**
     * 是否有評稅編號 有為Y，沒有為N
     */
    hasDsfRefNo?: string;
    /**
     * 軚盤位置Ｅ＝左盤, D=右盤 codeType:50118
     */
    steeringWheelPosition?: string;
    /**
     * txnId
     */
    txnId?: string;
    /**
     * 傳動方式
     */
    txType?: string;
    /**
     * 檔數
     */
    varSpeedQty?: number;
    /**
     * 商標code codeType:31204
     */
    vehBrandCode?: string;
    /**
     * 型號
     */
    vehModel?: string;
    /**
     * 款式年份
     */
    vehModelYear?: number;
    /**
     * 車輛級別 codeType:31201
     */
    vehType?: string;
    [property: string]: any;
}
