/**
 * 国际化问题修复
 * https://iwhale-citybrain.yuque.com/docs/share/9ea867f0-3285-43af-8907-b742c9407cf9?# 《如何为你的项目添加国际化配置（umi@3的国际化实践）》
 */
import { getAllLocales, setIntl, history } from 'umi';
import { LANG_CHANGE_EVENT, event } from '@@/plugin-locale/locale';
import { getLanguageFromUrl, transform2HantFormat, transform2MoFormat } from '@/locales/lang';
import { setMomentLocale } from './utils/setMomentLocale';

/* 自动将用户国际化配置转换为当前可用配置 */
function parseLanguage(pLang = '') {
  const lang = transform2MoFormat(pLang);
  const availableLanguages = getAllLocales();
  try {
    // 完全匹配
    if (availableLanguages.includes(lang)) {
      setMomentLocale(lang);
      return lang;
    }

    // 前缀匹配
    const matchedLang = availableLanguages.find(
      (aLang = '') => aLang.split('-')[0] === lang.split('-')[0],
    );
    if (matchedLang) {
      setMomentLocale(matchedLang);
      return matchedLang;
    }
  } catch (e) {
    console.error('Error while parse user\'s locale');
    console.error(e);
  }
  setMomentLocale('zh-MO');
  return /* availableLanguages[0] ||  */ 'zh-MO';
}

const locale = {
  /* 覆盖 @umijs/plugin-locale 的getLocale逻辑，保证取得的国际化配置，都是当前可用的 */
  getLocale() {
    // 1. 判断urlQuery上是否存在显示指定的language属性
    const localeFromUrl = getLanguageFromUrl(); // localeFromUrl: 'zh-Hant'|'zh-Hans'|'en'|'pt'
    if (localeFromUrl) {
      return parseLanguage(localeFromUrl);
    }
    // 2. 判断localStorage中是否存在国际化配置
    const localeFormLocalStorage = window.localStorage.getItem('umi_locale');
    if (localeFormLocalStorage) {
      return parseLanguage(localeFormLocalStorage);
    }
    // 3. 从navigator中获取国际化配置
    const localeFormNavigator = window.navigator.language;
    return parseLanguage(localeFormNavigator);
  },

  setLocale({
    lang,
    realReload,
  }: {
    // 跟随src/locale文件夹下文件名称变化
    lang: 'zh-MO' | 'zh-CN' | 'pt-PT' | 'en-US';
    realReload?: boolean;
  }) {
    const localeExp = new RegExp('^([a-z]{2})-?([A-Z]{2})?$');
    if (lang !== undefined && !localeExp.test(lang)) {
      // for reset when lang === undefined
      throw new Error('setLocale lang format error');
    }
    if (window.localStorage.getItem('umi_locale') !== lang) {
      if (typeof window.localStorage !== 'undefined') {
        window.localStorage.setItem('umi_locale', lang || '');
      }

      // 如果 url 中存在 language 字段，则需要修改 query
      const { location } = history;
      const { language: localeFromUrl } = location.query || {};

      if (localeFromUrl) {
        history.replace({
          pathname: location.pathname,
          state: location.state,
          hash: location.hash,
          query: {
            ...location.query,
            language: transform2HantFormat(lang),
          },
        });
      }

      setIntl(lang);
      if (realReload) {
        window.location.reload();
      } else {
        event.emit(LANG_CHANGE_EVENT, lang);
        // chrome 不支持这个事件。所以人肉触发一下
        if (window.dispatchEvent) {
          const event = new window.Event('languagechange');
          window.dispatchEvent(event);
        }
      }
    }
  },
};

export default locale;
