import { useEffect, useCallback } from 'react';
import { /*  PublicModelsState, useSelector, */ useDispatch } from 'umi';

/**
 * @param {object} query location.query
 */
const useTheme = (query) => {
  const dispatch = useDispatch();

  // const publicModel = useSelector(({ public: model }: { public: PublicModelsState }) => model);
  // const { isDark } = publicModel;

  /**
   * 设置深色模式
   * @param {boolean} status
   */
  const setIsDark = useCallback(
    (status: boolean) => {
      dispatch({ type: 'public/update', isDark: !!status });
      if (status) {
        document.querySelector('body')?.classList.add('theme-dark');
      } else {
        document.querySelector('body')?.classList.remove('theme-dark');
      }
    },
    [dispatch],
  );

  const setIsBusiness = useCallback(
    (status: boolean) => {
      dispatch({ type: 'public/update', isBusiness: !!status });
      if (status) {
        document.querySelector('body')?.classList.add('theme-blue');
        document.querySelector('body')?.classList.add('business');
      } else {
        document.querySelector('body')?.classList.remove('theme-blue');
        document.querySelector('body')?.classList.remove('business');
      }
    },
    [dispatch],
  );

  useEffect(() => {
    const { isDark } = query;
    if (isDark === 'true') {
      setIsDark(true);
    } else if (isDark === 'false') {
      setIsDark(false);
    }
  }, [query, setIsDark]);

  useEffect(() => {
    PLATFORM === 'business' && setIsBusiness(true);
  }, []);
};

export default useTheme;
