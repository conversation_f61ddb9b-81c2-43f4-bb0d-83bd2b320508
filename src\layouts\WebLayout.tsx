import React, { useState, useEffect } from 'react';
import { history, getLocale } from 'umi';
import { useMount } from 'ahooks';
import classPrefix from '@ali-whale/class-prefix';
import { zhHant } from '@gov-mo/components/esm/locales/pc';
import {  ConfigProvider, message } from 'antd';

// import Header from './components/Header';
import './WebLayout.less';
import { getLocale2ComponentLocaleMap } from '@/locales/lang';
import { updateLocale } from '@/utils/languageUtils';

import { subscribe } from '@/utils/customEvent';
import { EMIT_KEY_MESSAGE_GLOBLE_ERROR, MESSAGE_GLOBLE_ERROR } from '@/utils/const';
import { CloseOutlined } from '@ant-design/icons';

const px = classPrefix('web-layout');

const WebLayout = function ({ children }) {
  const [loaded, setLoaded] = useState(false);

  const {
    location: { query = {}},
  } = history;
  const { language } = query as Record<string, string>;
  useMount(() => {
    updateLocale(language);
    setLoaded(true);
  });

  const defaultAntdLocale = {
    ...(require('@gov-mo/components/esm/locales/pc')[getLocale2ComponentLocaleMap[getLocale()]] ??
      zhHant),
  };

  const LOADING_KEY = 'loadingKey'
  const [messageApi, contextHolder] = message.useMessage();
  const [showLoading, setShowLoading] = useState<boolean>(false);

  const requestStart = () => {
    if (showLoading) {
      return
    }

    setShowLoading(true);
    try {
      messageApi.open({
        key: LOADING_KEY,
        type: 'loading',
        content: 'Loading...',
        duration: 0
      });
    } catch (error) {}
  }

  const requestEnd = () => {
    setShowLoading(false);
    message.destroy(LOADING_KEY);
  }

  const onGlobleError = (msg: string) => {
    message.open({
      key: MESSAGE_GLOBLE_ERROR,
      type: 'error',
      content: <>{msg || 'Error'}  <CloseOutlined style={{marginLeft: '10px', color: '#0d0d0d'}} onClick={() => message.destroy(MESSAGE_GLOBLE_ERROR)}/> </>,
      duration: 5000,
      style: {
        marginTop: '10vh'
      }
    })
  }

  useEffect(() => {
    subscribe('request-start', () => {
      requestStart()
    });
    subscribe('request-end', () => {
      requestEnd()
    });
    subscribe(EMIT_KEY_MESSAGE_GLOBLE_ERROR, (params) => {
      onGlobleError(params?.content)
    });
  }, []);

  return (
    loaded && (
      <>
        { contextHolder }
        <ConfigProvider locale={defaultAntdLocale}>
          <div className={px('root')}>
            {/* 隐藏标题 */}
            {/* <Header /> */}
            <div className={px('content')} style={{ position: 'relative' }}>
              {children}
            </div>
          </div>
        </ConfigProvider>
      </>
    )
  );
};

export default WebLayout;
