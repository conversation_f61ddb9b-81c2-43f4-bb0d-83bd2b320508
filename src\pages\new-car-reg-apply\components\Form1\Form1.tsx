import React, { useState, useEffect } from 'react';
import { useIntl } from 'umi';
import pc from 'prefix-classnames';
import './Form1.less';

import { Input, Row, Col, Button, Form, Select } from 'antd';

const px = pc('form1');

export interface FormOneProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;
}

const FormOne = (props: FormOneProps) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  const [isSearch, setIsSearch] = useState(false);

  const search = () => {
    setIsSearch(true)
  }

  useEffect(() => {
    if(isSearch) {
      setDisableNextStep(false)
    } else {
      setDisableNextStep(true)
    }
  }, [isSearch])

  return (
    <>

    <div className={`${px('root')} ${className}`} {...otherProps}>
      {
        !isSearch ? (
          <>
          <Form form={form} className={px('form')} layout="vertical">
            <div className={px('sectionBody')}>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                <Select value={"1"}>
                  <Select.Option value="1">{intl.formatMessage({ id: 'light_vehicles' })}</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'license_plate_number' })}>
                <Input value="" />
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vin_4' })}>
                <Input value="" />
              </Form.Item>
              <div style={{marginBottom:'1.5em', width:'100%'}}>或者</div>
              <Form.Item label={intl.formatMessage({ id: 'license_plate_number' })}>
                <Input value="" />
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vin_4' })}>
                <Input value="" />
              </Form.Item>
              <div style={{width:'30%'}}></div>
              <div style={{marginTop:'30px', width:'100%'}}>
                <Button type='primary' onClick={() => {nextStep()}}>{intl.formatMessage({ id: 'search' })}</Button>
              </div>

            </div>
          </Form>
          </>
        ) : (
          <>

          </>
        )
      }
    </div>
    </>
  );
};

export default FormOne;
