import pc from 'prefix-classnames';
import React, { useEffect, useState } from 'react';
import { getLocale, history, Location, useIntl, useLocation } from 'umi';

import FooterButton from '@/components/FooterButton';

import GuideButton from '@/components/GuideButton';

import { getVehRegTxnData } from '@/services/0033D';
import { getLoginUserInfo, identityAuthorize } from '@/services/UserApi';
import { checkServiceAvailable, getServiceTitle } from '@/services/publicApi';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import FormBase from './components/FormBase';
import FormBaseMobile from './components/Mobile/FormBase';
import SuccessMobile from './components/Mobile/Success';
import Success from './components/Success';

import { getLangGroup, getResponseMessage, GovLang, Lang } from '@/locales/lang';
import { isMobile } from '@/utils';
import { FetchResponse, post } from '@/utils/fetch';
import { Page } from '@gov-mo/mpaas-js-bridge';

const classPrefix = isMobile() ? 'owner-confirm-new-car-mobile' : 'owner-confirm-page';
const px = pc(classPrefix);

type QueryLocation = {
  query: {
    txnId: string;
  };
};

type Props = {} & BaseProps;

const OwnerPage = (props: Props) => {
  const ComFormBase = isMobile() ? FormBaseMobile : FormBase;

  const [step, setStep] = useState(3);
  const intl = useIntl();
  const isPT = intl.locale === 'pt-PT';
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));

  const [showPrevStep, setShowPrevStep] = useState(false);
  const [showNextStep, setShowNextStep] = useState(false);
  const [showClose, setShowClose] = useState(true);
  const [showTempStore, setShowTempStore] = useState(false);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);
  const [disableConfirmApply, setDisableConfirmApply] = useState(true);
  const [disableTempStore, setDisableTempStore] = useState(false);

  const [showConfirmApply, setShowConfirmApply] = useState(false); // 確認本次申請

  const [txnId, setTxnId] = useState('');
  const [ownerId, setOwnerId] = useState('');

  const [detail, setDetail] = useState<getEXTxnDataRes>({} as getEXTxnDataRes);
  const [userProfile, setUserProfile] = useState<UserProfile>({} as UserProfile);

  const [currentOwner, setCurrentOwner] = useState<saveTxnOwnersParams>();
  const [tipMsg, setTipMsg] = useState('');

  const location = useLocation() as Location & QueryLocation;
  const [checkServiceModal, checkServiceContextHolder] = Modal.useModal();

  const [serviceTitle, setServiceTitle] = useState<string>(
    intl.formatMessage({ id: 'vehicle_registration_application' }),
  );

  const baseUrl =
    window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const prevStep = () => {
    if (step <= 1) {
      isMobile() && Page.close();
      return;
    }

    setStep(step - 1);
  };

  const nextStep = () => {
    setStep(step + 1);
  };

  const handleClose = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const handleTempStore = () => {};

  useEffect(() => {
    setDisablePrevStep(step <= 1);
  }, [step]);

  useEffect(() => {
    checkServiceAvailable('VEM').then((res) => {
      console.log('checkServiceAvailable', res);

      if (!res || !res.data) {
        init();
        return;
      }

      if (res?.data?.available === false) {
        history.push('/web/error');
        return;
      }

      if (isSuccessPage()) {
        init();
        return;
      }

      init();
      if (isMobile()) {
        Modal.warning({
          icon: null,
          centered: true,
          className: 'system-maintenance-mobile',
          title: intl.formatMessage({ id: 'warm_reminder' }),
          content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
          okText: intl.formatMessage({ id: 'confirm' }),
        });
        return;
      }
      checkServiceModal.warning({
        title: intl.formatMessage({ id: 'warm_reminder' }),
        icon: <ExclamationCircleOutlined />,
        content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
        okText: intl.formatMessage({ id: 'confirm' }),
      });
    });
  }, []);

  function init() {
    const txnId = location.query?.txnId as string;
    const ownerId = location.query?.ownerId as string;

    console.log('window.location.href', window.location.href);

    getServiceTitle('0033D').then((res) => {
      setServiceTitle(
        getLangGroup(
          res?.data?.serviceTitleCn,
          res?.data?.serviceTitlePt,
          res?.data?.serviceTitleEn,
        ),
      );
    });

    if (txnId) {
      setTxnId(txnId);
    }
    if (ownerId) {
      setOwnerId(ownerId);
    }

    if (!!location.query.faceAuth && !!location.query.code) {
      checkFaceAuth(txnId, ownerId);
      return;
    }

    // 獲取詳情數據
    getData();
  }

  function getData() {
    getVehRegTxnData(location.query?.txnId).then(async (res) => {
      console.log('getVehRegTxnData', res);
      if (res?.code === '401') {
        getData();
        return;
      }

      if (res?.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }

      setTipMsg('');

      // 獲取用戶數據
      const user = await getLoginUserInfo();
      const currentOwner = (res.data.ownerDataList || []).find(
        (item) => item.ownerIdentNo === user.identityNo,
      );

      setCurrentOwner(currentOwner);
      setShowConfirmApply(currentOwner?.ownerFacialStatus || false);
      setDetail(res.data || {});
      setUserProfile(user);

      !res?.data?.dsiMsgCodeDTO && setDisableConfirmApply(false);
    });
  }

  async function handleFaceAuth(auth = false) {
    if (!auth) return;

    const currentOwner = getCurrentOwner();
    if (!currentOwner) {
      return;
    }

    const language = getLocale() === Lang.葡语 ? GovLang.葡语 : GovLang.繁体中文;

    const res = await identityAuthorize({
      txnId,
      isShowHeader: true,
      ownerId: currentOwner.txnOwnerId,
      language: getLocale() || 'zh-MO',
      reason: '0033D',
      redirectUri: decodeURIComponent(
        `${baseUrl}/ovsap/web/vem/personal/owner-confirm-new-car?page=success&txnId=${txnId}&ownerId=${currentOwner.txnOwnerId}&language=${language}&faceAuth=1`,
      ),
      lastModifyDate: detail?.lastModifyDate,
    });

    res?.data && (window.location.href = res.data);
  }

  async function checkFaceAuth(txnId: string, ownerId: string) {
    const language = getLocale() === Lang.葡语 ? GovLang.葡语 : GovLang.繁体中文;

    const res = await post<FetchResponse<string>>('/oa/face/checkFaceRecognition', {
      code: location.query?.code as string,
      ownerId,
      verficationStatus: 'Y',
      redirectUri: decodeURIComponent(
        `${baseUrl}/ovsap/web/vem/personal/owner-confirm-new-car?page=success&txnId=${txnId}&ownerId=${ownerId}&language=${language}&faceAuth=1`,
      ),
    });

    if (res?.code === '401') {
      checkFaceAuth(txnId, ownerId);
      return;
    }

    if (res?.code !== '0') {
      setTipMsg(getResponseMessage(res));
      return;
    }

    setTipMsg('');
    getData();

    return res;
  }

  // 通過這個方法可以獲得當前車主資料
  function getCurrentOwner() {
    const currentUser = (detail.ownerDataList || []).find(
      (item) => item.ownerIdentNo === userProfile.identityNo,
    );
    if (!currentUser) {
      return undefined;
    }

    return currentUser;
  }

  function isSuccessPage() {
    return location.search?.includes('page=success');
  }

  if (isSuccessPage()) {
    return isMobile() ? (
      <SuccessMobile
        txnId={txnId}
        detail={detail}
        serviceTitle={serviceTitle}
        currentOwner={currentOwner}
      />
    ) : (
      <Success
        txnId={txnId}
        detail={detail}
        serviceTitle={serviceTitle}
        currentOwner={currentOwner}
      />
    );
  }

  return (
    <>
      <ComFormBase
        step={step}
        serviceTitle={serviceTitle}
        classPrefix={classPrefix}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
          handleFaceAuth(true);
        }}
        setDisableConfirmApply={(flag) => {
          setDisableConfirmApply(flag);
        }}
        nextStep={nextStep}
        setNextStepText={(text) => {
          setNextStepText(text);
        }}
        setShowTempStore={setShowTempStore}
        setShowConfirmApply={setShowConfirmApply}
        ownerId={ownerId}
        detail={detail}
        txnId={txnId}
        currentOwner={currentOwner}
        tipMsg={tipMsg}
        handlePrevStep={() => {
          prevStep();
        }}
      />
      <GuideButton itemId="" />
      <FooterButton
        // handleClose={handleClose}
        className={isPT ? `${px('footer-button')} ${px('pt-footer-button')}` : px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        showTempStore={showTempStore}
        showConfirmApply={showConfirmApply}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        setShowTempStore={() => {
          setShowTempStore;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        disableConfirmApply={disableConfirmApply}
        disableTempStore={disableTempStore}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        setDisableConfirmApply={(flag) => {
          setDisableConfirmApply(flag);
        }}
        setDisableTempStore={() => {
          setDisableTempStore;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
        handleTempStore={() => {
          handleTempStore();
        }}
        handleConfirmApply={() => {
          setDisableNextStep(true);
          handleFaceAuth(true);
        }}
      />
      
      {!isMobile() && checkServiceContextHolder}
    </>
  );
};

export default OwnerPage;
