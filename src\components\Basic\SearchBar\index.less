.searchBar {
  width: 100%;
  // background-color: #ffffff;
  :global {
    .am-search {
      background-color: transparent;
      padding: 0;
    }
    .am-search-input {
      height: 64px;
      border-radius: 64px;
      background-color: var(--firstBgColor);
    }
    .am-search-input .am-search-synthetic-ph {
      line-height: 64px;
      height: 64px;
      width: unset !important;
      padding-left: 25px !important;
      display: flex;
      align-items: center;
    }
    .am-search-input input[type='search'] {
      height: 64px;
      color: @font-color-base;
      opacity: 1;
      padding-left: 74px !important;
    }
    .am-search-input .am-search-clear {
      padding: 16px;
    }

    .am-search-input .am-search-synthetic-ph-placeholder {
      visibility: hidden !important;
    }
    .am-search-cancel {
      color: #666;
    }
  }
}
