.icon() {
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--secondBgColor);
  width: 40px;
  height: 40px;
  border-top-left-radius: 50%;
  border-bottom-left-radius: 50%;
  box-shadow: 10px 10px 20px 0px #00000014;
}
.list() {
  left: 40px;
  position: absolute;
  background: var(--secondBgColor);
  color: var(--firstTextColor);
  border-radius: 10px 0 0 10px;
  box-shadow: 10px 10px 20px 0px #00000014;
}
.list-item() {
  width: max-content;
  max-width: 199px;
  padding: 14px;
  overflow-wrap: anywhere;
  line-height: 18px;
  min-height: 44px;
}
