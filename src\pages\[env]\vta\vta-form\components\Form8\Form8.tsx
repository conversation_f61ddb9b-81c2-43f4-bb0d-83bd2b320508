import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Form8.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Collapse, Modal } from 'antd';

const px = pc('vta-form-form-8');

export interface FormEightProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setDisableNextStep: (boolean) => void;
  setNextStepText: (string) => void;
}

const FormEight = (props: FormEightProps) => {
  const { className = '', ...otherProps } = props;
  const { setDisableNextStep, setNextStepText } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  const [posted, setPosted] = useState(false);

  const [show, setShow] = useState(false);

  const [isModalOpen, setModalOpen] = useState(false);

  function handleOk() {
    setModalOpen(false);
    setShow(true);
  }

  function handleCancel() {
    setModalOpen(false);
  }

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      {show && (
        <div className="multi-card-container">
          <div className="multi-card-item">
            <div className="multi-card-title">
              <span>配件資料1</span>
              <div className="multi-card-title-action">
                <Space>
                  <Button>更新</Button>
                  <Button danger>{intl.formatMessage({ id: 'delete' })}</Button>
                </Space>
              </div>
            </div>
            <div className="multi-card-body">
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label="配件">
                  <div>尾翼</div>
                </Form.Item>
                <Form.Item label="配件性質">
                  <div>後</div>
                </Form.Item>
                <Form.Item label="配件狀態">
                  <div>個別</div>
                </Form.Item>
              </Form>
            </div>
          </div>
          <div className="multi-card-item">
            <div className="multi-card-title">
              <span>配件資料2</span>
              <div className="multi-card-title-action">
                <Space>
                  <Button>更新</Button>
                  <Button danger>{intl.formatMessage({ id: 'delete' })}</Button>
                </Space>
              </div>
            </div>
            <div className="multi-card-body">
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label="配件">
                  <div>繞流器</div>
                </Form.Item>
                <Form.Item label="配件性質">
                  <div>金屬</div>
                </Form.Item>
                <Form.Item label="配件狀態">
                  <div>原裝</div>
                </Form.Item>
              </Form>
            </div>
          </div>
        </div>
      )}

      <div className={px('add-btn')}>
        <Button
          type="primary"
          onClick={() => {
            setModalOpen(true);
          }}
        >
          新增配件資料
        </Button>
      </div>
      <Modal
        title="錄入配件資料"
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="確 認"
        width={600}
        cancelButtonProps={{ type: 'default' }}
      >
        <Form layout="vertical">
          <Form.Item label="配件" required>
            <Select
              style={{ width: '100%' }}
              value={'1'}
              options={[{ label: '繞流器', value: '1' }]}
              placeholder={intl.formatMessage({ id: 'please_select' })}
            />
          </Form.Item>
          <Form.Item label="配件性質" required>
            <Select
              style={{ width: '100%' }}
              value={'1'}
              options={[{ label: '金屬', value: '1' }]}
              placeholder={intl.formatMessage({ id: 'please_select' })}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FormEight;
