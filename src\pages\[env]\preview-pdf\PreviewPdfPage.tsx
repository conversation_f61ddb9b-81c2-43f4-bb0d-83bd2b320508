import React, { useEffect } from 'react';
import { Page, File } from '@gov-mo/mpaas-js-bridge';
import { isMobile } from '@/utils';
import { BASE_ORIGIN, PREVIEW_PDF_MAPPING } from '@/utils/const';
import * as queryString from 'query-string';

type Props = {

} & BaseProps

type Query = {
  type: string
  txnId: string
}

const PreviewPdfPage = (props: Props) => {

  useEffect(() => {
    const query = queryString.parse(decodeURIComponent(decodeURIComponent(window.location.search))) as Query
    const type = query?.type
    console.log('query parse', query)
    console.log('props', props)
    console.log('url', isMobile())    
    if (!type) {
      return
    }

    const url = `${BASE_ORIGIN}/ovsap/api/ignore/${PREVIEW_PDF_MAPPING[type]}?txnId=${query?.txnId}`

    url && isMobile() && File.previewPdfWithDownload({ url: `${url}#.pdf`, fileName: 'Preview.pdf' }).then(res => {
      setTimeout(() => Page.closePrePage(), 1000)
    })

    try {
      url && !isMobile() && window.open(url, '_blank')
    } finally {
      url && !isMobile() && window.close()
    }
  }, [])

  return <></>;
};

export default PreviewPdfPage;
