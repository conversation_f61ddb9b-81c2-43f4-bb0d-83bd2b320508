import React, { useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Form3.less';

import { useIntl } from 'umi';
import { Button, Col, Form, Input, InputNumber, Modal, Row, Space, Typography } from 'antd';
import { groupBy, keys } from 'lodash';
import { getSysCodesByCodeVatType } from '@/services/publicApi';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';
import { useGetForm1Param, useGetFormParam } from '../../hooks/useGetFormParam';
import { CarFuelSupplyType, CarLevel, useGetEmu, useGetRes } from '../../hooks';
import { getVTAPowerDeviceByTxnId, saveVTAPowerDevice } from '@/services/vta';
import { useFormAction } from '../../hooks/useFormAction';

const px = pc('vta-form-form-3');

export interface FormThreeProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId?: string;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

const FormThree = (props: FormThreeProps) => {
  const { className = '', txnId, ...otherProps } = props;
  const intl = useIntl();
  const [form] = Form.useForm();
  const [cylinderVolValue, setCylinderVolValue] = useState<number | undefined>();
  const [powerSource, setPowerSource] = useState();
  const handleValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {
    setPowerSource(allValues.powerSource);
    // 「汽缸容積」= [ (「口徑」/ 2 )^2 ] * 3.141596 *「衝程」*「氣缸數」/ 1000
    // 「汽缸容積」= 汽缸容量=PI*(口徑/2)^2*衡程*汽缸數量
    // cylinderQty 汽缸數量
    // bore 口徑
    // stroke 衝程
    if (
      (changedValues.cylinderQty || changedValues.bore || changedValues.stroke) &&
      allValues.cylinderQty &&
      allValues.bore &&
      allValues.stroke
    ) {
      // const cylinderVol = ((allValues.bore / 2) ** 2) * allValues.stroke * allValues.cylinderQty;
      const r = allValues.bore / 2;
      const s = 3.14 * Math.pow(r, 2);
      const vol = Number((s * allValues.stroke * allValues.cylinderQty) / 1000);
      const cylinderVol = Math.min(Number(vol.toFixed(3)), 99999.999);
      setCylinderVolValue(cylinderVol);
      return;
    }

    setCylinderVolValue(undefined);
  };
  let { emuObj } = useGetEmu({
    fuelType: '22008',
    powerSource: 50906,
    engineInjection: 50907,
    aspiration: 50901,
    powerUnitType: 31218,
    torqueUnitCode: 50004,
  });
  let { vehType, canEditable } = useFormAction(
    txnId,
    form,
    getVTAPowerDeviceByTxnId,
    saveVTAPowerDevice,
    props,
  );
  const powerFormItemList1 = useMemo(
    () => [
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'identifier' }),
        name: 'enginePattern1',
        required: true,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['enginePattern1']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'serial_number' }),
        name: 'engineNo1',
        required: false,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['engineNo1']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'max_power' }),
        name: 'maxPower1',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'powerUnitType1',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.powerUnitType}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxPowerRpm1',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'max_torque' }),
        name: 'maxTorque1',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'torqueUnitCode1',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.torqueUnitCode}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-1',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxTorqueRpm1',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
    ],
    [emuObj,vehType, canEditable],
  );
  const powerFormItemList2 = useMemo(
 () => [
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'identifier' }),
        name: 'enginePattern2',
        required: true,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['enginePattern2']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'serial_number' }),
        name: 'engineNo2',
        required: false,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['engineNo2']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'max_power' }),
        name: 'maxPower2',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'powerUnitType2',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.powerUnitType}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxPowerRpm2',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'max_torque' }),
        name: 'maxTorque2',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'torqueUnitCode2',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.torqueUnitCode}
          />
        ),
      },
      {
        type: 'input',
        category:  intl.formatMessage({ id: 'power_unit' })+'-2',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxTorqueRpm2',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
    ],
    [emuObj,vehType, canEditable],
  );
  const powerFormItemList3 = useMemo(
() => [
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'identifier' }),
        name: 'enginePattern3',
        required: true,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['enginePattern3']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'serial_number' }),
        name: 'engineNo3',
        required: false,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['engineNo3']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'max_power' }),
        name: 'maxPower3',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'powerUnitType3',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.powerUnitType}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxPowerRpm3',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'max_torque' }),
        name: 'maxTorque3',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'torqueUnitCode3',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.torqueUnitCode}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-3',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxTorqueRpm3',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
    ],
    [emuObj,vehType, canEditable],
  );
  const powerFormItemList4 = useMemo(
() => [
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'identifier' }),
        name: 'enginePattern4',
        required: true,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['enginePattern4']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'serial_number' }),
        name: 'engineNo4',
        required: false,
        span: 12,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['engineNo4']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'max_power' }),
        name: 'maxPower4',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'powerUnitType4',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.powerUnitType}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxPowerRpm4',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'max_torque' }),
        name: 'maxTorque4',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'unit' }),
        name: 'torqueUnitCode4',
        span: 8,
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.torqueUnitCode}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit' })+'-4',
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxTorqueRpm4',
        required: true,
        span: 8,
        hide: [CarLevel.半掛車].includes(vehType),
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
    ],
    [emuObj,vehType, canEditable],
  );
  const powerFormItemListCom = useMemo(
    () => [
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit_total' }),
        label: intl.formatMessage({ id: 'max_power' }),
        name: 'maxPower',
        required: true,
        span: 8,
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'power_unit_total' }),
        label: intl.formatMessage({ id: 'unit' }),
        name: 'powerUnitType',
        span: 8,
        required: true,
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.powerUnitType}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit_total' }),
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxPowerRpm',
        required: true,
        span: 8,
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit_total' }),
        label: intl.formatMessage({ id: 'max_torque' }),
        name: 'maxTorque',
        required: true,
        span: 8,
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'power_unit_total' }),
        label: intl.formatMessage({ id: 'unit' }),
        name: 'torqueUnitCode',
        span: 8,
        required: true,
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.torqueUnitCode}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'power_unit_total' }),
        label: intl.formatMessage({ id: 'rpm' }),
        name: 'maxTorqueRpm',
        required: true,
        span: 8,
        component: <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />,
      },
      // {
      //   type: 'select',
      //   category: intl.formatMessage({ id: 'power_unit_total' }),
      //   label: '傳動方式',
      //   name: 'txType',
      //   required: true,
      //   component: (
      //     <VtaSelect
      //       placeholder={intl.formatMessage({ id: 'please_select' })}
      //       options={([1, 2].includes(vehType)
      //         ? ['前輪驅動', '後輪驅動', '全輪驅動']
      //         : ['鏈', '帶', '軸']
      //       ).map((item, index) => ({
      //         label: item,
      //         value: Number(index),
      //       }))}
      //     />
      //   ),
      // },
      // {
      //   type: 'select',
      //   category: intl.formatMessage({ id: 'power_unit_total' }),
      //   label: '變速器',
      //   name: 'gearboxType',
      //   required: false,
      //   component: <Input readOnly />,
      // },
      // {
      //   type: 'select',
      //   category: intl.formatMessage({ id: 'power_unit_total' }),
      //   label: '檔數',
      //   name: 'varSpeedQty',
      //   required: true,
      //   component: (
      //     <VtaSelect
      //       placeholder={intl.formatMessage({ id: 'please_select' })}
      //       options={[...Array(21).keys()].map((index) => ({
      //         label: index == 0 ? '-' : index,
      //         value: Number(index),
      //       }))}
      //     />
      //   ),
      // },
    ],
    [emuObj,vehType, canEditable],
  );

  let powerFormItemList = useMemo(
    () => [
      ...powerFormItemList1,
      ...powerFormItemList2,
      ...powerFormItemList3,
      ...powerFormItemList4,
      ...powerFormItemListCom,
    ],
    [emuObj,vehType, canEditable],
  );

  const [show, setShow] = useState(false);

  const [isModalOpen, setModalOpen] = useState(false);

  function handleOk() {
    setModalOpen(false);
  }

  function handleCancel() {
    setModalOpen(false);
  }

  return (
    <div className={`${px('root')} ${className} multi-card-container`} {...otherProps}>
      <Form
        form={form}
        className={px('form')}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <Form.Item name="txnId" hidden initialValue={txnId}>
          <Input />
        </Form.Item>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>{intl.formatMessage({ id: 'power_unit' })}</span>
          </div>
          <div className="multi-card-item-sub">
            <div className="multi-card-title">
              <span>{intl.formatMessage({ id: 'energy_type' })}</span>
            </div>
            <div className="multi-card-body">
              <Row gutter={16}>
                {[CarLevel.半掛車].includes(vehType) ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'fuelSupplyType' })}
                      name="powerSource"
                      rules={[{ required: true }]}
                    >
                      <VtaSelect
                        options={emuObj.powerSource}
                        disabled={canEditable['powerSource']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  </Col>
                )}
                {[CarLevel.半掛車].includes(vehType) ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'fuelType' })}
                      name="fuelType"
                      rules={[{ required: true }]}
                    >
                      <VtaSelect
                        disabled={canEditable['fuelType']}
                        options={emuObj.fuelType}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </div>
          </div>
          <div className="multi-card-item-sub">
            <div className="multi-card-title">
              <span>{intl.formatMessage({ id: 'internal_engine' })}</span>
            </div>
            <div className="multi-card-body">
              <Row gutter={16}>
                {[CarLevel.半掛車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'cylinderQty' })}
                      name="cylinderQty"
                      rules={[{ required: true }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        disabled={canEditable['cylinderQty']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                )}
                {[CarLevel.半掛車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'valveQty' })}
                      name="valveQty"
                      rules={[{ required: true }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        disabled={canEditable['valveQty']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                )}
                {[CarLevel.半掛車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'bore' })}
                      name="bore"
                      rules={[{ required: true }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        disabled={canEditable['bore']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                )}
                {[CarLevel.半掛車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'stroke' })}
                      name="stroke"
                      rules={[{ required: true }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        disabled={canEditable['stroke']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                )}
                {[CarLevel.半掛車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={`${intl.formatMessage({
                        id: 'cylinderVol',
                      })}`}
                      name="cylinderVol"
                      rules={[{ required: true }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        disabled={canEditable['cylinderVol']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />

                    </Form.Item>
                      {cylinderVolValue && (
                        <Typography.Text
                          style={{ color: 'red', fontSize: 16 }}
                        >{`${intl.formatMessage({ id: 'cylinder_volume_calc' })}：${cylinderVolValue}`}</Typography.Text>
                      )}
                  </Col>
                )}
                {[CarLevel.半掛車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'engineInjection' })}
                      name="engineInjection"
                      rules={[{ required: true }]}
                    >
                      <VtaSelect
                        options={emuObj.engineInjection}
                        disabled={canEditable['engineInjection']}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  </Col>
                )}
                {[CarLevel.半掛車, CarLevel.重型摩托車, CarLevel.輕型摩托車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'aspiration' })}
                      name="aspiration"
                      rules={[{ required: true }]}
                    >
                      <VtaSelect
                        options={emuObj.aspiration}
                        disabled={canEditable['aspiration']}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  </Col>
                )}
                {[CarLevel.半掛車].includes(vehType) ||
                  CarFuelSupplyType.電力 === powerSource ? null : (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'fuel_tank' })}
                      name="fuelTankVol"
                      rules={[{ required: true }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        disabled={canEditable['fuelTankVol']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </div>
          </div>
          {keys(groupBy(powerFormItemList, 'category')).map((item, index) => {
            return (
              (index === 0 || show) && (
                <div className="multi-card-item-sub" key={index}>
                  <div className="multi-card-title">
                    <span>{item}</span>
                    {index === 0 && (
                      <div className="multi-card-title-action">
                        <Space>
                          <Button onClick={() => setShow(!show)}>{intl.formatMessage({ id: 'add_new' })}</Button>
                        </Space>
                      </div>
                    )}
                  </div>
                  <div className="multi-card-body">
                    <Row gutter={16}>
                      {groupBy(powerFormItemList, 'category')
                      [item].filter((ite) => !ite.hide)
                        .map((item, index) => (
                          <Col span={item.span ?? 24} key={index}>
                            <Form.Item
                              label={item.label}
                              name={item.name}
                              rules={[{ required: item.required }]}
                            >
                              {item.component}
                            </Form.Item>
                          </Col>
                        ))}
                    </Row>
                  </div>
                </div>
              )
            );
          })}
        </div>
      </Form>
      <Modal
        title={intl.formatMessage({ id: 'input_motor_data' })}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={intl.formatMessage({ id: 'confirm' })}
        width={600}
        cancelButtonProps={{ type: 'default' }}
      >
        <Form layout="vertical">
          <Form.Item label={intl.formatMessage({ id: 'engine_no_before' })} required>
            <Input />
          </Form.Item>
          <Form.Item label={intl.formatMessage({ id: 'engine_no_position' })} required>
            <Input />
          </Form.Item>
          <Form.Item label={intl.formatMessage({ id: 'notes' })}>
            <Input.TextArea placeholder={intl.formatMessage({ id: 'please_enter' })} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FormThree;
