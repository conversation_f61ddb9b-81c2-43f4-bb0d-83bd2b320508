@prefix: form5-28f-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 0.2rem 0.12rem 0.4rem;
      padding-bottom: 0.2rem;
      background-color: #ffffff;
      font-size: 0.16rem;
      border-radius: 0.12rem;
      overflow: hidden;

      .mophone-user .ant-form-item-label {
        display: none;
      }
      .mophone-user .mo-phone {
        width: 100%;
      }
      .ant-form {
        font-size: 0.16rem;
      }
      .am-list-item.mo-input-mobile-middle
      {
        height: 35px !important;
      }
      .am-list-extra
      {
        font-size: 0.16rem !important;
      }
      .am-list-item.mo-input-mobile-middle input
      {
        font-size: 0.16rem;
      }
      .ant-form-item-label > label {
        font-size: 0.16rem;
        flex-direction: row-reverse;
        display: flex;
        justify-content: flex-end;
      }
      .ant-form label {
        font-size: 0.16rem;
       
      }
      .ant-select {
        font-size: 0.16rem;
      }
      .ant-input {
        font-size: 0.16rem;
      }
      .ant-radio-group{
        display: flex;
      }
      .ant-radio, .ant-radio-wrapper{
        display: flex;
        align-items: center;
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 700;
      padding: 0.05rem 0.2rem;
      min-height: 0.41rem;
      font-size: 0.18rem;
      color: #ffffff;
      line-height: 0.21rem;
      background: @brand-primary;
    }
    &sectionBody {
      padding: 0.2rem 0.12rem;
    }
  }
}
