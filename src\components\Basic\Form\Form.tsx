import React from 'react';
import { List } from 'antd-mobile';
import styles from './Form.less';

const Form = (props) => {
  const { label, extra, bottomLine, tip, tipStyle, labelStyle, ...restProps } = props;
  return (
    <div className={styles.form}>
      {label && (
        <div className={styles.formLabel} style={labelStyle}>
          {label}
        </div>
      )}
      <div className={bottomLine !== false ? '' : styles.noBottomLine}>
        <List {...restProps} />
        {extra}
      </div>
      {tip && (
        <div className={styles.tip} style={tipStyle}>
          {tip}
        </div>
      )}
    </div>
  );
};

export default Form;
