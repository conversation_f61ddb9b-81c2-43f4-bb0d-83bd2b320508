import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Preview.less';

import { useIntl } from 'umi';
import { Form, Collapse, Alert, Checkbox } from 'antd';
import { getLangGroup } from '@/locales/lang';
import Watermark from '@/components/Watermark';
import dayjs from 'dayjs';
import { useToggle } from 'ahooks';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

const px = pc('owner-confirm-new-car-preview-mobile');

export interface FormEightProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setDisableNextStep: (boolean) => void;
  setNextStepText: (string) => void;
  setShowConfirmApply: (boolean) => void;
  setDisableConfirmApply: (boolean) => void;

  detail: getEXTxnDataRes;
  txnId: string | undefined;
  currentOwner?: saveTxnOwnersParams;
  tipMsg?: string;
}

const FormEight = (props: FormEightProps) => {
  const { className = '', ...otherProps } = props;
  const { setNextStepText, setDisableConfirmApply, detail } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  const [tipMsg, setTipMsg] = useState('');
  const [isChecked, setIsChecked] = useState(false);

  const [ownerDataList, setOwnerDataList] = useState<saveTxnOwnersParams[]>([]); // 車主資料
  const [currentOwnerList, setCurrentOwnerList] = useState<saveTxnOwnersParams[]>([]);
  const [otherOwnerList, setOtherOwnerList] = useState<saveTxnOwnersParams[]>([]);
  const [ovsapVehDataDTO, setOvsapVehDataDTO] = useState<ovsapVehDataDTO>(); // 車輛資料
  const [vehRegTopDataDTO, setVehRegTopDataDTO] = useState<VehRegTopDataDTOType>(); // 車牌資料
  const [dspaFpDataDto, setDspaFpDataDto] = useState<{ dspaFpNo: string }>();
  // 傳進來的 當前車主
  const [currentOwner, setCurrentOwner] = useState<saveTxnOwnersParams | undefined>(
    props.currentOwner,
  );

  useEffect(() => {
    setCurrentOwner(props.currentOwner);
    if (props.currentOwner?.verficationStatus === 'Y') {
      setIsChecked(true);
    }
    setTipMsg(props.tipMsg || '');
  }, [props.currentOwner, props.tipMsg]);

  useEffect(() => {
    setNextStepText(intl.formatMessage({ id: 'payment' }));
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }));
    };
  }, []);

  useEffect(() => {
    if (detail) {
      setDspaFpDataDto(detail?.dspaFpDataDTO || {});
      setOwnerDataList(detail.ownerDataList);
      setCurrentOwnerList(
        detail.ownerDataList?.filter(
          (item) => item.ownerIdentNo === props.currentOwner?.ownerIdentNo,
        ),
      );
      setOtherOwnerList(
        detail.ownerDataList?.filter(
          (item) => item.ownerIdentNo !== props.currentOwner?.ownerIdentNo,
        ),
      );
      setOvsapVehDataDTO(detail.ovsapVehDataDTO);
      setVehRegTopDataDTO(detail.vehRegTopDataDTO);
    }

    if (props.currentOwner) {
      setCurrentOwner(props.currentOwner);
    }
  }, [detail, props.currentOwner]);

  const [state, { toggle }] = useToggle();
  return (
    <Watermark text={dayjs().format('YYYY-MM-DD HH:mm:ss')}>
      {tipMsg && (
        <div style={{ padding: '0.2rem 0.24rem 0' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'data_confirmation' })}</div>
        {detail?.dsiMsgCodeDTO && (
          <div className="owner-checkbox">
            <Checkbox
              checked={isChecked}
              onChange={(e) => {
                setIsChecked(e.target.checked);
                setDisableConfirmApply(!e.target.checked);
              }}
            >
              {getLangGroup(
                detail?.dsiMsgCodeDTO?.codeCname,
                detail?.dsiMsgCodeDTO?.codePname,
                detail?.dsiMsgCodeDTO?.codeEname,
              )}
            </Checkbox>
          </div>
        )}
        <div className={px('sectionBody')}>
          {/* <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
            <Collapse.Panel
              showArrow={false}
              header={<>{intl.formatMessage({ id: 'car_owner_information' })}</>}
              key={1}
            >
              {ownerDataList?.map((item, index) => {
                return (
                  <div key={index}>
                    <p className="form-title">
                      {intl.formatMessage({ id: 'car_owner_information' })} {index + 1}：
                    </p>
                    <Form form={form} className={px('form')} layout="vertical">
                      {[
                        ...(currentOwner?.ownerIdentNo === item.ownerIdentNo
                          ? [
                              {
                                name: item.ownerIdentTypeCn,
                                label: intl.formatMessage({ id: 'certificate_type' }),
                              },
                              {
                                name: item?.ownerIdentNo,
                                label: intl.formatMessage({ id: 'id_number' }),
                              },
                            ]
                          : []),
                        {
                          name: `${item.ownerLastNameCn ?? ''}${item.ownerFirstNameCn ?? ''}`,
                          label: intl.formatMessage({ id: 'chinese_name' }),
                        },
                        {
                          name: `${item.ownerLastNamePt ?? ''} ${item.ownerFirstNamePt ?? ''}`,
                          label: intl.formatMessage({ id: 'portuguese_name' }),
                        },
                        ...(currentOwner?.ownerIdentNo === item.ownerIdentNo
                          ? [
                              {
                                name: item.ownerAddr,
                                label: intl.formatMessage({ id: 'contact_address' }),
                              },
                              {
                                name: item.addrLangCn,
                                label: intl.formatMessage({ id: 'language' }),
                              },
                            ]
                          : []),
                      ].map((field) => {
                        return (
                          <Form.Item label={field.label} key={field.label}>
                            <div>{field.name}</div>
                          </Form.Item>
                        );
                      })}
                      <Form.Item label="">
                        {item.verficationStatus === 'P' && (
                          <span className="statusBg1">{item.verficationStatusCn}</span>
                        )}
                        {item.verficationStatus === 'Y' && (
                          <span className="statusBg2">{item.verficationStatusCn}</span>
                        )}
                        {item.verficationStatus === 'N' && (
                          <span className="statusBg3">{item.verficationStatusCn}</span>
                        )}
                      </Form.Item>
                    </Form>
                  </div>
                );
              })}
            </Collapse.Panel>
          </Collapse> */}
          {/* <div className="owner-title-mobile">
            {intl.formatMessage({ id: 'car_owner_information' })}
          </div> */}
          {dspaFpDataDto?.dspaFpNo ? (
            <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
              <Collapse.Panel
                showArrow={false}
                header={<>{intl.formatMessage({ id: 'old_motorcycle_replace_subsidy' })}</>}
                key={1}
              >
                <div>
                  <Form form={form} className={px('form')} layout="vertical" colon={false}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'fp_no' })}
                      style={{ width: '100%' }}
                    >
                      <div>{dspaFpDataDto?.dspaFpNo}</div>
                      <div style={{ color: '#084ab8' }}>
                        {`${getLangGroup(
                          dspaFpDataDto?.dspaFpFeeCodeDto.codeCname,
                          dspaFpDataDto?.dspaFpFeeCodeDto.codePname,
                          dspaFpDataDto?.dspaFpFeeCodeDto.codeEname,
                        )}`}
                      </div>
                    </Form.Item>
                  </Form>
                </div>
              </Collapse.Panel>
            </Collapse>
          ) : (
            ''
          )}
          {/* 当前车主 */}
          {currentOwnerList?.length > 0 && (
            <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
              <Collapse.Panel
                showArrow={false}
                header={<>{intl.formatMessage({ id: 'current_car_owner' })} </>}
                key={1}
              >
                {currentOwnerList?.map((item, index, arr) => (
                  <Form form={form} className={px('form')} layout="vertical">
                    <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                      <div>
                        {item.ownerLastNameCn}
                        {item.ownerFirstNameCn}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                      <div>
                        {item.ownerLastNamePt} {item.ownerFirstNamePt}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                      <div>
                        {getLangGroup(
                          item.ownerIdentTypeCn,
                          item.ownerIdentTypePt,
                          item.ownerIdentTypeEn,
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'id_number' })}>
                      <div>{item.ownerIdentNo}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'contact_address' })}>
                      <div>{item.ownerAddr}</div>
                    </Form.Item>

                    {(ownerDataList || []).length > 1 &&
                      ['L', 'P'].includes(ovsapVehDataDTO?.vehType || '') && (
                        <Form.Item label={intl.formatMessage({ id: 'registered_residence' })}>
                          <div>{item.dsajAddr}</div>
                        </Form.Item>
                      )}
                    {(ownerDataList || []).length > 1 &&
                      ['L', 'P'].includes(ovsapVehDataDTO?.vehType || '') && (
                        <Form.Item label={intl.formatMessage({ id: 'share' })}>
                          <div>
                            {item.quotaNumerator} / {item.quotaDenominator}
                          </div>
                        </Form.Item>
                      )}
                    {item.isShowFacialLabel ? (
                      <Form.Item label="">
                        <span
                          className={
                            item.verficationStatus == 'P'
                              ? 'statusBg1'
                              : item.verficationStatus == 'Y'
                              ? 'statusBg2'
                              : 'statusBg1'
                          }
                        >
                          {getLangGroup(
                            item?.verficationStatusCn,
                            item?.verficationStatusPt,
                            item?.verficationStatusEn,
                          )}
                        </span>
                      </Form.Item>
                    ) : null}
                  </Form>
                ))}
              </Collapse.Panel>
            </Collapse>
          )}

          {/* 其他车主 */}
          {otherOwnerList?.length > 0 && (
            <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
              <Collapse.Panel
                showArrow={false}
                header={<>{intl.formatMessage({ id: 'other_car_owner' })} </>}
                key={1}
              >
                {otherOwnerList?.map((item, index, arr) => (
                  <div>
                    <p className="form-title">
                      {intl.formatMessage({ id: 'car_owner_information' })}
                      {arr.length > 1 ? index + 1 : ''}：
                    </p>
                    <Form form={form} className={px('form')} layout="vertical">
                      <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                        <div>
                          {item.ownerLastNameCn}
                          {item.ownerFirstNameCn}
                        </div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                        <div>
                          {item.ownerLastNamePt} {item.ownerFirstNamePt}
                        </div>
                      </Form.Item>
                      {(ownerDataList || []).length > 1 &&
                        ['L', 'P'].includes(ovsapVehDataDTO?.vehType || '') && (
                          <Form.Item label={intl.formatMessage({ id: 'share' })}>
                            <div>
                              {item.quotaNumerator} / {item.quotaDenominator}
                            </div>
                          </Form.Item>
                        )}
                      {item.isShowFacialLabel ? (
                        <Form.Item label="">
                          <span
                            className={
                              item.verficationStatus == 'P'
                                ? 'statusBg1'
                                : item.verficationStatus == 'Y'
                                ? 'statusBg2'
                                : 'statusBg1'
                            }
                          >
                            {getLangGroup(
                              item?.verficationStatusCn,
                              item?.verficationStatusPt,
                              item?.verficationStatusEn,
                            )}
                          </span>
                        </Form.Item>
                      ) : null}
                    </Form>
                  </div>
                ))}
              </Collapse.Panel>
            </Collapse>
          )}

          <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
            <Collapse.Panel
              showArrow={false}
              header={intl.formatMessage({ id: 'license_plate_information' })}
              key={1}
            >
              <Form form={form} className={px('form')} layout="vertical">
                {[
                  {
                    name: vehRegTopDataDTO?.plateNo,
                    label: intl.formatMessage({ id: 'license_plate_number' }),
                  },
                  {
                    name: vehRegTopDataDTO?.exRegDuration,
                    label: intl.formatMessage({ id: 'ex_license_plate_validity' }),
                  },
                  {
                    name: vehRegTopDataDTO?.lastVehRegDate,
                    label: intl.formatMessage({ id: 'apply_date_last' }),
                  },
                  ...(vehRegTopDataDTO?.isShowDrawResult
                    ? [
                        {
                          name: getLangGroup(vehRegTopDataDTO?.vehTodrawCn, vehRegTopDataDTO?.vehTodrawPt, vehRegTopDataDTO?.vehTodrawEn),
                          label: intl.formatMessage({ id: 'license_plate_lottery_status' }),
                        },
                        {
                          name: vehRegTopDataDTO?.drawResultNo,
                          label: intl.formatMessage({ id: 'lottery_result' }),
                        },
                      ]
                    : []),
                  ...(vehRegTopDataDTO?.tempMountPlateNo
                    ? [
                        {
                          name: vehRegTopDataDTO?.tempMountPlateNo,
                          label: intl.formatMessage({ id: 'payment_after_buy' }),
                        },
                      ]
                    : []),
                ].map((field) => {
                  return (
                    <Form.Item label={field.label} key={field.label}>
                      <div>{field.name}</div>
                    </Form.Item>
                  );
                })}
              </Form>
            </Collapse.Panel>
          </Collapse>

          <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
            <Collapse.Panel
              showArrow={false}
              header={
                <div className="flex-c-s" style={{ alignItems: 'center' }}>
                  <div>{intl.formatMessage({ id: 'vehicle_information' })} </div>
                  {!state ? (
                    <CaretDownOutlined onClick={() => toggle()} />
                  ) : (
                    <CaretUpOutlined onClick={() => toggle()} />
                  )}
                </div>
              }
              key={1}
            >
              <div>
                <Form form={form} className={px('form')} layout="vertical">
                  {false && (
                    <>
                      <Form.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                        <div>{ovsapVehDataDTO?.importNoFull}</div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                        <div>{ovsapVehDataDTO?.vtaNoFull}</div>
                      </Form.Item>
                    </>
                  )}

                  <Form.Item>
                    <div style={{ color: '#f00' }}>
                      {getLangGroup(
                        ovsapVehDataDTO?.newCheckResultCodeDto.codeCname,
                        ovsapVehDataDTO?.newCheckResultCodeDto.codeEname,
                        ovsapVehDataDTO?.newCheckResultCodeDto.codePname,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehBrandDescCn,
                        ovsapVehDataDTO?.vehBrandDescPt,
                        ovsapVehDataDTO?.vehBrandDescEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'style' })}>
                    <div>{ovsapVehDataDTO?.vehModel}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                    <div>{ovsapVehDataDTO?.vehBuildYear}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'color' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.colorDescCn,
                        ovsapVehDataDTO?.colorDescPt,
                        ovsapVehDataDTO?.colorDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehTypeDescCn,
                        ovsapVehDataDTO?.vehTypeDescPt,
                        ovsapVehDataDTO?.vehTypeDescEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehUsageDescCn,
                        ovsapVehDataDTO?.vehUsageDescPt,
                        ovsapVehDataDTO?.vehUsageDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                    <div>{ovsapVehDataDTO?.vin}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehSourceCtryDescCn' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehSourceCtryDescCn,
                        ovsapVehDataDTO?.vehSourceCtryDescPt,
                        ovsapVehDataDTO?.vehSourceCtryDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <div
                    style={{
                      height: state ? 'auto' : '0px',
                      overflow: 'hidden',
                      flexDirection: 'column',
                    }}
                    className="flex-c-s"
                  >
                    <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                      <div>
                        {getLangGroup(
                          ovsapVehDataDTO?.vehCateGoryDescCn,
                          ovsapVehDataDTO?.vehCateGoryDescPt,
                          ovsapVehDataDTO?.vehCateGoryDescEn,
                        )}
                      </div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                      <div>{ovsapVehDataDTO?.engineNo}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                      <div>{ovsapVehDataDTO?.engineNo2}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                      <div>{ovsapVehDataDTO?.engineNo3}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                      <div>{ovsapVehDataDTO?.engineNo4}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'cylinderQty' })}>
                      <div>{ovsapVehDataDTO?.cylinderQty}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'cylinderVol' })}>
                      <div>{`${ovsapVehDataDTO?.cylinderVol || 0} c.c`}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'maxPowerWithUnit' })}>
                      <div>{ovsapVehDataDTO?.maxPowerWithUnit}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'fuelTypeDescCn' })}>
                      <div>
                        {getLangGroup(
                          ovsapVehDataDTO?.fuelTypeDescCn,
                          ovsapVehDataDTO?.fuelTypeDescPt,
                          ovsapVehDataDTO?.fuelTypeDescEn,
                        )}
                      </div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'vehicle_size' })}>
                      {(ovsapVehDataDTO?.vehLength ||
                        ovsapVehDataDTO?.vehWidth ||
                        ovsapVehDataDTO?.vehHeight) && (
                        <div>
                          ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.vehLength} mm x
                          ({intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.vehWidth} mm x (
                          {intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.vehHeight} mm
                        </div>
                      )}
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'cabinTypeDescCn' })}>
                      <div>
                        {getLangGroup(
                          ovsapVehDataDTO?.cabinTypeDescCn,
                          ovsapVehDataDTO?.cabinTypeDescPt,
                          ovsapVehDataDTO?.cabinTypeDescEn,
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'car_size' })}>
                      {(ovsapVehDataDTO?.cabinLength ||
                        ovsapVehDataDTO?.cabinWidth ||
                        ovsapVehDataDTO?.cabinHeight) && (
                        <div>
                          ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.cabinLength} mm
                          x ({intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.cabinWidth} mm
                          x ({intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.cabinHeight}{' '}
                          mm
                        </div>
                      )}
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'seating_apacity' })}>
                      <div>{ovsapVehDataDTO?.seatQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'tyre_f_r_s' })}>
                      <div>{ovsapVehDataDTO?.tyreDesc}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'frontTyreQty' })}>
                      <div>{ovsapVehDataDTO?.frontTyreQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'rearTyreQty' })}>
                      <div>{ovsapVehDataDTO?.rearTyreQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'frontAxleQty' })}>
                      <div>{ovsapVehDataDTO?.frontAxleQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'rearAxleQty' })}>
                      <div>{ovsapVehDataDTO?.rearAxleQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'vehWeight' })}>
                      <div>
                        {ovsapVehDataDTO?.vehWeight} {ovsapVehDataDTO?.vehWeight && `kg`}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'vehLoadWeight' })}>
                      <div>
                        {ovsapVehDataDTO?.vehLoadWeight} {ovsapVehDataDTO?.vehLoadWeight && `kg`}
                      </div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'vehGrossWeight' })}>
                      <div>
                        {ovsapVehDataDTO?.vehGrossWeight} {ovsapVehDataDTO?.vehGrossWeight && `kg`}
                      </div>
                    </Form.Item>
                  </div>
                </Form>
              </div>
            </Collapse.Panel>
          </Collapse>
        </div>
      </div>
    </Watermark>
  );
};

export default FormEight;
