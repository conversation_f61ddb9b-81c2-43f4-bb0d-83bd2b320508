@prefix: preview-33d-ii-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 20px 12px;
      padding-bottom: 20px;
      font-size: 0.16rem;
      border-radius: 12px 12px 0px 0px;
      .my-card-item:nth-child(2) {
        background-color: #eee;
        .my-card-title {
          border-radius: 12px;
          background-color: #e9b745;
        }
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 700;
      padding: 5px 20px;
      min-height: 41px;
      font-size: 0.18rem;
      color: #ffffff;
      line-height: 21px;
      background: @brand-primary;
      border-radius: 10px 10px 0px 0px;
    }
    &sectionBody {
      .ant-form {
        border-radius: 0 0 12px 12px;
        background-color: #fff;
        .ant-form-item {
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
              // color: @brand-primary;
            }
          }
        }
      }
    }
  }
}

.owner-card-form-33d-ii-mobile {
  background-color: #fff;

  .preview-33d-ii-checkbox{
    font-size: 0.16rem;
    display: flex;
    align-items: center;
    .ant-checkbox-inner{
      width: 20px;
      height: 20px;
      border: 1px solid #ccc;
    }
  }
  .ant-form-item {
    font-size: 16px;
    .ant-form-item-label {
      label {
        font-size: 16px;
        color: #6c6c6c;
      }
    }

    .statusBg1 {
      padding: 0 2px;
      color: #f33b40;
    }
    .statusBg2 {
      padding: 0 2px;
      color: @brand-primary;
    }
  }
}

.preview-card-form-33d-ii-mobile{
  padding: 20px;
}

.owner-card-container-33d-ii-mobile {
  background-color: #eee;
  padding-top: 20px;
  .owner-card-item-33d-ii-mobile {
    margin-bottom: 20px;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    overflow: hidden;
    background: #fdf8f0;
    // &:nth-child(4n + 1) {
    //   background: #d9ebe7;
    // }
    // &:nth-child(4n + 2) {
    //   background: #dfeaf5;
    // }
    // &:nth-child(4n + 3) {
    //   background: #faedd9;
    // }
    // &:nth-child(4n + 4) {
    //   background: #f7e4da;
    // }

    .owner-card-title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .owner-card-form {
      background-color: #fff;
      padding: 20px;
    }
  }
  
}