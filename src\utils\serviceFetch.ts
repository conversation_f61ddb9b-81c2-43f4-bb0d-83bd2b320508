/* eslint-disable */

export type ServiceIntroItem = {
  content: {
    rendered: string;
  };
  acf: {
    public_services_faqs?: [];
    public_services_related_legislations: [];
  };
  slug: string;
  public_services_faqs_question: string;
  public_services_faqs_answer: string;
};

export type ServiceIntroType = ServiceIntroItem[];

const get = <T = any>(
  url: string,
  params?: Record<string, number | string | boolean> | URLSearchParams,
) =>
  window
    .fetch(`https://www.gov.mo${url}`, {
      body: JSON.stringify(params),
    })
    .then((res) => res.json());

export { get };
