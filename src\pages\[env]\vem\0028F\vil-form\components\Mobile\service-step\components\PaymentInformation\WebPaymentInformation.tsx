import React from 'react';
import { Table } from 'antd';
import { useIntl } from 'umi';

import styles from './WebPaymentInformation.less';

export interface WebPaymentInformationProps {
  data: {
    name: string;
    price: number;
    quantity: number;
  }[];
  total: number;
  unit: string;
}

const columnsConf = [
  { title: '收费项目', dataIndex: 'name', width: '60%' },
  {
    title: '单价',
    dataIndex: 'price',
    width: '20%',
    render: (price, { unit: curUnit }) => `${curUnit || 'MOP'} ${price.toFixed(2)}`,
  },
  { title: '数量', dataIndex: 'quantity', width: '20%' },
];

const WebPaymentInformation = (props: WebPaymentInformationProps) => {
  const { data, total, unit } = props;
  const intl = useIntl();
  const columns = columnsConf.map(i => ({ ...i, title: intl.formatMessage({ id: i.title }) }));
  return (
    <div className={styles.webRoot}>
      <Table columns={columns} dataSource={data} pagination={false} />
      <div className={styles.footer}>
        <div>{intl.formatMessage({ id: '合计' })}</div>
        <div className={styles.unit}>{unit}</div>
        <div className={styles.total}>{total.toFixed(2)}</div>
      </div>
    </div>
  );
};

export default WebPaymentInformation;
