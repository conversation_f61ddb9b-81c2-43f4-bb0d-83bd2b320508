import React, { useEffect, useState, useRef } from 'react';
import pc from 'prefix-classnames';
import './Success.less';

import { getLocale, history, useIntl } from 'umi';
import dayjs from 'dayjs';
import { Button, Alert, Image } from 'antd';
import {
  checkTxnJumpOrder,
  completeTxnForPay,
  getExTxnCompleteData,
  getTxnPendingApproval,
  receiptFileUrl,
} from '@/services/0028F';
import { NavBar } from 'antd-mobile';
import { LeftOutlined } from '@ant-design/icons';
import { Page, File } from '@gov-mo/mpaas-js-bridge';
import { getLangGroup, getLocaleMap, getResponseMessage, GovLang } from '@/locales/lang';
import { getBookingInfo } from '@/services/publicApi';
import { getStatusBarHeight } from '@/utils/hooks';
import { Toast } from 'antd-mobile-v5';

const px = pc('success-33d-mobile');

export interface FormTenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId: string;
  requestId: string;
  ID: string;
}

const FormTen = (props: FormTenProps) => {
  const intl = useIntl();
  const { className = '', txnId, requestId, ID, ...otherProps } = props;
  const [transId, setTransId] = useState(ID);
  const [tipMsg, setTipMsg] = useState('');
  const [completeData, setCompleteData] = useState<getExTxnCompleteDataRes>();
  const [pendingApprovalCodeDto, setPendingApprovalCodeDto] = useState<sysCodesByCodeTypeOpt | undefined>(undefined);

  const [download, setDownload] = useState<boolean>(false);
  const requestTimes = useRef<number>(0);

  function errorMessage(msg: string, duration?: number) {
    if (duration) {
      Toast.show({
        content: msg,
        icon: 'fail',
        duration,
      });
      return;
    }

    Toast.show({
      content: msg,
      icon: 'fail',
    });
  }

  const func = async () => {
    const res = await getExTxnCompleteData(txnId || requestId);
    if (!res) return;
    if (res?.code === 'W-8888') {
      setTimeout(() => {
        fetchExTxnCompleteData();
      }, 1000);
      return;
    }

    if (res && res.code === '0') {
      setCompleteData(res?.data ?? {});
      requestTimes.current = 0;
      setDownload(true);
    } else if (requestTimes.current < 21) {
      requestTimes.current += 1;

      setTimeout(() => {
        fetchExTxnCompleteData();
      }, 1000);
    } else if (requestTimes.current >= 21) {
      errorMessage(getResponseMessage(res), 0)
      setDownload(false);
    }
  };

  const handleBNU = async (transId: string) => {
    // BNU
    const res = await getTxnPendingApproval(transId);
    if (!res) return;
    if (res.code === '0') {
      setCompleteData(res.dataArr[0]);
      setDownload(true);
      setPendingApprovalCodeDto(res.dataArr[0]?.pendingApprovalCodeDto);
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const handleBOC = async () => {
    // BOC
    const res = await completeTxnForPay(requestId);
    if (!res) return;
    if (res.code === '0') {
      await func();
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const fetchExTxnCompleteData = async () => {
    try {
      if (txnId) {
        await func();
        return;
      }
      if (requestId) {
        const checkRes = await checkTxnJumpOrder(requestId);
        if (!checkRes) return;
        if (checkRes.code !== '0') {
          setTipMsg(getResponseMessage(checkRes));
          return;
        }
        if (checkRes.data.isShowCompletePage) {
          await handleBOC();
        }
        if (checkRes.data.isShowPendingPage) {
          setTransId(checkRes.data.transId);
          await handleBNU(checkRes.data.transId);
        }
        return;
      }
      if (transId) {
        // BNU
        await handleBNU(transId);
      }
    } catch (e: any) {
      setDownload(false);
      setTipMsg(e.message);
    }
  };

  useEffect(() => {
    fetchExTxnCompleteData();
  }, [txnId, requestId, transId]);

  function gotoIndex() {
    process.env.NODE_ENV === 'production' ? Page.closePage() : history.push('/web/vem');
  }

  async function receiptFile() {
    setTipMsg('');

    if (!txnId) {
      return;
    }

    const res = await receiptFileUrl(txnId);
    if (!res) return;
    res?.code === '0' &&
      File.previewPdfWithDownload({ url: `${res?.data}#.pdf`, fileName: `${txnId}.pdf` });
    res?.code !== '0' && setTipMsg(getResponseMessage(res));
  }

  function bookingInfo(spNo: string, vehId: string) {
    getBookingInfo(spNo, vehId, getLocaleMap[getLocale()] || GovLang.繁体中文).then((res) => {
      if (!res) return;
      if (res?.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }

      Page.openPage(res?.data);
    });
  }

  return (
    <>
      <NavBar
        style={{
          paddingTop: getStatusBarHeight(),
          color: '#ffffff',
          backgroundColor: completeData?.txnStatus
            ? completeData.txnStatus === 'E'
              ? '#f39373'
              : 'var(--primary-btn-fill)'
            : '',
        }}
        mode="light"
        leftContent={
          <LeftOutlined
            onClick={() => {
              gotoIndex();
            }}
          />
        }
      >
        <span style={{ color: '#ffffff' }}>{intl.formatMessage({ id: 'complete_submit' })}</span>
      </NavBar>

      {completeData && (
        <div
          style={{ paddingTop: 40 + getStatusBarHeight() }}
          className={`${px('root')} ${className}`}
          {...otherProps}
        >
          <Image
            preview={false}
            src={
              completeData.txnStatus === 'E'
                ? '/ovsap/image/failed/submit_failed.jpg'
                : '/ovsap/image/success/success-bg.jpg'
            }
            style={{ marginTop: '-1px' }}
            width="100%"
          />
          <h1>
            {/* {getLangGroup(
              completeData.txnStatusCn,
              completeData.txnStatusPt,
              completeData.txnStatusEn,
            )} */}
            {completeData.txnStatus === 'E' && (
              <>
                <span>{intl.formatMessage({ id: 'failed_submitted_application' })}</span>
              </>
            )}
            {['A', 'F'].includes(completeData.txnStatus || '') && (
              <>
                <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
              </>
            )}
            {completeData.txnStatus === 'S' && (
              <>
                <span>{intl.formatMessage({ id: 'bnu_pay_submitted_application' })}</span>
              </>
            )}
          </h1>

          {tipMsg && (
            <div className={px('alert')} >
              <Alert message={tipMsg} type="error" showIcon />
            </div>
          )}

          <div className={px('body')}>
            <div className={px('row')}>
              <div className={px('value')} style={{fontSize:'0.16rem'}}>{getLangGroup(completeData.serviceTitleRespDTO?.serviceTitleCn, completeData.serviceTitleRespDTO?.serviceTitlePt, completeData.serviceTitleRespDTO?.serviceTitleEn)}</div>
            </div>
            {transId ? (
              <>
                <div className={px('row')}>
                  <div className={px('label')}>
                    {intl.formatMessage({ id: 'import_license_number' })}
                  </div>
                  <div className={px('value')}>{completeData.importNoFull}</div>
                </div>

                <div className={px('row')}>
                  <div className={px('label')}>{intl.formatMessage({ id: 'vehicle_level' })}</div>
                  <div className={px('value')}>
                    {getLangGroup(
                      completeData.vehTypeDescCn,
                      completeData.vehTypeDescPt,
                      completeData.vehTypeDescEn,
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className={px('row')}>
                  <div className={px('label')}>{intl.formatMessage({ id: 'query_number' })}</div>
                  <div className={px('value')}>{completeData.spNo}</div>
                </div>
                <div className={px('row')}>
                  <div className={px('label')}>
                    {intl.formatMessage({ id: 'establishment_time' })}
                  </div>
                  <div className={px('value')}>
                    {completeData.txnStatusDate
                      ? dayjs(completeData.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                      : ''}
                  </div>
                </div>
              </>
            )}
            <div className={px('successTitlleMobile')} style={{textAlign:'left'}}>
              {
                pendingApprovalCodeDto ? getLangGroup(pendingApprovalCodeDto.codeCname, pendingApprovalCodeDto.codePname, pendingApprovalCodeDto.codeEname) : 
                intl.formatMessage(
                  { id: 'success_bottom_title_sst' },
                  {
                    platform: (
                      <span className={px('successBottomTitleSpan')}>
                        {intl.formatMessage({ id: 'platform_sst' })}
                      </span>
                    ),
                  },
                )
              }
            </div>
            {/* <div>{intl.formatMessage({ id: 'success_msg_tip' })} </div> */}
          </div>

          <div className={px('footer')}>
            {download && (
              <Button
                type="default"
                onClick={() => {
                  gotoIndex();
                }}
              >
                {intl.formatMessage({ id: 'complete' })}
              </Button>
            )}
            {download && completeData.txnStatus === 'F' && !transId && (
              <>
                <Button type="primary" onClick={receiptFile}>
                  {intl.formatMessage({ id: 'download_receipt' })}
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    bookingInfo(completeData.spNo, completeData.vehId);
                  }}
                >
                  {intl.formatMessage({ id: 'appointment_vehicle_inspection' })}
                </Button>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default FormTen;
