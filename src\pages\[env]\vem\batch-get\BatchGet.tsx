import React, { useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import { getLocale, history, Location, useIntl, useLocation } from 'umi';
import './BatchGet.less';
import { Alert, Button, Form, Input, Modal, Select, Tag } from 'antd';
import QRCode from 'qrcode';
import { isMobile } from '@/utils';
import { TweenOneGroup } from 'rc-tween-one';
import {
  createBatchExData,
  deleteBatchExDetail,
  getBatchExDataById,
  getBatchExTxnData,
  updateBatchExData,
} from '@/services/0028F';
import { NavBar } from 'antd-mobile';
import { getStatusBarHeight } from '@/utils/hooks';
import { LeftOutlined } from '@ant-design/icons';
import { getLangGroup, getResponseMessage, Lang } from '@/locales/lang';
import { chain } from 'lodash';
import dayjs from 'dayjs';
import { downQRCode } from '@/hook';

type BatchGetProps = {};

type BatchGetLocation = {
  query: {
    txnIds: string;
    batchId: string;
  };
} & Location;

const BatchGetPage = (props: BatchGetProps) => {
  const px = pc(isMobile() ? 'page-batch-get-mobile' : 'page-batch-get');
  const intl = useIntl();
  const [tipMsg, setTipMsg] = useState('');

  const { txnIds, batchId: bId } = (useLocation() as BatchGetLocation).query;

  const [form] = Form.useForm();
  const [qrcodeStatus, setQrcodeStatus] = useState(false);
  const [qrcodeUrl, setQrcodeUrl] = useState('');
  const [info, setInfo] = useState<getBatchExTxnDataRes>();
  const [getAddressList, setGetAddressList] = useState<any>([]);
  // const [carList, setCarList] = useState([]);
  const [batchId, setBatchId] = useState(bId);

  const getList = async () => {
    const func = batchId ? getBatchExDataById : getBatchExTxnData;
    console.log('batchId', batchId);
    const postData = {
      batchId: batchId || '',
      txnIds: txnIds?.split(','),
    };
    const res = await func(postData);
    if (res?.code === '0') {
      console.log('res', res, batchId);
      batchId && openQRCode(res.data);
      setInfo(res.data);
      setGetAddressList(
        res.data?.addrLicCodes.map((item) => {
          return {
            label: getLangGroup(item.codeCname, item.codePname, item.codeEname),
            value: item.codeKey,
          };
        }),
      );
      setFieldsValue({
        list: res.data?.ovsapBatchExDataDTOs,
        licAddrCode: res.data.licAddrCode || undefined,
      });
      return;
    }

    setTipMsg(getResponseMessage(res));
  };
  let setFieldsValue = (data) => {
    form.setFieldsValue(data);
    setTimeout(() => {
      // let list = form.getFieldValue('list');
      // setCarList(list);
    }, 0);
  };
  useEffect(() => {
    // 从记录过来的 从列表过来的
    if (txnIds || batchId) {
      getList();
    }
  }, [txnIds, batchId]);

  const handleClose = () => {
    history.push('/web/vem/batch-get/list');
  };

  const openQRCode = async (info) => {
    if (info?.batchCode) {
      const url = await QRCode.toDataURL(info?.batchCode, { errorCorrectionLevel: 'H' });
      setQrcodeUrl(url);
    }
  };

  const handleGet = async () => {
    const values = await form.validateFields();
    const txnIdsList = values.list.map((item) => item.txnId);
    const func = batchId ? updateBatchExData : createBatchExData;
    const res = await func({
      batchId,
      txnIds: txnIdsList,
      licAddrCode: values.licAddrCode,
    });
    console.log(res);
    if (res?.code === '0') {
      if (!batchId) {
        setBatchId((res.data as any)?.batchId);
      }

      history.push({
        pathname: `/web/vem/batch-get/list`,
      });
    }
  };

  const downloadQRCode = (base64Image, item) => {
    let byteString = atob(base64Image.split(',')[1]);
    let mimeString = base64Image.split(',')[0].split(':')[1].split(';')[0];
    let ab = new ArrayBuffer(byteString.length);
    let ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    let blob = new Blob([ab], { type: mimeString });

    let url = URL.createObjectURL(blob);
    let a = document.createElement('a');
    a.href = url;
    a.download = item.batchCode + '.png';
    document.body.appendChild(a);
    a.click();

    // 释放URL对象
    URL.revokeObjectURL(url);
  };
  let carData = useMemo(() => {
    const groupKey = getLocale() === Lang.葡语 ? 'codePname' : 'codeCname';
    let data = chain(info?.vemGroup)
      .groupBy(groupKey) // 先按 type 分组
      .map((list, label) => ({ label, list })) // 转换结构
      .value();
    console.log('vemGroup data', data);
    return [...data];
  }, [info]);
  return (
    <>
      {isMobile() ? (
        <NavBar
          style={{
            paddingTop: getStatusBarHeight(),
          }}
          mode="light"
          leftContent={
            <LeftOutlined
              onClick={() => {
                handleClose();
              }}
            />
          }
        >
          <span style={{ color: '#333' }}>{intl.formatMessage({ id: 'batch_get' })}</span>
        </NavBar>
      ) : null}

      <div
        className={`${px('root')}`}
        style={{ paddingTop: isMobile() ? 30 + 10 + getStatusBarHeight() : 0 }}
      >
        {tipMsg ? (
          <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
            <Alert message={tipMsg} type="error" showIcon />
          </div>
        ) : null}
        {false && (
          <h2 className={px('title')}>
            {intl.formatMessage({ id: 'batch_no' })}
            {info?.batchId || ''}
          </h2>
        )}
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'batch_get' })}</div>

        <div className="my-card-container" style={{ marginTop: '16px' }}>
          <Form
            form={form}
            layout={isMobile() ? 'horizontal' : 'vertical'}
            preserve={false}
            scrollToFirstError
            className="batch-get-card-form"
            style={{ position: 'relative' }}
          >
            <Form.Item name="list" hidden>
              <Input />
            </Form.Item>
            {false && (
              <Form.List name="list">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map((field, index) => {
                      return (
                        <div className="my-card-item">
                          <div className="my-card-title">
                            <span>
                              {form.getFieldValue(['list', field.name, 'detailStatusCn']) ||
                                intl.formatMessage({ id: 'waiting_ex_cards' })}
                            </span>
                            {fields.length > 1 &&
                            form.getFieldValue(['list', field.name, 'detailStatus']) == 'A' ? (
                              <Button
                                onClick={async () => {
                                  if (fields.length > 1) {
                                    const current = form.getFieldValue(['list', field.name]); // 当前项
                                    const res = await deleteBatchExDetail({
                                      detailId: current.detailId,
                                      batchId,
                                      vehId: current.vehId,
                                      txnId: current.txnId,
                                    });
                                    if (res?.code === '0') {
                                      remove(field.name);
                                      getList();
                                    }
                                  }
                                }}
                                danger
                              >
                                {intl.formatMessage({ id: 'delete' })}
                              </Button>
                            ) : null}
                          </div>
                          <div className="my-card-body">
                            <Form.Item
                              name={[field.name, 'vehTypeDescCn']}
                              label={intl.formatMessage({ id: 'vehicle_level' })}
                            >
                              <div>{form.getFieldValue(['list', field.name, 'vehTypeDescCn'])}</div>
                            </Form.Item>
                            <Form.Item
                              name={[field.name, 'vtaNoFull']}
                              label={intl.formatMessage({ id: 'model_approval_number' })}
                            >
                              <div>{form.getFieldValue(['list', field.name, 'vtaNoFull'])}</div>
                            </Form.Item>
                            <Form.Item
                              name={[field.name, 'vehBrandDescCn']}
                              label={intl.formatMessage({ id: 'brand' })}
                            >
                              <div>
                                {form.getFieldValue(['list', field.name, 'vehBrandDescCn'])}
                              </div>
                            </Form.Item>
                            <Form.Item
                              name={[field.name, 'vehModel']}
                              label={intl.formatMessage({ id: 'style' })}
                            >
                              <div>{form.getFieldValue(['list', field.name, 'vehModel'])}</div>
                            </Form.Item>
                          </div>
                        </div>
                      );
                    })}
                  </>
                )}
              </Form.List>
            )}
            <div>
              <div className="my-card-item" style={{ marginBottom: '0px' }}>
                <div className="my-card-title">
                  {getLangGroup(info?.companyCname, info?.companyEname, info?.companyPname)}
                </div>
                <div className="my-card-body flex-r-s" style={{ alignItems: 'flex-start' }}>
                  <div className="flex-c-c" style={{ alignItems: 'flex-start' }}>
                    {carData.map((item) => (
                      <div>
                        <div>{item.label}</div>
                        <div className="flex-1">
                          <TweenOneGroup
                            enter={{
                              scale: 0.8,
                              opacity: 0,
                              type: 'from',
                              duration: 100,
                            }}
                            onEnd={(e) => {
                              if (e.type === 'appear' || e.type === 'enter') {
                                (e.target as any).style = 'display: inline-block';
                              }
                            }}
                            leave={{ opacity: 0, width: 0, scale: 0, duration: 200 }}
                            appear={false}
                          >
                            {item.list.map((field, index) => {
                              return (
                                <Tag
                                  closable={info?.batchStatus === 'A'}
                                  onClose={async (e) => {
                                    e.preventDefault();
                                    const res = await deleteBatchExDetail({
                                      detailId: field.detailId,
                                      batchId,
                                      vehId: field.vehId,
                                      txnId: field.txnId,
                                    });
                                    if (res?.code === '0') {
                                      getList();
                                    }
                                  }}
                                >
                                  {field.plateNo}
                                </Tag>
                              );
                            })}
                          </TweenOneGroup>
                        </div>
                      </div>
                    ))}
                  </div>
                  {info?.batchCode ? (
                    <div
                      className="flex-c-c"
                      style={{ width: '200px', flex: 1, alignItems: isMobile() ? 'center' : 'end' }}
                    >
                      <img src={qrcodeUrl} style={{ width: 200, height: 200 }} />
                      <div style={{ textAlign: 'center', fontSize: '16px', width: '200px' }}>
                        {intl.formatMessage(
                          { id: 'qrcode_car_count' },
                          { count: info?.batchDetailSize || 0 },
                        )}
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
              <div style={{ color: '#f00', paddingBottom: '10px', marginTop: '10px' }}>
                {intl.formatMessage({ id: 'license_plate' })}
              </div>
              <Form.Item
                name="licAddrCode"
                label={intl.formatMessage({ id: 'get_address' })}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'please_select' }),
                  },
                ]}
              >
                <Select
                  options={getAddressList}
                  placeholder={intl.formatMessage({ id: 'licAddrCode' })}
                ></Select>
              </Form.Item>
            </div>
            <div
              id="capture-area"
              style={{
                position: 'absolute',
                left: '-99999px',
                width: '100%',
              }}
            >
              <div className="my-card-item" style={{ marginBottom: '0px' }}>
                <div className="my-card-title" style={{ fontSize: '16px' }}>
                  {getLangGroup(info?.companyCname, info?.companyEname, info?.companyPname)}
                </div>
                <div className="my-card-body">
                  <div className="flex-r-s" style={{ alignItems: 'flex-start' }}>
                    <div>
                      <div className="flex-c-c" style={{ alignItems: 'flex-start' }}>
                        {carData.map((item) => (
                          <div>
                            <div>{item.label}</div>
                            <div className="flex-1">
                              {item.list.map((field, index) => {
                                return <Tag onClose={async (e) => {}}>{field.plateNo}</Tag>;
                              })}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div
                      className="flex-c-c"
                      style={{ width: '200px', flex: 1, alignItems: isMobile() ? 'center' : 'end' }}
                    >
                      <img src={qrcodeUrl} style={{ width: 200, height: 200 }} />
                      <div style={{ textAlign: 'center', fontSize: '16px', width: '200px' }}>
                        {intl.formatMessage(
                          { id: 'qrcode_car_count' },
                          { count: info?.batchDetailSize || 0 },
                        )}
                      </div>
                    </div>
                  </div>
                  <div style={{ marginTop: isMobile() ? '0px' : '-120px' }}>
                    <div style={{ fontSize: '16px', marginTop: '30px' }}>
                      <span style={{ color: 'rgb(108, 108, 108)' }}>
                        {intl.formatMessage({ id: 'collection_time' })} &nbsp;&nbsp;
                        {dayjs().format('YYYY-MM-DD HH:mm:ss')}
                      </span>
                    </div>
                    <div style={{ fontSize: '16px', marginBottom: '16px' }}>
                      <span style={{ color: 'rgb(108, 108, 108)' }}>
                        {intl.formatMessage({ id: 'get_address' })} &nbsp;&nbsp;
                        {
                          getAddressList.find(
                            (item) => item.value === form.getFieldValue('licAddrCode'),
                          )?.label
                        }
                      </span>
                    </div>
                  </div>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: getLangGroup(
                        info?.addrLicCodeMsgDto?.codeCname,
                        info?.addrLicCodeMsgDto?.codePname,
                        info?.addrLicCodeMsgDto?.codeEname,
                      ),
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </Form>
        </div>
        <div className="batch-get-footer">
          <div
            className={`${'footer-container'}`}
            style={{ flexDirection: isMobile() ? 'column' : 'row', gridGap: '12px' }}
          >
            <Button onClick={handleClose} type="default">
              {intl.formatMessage({ id: 'turn_off' })}
            </Button>
            {info?.batchCode && (
              <Button onClick={() => downQRCode('capture-area', `${info?.batchId}`)} type="default">
                {intl.formatMessage({ id: 'qr_code' })}
              </Button>
            )}
            {(info?.batchStatus === 'A' || !batchId) && (
              <Button onClick={handleGet} type="primary">
                {intl.formatMessage({ id: 'saved' })}
              </Button>
            )}
          </div>
        </div>
      </div>

      <Modal
        title={intl.formatMessage({ id: 'qr_code' })}
        width="300px"
        cancelText={intl.formatMessage({ id: 'turn_off' })}
        visible={qrcodeStatus}
        onCancel={() => setQrcodeStatus(false)}
        footer={null}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <img src={qrcodeUrl} style={{ width: 200, height: 200 }} />
          <div style={{ fontSize: 16 }}>
            {getLangGroup(info?.companyCname, info?.companyPname, info?.companyEname)}
          </div>
          <div style={{ fontSize: 16, marginBottom: 10 }}>
            {intl.formatMessage({ id: 'qrcode_car_count' }, { count: info?.batchDetailSize || 0 })}
          </div>
          <Button type="primary" onClick={() => downloadQRCode(qrcodeUrl, info)}>
            {intl.formatMessage({ id: 'download' })}
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default BatchGetPage;
