@prefix: list-three;

.@{prefix} {
  &- {
    &root {
      max-width: 1200px;
      margin: auto;
      height: 100%;
      background-color: #fff;
      .web-base-index-ad-main {
        padding: 0 0.32rem;
        padding-top: 0.48rem;
        flex-grow: 1;
        overflow-y: auto
      }

      .web-base-index-ad-title {
        background: #79f29d
      }

      .web-base-index-ad-button {
        font-weight: 700;
        display: flex;
        align-items: center;
        height: 1rem;
        border-radius: 0.2rem;
        font-size: 0.2rem;
        padding: 0.1rem;
      }

      .web-base-index-ad-changename {
        background: linear-gradient(135deg,#f9eed5,#fdf8ec)
      }

      .web-base-index-ad-changefoot {
        background: linear-gradient(135deg,#d4efe9,#ecf8f6)
      }

      .web-base-index-ad-button3 {
        background: linear-gradient(134deg,#fffef9 3%,#ffeccd 98%)
      }

      .web-base-index-ad-icon_position {
        position: absolute;
        right: 0.4rem;
      }

      .web-base-index-ad-icon_size {
        width: 0.8rem;
        height: 0.8rem;
        padding: 0.1rem;
      }


      .am-whitespace.am-whitespace-md {
        height: 0.28rem;
      }



      .web-base-index-ad-business {
        background: #084AB8;
      }
      .web-base-index-ad-business a{
        color: #fff;
      }
      .web-base-index-ad-record {
        background: #E9B745;
      }
      .web-base-index-ad-record a{
        color: #fff;
      }


    }
  }
}
