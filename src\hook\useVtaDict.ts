import { useEffect, useState } from 'react';
import { getSysCodeByTypeRd } from '@/services/vta';
import { getLangGroup } from '@/locales/lang';

export type SelectOptions = {
  label: string;
  value: string;
};

export const useVtaDict = <T extends readonly number[]>(dictCodes: T) => {
  type DictKey = T[number];
  type DictRecord = Record<DictKey, SelectOptions[]>;

  const [dict, setDict] = useState<DictRecord>();
  const [loading, setLoading] = useState<boolean>(false);

  const getDictAll = async () => {
    setLoading(true);
    try {
      const res = await Promise.all(
        dictCodes.map((code) => getSysCodeByTypeRd({ codeType: code, codeStatus: 'A' })),
      );
      const dictData: Record<number, SelectOptions[]> = {};
      dictCodes.forEach((code, index) => {
        if (res && res[index]) {
          const item = res[index];
          if (item && item.dataArr.length) {
            dictData[code] = item.dataArr.map((item) => ({
              label: getLangGroup(item.codeCname, item.codePname, item.codeEname),
              value: item.codeKey,
            }));
          }
        }
      });
      setDict(dictData);
      console.log(dictData);
    } catch (error) {
      console.error('Failed to fetch dictionary data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (dictCodes && dictCodes.length > 0) {
      getDictAll().then();
    }
  }, [dictCodes.join(',')]);

  return { dict, loading, refresh: getDictAll };
};
