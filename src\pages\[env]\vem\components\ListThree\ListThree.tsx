import React from 'react';
import pc from 'prefix-classnames';
import './ListThree.less';
import { useIntl } from 'umi';
import { getStatusBarHeight } from '@/utils/hooks';
import { isMobile } from '@/utils';

const px = pc('list-three');

export interface ListTwoProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  onGoto: (url: string) => void;
}

const ListTwo = (props: ListTwoProps) => {
  const intl = useIntl();
  const { className = '', ...otherProps } = props;
  return (
    <div
      style={{ paddingTop: isMobile() ? 40 + getStatusBarHeight() : 0 }}
      className={`${px('root')} ${className}`}
      {...otherProps}
    >
      <div className="web-base-index-ad-main">
        <div className="web-base-index-ad-business web-base-index-ad-button">
          <a
            onClick={() => {
              otherProps.onGoto('/vta-form');
            }}
          >
            {' '}
            {intl.formatMessage({ id: 'model_approval_application' })}
            <span role="img">
              <svg
                viewBox="64 64 896 896"
                focusable="false"
                data-icon="right"
                width="1em"
                height="1em"
                fill="currentColor"
                aria-hidden="true"
              >
                <path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"></path>
              </svg>
            </span>
          </a>
        </div>
        <div className="am-whitespace am-whitespace-md"></div>
        <div className="web-base-index-ad-record web-base-index-ad-button">
          <a
            onClick={() => {
              otherProps.onGoto('/vta-record');
            }}
          >
            {intl.formatMessage({ id: 'application_record' })}
            <span role="img">
              <svg
                viewBox="64 64 896 896"
                focusable="false"
                data-icon="right"
                width="1em"
                height="1em"
                fill="currentColor"
                aria-hidden="true"
              >
                <path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"></path>
              </svg>
            </span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default ListTwo;
