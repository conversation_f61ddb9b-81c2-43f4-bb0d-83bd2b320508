import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';
import { Steps } from 'antd';
import Preview from '../Preview';
import { useIntl } from 'umi';
import { Page } from '@gov-mo/mpaas-js-bridge';
import { NavBar } from 'antd-mobile';
import { LeftOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { history } from 'umi';
import { getStatusBarHeight } from '@/utils/hooks';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  classPrefix: string;
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;
  setDisableConfirmApply: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (string) => void;

  setShowTempStore: (boolean) => void;
  setShowConfirmApply: (boolean) => void;

  detail: getEXTxnDataRes;
  txnId: string | undefined;
  verficationStatus?: string;
  currentOwner?: saveTxnOwnersParams;
  tipMsg?: string;
  serviceTitle?: string;
  ownerId?: string;
  handlePrevStep: () => void;
}

const FormBase = (props: FormBaseProps) => {
  const intl = useIntl();

  const { step, className = '', ...otherProps } = props;
  const [currentStep, setCurrentStep] = useState(step);
  const {
    setDisableNextStep,
    setNextStepText,
    setShowConfirmApply,
    setDisableConfirmApply,
    detail,
    txnId,
    handlePrevStep,
  } = otherProps;

  const px = pc(otherProps.classPrefix);
  const [headerOption, showHeaderOption] = useState(false);

  const [verficationStatus, setVerficationStatus] = useState<string>(props.verficationStatus || '');
  const [currentOwner, setCurrentOwner] = useState<saveTxnOwnersParams | undefined>();
  const [tipMsg, setTipMsg] = useState(props.tipMsg);
  const [serviceTitle, setServiceTitle] = useState(
    props.serviceTitle || intl.formatMessage({ id: 'ex_test_license_plate_application' }),
  );

  useEffect(() => {
    setVerficationStatus(props.verficationStatus || '');
    setCurrentOwner(props.currentOwner);
    setCurrentStep(props.currentOwner?.verficationStatus === 'Y' ? 2 : 1);
    setTipMsg(props.tipMsg);
    setServiceTitle(
      props.serviceTitle || intl.formatMessage({ id: 'ex_test_license_plate_application' }),
    );
  }, [props.verficationStatus, props.currentOwner, props.tipMsg, props.serviceTitle]);

  // 生成右侧表单内容
  const renderRightContent = () => {
    return (
      <Preview
        setShowConfirmApply={setShowConfirmApply}
        setDisableNextStep={setDisableNextStep}
        setNextStepText={setNextStepText}
        detail={detail}
        txnId={txnId}
        verficationStatus={verficationStatus}
        currentOwner={currentOwner}
        tipMsg={tipMsg}
        setDisableConfirmApply={setDisableConfirmApply}
      />
    );
  };

  const stepOptions = [
    {
      sort: 1,
      title: intl.formatMessage({ id: 'data_confirmation' }),
    },
    {
      sort: 2,
      title: intl.formatMessage({ id: 'identity_recognition' }),
    },
  ];

  return (
    <>
      <NavBar
        style={{ paddingTop: getStatusBarHeight() }}
        mode="light"
        leftContent={<LeftOutlined onClick={handlePrevStep} />}
        rightContent={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {(
              <div className="select">
                <span
                  onClick={() => {
                    showHeaderOption(!headerOption);
                  }}
                >
                  { intl.formatMessage({ id: 'application_steps' }) }
                </span>
                <div
                  className="opt-wrap"
                  style={{ display: headerOption ? 'block' : 'none' }}
                  onClick={() => {
                    showHeaderOption(false);
                  }}
                >
                  <div className={px('step')}>
                    <Steps direction="vertical" size="small" current={currentStep - 1}>
                      {stepOptions.map((item) => {
                        return <Steps.Step key={item.sort} title={item.title} icon={item.sort} />;
                      })}
                    </Steps>
                  </div>
                </div>
              </div>
            )}
            <CloseCircleOutlined
              onClick={() => {
                process.env.NODE_ENV === 'production' ? Page.close() : history.push('/web/vem');
              }}
            />
          </div>
        }
      ></NavBar>
      <div
        style={{ paddingTop: 45 + getStatusBarHeight() }}
        className={`${px('root')} ${className}`}
        {...otherProps}
      >
        <h2 className={px('title')}>{serviceTitle}</h2>
        <div className={px('stepinfo')}>
          <div className={px('mobile-step')}>
            {stepOptions.map((item, index) => {
              return (
                <>
                  {index > 0 && <div className="line"></div>}
                  <div
                    className={`step-item ${currentStep === item.sort ? 'progress' : ''} ${
                      item.sort < currentStep ? 'finish' : ''
                    }`}
                  >
                    <span>{item.sort}</span>
                  </div>
                </>
              );
            })}
          </div>
          <div className={px('form')}>{renderRightContent()}</div>
        </div>
      </div>
    </>
  );
};

export default FormBase;
