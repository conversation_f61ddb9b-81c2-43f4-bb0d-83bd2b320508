import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './FormEight.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Collapse } from 'antd';

const px = pc('form-eight');

export interface FormEightProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setDisableNextStep: (boolean) => void;
  setNextStepText: (string) => void;
}

const FormEight = (props: FormEightProps) => {
  const { className = '', ...otherProps } = props;
  const { setDisableNextStep, setNextStepText } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  const [posted, setPosted] = useState(false)

  useEffect(() => {
    setNextStepText('繳費')
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }))
    }
  }, [])

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
  );
};

export default FormEight;
