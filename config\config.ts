import { defineConfig } from 'umi';
import theme from '@gov-mo/components/lib/theme';
import px2rem from 'postcss-plugin-px2rem';
import px2vm from 'postcss-px-to-viewport';

export default defineConfig({
  locale: {
    default: 'zh-MO',
    antd: false,
  },
  hash: true,
  ignoreMomentLocale: true,
  antd: false,
  lessLoader: {
    modifyVars: {
      hack: `true; @import "~@/mixins.less";`,
      '@font-size-base': '16px',
    },
    javascriptEnabled: true,
  },
  theme: {
    ...theme,
    '@hd': '2px',
    '@color-primary': '#15AADA',
    '@brand-success': '#084ab8', // 13A07B
    '@brand-gradient': 'linear-gradient(52deg, #1382E4 0%, #33A3FE 48%, #1EF5CB 100%)',
    '@brand-gradient-90': 'linear-gradient(90deg, #1382E4 0%, #33A3FE 48%, #1EF5CB 100%)',
    "@font-size-md": '16px',
    '@mobile-primary-prefix': 'cbd-mobile-form',
    '@mobile-baseColor': '#595959',
    '@mobile-subColor': '#8c8c8c',
    '@font-size-base': '16px',

    // business
    '@web-primary-color': '#084ab8',
    '@brand-primary': '#084ab8',

    // personal
    // '@web-primary-color': '#13a07b',
    // '@brand-primary':'#13a07b',

  },
  publicPath: '/',
  externals: {
    '@gov-mo/mpaas-js-bridge': 'JsApi',
  },
  mPaaS: {
    production: {
      virtualDomain: 'https://mds-mpaas-uat.mo.gov.mo',
      packagePath: 'A0DD45C221421_sit',
      devTool: true,
    },
    development: {
      virtualDomain: 'https://mds-mpaas-uat.mo.gov.mo',
      packagePath: 'A0DD45C221421_sit',
      devTool: true,
    },
  },
  plugins: [
    '@gov-mo/framework/plugin/umi-plugin-auto-config',
    '@gov-mo/framework/plugin/umi-plugin-excludes',
  ],
  extraPostCSSPlugins: [
    px2rem({
      propWhiteList: ['font-size'],
      selectorBlackList: [
        'no2rem',
        /^html$/,
        'web',
        /^(\.ant-){1}(?!form)/,
        'rmc-picker',
        'am-toast',
      ],
      exclude: /node_modules/i,
    }),
    // px2vm({
    //   viewportWidth: 750,
    //   viewportHeight: 1334,
    //   unitPrecision: 3,
    //   viewportUnit: 'vw',
    //   selectorBlackList: [
    //     '.ignore',
    //     '.hairlines',
    //     'web',
    //     /^(\.ant-){1}(?!form)/,
    //     'rmc-picker',
    //     'am-toast',
    //   ],
    //   minPixelValue: 1,
    //   mediaQuery: false,
    // }),
  ],
  copy: [
    {
      from: 'public/constantsConfig.js',
      to: 'ovsap/constantsConfig.js',
    }
  ],
  chainWebpack(config, { webpack }) {
    process.env.NODE_ENV === 'production' && config.output
      .filename('ovsap/[name].[contenthash].js')
      .chunkFilename('ovsap/[name].[contenthash].chunk.js');

    process.env.NODE_ENV === 'production' && config.plugin('extract-css').tap(args => {
      args[0].filename = 'ovsap/[name].[contenthash].css';
      args[0].chunkFilename = 'ovsap/[name].[contenthash].chunk.css';
      return args;
    });

    config.plugin('DefinePlugin').use(
      new webpack.DefinePlugin({
        ENV: 'window.GOV_MO.ENV',
        PLATFORM: 'window.GOV_MO.PLATFORM',
        COMMON_ACCESS_TOKEN: 'window.GOV_MO.COMMON_ACCESS_TOKEN',
        API_HOST: 'window.GOV_MO.API_HOST',
        APP_ID: 'window.GOV_MO.APP_ID',
        AUTH_SERVER: 'window.GOV_MO.AUTH_SERVER',
        AUTH_SERVER_BUSINESS: 'window.GOV_MO.AUTH_SERVER_BUSINESS',
        CLIENT_ID: 'window.GOV_MO.CLIENT_ID',
        CLIENT_SECRET: 'window.GOV_MO.CLIENT_SECRET',
        BUCKET_ID: 'window.GOV_MO.BUCKET_ID',
        DEV_REDIRECT_URL: 'window.GOV_MO.DEV_REDIRECT_URL',
      }),
    );
  },
  headScripts: [{ src: process.env.NODE_ENV === 'production' ? '/ovsap/constantsConfig.js' : '/constantsConfig.js' }],
  proxy: {
    // '/ovsap/api/admin': {
    //   target: 'https://appdev.dsat.gov.mo/',
    //   pathRewrite: { '^/ovsap/api/admin': '/ovsap/admin' },
    //   changeOrigin: true,
    // },
    '/ovsap/api': {
      target: 'https://appdev.dsat.gov.mo/',
      changeOrigin: true,
    },
    // '/ovsap/api': {
    //   target: 'http://**************:8443',
    //   pathRewrite: { '^/ovsap/api': '/ovsap' },
    // },
  },
});
