import React, { useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Form1.less';

import { useIntl } from 'umi';
import { Col, Form, Input, InputNumber, Radio, Row } from 'antd';
import { useVtaDict } from '@/hook/useVtaDict';
import { getVTAFeaturesByTxnId, saveVTAFeatures } from '@/services/vta';
import { useGetEmu } from '../../hooks';
import { useGetRes } from '../../hooks/useGetRes';
import { useGetFormParam } from '../../hooks/useGetFormParam';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';

const px = pc('vta-form-form-1');

export interface FormOneProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  setDisablePrevStep: (status: boolean) => void;
  setDisableNextStep: (status: boolean) => void;
  nextStep: (callback?: () => boolean | Promise<boolean>) => void;
  txnId?: string;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

// [
//   { "label": "1輕型汽車", "value": "L" },
//   { "label": "2重型汽車", "value": "P" },
//   { "label": "3輕型摩托車", "value": "C" },
//   { "label": "4重型摩托車", "value": "M" },
//   { "label": "6半掛車", "value": "S" }
//   { "label": "7工業機器", "value": "I" },
// ]
type VehTypeKey = 'C' | 'M' | 'L' | 'P' | 'I' | 'S';
// [
//   { "label": "貨車", "value": "M" },
//   { "label": "其它", "value": "O" },
//   { "label": "客車", "value": "P" },
//   { "label": "客貨車", "value": "X" },
// ]
type VtaCategoryKey = 'M' | 'O' | 'P' | 'X';
// [
//   { "label": "公共巴士", "value": "1" },
//   { "label": "不設站位的客車", "value": "2" },
//   { "label": "其他", "value": "3" }
// ]
type VtaUsageCodeKey = '1' | '2' | '3';

console.log('Component:Form1');
const FormOne = (props: FormOneProps) => {
  const {
    className = '',
    setDisableNextStep,
    txnId,
    nextStepTrigger,
    handleNextStep,
    ...otherProps
  } = props;
  // 出產國或地區 	出產國	vehBuildCtryCode	31203
  // 來源國或地區		vehSourceCtryCode	31203
  // 種類	車輛種類	vtaCategory		22007
  // 重型客車用途	車輛用途	vtaUsageCode	22002
  // 座椅尺寸 (mm) *		singleSeatSize 22022
  // 乘客腳踏型式		passFootholdType		50013
  // 扶手位置		passHandrailType		50012
  const { dict } = useVtaDict([31203, 22007, 22002, 22022, 50013, 50012, 50107] as const);
  let { emuObj } = useGetEmu();
  //   {
  //   vehTypeOp: '31201',
  //   steeringWheelPositionOp: '50118',
  //   gearboxTypeOp: '31223',
  //   chandongfangshi1: '22011',
  //   chandongfangshi2: '22012',
  //   varSpeedQtyOp: '22013',
  //   vehBrandCodeOp: '31204',
  // }
  const intl = useIntl();
  const [form] = Form.useForm();

  const [vtaCategory, setVtaCategory] = useState<VtaCategoryKey>();
  const [vtaUsageCode, setVtaUsageCode] = useState<VtaUsageCodeKey>();
  const [passCapacity, setPassCapacity] = useState<number>();
  // dict?.[22022] 跟座位數量來決定，如果座位數量填1，一定是單一；如果超過1，則顯示2個checkbox:單一和獨立，並填寫對應尺寸。型號認可主檔都新增4個欄位分開存獨立和單一座位的長和寬
  const [seatType, setSeatType] = useState<string>();
  const seatTypeOpt = useMemo(() => {
    if (passCapacity === 1) {
      return dict?.[22022].filter((item) => item.value === 'SL');
    }
    return dict?.[22022];
  }, [passCapacity, dict?.[22022]]);

  let { getFormData } = useGetRes();
  let { formData } = useGetFormParam(txnId);
  let vehType: any = useMemo(() => {
    return formData?.vehType;
  }, [formData]);
  const [canEditable, setCanEditable] = useState({});
  const getRecordData = async () => {
    if (!txnId) return;
    const res = await getVTAFeaturesByTxnId({ txnId: txnId });
    let { formData, canEditable } = getFormData(res.data);
    setCanEditable(canEditable);
    form.setFieldsValue({ ...formData });
  };

  useEffect(() => {
    form.setFieldsValue({ txnId });
    setDisableNextStep(false);
    getRecordData();
  }, [txnId]);

  const submit = async () => {
    try {
      const data = await form.validateFields();
      let res = await saveVTAFeatures(data);
      if (res.error) {
        return false;
      }
      return true;
    } catch (error) {
      return false;
    }
  };
  useEffect(() => {
    if (!nextStepTrigger || nextStepTrigger === 0) {
      return;
    }
    handleNextStep(() => {
      return submit();
    });
  }, [nextStepTrigger]);
  // [
  //   { "label": "1輕型汽車", "value": "L" },
  //   { "label": "2重型汽車", "value": "P" },
  //   { "label": "3輕型摩托車", "value": "C" },
  //   { "label": "4重型摩托車", "value": "M" },
  //   { "label": "6半掛車", "value": "S" }
  //   { "label": "7工業機器", "value": "I" },
  // ]
  const formItemList1 = useMemo(
    () => [
      {
        label: intl.formatMessage({ id: 'vehBrandCode' }),
        name: 'vehBrandCode',
        required: true,
        span: 12,
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.vehBrandCodeOp}
          />
        ),
      },
      {
        label: intl.formatMessage({ id: 'vehModel' }),
        name: 'vehModel',
        required: true,
        span: 12,
        component: <Input placeholder={intl.formatMessage({ id: 'please_select' })} />,
      },
      {
        label: intl.formatMessage({ id: 'vehModelYear' }),
        name: 'vehModelYear',
        required: true,
        span: 12,
        component: <Input placeholder={intl.formatMessage({ id: 'please_select' })} />,
      },
      {
        label: intl.formatMessage({ id: 'customModelCode' }),
        name: 'customModelCode',
        required: true,
        span: 12,
        component: <Input placeholder={intl.formatMessage({ id: 'please_select' })} />,
      },
      {
        label: intl.formatMessage({ id: 'steeringWheelPosition' }),
        name: 'steeringWheelPosition',
        required: true,
        span: 12,
        hidden: ['C', 'M', 'S'].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.steeringWheelPositionOp}
          />
        ),
      },
      {
        label: intl.formatMessage({ id: 'gearboxType' }),
        name: 'gearboxType',
        required: true,
        span: 12,
        hidden: ['S'].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.gearboxTypeOp}
          />
        ),
      },
      //未处理options
      {
        label: intl.formatMessage({ id: 'txType' }),
        name: 'txType',
        required: true,
        span: 12,
        hidden: ['S'].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_select' })}
            options={emuObj.gearboxTypeOp}
          />
        ),
      },
    ],
    [emuObj, vehType],
  );
  const handleValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {
    if ('vtaCategory' in changedValues) {
      setVtaCategory(changedValues.vtaCategory);
    }
    if ('passCapacity' in changedValues) {
      setPassCapacity(changedValues.passCapacity);
    }
  };
  return (
    <div className={`${px('root')} ${className} multi-card-container`}>
      <Form
        form={form}
        className={px('form')}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <Form.Item hidden name="txnId">
          <Input />
        </Form.Item>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>{intl.formatMessage({ id: 'features' })}</span>
          </div>
          <div className="multi-card-body">
            <Row gutter={16}>
              {false &&
                formItemList1.map((item) => {
                  let { span, required, component } = item;
                  return (
                    <Col span={span} key={item.name}>
                      <Form.Item name={item.name} label={item.label} rules={[{ required }]}>
                        {component}
                      </Form.Item>
                    </Col>
                  );
                })}

              <Col span={12}>
                <Form.Item
                  label={intl.formatMessage({ id: 'vehBuildYear' })}
                  name="vehBuildYear"
                  required
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({ id: 'please_enter' }),
                    },
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder={intl.formatMessage({ id: 'please_enter' })}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={intl.formatMessage({ id: 'vehBuildCtryCode' })}
                  name="vehBuildCtryCode"
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({ id: 'please_select' }),
                    },
                  ]}
                >
                  <VtaSelect
                    placeholder={intl.formatMessage({ id: 'please_select' })}
                    options={dict?.[31203] ?? []}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={intl.formatMessage({ id: 'vehSourceCtryCode' })}
                  name="vehSourceCtryCode"
                  required
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({ id: 'please_select' }),
                    },
                  ]}
                >
                  <VtaSelect
                    placeholder={intl.formatMessage({ id: 'please_select' })}
                    options={dict?.[31203] ?? []}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={intl.formatMessage({ id: 'vtaCategory' })}
                  name="vtaCategory"
                  required
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({ id: 'please_select' }),
                    },
                  ]}
                >
                  <VtaSelect
                    options={dict?.[22007] ?? []}
                    placeholder={intl.formatMessage({ id: 'please_select' })}
                  />
                </Form.Item>
                {vtaCategory === 'O' && (
                  <Form.Item
                    name="vtaCategoryRemark"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_select' }),
                      },
                    ]}
                  >
                    <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />
                  </Form.Item>
                )}
              </Col>

              {['P', 'I'].includes(vehType) && vtaCategory && ['P'].includes(vtaCategory) && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vtaUsageCode' })}
                    name="vtaUsageCode"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_select' }),
                      },
                    ]}
                  >
                    <VtaSelect
                      options={dict?.[22002] ?? []}
                      disabled={canEditable['vtaUsageCode']}
                      placeholder={intl.formatMessage({ id: 'please_select' })}
                      value={vtaUsageCode}
                      onChange={(value) => setVtaUsageCode(value)}
                    />
                  </Form.Item>
                  {vtaUsageCode === '3' && (
                    <Form.Item
                      name="vtaUsageCodeRemark"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <Input
                        disabled={canEditable['vtaUsageCodeRemark']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  )}
                </Col>
              )}
              <Col span={12}>
                {['L', 'P', 'C', 'M', 'I'].includes(vehType) && (
                  <Form.Item
                    label={intl.formatMessage({ id: 'passCapacity' })}
                    name="passCapacity"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['passCapacity']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                      {...(vehType === 'C' || vehType === 'M'
                        ? {
                            formatter: (value) => {
                              if (!value) return '';
                              const str = value.toString();
                              const filtered = str.replace(/[^1-3]/g, '');
                              return filtered ? filtered[0] : '';
                            },
                            parser: (value) => {
                              if (!value) return '';
                              const filtered = value.replace(/[^1-3]/g, '');
                              return filtered ? filtered[0] : '';
                            },
                          }
                        : {})}
                    />
                  </Form.Item>
                )}
              </Col>
              {['L', 'P', 'I'].includes(vehType) && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'seatQty' })}
                    name="seatQty"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['seatQty']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
              )}
              {['P', 'I'].includes(vehType) && vtaCategory === 'P' && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'standQty' })}
                    name="standQty"
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['standQty']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
              )}
              {['L', 'P'].includes(vehType) && vtaCategory === 'P' && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'disablilitySeatQty' })}
                    name="disablilitySeatQty"
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['disablilitySeatQty']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
              )}
              {['P', 'I'].includes(vehType) && vtaCategory === 'P' && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'touristGuideQty' })}
                    name="touristGuideQty"
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['touristGuideQty']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
              )}
              {['L', 'P', 'I'].includes(vehType) && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'doorQty' })}
                    name="doorQty"
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['doorQty']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
              )}
              {['P', 'I'].includes(vehType) && vtaCategory === 'P' && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'emergencyExitQty' })}
                    name="emergencyExitQty"
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['emergencyExitQty']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
              )}
              {['C', 'M'].includes(vehType) && (
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'seatType' })}
                    name="seatType"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_select' }),
                      },
                    ]}
                  >
                    <Radio.Group
                      disabled={canEditable['seatType']}
                      onChange={(e) => setSeatType(e.target.value)}
                      options={seatTypeOpt}
                    />
                  </Form.Item>
                </Col>
              )}
               {seatType === 'ST' &&<Col span={12}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label={intl.formatMessage({ id: 'sideSeatLength' })}
                        name="sideSeatLength"
                        required
                        rules={[
                          {
                            required: true,
                            message: intl.formatMessage({ id: 'please_enter' }),
                          },
                        ]}
                      >
                        <Input
                          disabled={canEditable['sideSeatLength']}
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label={intl.formatMessage({ id: 'sideSeatWidth' })}
                        name="sideSeatWidth"
                        required
                        rules={[
                          {
                            required: true,
                            message: intl.formatMessage({ id: 'please_enter' }),
                          },
                        ]}
                      >
                        <Input
                          disabled={canEditable['sideSeatWidth']}
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
              </Col>}
              {(['L', 'P', 'S', 'I'].includes(vehType) ||
                (['C', 'M'].includes(vehType) && passCapacity && passCapacity >= 1)) && (
                <>
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'driverSeatLength' })}
                      name="driverSeatLength"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <Input
                        disabled={canEditable['driverSeatLength']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'driverSeatWidth' })}
                      name="driverSeatWidth"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <Input
                        disabled={canEditable['driverSeatWidth']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                </>
              )}
              {(['L', 'P', 'S', 'I'].includes(vehType) ||
                (['C', 'M'].includes(vehType) && passCapacity && passCapacity >= 3)) && (
                <>
                  {' '}
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'passSeatLength' })}
                      name="passSeatLength"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <Input
                        disabled={canEditable['passSeatLength']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'passSeatWidth' })}
                      name="passSeatWidth"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <Input
                        disabled={canEditable['passSeatWidth']}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                </>
              )}
              {['C', 'M'].includes(vehType) && (
                <>
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'passFootholdType' })}
                      name="passFootholdType"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_select' }),
                        },
                      ]}
                    >
                      <VtaSelect
                        disabled={canEditable['passFootholdType']}
                        options={dict?.[50013] ?? []}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'passHandrailType' })}
                      name="passHandrailType"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_select' }),
                        },
                      ]}
                    >
                      <VtaSelect
                        disabled={canEditable['passHandrailType']}
                        options={dict?.[50012] ?? []}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  </Col>
                </>
              )}
              <Col span={12}>
                <Form.Item
                  label={intl.formatMessage({ id: 'vehHeadlamp' })}
                  name="vehHeadlamp"
                  required
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({ id: 'please_select' }),
                    },
                  ]}
                >
                  <VtaSelect
                    disabled={canEditable['vehHeadlamp']}
                    placeholder={intl.formatMessage({ id: 'please_select' })}
                    options={dict?.[50107] ?? []}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default FormOne;
