import { keys } from 'lodash';
import { useCallback, useMemo, useState } from 'react';
export const useGetRes = () => {
  let getFormData = useCallback((data) => {
    let obj = {};
    let obj2 = {};
    keys(data).forEach((item) => {
      obj[item] = data[item].value;
      obj2[item] = data[item].editable !== 'Y';
    }, {});
    return {
      formData: obj,
      canEditable: obj2,
    };
  }, []);

  return { getFormData };
};
