import React from 'react';
import type { FormInstance } from 'antd';

const StepContext = React.createContext<{
  isAuth: boolean;
  formRenderInstance: FormInstance | null;
  receiveResultInstance: FormInstance | null;
  uploadInstance: FormInstance | null;
  initFormValue?: string;
  initUploadValue?: string;
  moPayRef;
  isPayed: boolean;
  identityRef;
}>({
  isAuth: false,
  formRenderInstance: null,
  receiveResultInstance: null,
  uploadInstance: null,
  moPayRef: null,
  isPayed: false,
  identityRef: null,
});

export default StepContext;
