import React from 'react';
import pc from 'prefix-classnames';
import './FormFour.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Space } from 'antd';

const px = pc('form-four');

export interface FormFourProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {

}

const FormFour = (props: FormFourProps) => {
  const { className = '', ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
  );
};

export default FormFour;
