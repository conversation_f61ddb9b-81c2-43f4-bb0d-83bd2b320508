import React from 'react';
import { MoUpload } from '@gov-mo/components';
import type { UploadFileType } from '../../type/defs';
import type { MaterialsListType } from '../../../../types/defs';

export interface UploadProps {
  onChange?: (v: MaterialsListType) => void;
  value?: MaterialsListType;
  materialsId: string;
  materialsCode: string;
  materialsName: string;
  isFirstClick: boolean;
  setIsFirstClick: (v: boolean) => void;
  setModalVisible: (v: boolean) => void;
}

function Upload(props: UploadProps) {
  const {
    onChange,
    value,
    materialsId,
    materialsCode,
    materialsName,
    isFirstClick,
    setIsFirstClick,
    setModalVisible,
  } = props;

  const handleFileChange = (file: UploadFileType) => {
    if (onChange) {
      onChange([
        ...(value ?? []),
        {
          uploadFileCode: file.fileCode,
          uploadFileName: file.name,
          uploadFileType: file.ext ?? '',
          uploadFileUrl: file.url,
          materialsCode,
          materialsId,
          materialsName,
        },
      ]);
    }
  };
  const handleFileRemove = (file: UploadFileType) => {
    if (onChange) {
      onChange((value ?? []).filter(i => i.uploadFileCode !== file.fileCode));
    }
  };

  const handleUploadClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (isFirstClick && (value ?? []).length === 0) {
      e.stopPropagation();
      e.preventDefault();
      setIsFirstClick(false);
      setModalVisible(true);
    }
  };

  return (
    <div onClickCapture={handleUploadClick}>
      <MoUpload
        appId="app"
        uploadWays={['take-photo', 'local-photo', 'local-file', 'scan']}
        onChange={handleFileChange}
        onRemove={handleFileRemove}
        fileList={(value ?? []).map(item => ({
          url: item.uploadFileUrl,
          fileCode: item.uploadFileCode,
          ext: item.uploadFileType,
          name: item.uploadFileName,
        }))}
      />
    </div>
  );
}

export default Upload;
