@classprefix: web-service-step;

.stepIcon(@iconColor) {
  .ant-steps-item-icon {
    background-color: fadeout(@iconColor, 75%);
  }
  .ant-steps-icon {
    background-color: @iconColor;
  }
}
.centerHeight(@contentHeight) {
  height: @contentHeight;
  min-height: @contentHeight;
  display: flex;
  align-items: center;
}

.@{classprefix} {
  &-root {
    padding: 0 10% 88px;
    height: calc(100% - 68px);
    overflow-y: auto;
  }
  &-title {
    font-size: 20px;
    font-weight: bold;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  }
  &-stepinfo {
    display: flex;
  }
  &-step {
    margin-top: 20px;
    padding: 0 22px;
    width: 240px;
    border-right: 1px solid rgba(0, 0, 0, 0.15);
    h3 {
      font-size: 16px;
      line-height: 22px;
      font-weight: bold;
      padding: 0 2px 16px;
    }
    .ant-steps-small {
      font-size: 16px;
      .ant-steps-item {
        height: 70px;
        &.ant-steps-item-finish {
          .stepIcon(@brand-success);
          .ant-steps-item-tail {
            &::after {
              background-color: @brand-success;
              height: 30px;
            }
          }
        }
        &.ant-steps-item-process {
          .stepIcon(#ffc107);
          .ant-steps-item-tail {
            &::after {
              background-color: #c4c4c4;
              height: 30px;
            }
          }
        }
        &.ant-steps-item-wait {
          .stepIcon(#c4c4c4);
          .ant-steps-item-tail {
            &::after {
              background-color: #c4c4c4;
              height: 30px;
            }
          }
          &:last-child {
            .ant-steps-item-tail {
              &::after {
                display: none;
              }
            }
          }
        }
        .ant-steps-item-container {
          .centerHeight(40px);
          .ant-steps-item-tail {
            bottom: 0;
            top: 40px;
            left: 14px;
            padding: 0;
          }
          .ant-steps-item-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            margin-right: 6px;
            cursor: pointer;
            flex-shrink: 0;
            .ant-steps-icon {
              .centerHeight(18px);
              width: 18px;
              line-height: 18px;
              left: 5px;
              top: 5px;
              font-size: 14px;
              justify-content: center;
              color: #ffffff;
              border-radius: 50%;
            }
          }
          .ant-steps-item-content {
            .centerHeight(40px);
            line-height: 16px;
            .ant-steps-item-title {
              font-size: 16px;
              color: #333333;
            }
          }
        }
      }
    }
  }
  &-form {
    flex: 1;
    margin-top: 20px;
    h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: bold;
      margin-left: 24px;
    }
    & > .pc-pay-ui-root {
      margin-left: 24px;
    }
    .pc-pay-ui-top-amount {
      line-height: 65px;
      span {
        position: relative;
      }
      span:first-child {
        font-size: 16px;
        color: #666;
        top: -2px;
      }
      span:nth-child(2) {
        font-size: 24px;
        top: -2px;
      }
    }
  }
  &-formTitle {
    display: flex;
    align-items: center;
    font-weight: bold;
    margin-left: 24px;
    font-size: 16px;
    color: #232323;
    padding-bottom: 10px;
    &:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: #008378;
      margin-right: 8px;
    }
  }
  &-spin {
    width: 100%;
    height: 100%;
    padding: 45%;
  }
  &-footer-button {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 10%;
    background: #ffffff;
    box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.1);
    .ant-btn {
      min-width: 128px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
      margin-left: 12px;
      font-size: 16px;
      padding: 0 5px;
      &.ant-btn-default {
        background: #ffffff;
        color: @brand-success;
        border: 1px solid @brand-success;
      }
    }
  }
}
