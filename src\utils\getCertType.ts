const certType = {
  'zh-Hans': {
    '0': '其他文件',
    '1': '澳门身份证',
    '2': '澳门发出之葡国认别证',
    '3': '军人证(于特区政府已无效)',
    '4': '外国身份证',
    '5': '澳门居民身份证',
    '6': '葡国认别证',
    '7': '临时逗留证',
    '8': '中国护照(绿)',
    '9': '香港身份证',
    '10': '澳门发出之葡国护照',
    '11': '葡国发出之葡国护照',
    '12': '外国护照',
    '13': '澳门特别行政区护照',
    '14': '澳门特区非永久性居民身份证',
    '15': '澳门特区永久性居民身份证',
    '16': '永久居留证',
    '17': '出生证明文件',
    '18': '外地劳工证',
    '19': '外地僱员身份认别证',
    '20': '中国居民身份证',
    '21': '因公往来港澳通行证',
    '22': '往来港澳通行证',
  },
  'zh-Hant': {
    '0': '其他檔案',
    '1': '澳門身份證',
    '2': '澳門發出之葡國認別證',
    '3': '軍人證（於特區政府已無效）',
    '4': '外國身份證',
    '5': '澳門居民身份證',
    '6': '葡國認別證',
    '7': '臨時逗留證',
    '8': '中國護照（綠）',
    '9': '香港身份證',
    '10': '澳門發出之葡國護照',
    '11': '葡國發出之葡國護照',
    '12': '外國護照',
    '13': '澳門特別行政區護照',
    '14': '澳門特區非永久性居民身份證',
    '15': '澳門特區永久性居民身份證',
    '16': '永久居留證',
    '17': '出生證明檔案',
    '18': '外地勞工證',
    '19': '外地雇員身份認別證',
    '20': '中國居民身份證',
    '21': '因公往來港澳通行證',
    '22': '往來港澳通行證',
  },
  en: {
    '0': 'other documents',
    '1': 'Macao ID card',
    '2': 'Portuguese identification certificate issued by Macao',
    '3': 'military ID card (invalid in the SAR Government)',
    '4': 'foreign ID card',
    '5': 'Macao resident identity card',
    '6': 'Portuguese identification certificate',
    '7': 'temporary residence permit',
    '8': 'Chinese passport (green)',
    '9': 'Hong Kong Identity Card',
    '10': 'Portuguese passport issued by Macao',
    '11': 'Portuguese passport issued by Portugal',
    '12': 'foreign passport',
    '13': 'Macao SAR passport',
    '14': 'Macao SAR non permanent identity card',
    '15': 'Macao SAR permanent identity card',
    '16': 'permanent residence permit',
    '17': 'birth certificate',
    '18': 'foreign labor certificate',
    '19': 'foreign employee identification card',
    '20': 'Chinese ID card',
    '21': 'business pass to Hong Kong and Macao',
    '22': 'pass to and from Hong Kong and Macao',
  },
  pt: {
    '0': 'outros arquivos',
    '1': 'Cartão de identificação de Macau',
    '2': 'Certificado de identificação português emitido por Macau',
    '3': 'Certificado Militar (inválido no Governo Da RAE)',
    '4': 'cartão de identificação Estrangeiro',
    '5': 'Cartão de identidade de Macau',
    '6': 'Certificado de identificação português',
    '7': 'Autorização de residência temporária',
    '8': 'passaporte chinês',
    '9': 'Cartão de Identidade de Hong Kong',
    '10': 'Passaporte português emitido por Macau',
    '11': 'Passaporte português emitido por Portugal',
    '12': 'passaporte Estrangeiro',
    '13': 'Passaporte de Macao SAR',
    '14': 'Macau SAR não cartão de identidade permanente',
    '15': 'Macau SAR cartão de identidade permanente',
    '16': 'Autorização de residência permanente',
    '17': 'certidão de Nascimento',
    '18': 'certificado de trabalho Estrangeiro',
    '19': 'cartão de identificação de empregados estrangeiros',
    '20': 'Cartão de identificação chinês',
    '21': 'Passagem de negócios para Hong Kong e Macau',
    '22': 'passe para Hong Kong e Macau',
  },
};

const getCertType = (code, language = 'zh-Hant') => certType[language]?.[code] ?? '';

export default getCertType;
