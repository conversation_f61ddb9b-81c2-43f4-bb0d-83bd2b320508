import React from 'react';
import type { ModalFuncProps } from 'antd';
import { Modal } from 'antd';
import { useIntl } from 'umi';
import { ExclamationCircleFilled } from '@ant-design/icons';

export const getModalConfig = (intl) => ({
  okText: intl.formatMessage({ id: '确认' }),
  cancelText: intl.formatMessage({ id: '取消' }),
  className: 'web-ant-modal',
  icon: <ExclamationCircleFilled />,
  centered: true,
});

const useModalMethod = () => {
  const intl = useIntl();
  // 警告信息弹窗.
  const warning = ({ className, ...otherProps }: ModalFuncProps = {}) =>
    Modal.warning({
      ...getModalConfig(intl),
      className: `web-ant-modal ${className}`,
      ...otherProps,
    });
  // 普通确认弹窗
  const confirm = ({ className, ...otherProps }: ModalFuncProps = {}) =>
    Modal.confirm({
      ...getModalConfig(intl),
      className: `web-ant-modal ${className}`,
      ...otherProps,
    });
  // 提示信息弹窗
  const info = ({ className, ...otherProps }: ModalFuncProps = {}) =>
    Modal.info({
      ...getModalConfig(intl),
      className: `web-ant-modal ${className}`,
      ...otherProps,
    });
  // 退出确认弹窗
  const quitConfirm = (options: ModalFuncProps = {}) =>
    confirm({
      title: intl.formatMessage({ id: '温馨提示' }),
      content: intl.formatMessage({ id: '确认放弃当前操作，并退出' }),
      onOk() {
        (window.top as any).close();
      },
      ...options,
    });
  // 电子文件提示弹窗
  const electronicDocInfo = (options: ModalFuncProps = {}) =>
    info({
      title: intl.formatMessage({ id: '温馨提示' }),
      content: intl.formatMessage({ id: 'electronicDoc_tips' }),
      ...options,
    });
  return [{ warning, confirm, quitConfirm, info, electronicDocInfo }];
};

export default useModalMethod;
