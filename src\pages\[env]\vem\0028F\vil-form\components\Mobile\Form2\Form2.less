@prefix: form2-28f-mobile;

.@{prefix} {
  &- {
    &root {

      flex: 1;
      margin: 0.2rem 0.12rem;
      padding-bottom: 0.2rem;
      font-size: 0.16rem;
      border-radius: 0.1rem 0.1rem 0 0;

      .ant-form {
        font-size: 0.16rem;
      }

      .ant-form-item-label > label {
        font-size: 0.16rem;
        flex-direction: row-reverse;
        display: flex;
        justify-content: flex-end;
      }

      .ant-select {
        font-size: 0.16rem;
      }

      .ant-input {
        font-size: 0.16rem;
      }

      .line-flex {
        display: flex;
      }

      .ant-btn {
        min-width: 0.62rem;
        height: 0.3rem;
        border-radius: 0.24rem;
        font-size: 0.16rem;
        line-height: 0.3rem;
      }

      .ant-btn.ant-btn-dangerous {
        background: #fff;
        color: #f33b40;
        border: 1px solid #f33b40;
      }
    }

    &add-box {
      padding: 0 0.2rem;
    }

    &add-car-owner-btn {
      width: 100%;
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 700;
      padding: 0.05rem 0.2rem;
      min-height: 0.41rem;
      font-size: 0.18rem;
      color: #ffffff;
      line-height: 0.21rem;
      background: @brand-primary;
      border-radius: 0.12rem 0.12rem 0 0;
    }

    &sectionBody {
      padding: 0.2rem 0.12rem;
      background-color: #ffffff;
      margin-bottom: 0.2rem;
      border-radius: 0 0 0.12rem 0.12rem;
    }

    &seperator {
      width: 0.2rem;
      height: 0.36rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.2rem;
    }

    &moAddressDisabled {
      pointer-events: none;
      user-select: none;

      .ant-input[disabled] {
        color: rgba(0, 0, 0, 0.85);
        background-color: #fff;
        pointer-events: none;
      }
    }
  }
}

.vil-form-mobile-page-footer-button {
  justify-content: space-around;

  .ant-btn {
    height: 0.41rem !important;
    border-radius: 0.23rem !important;
    font-size: 0.16rem !important;
    line-height: 0.41rem !important;
    flex: 1;
    margin: 0 0.05rem;
    min-width: 1em;
  }
}

.my-card-title-action {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-28f-mobile-checkbox {
  font-size: 0.16rem;
  display: flex;
  align-items: center;

  .ant-checkbox-inner {
    width: 0.2rem;
    height: 0.2rem;
    border: 1px solid #ccc;
  }

  .ant-checkbox + span {
    font-size: 0.16rem;
  }
}

.add-owner-btn-mobile {
  .ant-btn {
    min-width: 0.62rem;
    height: 0.52rem;
    border-radius: 0.26rem;
    font-size: 0.16rem;
    line-height: 0.52rem;
  }
}

.am-navbar .am-navbar-title .mo-baseContainer-mobile-title {
  font-size: 0.18rem !important;
  font-weight: 600;
}
