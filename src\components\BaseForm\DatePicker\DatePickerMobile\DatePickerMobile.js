import React, { useState, useContext } from 'react';
import { DatePicker, Calendar, List } from 'antd-mobile';
import { CalendarOutlined } from '@ant-design/icons';
import moment from 'moment';
import cn from 'classnames';
import classPrefix from '@ali-whale/class-prefix';
import MobileCalendar from './MobileCalendar';
import FormContext from '../../FormContext';
import './DatePickerMobile.less';

// 通过自定义 moneyKeyboardWrapProps 修复虚拟键盘滚动穿透问题
// https://github.com/ant-design/ant-design-mobile/issues/307
// https://github.com/ant-design/ant-design-mobile/issues/163
const isIPhone = new RegExp('\\biPhone\\b|\\biPod\\b', 'i').test(window.navigator.userAgent);
let moneyKeyboardWrapProps;
if (isIPhone) {
  moneyKeyboardWrapProps = {
    onTouchStart: e => e.preventDefault(),
  };
}

/**
 * 根据需求日期选择器，最大可选年份的日期要完整(原最大可选为2030年2月2日)
 */
const MAX_DATE = new Date(new Date().getFullYear() + 10, 0, 0);

const px = classPrefix('cbd-mobile-base-form');

const DatePickerMobile = (props) => {
  const { isDetail } = useContext(FormContext);
  const [visible, setVible] = useState(false);
  const [rangeDate, setRangeDate] = useState(undefined);
  const {
    value,
    onChange,
    placeholder = '',
    format = 'YYYY-MM-DD',
    type = 'single',
    enableDateSetting = {},
    ...otherProps
  } = props;
  const pickTime = format === 'YYYY-MM-DD';

  const { start, end } = enableDateSetting;
  let minDate = false;
  let maxDate = MAX_DATE;
  if ('start' in enableDateSetting) {
    minDate = moment()
      .add(+start, 'days')
      .toDate();
  }
  if ('end' in enableDateSetting) {
    maxDate = moment()
      .add(+end, 'days')
      .toDate();
  }

  const triggerChange = (dateString) => {
    onChange(moment(dateString).format(format));
  };

  const formatDate = (date) => {
    const pad = n => (n < 10 ? `0${n}` : n);
    const dateStr = `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`;
    if (pickTime) {
      return dateStr;
    }
    const timeStr = `${pad(date.getHours())}:${pad(date.getMinutes())}`;
    const seconds = `${pad(new Date().getSeconds())}`;
    return `${dateStr} ${timeStr}:${seconds}`;
  };

  const onConfirm = (startTime, endTime) => {
    setVible(false);
    // onChange([startTime, endTime]);
    const startDate = startTime ? moment(startTime).format(format) : undefined;
    const endDate = endTime ? moment(endTime).format(format) : undefined;
    onChange([startDate, endDate]);
    setRangeDate(`${formatDate(startTime)}~${formatDate(endTime)}`);
  };

  if (isDetail) {
    const showText = Array.isArray(value) ? value.join('~') : value;
    return <div>{showText}</div>;
  }

  const CustomChildren = ({ extra, onClick, children }) => (
    <div onClick={onClick} className={px('single-date')}>
      <div className={cn(px('date'), { [px('date-placeholder')]: !value })}>
        {extra}
        <span className={px('date-icon')}>
          <CalendarOutlined />
        </span>
      </div>
    </div>
  );

  if (type === 'single') {
    return (
      <DatePicker
        mode={pickTime ? 'date' : 'datetime'}
        placeholder={placeholder}
        onChange={triggerChange}
        moneyKeyboardWrapProps={moneyKeyboardWrapProps}
        format={pickTime ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'}
        value={value ? new Date(value) : undefined}
        extra={placeholder}
        minDate={minDate}
        maxDate={maxDate}
        {...otherProps}
      >
        {/* <List.Item arrow="horizontal" onClick={() => setVible(true)}>
          {label}
        </List.Item> */}
        <CustomChildren />
      </DatePicker>
    );
  }
  if (type === 'startAndEnd') {
    return (
      <>
        <List.Item
          arrow="horizontal"
          className={px('mobile-range-picker')}
          onClick={() => {
            setVible(true);
          }}
        >
          <div className={px('mobile-range-picker-wrapper')}>
            {!value ? (
              <div className={px('mobile-range-picker-placeholder')}>{placeholder}</div>
            ) : (
              <div className={px('mobile-range-picker-value')}>{rangeDate}</div>
            )}
          </div>
        </List.Item>
        <MobileCalendar domNode={document.getElementsByTagName('body')[0]}>
          <Calendar
            visible={visible}
            onCancel={() => setVible(false)}
            placeholder={placeholder}
            onConfirm={onConfirm}
            pickTime={!pickTime}
            type="range"
            extra={placeholder}
            minDate={minDate}
            maxDate={maxDate}
            {...otherProps}
          />
        </MobileCalendar>
      </>
    );
  }
  return null;
};

export default DatePickerMobile;
