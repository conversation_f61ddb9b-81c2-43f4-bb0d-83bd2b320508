import pc from 'prefix-classnames';
import React, { useEffect, useState } from 'react';
import './Success.less';

import { <PERSON><PERSON>, But<PERSON>, Image } from 'antd';
import dayjs from 'dayjs';
import { history, useIntl } from 'umi';

import {
  checkTxnJumpOrder,
  completeTxnForPay,
  getExTxnCompleteData,
  getTxnPendingApproval,
  receiptFileUrl,
} from '@/services/0028F';
import { FetchResponse } from '@/utils/fetch';
import { File, Page } from '@gov-mo/mpaas-js-bridge';

import { getLangGroup, getResponseMessage } from '@/locales/lang';
import { getStatusBarHeight } from '@/utils/hooks';
import { LeftOutlined } from '@ant-design/icons';
import { NavBar } from 'antd-mobile';
import { Toast } from "antd-mobile-v5";

const px = pc('success-28f-mobile');

export interface FormTenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId: string;
  requestId: string;
  ID: string;
}

const FormTen = (props: FormTenProps) => {
  const intl = useIntl();
  const { className = '', txnId, requestId, ID, ...otherProps } = props;
  const [transId, setTransId] = useState(ID);
  const [tipMsg, setTipMsg] = useState('');
  const [data, setData] = useState<getExTxnCompleteDataRes>();
  const [download, setDownload] = useState<boolean>(false);
  const [pendingApprovalCodeDto, setPendingApprovalCodeDto] = useState<sysCodesByCodeTypeOpt | undefined>(undefined);

  let times: number = 0;

  function gotoIndex() {
    process.env.NODE_ENV === 'production' ? Page.closePage() : history.push('/web/vem');
  }

  function errorMessage(msg: string, duration?: number) {
    if (duration) {
      Toast.show({
        content: msg,
        icon: 'fail',
        duration,
      });
      return;
    }

    Toast.show({
      content: msg,
      icon: 'fail',
    });
  }

  const receiptFile = async () => {
    setTipMsg('');

    if (!txnId && !requestId) {
      return;
    }

    const res = await receiptFileUrl(txnId || requestId);
    if (!res) return;
    res?.code === '0' &&
      File.previewPdfWithDownload({ url: `${res?.data}#.pdf`, fileName: `${txnId}.pdf` });
    res?.code !== '0' && setTipMsg(getResponseMessage(res));
  };

  const hander = (res: FetchResponse<getExTxnCompleteDataRes>) => {
    if (times >= 21) {
      errorMessage(getResponseMessage(res), 0)
      setDownload(false);
      return;
    }

    times++;

    timeout(1000);
  };

  const timeout = (ms: number) => {
    setTimeout(() => {
      getData();
    }, ms);
  };

  const func = () => {
    getExTxnCompleteData(txnId || requestId)
      .then((ret) => {
        if (!ret) return;
        if (ret.code === 'W-8888') {
          timeout(1000);
          return;
        }

        if (ret.code !== '0') {
          hander(ret);
          return;
        }

        setData(ret.data);
        setDownload(true);
        times = 0;
      })
      .catch((e) => {
        setDownload(false);
        setTipMsg(e.message);
      });
  };

  const handleBNU = async (transId: string) => {
    // BNU
    const res = await getTxnPendingApproval(transId);
    if (!res) return;
    if (res.code === '0') {
      setData(res.dataArr[0]);
      setDownload(true);
      setPendingApprovalCodeDto(res.dataArr[0]?.pendingApprovalCodeDto);
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const handleBOC = async () => {
    // BOC
    const res = await completeTxnForPay(requestId);
    if (!res) return;
    if (res.code === '0') {
      func();
      return;
    }

    setTipMsg(getResponseMessage(res));
  };
  const getData = async () => {
    if (txnId) {
      func();
      return;
    }
    if (requestId) {
      const checkRes = await checkTxnJumpOrder(requestId);
      if (!checkRes) return;
      if (checkRes.code !== '0') {
        setTipMsg(getResponseMessage(checkRes));
        return;
      }
      if (checkRes.data.isShowCompletePage) {
        await handleBOC();
      }
      if (checkRes.data.isShowPendingPage) {
        setTransId(checkRes.data.transId);
        await handleBNU(checkRes.data.transId);
      }
      return;
    }
    if (transId) {
      // BNU
      await handleBNU(transId);
    }
  };

  useEffect(() => {
    getData();
  }, [txnId, requestId, transId]);

  return (
    <>
      <NavBar
        style={{
          paddingTop: getStatusBarHeight(),
          color: '#ffffff',
          backgroundColor: data?.txnStatus
            ? data.txnStatus === 'E'
              ? '#f39373'
              : 'var(--primary-btn-fill)'
            : '',
        }}
        mode="light"
        leftContent={
          <LeftOutlined
            onClick={() => {
              gotoIndex();
            }}
          />
        }
      >
        <span style={{ color: '#ffffff' }}>{intl.formatMessage({ id: 'complete_submit' })}</span>
      </NavBar>
      {data && (
        <div
          style={{ paddingTop: 40 + getStatusBarHeight() }}
          className={`${px('root')} ${className}`}
          {...otherProps}
        >
          <Image
            preview={false}
            src={
              data.txnStatus === 'E'
                ? '/ovsap/image/failed/submit_failed.jpg'
                : '/ovsap/image/success/success-bg.jpg'
            }
            width="100%"
            style={{ marginTop: '-1px' }}
          />
          <h1>
            {data.txnStatus === 'E' && (
              <>
                <span>{intl.formatMessage({ id: 'failed_submitted_application' })}</span>
              </>
            )}
            {['A', 'F'].includes(data.txnStatus || '') && (
              <>
                <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
              </>
            )}
            {data.txnStatus === 'S' && (
              <>
                <span>{intl.formatMessage({ id: 'bnu_pay_submitted_application' })}</span>
              </>
            )}
            {/* {getLangGroup(data?.txnStatusCn, data?.txnStatusPt, data?.txnStatusEn)} */}
          </h1>

          {tipMsg && (
            <div className={px('alert')}>
              <Alert message={tipMsg} type="error" showIcon />
            </div>
          )}

          <div className={px('body')}>
            <div className={px('row')}>
              <div className={px('value')}>{getLangGroup(data.serviceTitleRespDTO.serviceTitleCn, data.serviceTitleRespDTO.serviceTitlePt, data.serviceTitleRespDTO.serviceTitleEn)}</div>
            </div>
            {/* BNU支付显示信息 */}
            {transId ? (
              <>
                <div className={px('row')}>
                  <div className={px('label')}>
                    {intl.formatMessage({ id: 'import_license_number' })}
                  </div>
                  <div className={px('value')}>{data.importNoFull}</div>
                </div>

                <div className={px('row')}>
                  <div className={px('label')}>{intl.formatMessage({ id: 'vehicle_level' })}</div>
                  <div className={px('value')}>
                    {getLangGroup(data.vehTypeDescCn, data.vehTypeDescPt, data.vehTypeDescEn)}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className={px('row')}>
                  <div className={px('label')}>{intl.formatMessage({ id: 'query_number' })}</div>
                  <div className={px('value')}>{data.spNo.split('-').pop()}</div>
                </div>

                <div className={px('row')}>
                  <div className={px('label')}>
                    {intl.formatMessage({ id: 'establishment_time' })}
                  </div>
                  <div className={px('value')}>
                    {data.txnStatusDate
                      ? dayjs(data.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                      : ''}
                  </div>
                </div>
              </>
            )}
            <div className={px('successTitlleMobile')} style={{textAlign:'left'}}>
              {
                pendingApprovalCodeDto ? getLangGroup(pendingApprovalCodeDto.codeCname, pendingApprovalCodeDto.codePname, pendingApprovalCodeDto.codeEname) : 
                intl.formatMessage(
                  { id: 'success_bottom_title_sst' },
                  {
                    platform: (
                      <span className={px('successBottomTitleSpan')}>
                        {intl.formatMessage({ id: 'platform_sst' })}
                      </span>
                    ),
                  },
                )
              }
            </div>
            {/* <div>{intl.formatMessage({ id: 'success_msg_tip' })} </div> */}
          </div>
          <div className={px('footer')}>
            {download && (
              <Button
                type="default"
                onClick={() => {
                  gotoIndex();
                }}
              >
                {intl.formatMessage({ id: 'complete' })}
              </Button>
            )}
            {download && data.txnStatus === 'F' && !transId && (
              <Button
                type="primary"
                onClick={() => {
                  receiptFile();
                }}
              >
                {intl.formatMessage({ id: 'download_receipt' })}
              </Button>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default FormTen;
