import { post } from '@/utils/fetch';
import type { FetchResponse } from '@/utils/fetch';

// 獲取批量繳費項 todo 類型
export const getServiceFeesInBatch = (txnIds: string[]) =>
  post<FetchResponse<getServiceFeesInBatchRes>>('/oa/service/getServiceFeesInBatch', { txnIds });

// 批量服務完成後的頁面數據
export const getCompleteTxnsInBatch = (batchId: string ) =>
    post<FetchResponse<getExTxnCompleteDataRes>>('/oa/vem/getCompleteTxnsInBatch', { batchId });