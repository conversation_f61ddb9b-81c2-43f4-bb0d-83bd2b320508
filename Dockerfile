FROM nginx:1.17.5-alpine
MAINTAINER gts

ENV RUN_GROUP nginx  
ENV DATA_DIR /home/<USER>/dist
ENV BACKEND_SERVER http://gov-mo-demo-api-pre-http:8080/

# 复制应用包到/home/<USER>
# 请替换成自己的应用包
# 复制源码到容器的/home目录
ADD gov-mo-demo.tar.gz  /home/<USER>/dist

COPY ./docker/nginx/conf.d /etc/nginx/conf.d

RUN mv /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf.template

# 指定执行的工作目录
WORKDIR /home/<USER>

EXPOSE 8080


#CMD 运行以下命令 
#CMD ["nginx", "-g", "daemon off;"]
#多个变量则逗号隔开 例如 envsubst '${env1},${env2},${env3}'
CMD ["/bin/sh", "-c", "envsubst '${BACKEND_SERVER}' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf && exec nginx -g 'daemon off;'"]
