import React, { useState, useEffect, useContext } from 'react';
import pc from 'prefix-classnames';
import './Form6.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button } from 'antd';
import type { RadioChangeEvent } from 'antd';
import SpinContext from '../../../../../../../components/SpinContext';

const px = pc('form6-28f');

export interface FormSixProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;
}

const FormSix = (props: FormSixProps) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  const [val, setVal] = useState(0)
  const [more, setMore] = useState(false)
  const [search, setSearch] = useState(false)
  const { setLoading } = useContext(SpinContext);

  const onChange = (e: RadioChangeEvent) => {
    setVal(e.target.value);
  };

  useEffect(() => {
    setDisableNextStep(true)
    return () => {
      setDisableNextStep(false)
    }
  }, [])

  useEffect(() => {
    if(val === 2) {
      setMore(true)
      if(search) {
        setDisableNextStep(false)
      } else {
        setDisableNextStep(true)
      }
    } else {
      setMore(false)
      if(val === 1) {
        setDisableNextStep(false)
      }
    }
  }, [val, search])

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <Form form={form} className={px('form')} layout="vertical">
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'usage_license_plates' })}</div>
        <div className={px('sectionBody')}>
          <Form.Item label="">
            <Radio.Group onChange={onChange} value={val} >
              <Radio value={1}>{intl.formatMessage({ id: 'draw_lots' })}</Radio>
              <Radio value={2}>{intl.formatMessage({ id: 'use_license_plates' })}</Radio>
            </Radio.Group>
          </Form.Item>
          {more && (
            <div style={{width: '100%', display: 'flex'}}>
            <Form.Item label={intl.formatMessage({ id: 'add_license_plate_number' })}>
              <Input value="" />
            </Form.Item>
            <Form.Item label=" ">
              <Button type="primary" style={{marginLeft:'12px'}} onClick={() => { setSearch(true) }}>{intl.formatMessage({ id: 'search' })}</Button>
            </Form.Item>
            </div>
          )}
          {search && (
            <>
            <div style={{width: '100%'}}>
            <Form.Item label={intl.formatMessage({ id: 'buy_license_plate_number' })}>
              <span>MA-12-34</span>
            </Form.Item>
            </div>
            <div style={{width: '100%'}}>
              <span style={{color: '#13a07b'}}>{intl.formatMessage({ id: 'license_plate_matches' })}</span>
            </div>
            </>
          )}
        </div>
      </Form>
    </div>
  );
};

export default FormSix;
