import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { Toast } from 'antd-mobile';
import filePng from '@/assets/file.png';
import { Image, File } from '@gov-mo/mpaas-js-bridge';
import type { UploadModelsState } from 'umi';
import type { MaterialsListType } from '../../../../types/defs';
import styles from './UploadDetail.less';

const ImgTypes = ['image', 'jpeg', 'jpg', 'png', 'tiff', 'gif'];

const PreviewByUrl = (url: string, type = 'image') => {
  if (ImgTypes.includes(type)) {
    Image.previewPicture({ url });
    return;
  }

  if (type === 'pdf') {
    File.previewPdf({ url });
    return;
  }
  Toast.fail('該文件暫不支持預覽');
};

function UploadDetail() {
  const { applicantMaterials } = useSelector(
    ({ uploadModels }: { uploadModels: UploadModelsState }) => uploadModels,
  );

  const renderList = useMemo(() => {
    const res: Record<
      string,
      {
        pic: MaterialsListType;
        file: MaterialsListType;
        fileName: string;
      }
    > = {};
    applicantMaterials?.forEach((material) => {
      if (!res[material.materialsId]) {
        res[material.materialsId] = {
          pic: [],
          file: [],
          fileName: material.materialsName,
        };
      }
      const dot = material.uploadFileType || material.uploadFileCode.split('.').reverse()[0];
      if (ImgTypes.includes(dot)) {
        res[material.materialsId].pic.push(material);
      } else {
        res[material.materialsId].file.push(material);
      }
    });
    return res;
  }, [applicantMaterials]);

  return (
    <div>
      {Object.keys(renderList).map(key => (
        <React.Fragment key={key}>
          <div className={styles.fileName}>{renderList[key].fileName}</div>
          <div className={styles.imgArea}>
            {renderList[key].pic.map(material => (
              <img
                onClick={() => {
                  PreviewByUrl(material.uploadFileUrl, material.uploadFileType);
                }}
                className={styles.img}
                key={material.uploadFileCode}
                src={material.uploadFileUrl}
              />
            ))}
          </div>
          <div className={styles.fileArea}>
            {renderList[key].file.map(material => (
              <div
                className={styles.file}
                key={material.materialsCode}
                onClick={() => {
                  PreviewByUrl(material.uploadFileUrl, material.uploadFileType);
                }}
              >
                <img className={styles.fileImg} src={filePng} />
                <span>{material.uploadFileName}</span>
              </div>
            ))}
          </div>
        </React.Fragment>
      ))}
    </div>
  );
}

export default UploadDetail;
