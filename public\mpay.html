<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>mpay</title>
    <script>
      // 判断环境
      function isMobile() {
        if (
          /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i.exec(
            navigator.userAgent,
          )
        ) {
          return true;
        }
        return false;
      }

      // 初始化桥连接
      function initAliBridge(cb) {
        window.onload = function () {
          window.timmer = setInterval(() => {
            if (AlipayJSBridge) {
              cb();
            }
          }, 100);
        };
      }

      // 获取移动端数据
      function mobileLoader() {
        clearInterval(window.timmer);
        AlipayJSBridge.call('getStartupParams', {}, function (result) {
          let { content: html } = result;
          document.write(html);
        });
      }

      function receiveMessageAndLoadPage() {
        if (isMobile()) {
          // 移动处理方式
          initAliBridge(mobileLoader);
        } else {
          // pc处理方式
          window.addEventListener(
            'message',
            function (event) {
              document.write(event.data);
            },
            false,
          );
        }
      }
      // 接受信息并加载页面
      receiveMessageAndLoadPage();
    </script>
  </head>

  <body></body>
</html>
