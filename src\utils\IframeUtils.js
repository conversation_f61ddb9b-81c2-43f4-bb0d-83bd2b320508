export class IframeUtils {
  /**
   * postmessage事件编码
   * @property {100} SAVE 保存表单(需校验)
   * @property {101} SAVE_WITHOUT_VALIDATE 保存表单(无需校验)
   * @property {110} SAVE_SUCCESS 响应-保存表单成功
   * @property {111} SAVE_FAIL 响应-保存表单失败
   * @property {200} SUBMIT 提交表单
   * @property {210} SUBMIT_SUCCESS 响应-提交表单成功
   * @property {211} SUBMIT_FAIL 响应-提交表单失败
   * @property {300} RESET 重置表单项
   * @property {880} SET_SUBMIT_VALUE 表单赋值 - 已提交的数据
   * @property {888} SET_TEMPORARY_VALUE 表单赋值 - 暂存的数据
   * @property {1000} DISABLE 禁用表单
   * @property {1001} ENABLE 启用表单
   */
  static MESSAGE_CODE = {
    SAVE: 100,
    SAVE_WITHOUT_VALIDATE: 101,
    SAVE_SUCCESS: 110,
    SAVE_FAIL: 111,
    SUBMIT: 200,
    SUBMIT_SUCCESS: 210,
    SUBMIT_FAIL: 211,
    RESET: 300,
    SET_SUBMIT_VALUE: 880,
    SET_TEMPORARY_VALUE: 888,
    DISABLE: 1000,
    ENABLE: 1001,
  };

  /**
   * postmessage响应字段映射
   */
  static RES_FIELDS_MAP = {
    FORM_DETAIL_ID: 'formDetailId',
    FORM_TEMPORARY_ID: 'saveId',
  };

  /**
   * 构造函数
   * @param {HTMLElement} iframeRef iframe引用
   */
  constructor(iframeRef) {
    this.iframeRef = iframeRef;
  }

  dispatch(
    param = {
      code: '',
    },
    targetOrigin = '*',
  ) {
    if (!this.iframeRef) {
      return;
    }
    this.iframeRef.contentWindow.postMessage(param, targetOrigin);
  }

  /**
   * 监听dispatch结果
   * @param {Promise.resolve} resolve
   * @param {MESSAGE_CODE[]} codes 监听的事件编码集
   */
  // eslint-disable-next-line class-methods-use-this
  _monitorResponse(resolve, codes) {
    const receiveMessage = (event) => {
      const { code, ...otherDatas } = event?.data ?? {};
      if (codes.includes(code)) {
        resolve(otherDatas);
        window.removeEventListener('message', receiveMessage);
      }
    };
    window.addEventListener('message', receiveMessage, false);
  }

  /**
   * 提交
   * @return {Promise} res
   * @return {boolean} res.success true:成功；false：失败
   * @return {string} res[RES_FIELDS_MAP.FORM_DETAIL_ID] 表单数据id
   */
  submit() {
    return new Promise((resolve) => {
      this._monitorResponse(resolve, [
        IframeUtils.MESSAGE_CODE.SUBMIT_SUCCESS,
        IframeUtils.MESSAGE_CODE.SUBMIT_FAIL,
      ]);
      this.dispatch({ code: IframeUtils.MESSAGE_CODE.SUBMIT });
    });
  }

  /**
   * 保存(需校验)
   * @return {Promise} res
   * @return {boolean} res.success true:成功；false：失败
   * @return {string} res[RES_FIELDS_MAP.FORM_TEMPORARY_ID] 暂存的表单数据id
   */
  save() {
    return new Promise((resolve) => {
      this._monitorResponse(resolve, [
        IframeUtils.MESSAGE_CODE.SAVE_SUCCESS,
        IframeUtils.MESSAGE_CODE.SAVE_FAIL,
      ]);
      this.dispatch({ code: IframeUtils.MESSAGE_CODE.SAVE });
    });
  }

  /**
   * 保存(无需校验)
   * @return {Promise} res
   * @return {boolean} res.success true:成功；false：失败
   * @return {string} res[RES_FIELDS_MAP.FORM_TEMPORARY_ID] 暂存的表单数据id
   */
  saveWithoutValidate() {
    return new Promise((resolve) => {
      this._monitorResponse(resolve, [
        IframeUtils.MESSAGE_CODE.SAVE_SUCCESS,
        IframeUtils.MESSAGE_CODE.SAVE_FAIL,
      ]);
      this.dispatch({ code: IframeUtils.MESSAGE_CODE.SAVE_WITHOUT_VALIDATE });
    });
  }

  /**
   * 表单赋值 - 已提交的数据
   * @param {string} formDetailId 表单数据id
   */
  setFormDataByDetailId(formDetailId) {
    this.dispatch({
      code: IframeUtils.MESSAGE_CODE.SET_SUBMIT_VALUE,
      [IframeUtils.RES_FIELDS_MAP.FORM_DETAIL_ID]: formDetailId,
    });
  }

  /**
   * 表单赋值 - 暂存的数据
   * @param {string} saveId 暂存的表单数据id
   */
  setFormDataBySaveId(saveId) {
    this.dispatch({
      code: IframeUtils.MESSAGE_CODE.SET_TEMPORARY_VALUE,
      [IframeUtils.RES_FIELDS_MAP.FORM_TEMPORARY_ID]: saveId,
    });
  }

  /**
   * 禁用表单
   */
  disable() {
    this.dispatch({
      code: IframeUtils.MESSAGE_CODE.DISABLE,
    });
  }

  /**
   * 启用表单
   */
  enable() {
    this.dispatch({
      code: IframeUtils.MESSAGE_CODE.ENABLE,
    });
  }
}
