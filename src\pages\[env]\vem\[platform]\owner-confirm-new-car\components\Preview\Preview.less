@prefix: vil-form-preview;

.@{prefix} {
  &- {
    &root {
      margin-left: 24px;
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }

    &sectionBody {
      .ant-collapse {
        color: #fff;
        border: 1px solid #f0f0f0;
        margin-bottom: 20px;
        border-radius: 12px;
        overflow: hidden;

        &:nth-child(4n + 1) {
          background: #d9ebe7;
        }
        &:nth-child(4n + 2) {
          background: #dfeaf5;
        }
        &:nth-child(4n + 3) {
          background: #faedd9;
        }
        &:nth-child(4n + 4) {
          background: #f7e4da;
        }

        .ant-collapse-header {
          padding: 12px 16px 12px 40px !important;
          color: #000 !important;
          font-size: 16px;
          cursor: pointer;
        }
        .ant-collapse-content {
          color: rgba(0, 0, 0, 0.85);
          background-color: #fff;
          border-top: 1px solid #f0f0f0;
        }
        .form-title {
          font-size: 18px;
          color: @brand-primary;
        }
        .form-title2 {
          display: flex;
          align-items: center;
          font-weight: 700;
          font-size: 18px;
          color: #232323;
          margin: 0 24px;
          padding-bottom: 10px;
        }
        .form-title2:before {
          display: block;
          content: '';
          width: 4px;
          height: 16px;
          background: @brand-primary;
          margin-right: 8px;
        }
      }
      .ant-form {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 0 24px;
        .ant-form-item {
          width: 48%;
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
              // color: @brand-primary;
            }
          }
          .statusBg1 {
            padding: 0 2px;
            color: #f33b40;
          }
          .statusBg2 {
            padding: 0 2px;
            color: @brand-primary;
          }
          .bg {
            padding: 0 2px;
            color: #ffffff;
            background-color: @brand-primary;
            &.warn {
              background-color: #ffc107;
            }
          }
        }
      }
    }
  }
}
.owner-title {
  height: 50px;
  background-color: @brand-primary;
  font-size: 0.16rem;
  display: flex;
  align-items: center;
  padding-left: 40px;
  margin-bottom: 20px;
  border-radius: 12px;
  background-color: #d9ebe7;
  color: #000;
}
.owner-checkbox {
  font-size: 20px;
  margin-bottom: 10px;
}