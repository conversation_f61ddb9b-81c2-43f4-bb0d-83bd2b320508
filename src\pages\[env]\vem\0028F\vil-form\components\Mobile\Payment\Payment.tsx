import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './Payment.less';

import { useIntl } from 'umi';
import { Alert, Radio } from 'antd';

import { cancelTxnPay, completePayment, getServiceFee } from '@/services/0028F';
import { isMobile } from '@/utils';
import { getLangGroup, getResponseMessage } from '@/locales/lang';

const px = pc('payment-28f-mobile');

export interface FormNineProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setNextStepText: (text: string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;

  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;

  txnId: string | undefined;
}

const FormNine = (props: FormNineProps) => {
  const { className = '', ...otherProps } = props;
  const {
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,

    contentNextStep,

    contentForNextStep,

    txnId,
  } = otherProps;

  const [tipMsg, setTipMsg] = useState('');

  const intl = useIntl();
  const baseUrl =
    window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const submit = async () => {
    setTipMsg('');

    // 是否是取消支付
    if (cancelPayDto?.isShowCancelPayButton) {
      handleCancelTxnPay().then();
      return;
    }

    let pageUrl = 'ovsap/web/vem/0028F/vil-form?page=success';
    if (paySelected === 'BOCP' || paySelected === 'BOC' || paySelected === 'BNU') {
      pageUrl = `${baseUrl}/${pageUrl}`;
    }
    const data: completePaymentParams = {
      txnId: txnId!,
      payMethodCodeKey: paySelected,
      lang: 'zh_TW',
      ua: isMobile() ? 'MobileWeb' : 'PCWeb',
      remark: '',
      // allSubTotal: total,
      pageUrl,
      // serviceFeeItems: fees
    };

    const ret = await completePayment(data).catch((e) => {
      setTipMsg(e.message);
    });
    if (!ret) {
      return;
    }
    if (ret.code === '0') {
      const { data } = ret;

      if (data.payUrl) {
        location.href = data.payUrl;
      } else if (data.returnHTML) {
        document.write(data.returnHTML?.replace(/\\/, ''));
      }
      // contentForNextStep(2);
    } else {
      setTipMsg(getResponseMessage(ret));
    }
  };

  const [payCodes, setPayCodes] = useState<payCodes[]>();
  const [_, setFeeItems] = useState<feeItems[]>();
  const [total, setTotal] = useState(0);
  const [paySelected, setPaySelected] = useState('');
  const [cancelPayDto, setCancelPayDto] = useState<CancelPayDto>();
  const [serviceFeeData, setServiceFeeData] = useState<getServiceFeeRes>({} as getServiceFeeRes);

  const handleCancelTxnPay = async () => {
    if (!txnId) return;
    const res = await cancelTxnPay([txnId]);
    if (res?.code !== '0') {
      setTipMsg(getResponseMessage(res));
      return;
    }
    // 刷新页面
    location.reload();
  };

  useEffect(() => {
    if (txnId) {
      // setPayCodes([{
      //   codeId: 1,
      //   codeType: 1,
      //   codeKey: 'GOV',
      //   codeStatus: '',
      //   codePname: '',
      //   codeCname: '',
      //   codeEname: '',
      // },{
      //   codeId: 2,
      //   codeType: 2,
      //   codeKey: 'BOC',
      //   codeStatus: '',
      //   codePname: '',
      //   codeCname: '',
      //   codeEname: '',
      // }])
      getServiceFee(txnId)
        .then((ret) => {
          if (ret?.code === '0') {
            const { data } = ret;
            setServiceFeeData(data);
            // 设置取消支付状态
            setCancelPayDto(data?.cancelPayDto);
            // 如果需要取消支付
            if (data?.cancelPayDto?.isShowCancelPayButton === true) {
              // 禁用支付选择
              data.payCodes.forEach((item) => {
                item.available = false;
              });
              // 设置取消支付按钮文案
              setNextStepText(intl.formatMessage({ id: 'cancel_payment' }));
            }

            setPayCodes(data.payCodes);
            setFeeItems(data.feeItems);
            setTotal(data.total);

            const tableData: {
              key: string;
              title: string;
              amount: string;
              tax: string;
              subtotal: string;
            }[] = [];
            data.feeItems.forEach((item, index) => {
              tableData.push({
                key: (index + 1).toString(),
                title: getLangGroup(item.zh, item.pt),
                amount: item.priceTotal?.toLocaleString(),
                tax: item.taxPriceTotal?.toLocaleString(),
                subtotal: item.subtotal?.toLocaleString(),
              });
            });

            setTableData(tableData);

            if (data.payCodes && data.payCodes.length) {
              const codeKey =
                data?.cancelPayDto?.isShowCancelPayButton === true
                  ? data.cancelPayDto.payCodeKey
                  : data.payCodes[0].codeKey;
              setPaySelected(codeKey);
            }
          }
        })
        .catch((e) => {
          setTipMsg(e.message);
        });
    }
  }, [txnId]);

  useEffect(() => {
    setShowClose(false);
    setShowTempStore(false);
    setShowCloseAndTempStore(true);

    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }));

    contentForNextStep(1);
    return () => {
      setShowClose(true);
      setShowTempStore(true);
      setShowCloseAndTempStore(false);

      setNextStepText(intl.formatMessage({ id: 'next' }));

      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      submit();
    }
  }, [contentNextStep]);

  const [tableData, setTableData] = useState<
    { key: string; title: string; amount: string; tax: string; subtotal: string }[]
  >([]);

  return (
    <>
      {tipMsg && (
        <div style={{ padding: '0.2rem 0.24rem 0' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        {/*<Table columns={columns} dataSource={tableData} pagination={false} />*/}
        <div className="pay-way-mobile">
          <p className={px('cost-title')}>{intl.formatMessage({ id: 'cost_details' })}</p>
          {tableData.map((item) => {
            return (
              <div className={px('cost-container')}>
                <div key={item.key} className={px('cost-content')}>
                  {/*缴费项*/}
                  <div className={px('cost-content-item')}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'fee_item' })}
                    </div>
                    <div className={px('cost-content-item-text-black')}>{item.title}</div>
                  </div>
                  {/*金额*/}
                  <div className={px('cost-content-item')}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'amount' })}
                    </div>
                    <div className={px('cost-content-item-text-black')}>{item.amount}</div>
                  </div>
                  {/*税款*/}
                  <div className={px('cost-content-item')}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'tax_payment' })}
                    </div>
                    <div className={px('cost-content-item-text-black')}>{item.tax}</div>
                  </div>
                  {/*小计*/}
                  <div className={`${px('cost-content-item')} ${px('cost-divide')}`}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'subtotal' })}
                    </div>
                    <div className={px('cost-content-item-text-primary')}>{item.subtotal}</div>
                  </div>
                </div>
              </div>
            );
          })}
          <div className={px('cost-divide')} style={{ marginTop: '0.12rem' }}>
            <div className={px('cost-content-item')} style={{ padding: '0 0.28rem' }}>
              <div className={px('cost-content-item-label')}>
                {intl.formatMessage({ id: 'total' })}
              </div>
              <div className={px('cost-content-item-text-primary')}>
                <span style={{ marginRight: '0.1rem' }}>MOP</span>
                {total?.toLocaleString()}
              </div>
            </div>
          </div>
        </div>
        <div className="pay-way-mobile">
          <p className={px('formTitle')} style={{ marginBottom: '10px' }}>
            {intl.formatMessage({ id: 'payment_channels' })}
            {cancelPayDto?.isShowCancelPayButton && (
              // <span style={{ marginLeft: '0.1rem', color: 'red' }}>
              //   (
              //   {getLangGroup(
              //     cancelPayDto?.payMsgCodeDto.codeCname,
              //     cancelPayDto?.payMsgCodeDto.codePname,
              //     cancelPayDto?.payMsgCodeDto.codeEname,
              //   )}
              //   )
              // </span>
              <div style={{ marginLeft: '10px', color: 'red' }} dangerouslySetInnerHTML={{
                __html: getLangGroup(
                  cancelPayDto?.payMsgCodeDto.codeCname,
                  cancelPayDto?.payMsgCodeDto.codePname,
                  cancelPayDto?.payMsgCodeDto.codeEname,
                )
              }}
              />
            )}
          </p>
          {payCodes?.map((item) => (
            <div style={{ paddingLeft: '30px', marginTop: '5px' }}>
              <Radio
                disabled={!item.available}
                checked={item.codeKey == paySelected}
                onChange={() => {
                  setPaySelected(item.codeKey);
                }}
              >
                {item.codeKey === 'GOV' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/GOVpay.png" alt="" />
                )}
                {item.codeKey === 'BOC' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCEPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BOCP' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCPPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BNU' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/BNU.png" alt="" />
                )}
              </Radio>
              {!item.available && (
                <div style={{ color: 'red', fontSize: '16px' }}>
                  {getLangGroup(
                    item.payMaintenanceCodeDto?.codeCname,
                    item.payMaintenanceCodeDto?.codePname,
                    item.payMaintenanceCodeDto?.codeEname,
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default FormNine;
