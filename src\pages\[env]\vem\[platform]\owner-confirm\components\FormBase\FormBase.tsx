import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';

import { Steps } from 'antd';
import Preview from '../Preview';
import { useIntl } from 'umi';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  classPrefix: string;
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (string) => void;

  setShowTempStore: (boolean) => void;
  setShowConfirmApply: (boolean) => void;
  setDisableConfirmApply: (boolean) => void;
  detail: getEXTxnDataRes;
  txnId: string | undefined;
  verficationStatus?: string;
  currentOwner?: saveTxnOwnersParams;
  tipMsg?: string;
  serviceTitle?: string;
  ownerId?: string;
}

const FormBase = (props: FormBaseProps) => {
  const intl = useIntl();

  const { step, className = '', ...otherProps } = props;
  const {
    setDisableNextStep,
    setNextStepText,
    setShowConfirmApply,
    setDisableConfirmApply,
    detail,
    txnId,
  } = otherProps;

  const px = pc(otherProps.classPrefix);

  const [verficationStatus, setVerficationStatus] = useState<string>(props.verficationStatus || '');
  const [currentOwner, setCurrentOwner] = useState<saveTxnOwnersParams | undefined>();
  const [tipMsg, setTipMsg] = useState(props.tipMsg);
  const [serviceTitle, setServiceTitle] = useState(
    props.serviceTitle || intl.formatMessage({ id: 'ex_test_license_plate_application' }),
  );

  useEffect(() => {
    setVerficationStatus(props.verficationStatus || '');
    console.log('props.currentOwner===', props.currentOwner);
    setCurrentOwner(props.currentOwner);
    setTipMsg(props.tipMsg);
    setServiceTitle(
      props.serviceTitle || intl.formatMessage({ id: 'ex_test_license_plate_application' }),
    );
  }, [props.verficationStatus, props.currentOwner, props.tipMsg, props.serviceTitle]);

  useEffect(() => {
    console.log('[FormBase] step', step);
  }, [step]);

  // 生成右侧表单内容
  const renderRightContent = () => {
    return (
      <Preview
        setShowConfirmApply={setShowConfirmApply}
        setDisableNextStep={setDisableNextStep}
        setNextStepText={setNextStepText}
        setDisableConfirmApply={setDisableConfirmApply}
        detail={detail}
        txnId={txnId}
        verficationStatus={verficationStatus}
        currentOwner={currentOwner}
        tipMsg={tipMsg}
      />
    );
  };

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <h2 className={px('title')}>{serviceTitle}</h2>
      <div className={px('stepinfo')}>
        <div className={px('step')}>
          <h3>{intl.formatMessage({ id: 'application_steps' })}</h3>
          <Steps
            direction="vertical"
            size="small"
            current={currentOwner?.verficationStatus === 'Y' ? 1 : 0}
          >
            <Steps.Step title={intl.formatMessage({ id: 'data_confirmation' })} icon={1} />
            <Steps.Step title={intl.formatMessage({ id: 'identity_recognition' })} icon={2} />
          </Steps>
        </div>
        <div className={px('form')}>{renderRightContent()}</div>
      </div>
    </div>
  );
};

export default FormBase;
