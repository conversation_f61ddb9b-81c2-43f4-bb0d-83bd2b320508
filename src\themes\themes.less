body {
  // 公共
  --primaryColor: #084ab8; //  13a07b
  --firstBgColor: #ffffff; //f2f3f5
  --secondBgColor: #ffffff;
  --thirdBgColor: #d1d1d640;
  // --fourthBgColor: #868686;
  --firstTextColor: #232323;
  --secondTextColor: #6c6c6c;
  --thirdTextColor: #8a8a8a;
  // --fourthTextColor: #ffffff66;
  --borderColor: #c1c1c1;
  --loopColor1: #d9ebe7;
  --loopColor1-dark7: darken(#d9ebe7, 7%);
  --loopColor2: #dfeaf5;
  --loopColor2-dark7: darken(#dfeaf5, 7%);
  --loopColor3: #faedd9;
  --loopColor3-dark7: darken(#faedd9, 7%);
  --loopColor4: #f7e4da;
  --loopColor4-dark7: darken(#f7e4da, 7%);
  --picker-mask-first: hsla(0, 0%, 100%, 0.95);
  --picker-mask-second: hsla(0, 0%, 100%, 0.6);
  --item-border: #d4d4d4;
  --step-focuse: #ffc107;
  --step-number: #c1c1c1;
  --error: #f33b40;


  --adm-color-primary: #084ab8;
}

body.theme-dark {
  // 公共
  --primaryColor: #084ab8; //46c7a5
  --firstBgColor: #000000;
  --secondBgColor: #1f1f1f;
  --thirdBgColor: #373737;
  --firstTextColor: #fffffff5;
  --secondTextColor: #ffffffc2;
  --thirdTextColor: #ffffff99;
  --borderColor: #3e3e3e;
  --loopColor1: #3e3e3e;
  --loopColor1-dark7: #373737;
  --loopColor2: #3e3e3e;
  --loopColor2-dark7: #373737;
  --loopColor3: #3e3e3e;
  --loopColor3-dark7: #373737;
  --loopColor4: #3e3e3e;
  --loopColor4-dark7: #373737;
  --picker-mask-first: hsla(0, 0%, 12%, 0.95);
  --picker-mask-second: hsla(0, 0%, 12%, 0.6);
  --item-border: #979797;
  --step-focuse: #ffd351;
  --step-number: #868686;
  --error: #ff5358;
}

.am-picker-popup {
  background: var(--secondBgColor);
  .am-picker-col-mask {
    // background: transparent;
    background-image: linear-gradient(180deg, var(--picker-mask-first), var(--picker-mask-second)),
      linear-gradient(0deg, var(--picker-mask-first), var(--picker-mask-second));
  }
}

.am-button.am-button-primary {
  background-color: var(--primaryColor);
}

.ant-divider {
  border-top-color: var(--borderColor);
}

.adm-popup {
  // popup圆角 底色
  .adm-popup-body {
    background-color: var(--secondBgColor);
  }

  // header按钮
  .adm-picker-header-button {
    color: var(--thirdTextColor);
  }

  // 选项颜色
  .adm-picker-view-column-item {
    color: var(--firstTextColor);
  }

  // 遮罩
  .adm-picker-view-mask-top {
    background-image: linear-gradient(180deg, var(--picker-mask-first), var(--picker-mask-second));
  }

  // 遮罩
  .adm-picker-view-mask-bottom {
    background-image: linear-gradient(0deg, var(--picker-mask-first), var(--picker-mask-second));
  }
}


.ant-modal-root {
  .ant-modal-footer {
    .ant-btn.ant-btn-default {
      background: #fff;
      color: @brand-primary;
      border: 1px solid @brand-primary
    }
  }
}

