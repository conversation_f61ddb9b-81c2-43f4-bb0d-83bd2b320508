export interface ApplicantInformationsModelsState {
  applicantInformations: {
    // 申请人姓（中文）
    applicantLastnameZh: string;
    // 申请人名（中文）
    applicantNameZh: string;
    // 申请人姓（外文）
    applicantLastnameFl: string;
    // 申请人名（中文）
    applicantNameFl: string;
    // 申請主體證件類型
    applicantCertType: number;
    applicantCertTypeDesc: string;
    // 申請主體證件號碼
    applicantCertNo: string;
    // 通知语言
    informLanguage: string;
    // 流动电话号码
    mobilePhone: string;
    // 接收讯息方式
    acceptInformType: string;
    euid: string;
  };
  hasApplicantInformations: boolean;
}

export const initApplicantInformationsModelsState = {
  applicantInformations: {
    applicantLastnameZh: 'Safp',
    applicantNameZh: 'user01',
    applicantLastnameFl: 'SALEMA',
    applicantNameFl: 'MARIA',
    applicantCertType: '15',
    applicantCertTypeDesc: '澳門特區永久性居民身分證',
    applicantCertNo: '90000101',
    informLanguage: 'zh-MO',
    mobilePhone: '66131153',
    acceptInformType: '短訊',
  },
  hasApplicantInformations: true,
};

export default {
  namespace: 'applicantInformationsModels',
  state: initApplicantInformationsModelsState,

  reducers: {
    update: (
      state: ApplicantInformationsModelsState,
      { payload }: { payload: Partial<ApplicantInformationsModelsState> },
    ) => ({
      ...state,
      ...payload,
    }),
    init: () => initApplicantInformationsModelsState,
  },
};
