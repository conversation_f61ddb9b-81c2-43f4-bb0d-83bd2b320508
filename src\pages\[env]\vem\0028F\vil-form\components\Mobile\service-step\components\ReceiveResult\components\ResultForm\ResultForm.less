.root {
  // padding: 0 40px;
  .rowVertical {
    margin-bottom: 40px;
    font-size: 28px;
    color: #595959;
    .label {
      margin-bottom: 16px;
    }
    .required::after {
      display: inline-block;
      margin-left: 0.04rem;
      color: #ff5b60;
      font-size: 0.12rem;
      line-height: 1;
      content: '*';
    }
  }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    font-size: 28px;
    color: #595959;
  }
  .name {
    font-weight: bold;
    font-size: 32px;
  }
  .radio {
    margin-right: 200px;
  }
  .rowVertical {
    margin-bottom: 40px;
    font-size: 28px;
    color: #595959;
  }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    font-size: 28px;
    color: #595959;
  }

  .label {
    margin-bottom: 16px;
  }
  .required::after {
    display: inline-block;
    margin-left: 0.04rem;
    color: #ff5b60;
    font-size: 0.16rem;
    line-height: 1;
    content: '*';
  }

  .feeContainer {
    > div {
      display: flex;
      color: var(--thirdTextColor);
    }
  }

  :global {
    .cbd-mobile-base-form-checkbox-horizontal {
      .am-checkbox-wrapper:last-child {
        margin-right: 0;
      }
    }
  }
}

:global .webServiceModal .ant-btn {
  height: 48px !important;
  line-height: 0px !important;
  border-radius: 20px !important;
  padding: 20px 30px !important;
  font-size: 14px !important;
  color: #13a07b !important;
  border: 2px solid #13a07b !important;
  background-color: #fff !important;
}
