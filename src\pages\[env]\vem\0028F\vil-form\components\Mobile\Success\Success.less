@prefix: success-28f-mobile;

.@{prefix} {
  &- {
    &root {
      position: relative;
      min-height: 100%;
      background-color: #fff;

      h1 {
        font-size: 0.24rem;
        text-align: center;
      }
    }

    &alert {
      padding: 0.2rem;
    }

    &body {
      margin: 0.1rem auto;
      padding: 0.2rem 0;
      width: 90%;
      border-top: 1px solid #eeeeee;
      border-bottom: 1px solid #eeeeee;
      font-size: 0.14rem;
    }

    &row {
      display: flex;
      margin: 0.1rem 0;
      line-height: 1.5em;
    }

    &label {
      width: 7em;
      color: #aaaaaa;
    }

    &value {
      flex: 1;
      text-align: left;
      word-break: break-all;
    }

    &successTitlleMobile {
      color: #aaaaaa;
      // color: #323232;
      text-align: center;
      font-size: 0.12rem;
      height: auto;
      border-top: 1px solid #eeeeee;
      line-height: 30px;
      margin: 20px 0px 10px 0px;
      padding: 10px 0;
    }

    &successBottomTitleSpan {
      color: #084ab8;
    }

    &footer {
      width: 100%;
      height: 1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 0.2rem;
      background: #fff;

      .ant-btn {
        margin-top: 0.2rem;
        flex: 1;
        width: 100%;
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;
        height: 0.52rem;
        line-height: 0.52rem;
        border-radius: 0.26rem;
        font-size: 0.16rem;
        padding: 0 0.05rem;
      }
    }
  }
}
