import React from 'react';
import pc from 'prefix-classnames';
import './ListOne.less';
import { useIntl, history } from 'umi';

const px = pc('list-one');

export interface ListOneProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  onGoto: (url: number) => void;
}

const ListOne = (props: ListOneProps) => {
  const intl = useIntl();
  const { className = '', ...otherProps } = props;
  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <div className="web-base-index-ad-main">
        <div className="web-base-index-ad-business web-base-index-ad-button">
          <a
            onClick={() => {
              otherProps.onGoto(2);
            }}
          >
            <svg
              width="0.34rem"
              height="0.34rem"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="1506"
            >
              <path
                d="M931.43 411.026c-7.767 0-19.418-11.65-23.301-23.302-23.302-58.254-42.72-116.508-66.022-174.762-11.65-38.836-38.836-62.138-73.788-69.905-50.487-11.651-100.974-19.418-147.578-31.07-73.788-11.65-135.926-11.65-217.482 0-27.186 3.884-54.37 7.768-85.44 11.652-73.788 15.534-132.043 46.603-155.344 124.275-11.651 46.604-31.07 93.207-50.487 139.81-3.884 7.768-15.535 23.302-19.418 23.302-50.487-7.767-73.79 23.302-85.44 66.021-3.884 7.768 0 19.419 0 23.302C26.548 504.233 45.966 512 61.5 515.884c0 73.788-3.883 147.577-3.883 221.366 0 38.836-3.884 81.556 0 120.392 0 19.418 7.767 46.603 19.418 54.37 7.767 11.651 139.81 7.768 166.996 0 11.65-3.883 15.534-62.138 27.185-97.09H760.55c11.651 31.069-11.65 73.789 27.186 97.09 11.65 7.768 155.344 7.768 159.228-3.883 7.767-3.884 15.534-31.07 15.534-46.604V578.021c0-19.418-3.883-42.72-7.767-62.137 19.418-3.884 38.836-11.651 58.254-15.535 0-7.767 3.884-19.418 0-31.069-11.65-34.952-34.952-62.138-81.556-58.254zM278.983 663.461c-19.418 3.884-38.836 3.884-54.37 3.884-27.186-3.884-54.371-3.884-77.673-7.768-34.952-7.767-42.72-31.069-31.069-77.672 7.768-31.069 31.07-31.069 50.487-23.302 42.72 15.535 85.44 31.07 124.276 50.487 11.65 3.884 23.302 23.302 19.418 31.07-3.884 11.65-19.418 19.417-31.069 23.301z m-97.09-236.9c23.301-69.906 46.603-132.043 69.905-194.181 3.883-11.651 23.301-23.302 38.836-27.186 31.069-3.883 66.021-11.65 100.974-11.65 104.858 0 209.715 3.883 314.573 7.767 42.72 0 69.905 23.302 85.44 66.021 15.534 50.487 31.068 100.974 50.486 159.228-221.366 46.604-438.848 46.604-660.214 0z m702.934 233.016c-27.185 7.768-54.37 7.768-77.672 11.651-27.186-3.883-46.604 0-69.905-7.767-27.186-3.884-34.953-34.953-11.651-46.603 50.487-23.302 100.974-42.72 151.46-58.255 19.419-7.767 34.953 11.651 34.953 34.953 0 23.302 7.768 54.37-27.185 66.021z"
                fill="currentColor"
                p-id="1507"
              ></path>
            </svg>
            {intl.formatMessage({ id: 'new_car_registration' })}
            <svg
              viewBox="64 64 896 896"
              focusable="false"
              data-icon="right"
              width="1em"
              height="1em"
              fill="currentColor"
              aria-hidden="true"
            >
              <path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"></path>
            </svg>
          </a>
        </div>
        <div className="web-base-index-ad-business web-base-index-ad-button">
          <a
            onClick={() => {
              otherProps.onGoto(3);
            }}
          >
            <svg
              width="0.34rem"
              height="0.34rem"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="2592"
            >
              <path
                d="M511.953455 1002.146909c-142.987636 0-408.901818-218.763636-408.901818-425.634909L103.051636 164.421818l40.657455-0.674909c0.861091 0 91.624727-1.931636 185.274182-39.936 96.046545-39.028364 157.998545-83.828364 158.580364-84.247273l24.273455-17.687273 24.482909 17.687273c0.581818 0.442182 62.533818 45.218909 158.580364 84.247273 93.649455 38.004364 184.413091 39.936 185.367273 39.936l40.471273 0.674909 0.186182 412.090182C920.948364 783.36 655.034182 1002.146909 511.953455 1002.146909L511.953455 1002.146909zM185.623273 243.409455l0 333.079273c0 159.953455 231.633455 343.063273 326.330182 343.063273 94.72 0 326.330182-183.109818 326.330182-343.063273L838.283636 243.409455c-40.471273-4.375273-106.170182-15.429818-174.405818-43.124364-69.934545-28.439273-123.042909-59.345455-151.947636-77.754182-28.811636 18.408727-81.989818 49.314909-151.854545 77.754182C291.793455 228.002909 226.071273 239.034182 185.623273 243.409455L185.623273 243.409455zM490.077091 731.345455l-173.614545-147.898182 53.387636-62.813091 111.383273 94.813091 211.386182-243.525818 62.417455 54.155636L490.077091 731.345455 490.077091 731.345455zM490.077091 731.345455"
                fill="currentColor"
                p-id="2593"
              ></path>
            </svg>

            {intl.formatMessage({ id: 'model_approval' })}
            <svg
              viewBox="64 64 896 896"
              focusable="false"
              data-icon="right"
              width="1em"
              height="1em"
              fill="currentColor"
              aria-hidden="true"
            >
              <path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  );
};

export default ListOne;
