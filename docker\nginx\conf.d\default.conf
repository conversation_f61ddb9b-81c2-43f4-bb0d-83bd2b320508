
server {

      #error_page  404              /404.html;
      error_page   500 502 503 504  /50x.html;

      location = /50x.html {
          root   html;
      }

      listen 8080;
      # server_name xxx.com;

      location / {
          charset utf-8;
          root   /home/<USER>/dist;
          if ($request_filename ~ .*\.(htm|html)$)
          {
            add_header Cache-Control no-cache;
          }
          index  index.html index.htm index.shtml;
          try_files $uri /index.html;
      }

      location /api/ {
          proxy_pass ${BACKEND_SERVER};
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "Upgrade";
          proxy_set_header Host $host;
      }
}
