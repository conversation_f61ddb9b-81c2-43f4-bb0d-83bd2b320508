@prefix: owner-confirm-page;

.@{prefix} {
  &- {
    &root {
      padding: 0 10% 88px;
      height: calc(100% - 68px);
      overflow-y: auto
    }

    &title {
        font-size: 20px;
        font-weight: 700;
        padding: 20px 24px;
        border-bottom: 1px solid rgba(0,0,0,.15)
    }

    &stepinfo {
        display: flex
    }

    &step {
        margin-top: 20px;
        padding: 0 22px;
        width: 240px;
        min-width: -webkit-fit-content;
        min-width: -moz-fit-content;
        min-width: fit-content;
        border-right: 1px solid rgba(0,0,0,.15)
    }

    &step h3 {
        font-size: 16px;
        line-height: 22px;
        font-weight: 700;
        padding: 0 2px 16px
    }

    &step .ant-steps-small {
        font-size: 16px
    }

    &step .ant-steps-small .ant-steps-item {
        height: 70px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon {
        background-color: rgba(8,74,184,.25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-icon {
        background-color: @brand-primary
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-tail:after {
        background-color: @brand-primary;
        height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-icon {
        background-color: rgba(255,193,7,.25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-icon {
        background-color: #ffc107
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-tail:after {
        background-color: #c4c4c4;
        height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-icon {
        background-color: hsla(0,0%,76.9%,.25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-icon {
        background-color: #c4c4c4
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-tail:after {
        background-color: #c4c4c4;
        height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait:last-child .ant-steps-item-tail:after {
        display: none
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container {
        height: 40px;
        min-height: 40px;
        display: flex;
        align-items: center
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-tail {
        bottom: 0;
        top: 40px;
        left: 14px;
        padding: 0
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        margin-right: 6px;
        cursor: pointer;
        flex-shrink: 0
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon .ant-steps-icon {
        height: 18px;
        min-height: 18px;
        display: flex;
        align-items: center;
        width: 18px;
        line-height: 18px;
        left: 5px;
        top: 5px;
        font-size: 14px;
        justify-content: center;
        color: #fff;
        border-radius: 50%
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content {
        height: 40px;
        min-height: 40px;
        display: flex;
        align-items: center;
        line-height: 16px
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content .ant-steps-item-title {
        font-size: 16px;
        color: #333
    }

    &form {
        flex: 1 1;
        margin-top: 20px
    }

    &form h3 {
        font-size: 16px;
        color: rgba(0,0,0,.85);
        font-weight: 700;
        margin-left: 24px
    }

    &form>.pc-pay-ui-root {
        margin-left: 24px
    }

    &form .pc-pay-ui-top-amount {
        line-height: 65px
    }

    &form .pc-pay-ui-top-amount span {
        position: relative
    }

    &form .pc-pay-ui-top-amount span:first-child {
        font-size: 16px;
        color: #666;
        top: -2px
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
        font-size: 24px;
        top: -2px
    }

    &formTitle {
        display: flex;
        align-items: center;
        font-weight: 700;
        margin-left: 24px;
        font-size: 18px;
        color: #232323;
        padding-bottom: 10px
    }

    &formTitle:before {
        display: block;
        content: "";
        width: 4px;
        height: 16px;
        background: @brand-primary;
        margin-right: 8px
    }

    &spin {
        width: 100%;
        height: 100%;
        padding: 45%
    }

    &footer-button {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 68px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 0 10%;
        background: #fff;
        box-shadow: 0 -2px 8px 0 rgba(0,0,0,.1)
    }

    &footer-button .ant-btn {
        min-width: 128px;
        height: 48px;
        line-height: 48px;
        border-radius: 24px;
        margin-left: 12px;
        font-size: 16px;
        padding: 0 5px
    }

    &footer-button .ant-btn.ant-btn-default {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary
    }
  }
}
