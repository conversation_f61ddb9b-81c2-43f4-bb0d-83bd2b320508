<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<svg width="330" height="330" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect id="background" x="0" y="0" width="330" height="330" fill="white"/>
<defs>
<rect id="mark" width="10" height="10" fill="black"/>
</defs>
<g id="quickmark" fill="black" transform="translate(40,40)">
<use x="0" y="0" xlink:href="#mark"/>
<use x="10" y="0" xlink:href="#mark"/>
<use x="20" y="0" xlink:href="#mark"/>
<use x="30" y="0" xlink:href="#mark"/>
<use x="40" y="0" xlink:href="#mark"/>
<use x="50" y="0" xlink:href="#mark"/>
<use x="60" y="0" xlink:href="#mark"/>
<use x="80" y="0" xlink:href="#mark"/>
<use x="90" y="0" xlink:href="#mark"/>
<use x="100" y="0" xlink:href="#mark"/>
<use x="140" y="0" xlink:href="#mark"/>
<use x="180" y="0" xlink:href="#mark"/>
<use x="190" y="0" xlink:href="#mark"/>
<use x="200" y="0" xlink:href="#mark"/>
<use x="210" y="0" xlink:href="#mark"/>
<use x="220" y="0" xlink:href="#mark"/>
<use x="230" y="0" xlink:href="#mark"/>
<use x="240" y="0" xlink:href="#mark"/>
<use x="0" y="10" xlink:href="#mark"/>
<use x="60" y="10" xlink:href="#mark"/>
<use x="110" y="10" xlink:href="#mark"/>
<use x="180" y="10" xlink:href="#mark"/>
<use x="240" y="10" xlink:href="#mark"/>
<use x="0" y="20" xlink:href="#mark"/>
<use x="20" y="20" xlink:href="#mark"/>
<use x="30" y="20" xlink:href="#mark"/>
<use x="40" y="20" xlink:href="#mark"/>
<use x="60" y="20" xlink:href="#mark"/>
<use x="80" y="20" xlink:href="#mark"/>
<use x="90" y="20" xlink:href="#mark"/>
<use x="120" y="20" xlink:href="#mark"/>
<use x="130" y="20" xlink:href="#mark"/>
<use x="150" y="20" xlink:href="#mark"/>
<use x="180" y="20" xlink:href="#mark"/>
<use x="200" y="20" xlink:href="#mark"/>
<use x="210" y="20" xlink:href="#mark"/>
<use x="220" y="20" xlink:href="#mark"/>
<use x="240" y="20" xlink:href="#mark"/>
<use x="0" y="30" xlink:href="#mark"/>
<use x="20" y="30" xlink:href="#mark"/>
<use x="30" y="30" xlink:href="#mark"/>
<use x="40" y="30" xlink:href="#mark"/>
<use x="60" y="30" xlink:href="#mark"/>
<use x="90" y="30" xlink:href="#mark"/>
<use x="100" y="30" xlink:href="#mark"/>
<use x="130" y="30" xlink:href="#mark"/>
<use x="140" y="30" xlink:href="#mark"/>
<use x="180" y="30" xlink:href="#mark"/>
<use x="200" y="30" xlink:href="#mark"/>
<use x="210" y="30" xlink:href="#mark"/>
<use x="220" y="30" xlink:href="#mark"/>
<use x="240" y="30" xlink:href="#mark"/>
<use x="0" y="40" xlink:href="#mark"/>
<use x="20" y="40" xlink:href="#mark"/>
<use x="30" y="40" xlink:href="#mark"/>
<use x="40" y="40" xlink:href="#mark"/>
<use x="60" y="40" xlink:href="#mark"/>
<use x="100" y="40" xlink:href="#mark"/>
<use x="130" y="40" xlink:href="#mark"/>
<use x="140" y="40" xlink:href="#mark"/>
<use x="180" y="40" xlink:href="#mark"/>
<use x="200" y="40" xlink:href="#mark"/>
<use x="210" y="40" xlink:href="#mark"/>
<use x="220" y="40" xlink:href="#mark"/>
<use x="240" y="40" xlink:href="#mark"/>
<use x="0" y="50" xlink:href="#mark"/>
<use x="60" y="50" xlink:href="#mark"/>
<use x="80" y="50" xlink:href="#mark"/>
<use x="100" y="50" xlink:href="#mark"/>
<use x="120" y="50" xlink:href="#mark"/>
<use x="160" y="50" xlink:href="#mark"/>
<use x="180" y="50" xlink:href="#mark"/>
<use x="240" y="50" xlink:href="#mark"/>
<use x="0" y="60" xlink:href="#mark"/>
<use x="10" y="60" xlink:href="#mark"/>
<use x="20" y="60" xlink:href="#mark"/>
<use x="30" y="60" xlink:href="#mark"/>
<use x="40" y="60" xlink:href="#mark"/>
<use x="50" y="60" xlink:href="#mark"/>
<use x="60" y="60" xlink:href="#mark"/>
<use x="80" y="60" xlink:href="#mark"/>
<use x="100" y="60" xlink:href="#mark"/>
<use x="120" y="60" xlink:href="#mark"/>
<use x="140" y="60" xlink:href="#mark"/>
<use x="160" y="60" xlink:href="#mark"/>
<use x="180" y="60" xlink:href="#mark"/>
<use x="190" y="60" xlink:href="#mark"/>
<use x="200" y="60" xlink:href="#mark"/>
<use x="210" y="60" xlink:href="#mark"/>
<use x="220" y="60" xlink:href="#mark"/>
<use x="230" y="60" xlink:href="#mark"/>
<use x="240" y="60" xlink:href="#mark"/>
<use x="100" y="70" xlink:href="#mark"/>
<use x="120" y="70" xlink:href="#mark"/>
<use x="140" y="70" xlink:href="#mark"/>
<use x="160" y="70" xlink:href="#mark"/>
<use x="0" y="80" xlink:href="#mark"/>
<use x="20" y="80" xlink:href="#mark"/>
<use x="60" y="80" xlink:href="#mark"/>
<use x="70" y="80" xlink:href="#mark"/>
<use x="90" y="80" xlink:href="#mark"/>
<use x="100" y="80" xlink:href="#mark"/>
<use x="110" y="80" xlink:href="#mark"/>
<use x="120" y="80" xlink:href="#mark"/>
<use x="140" y="80" xlink:href="#mark"/>
<use x="150" y="80" xlink:href="#mark"/>
<use x="160" y="80" xlink:href="#mark"/>
<use x="190" y="80" xlink:href="#mark"/>
<use x="220" y="80" xlink:href="#mark"/>
<use x="240" y="80" xlink:href="#mark"/>
<use x="0" y="90" xlink:href="#mark"/>
<use x="20" y="90" xlink:href="#mark"/>
<use x="30" y="90" xlink:href="#mark"/>
<use x="50" y="90" xlink:href="#mark"/>
<use x="90" y="90" xlink:href="#mark"/>
<use x="120" y="90" xlink:href="#mark"/>
<use x="130" y="90" xlink:href="#mark"/>
<use x="150" y="90" xlink:href="#mark"/>
<use x="160" y="90" xlink:href="#mark"/>
<use x="180" y="90" xlink:href="#mark"/>
<use x="210" y="90" xlink:href="#mark"/>
<use x="230" y="90" xlink:href="#mark"/>
<use x="240" y="90" xlink:href="#mark"/>
<use x="0" y="100" xlink:href="#mark"/>
<use x="20" y="100" xlink:href="#mark"/>
<use x="30" y="100" xlink:href="#mark"/>
<use x="60" y="100" xlink:href="#mark"/>
<use x="90" y="100" xlink:href="#mark"/>
<use x="110" y="100" xlink:href="#mark"/>
<use x="150" y="100" xlink:href="#mark"/>
<use x="160" y="100" xlink:href="#mark"/>
<use x="190" y="100" xlink:href="#mark"/>
<use x="200" y="100" xlink:href="#mark"/>
<use x="210" y="100" xlink:href="#mark"/>
<use x="220" y="100" xlink:href="#mark"/>
<use x="240" y="100" xlink:href="#mark"/>
<use x="10" y="110" xlink:href="#mark"/>
<use x="30" y="110" xlink:href="#mark"/>
<use x="40" y="110" xlink:href="#mark"/>
<use x="70" y="110" xlink:href="#mark"/>
<use x="90" y="110" xlink:href="#mark"/>
<use x="100" y="110" xlink:href="#mark"/>
<use x="110" y="110" xlink:href="#mark"/>
<use x="180" y="110" xlink:href="#mark"/>
<use x="200" y="110" xlink:href="#mark"/>
<use x="210" y="110" xlink:href="#mark"/>
<use x="0" y="120" xlink:href="#mark"/>
<use x="10" y="120" xlink:href="#mark"/>
<use x="30" y="120" xlink:href="#mark"/>
<use x="60" y="120" xlink:href="#mark"/>
<use x="80" y="120" xlink:href="#mark"/>
<use x="100" y="120" xlink:href="#mark"/>
<use x="110" y="120" xlink:href="#mark"/>
<use x="150" y="120" xlink:href="#mark"/>
<use x="180" y="120" xlink:href="#mark"/>
<use x="190" y="120" xlink:href="#mark"/>
<use x="240" y="120" xlink:href="#mark"/>
<use x="30" y="130" xlink:href="#mark"/>
<use x="80" y="130" xlink:href="#mark"/>
<use x="90" y="130" xlink:href="#mark"/>
<use x="130" y="130" xlink:href="#mark"/>
<use x="150" y="130" xlink:href="#mark"/>
<use x="160" y="130" xlink:href="#mark"/>
<use x="170" y="130" xlink:href="#mark"/>
<use x="180" y="130" xlink:href="#mark"/>
<use x="190" y="130" xlink:href="#mark"/>
<use x="230" y="130" xlink:href="#mark"/>
<use x="240" y="130" xlink:href="#mark"/>
<use x="30" y="140" xlink:href="#mark"/>
<use x="40" y="140" xlink:href="#mark"/>
<use x="50" y="140" xlink:href="#mark"/>
<use x="60" y="140" xlink:href="#mark"/>
<use x="70" y="140" xlink:href="#mark"/>
<use x="110" y="140" xlink:href="#mark"/>
<use x="150" y="140" xlink:href="#mark"/>
<use x="180" y="140" xlink:href="#mark"/>
<use x="190" y="140" xlink:href="#mark"/>
<use x="210" y="140" xlink:href="#mark"/>
<use x="220" y="140" xlink:href="#mark"/>
<use x="240" y="140" xlink:href="#mark"/>
<use x="0" y="150" xlink:href="#mark"/>
<use x="10" y="150" xlink:href="#mark"/>
<use x="30" y="150" xlink:href="#mark"/>
<use x="40" y="150" xlink:href="#mark"/>
<use x="80" y="150" xlink:href="#mark"/>
<use x="90" y="150" xlink:href="#mark"/>
<use x="100" y="150" xlink:href="#mark"/>
<use x="140" y="150" xlink:href="#mark"/>
<use x="170" y="150" xlink:href="#mark"/>
<use x="180" y="150" xlink:href="#mark"/>
<use x="200" y="150" xlink:href="#mark"/>
<use x="50" y="160" xlink:href="#mark"/>
<use x="60" y="160" xlink:href="#mark"/>
<use x="80" y="160" xlink:href="#mark"/>
<use x="90" y="160" xlink:href="#mark"/>
<use x="110" y="160" xlink:href="#mark"/>
<use x="120" y="160" xlink:href="#mark"/>
<use x="160" y="160" xlink:href="#mark"/>
<use x="170" y="160" xlink:href="#mark"/>
<use x="180" y="160" xlink:href="#mark"/>
<use x="190" y="160" xlink:href="#mark"/>
<use x="200" y="160" xlink:href="#mark"/>
<use x="230" y="160" xlink:href="#mark"/>
<use x="80" y="170" xlink:href="#mark"/>
<use x="100" y="170" xlink:href="#mark"/>
<use x="120" y="170" xlink:href="#mark"/>
<use x="140" y="170" xlink:href="#mark"/>
<use x="160" y="170" xlink:href="#mark"/>
<use x="200" y="170" xlink:href="#mark"/>
<use x="220" y="170" xlink:href="#mark"/>
<use x="240" y="170" xlink:href="#mark"/>
<use x="0" y="180" xlink:href="#mark"/>
<use x="10" y="180" xlink:href="#mark"/>
<use x="20" y="180" xlink:href="#mark"/>
<use x="30" y="180" xlink:href="#mark"/>
<use x="40" y="180" xlink:href="#mark"/>
<use x="50" y="180" xlink:href="#mark"/>
<use x="60" y="180" xlink:href="#mark"/>
<use x="80" y="180" xlink:href="#mark"/>
<use x="120" y="180" xlink:href="#mark"/>
<use x="140" y="180" xlink:href="#mark"/>
<use x="160" y="180" xlink:href="#mark"/>
<use x="180" y="180" xlink:href="#mark"/>
<use x="200" y="180" xlink:href="#mark"/>
<use x="210" y="180" xlink:href="#mark"/>
<use x="240" y="180" xlink:href="#mark"/>
<use x="0" y="190" xlink:href="#mark"/>
<use x="60" y="190" xlink:href="#mark"/>
<use x="90" y="190" xlink:href="#mark"/>
<use x="110" y="190" xlink:href="#mark"/>
<use x="120" y="190" xlink:href="#mark"/>
<use x="150" y="190" xlink:href="#mark"/>
<use x="160" y="190" xlink:href="#mark"/>
<use x="200" y="190" xlink:href="#mark"/>
<use x="0" y="200" xlink:href="#mark"/>
<use x="20" y="200" xlink:href="#mark"/>
<use x="30" y="200" xlink:href="#mark"/>
<use x="40" y="200" xlink:href="#mark"/>
<use x="60" y="200" xlink:href="#mark"/>
<use x="90" y="200" xlink:href="#mark"/>
<use x="100" y="200" xlink:href="#mark"/>
<use x="120" y="200" xlink:href="#mark"/>
<use x="150" y="200" xlink:href="#mark"/>
<use x="160" y="200" xlink:href="#mark"/>
<use x="170" y="200" xlink:href="#mark"/>
<use x="180" y="200" xlink:href="#mark"/>
<use x="190" y="200" xlink:href="#mark"/>
<use x="200" y="200" xlink:href="#mark"/>
<use x="210" y="200" xlink:href="#mark"/>
<use x="230" y="200" xlink:href="#mark"/>
<use x="0" y="210" xlink:href="#mark"/>
<use x="20" y="210" xlink:href="#mark"/>
<use x="30" y="210" xlink:href="#mark"/>
<use x="40" y="210" xlink:href="#mark"/>
<use x="60" y="210" xlink:href="#mark"/>
<use x="100" y="210" xlink:href="#mark"/>
<use x="150" y="210" xlink:href="#mark"/>
<use x="160" y="210" xlink:href="#mark"/>
<use x="170" y="210" xlink:href="#mark"/>
<use x="190" y="210" xlink:href="#mark"/>
<use x="200" y="210" xlink:href="#mark"/>
<use x="210" y="210" xlink:href="#mark"/>
<use x="220" y="210" xlink:href="#mark"/>
<use x="230" y="210" xlink:href="#mark"/>
<use x="0" y="220" xlink:href="#mark"/>
<use x="20" y="220" xlink:href="#mark"/>
<use x="30" y="220" xlink:href="#mark"/>
<use x="40" y="220" xlink:href="#mark"/>
<use x="60" y="220" xlink:href="#mark"/>
<use x="80" y="220" xlink:href="#mark"/>
<use x="100" y="220" xlink:href="#mark"/>
<use x="110" y="220" xlink:href="#mark"/>
<use x="150" y="220" xlink:href="#mark"/>
<use x="170" y="220" xlink:href="#mark"/>
<use x="200" y="220" xlink:href="#mark"/>
<use x="230" y="220" xlink:href="#mark"/>
<use x="240" y="220" xlink:href="#mark"/>
<use x="0" y="230" xlink:href="#mark"/>
<use x="60" y="230" xlink:href="#mark"/>
<use x="90" y="230" xlink:href="#mark"/>
<use x="110" y="230" xlink:href="#mark"/>
<use x="120" y="230" xlink:href="#mark"/>
<use x="140" y="230" xlink:href="#mark"/>
<use x="160" y="230" xlink:href="#mark"/>
<use x="180" y="230" xlink:href="#mark"/>
<use x="190" y="230" xlink:href="#mark"/>
<use x="200" y="230" xlink:href="#mark"/>
<use x="0" y="240" xlink:href="#mark"/>
<use x="10" y="240" xlink:href="#mark"/>
<use x="20" y="240" xlink:href="#mark"/>
<use x="30" y="240" xlink:href="#mark"/>
<use x="40" y="240" xlink:href="#mark"/>
<use x="50" y="240" xlink:href="#mark"/>
<use x="60" y="240" xlink:href="#mark"/>
<use x="80" y="240" xlink:href="#mark"/>
<use x="100" y="240" xlink:href="#mark"/>
<use x="120" y="240" xlink:href="#mark"/>
<use x="140" y="240" xlink:href="#mark"/>
<use x="160" y="240" xlink:href="#mark"/>
<use x="180" y="240" xlink:href="#mark"/>
<use x="210" y="240" xlink:href="#mark"/>
<use x="240" y="240" xlink:href="#mark"/>
</g></svg>