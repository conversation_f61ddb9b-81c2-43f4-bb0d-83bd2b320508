import React, { useContext, useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Payment.less';

import { Alert, Radio } from 'antd';
import { cancelTxnPay, completePayment, getServiceFee } from '@/services/0028F';
import { useIntl } from 'umi';
import { isMobile } from '@/utils';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import SpinContext from '@/components/SpinContext';

const px = pc('payment-33d-mobile');

export interface PaymentProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;
  txnId: string | undefined;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (text: string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;
  contentForNextStep: (number) => void;
}

export type ServiceFeeType = {
  key: string;
  title: string;
  amount: string;
  tax: any;
  subtotal: any;
};

const Payment = (props: PaymentProps) => {
  const intl = useIntl();
  const { className = '', ...otherProps } = props;
  const {
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    txnId,
    contentForNextStep,
    contentNextStep,
    setDisableNextStep,
  } = otherProps;

  const [tipMsg, setTipMsg] = useState<string>('');
  const [serviceFeeData, setServiceFeeData] = useState<getServiceFeeRes>({} as getServiceFeeRes);
  const [paySelected, setPaySelected] = useState<string>('');
  const { setLoading } = useContext(SpinContext);
  const baseUrl =
    window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const [cancelPayDto, setCancelPayDto] = useState<CancelPayDto>();
  const handleCancelTxnPay = async () => {
    if (!txnId) return;
    try {
      const res = await cancelTxnPay([txnId]);
      if (res?.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }
      // 刷新页面
      location.reload();
    } finally {
      setDisableNextStep(false);
    }
  };

  useEffect(() => {
    const fetchServiceFee = async () => {
      try {
        if (txnId) {
          const res = await getServiceFee(txnId);
          setLoading(false);
          if (res && res.code === '0') {
            const { data } = res;
            // 设置取消支付状态
            setCancelPayDto(data?.cancelPayDto);
            // 如果需要取消支付
            if (data?.cancelPayDto?.isShowCancelPayButton === true) {
              // 禁用支付选择
              data.payCodes.forEach((item) => {
                item.available = false;
              });
              // 设置取消支付按钮文案
              setNextStepText(intl.formatMessage({ id: 'cancel_payment' }));
            }

            setServiceFeeData(res.data ?? {});
            if (res.data.payCodes && res.data.payCodes?.length) {
              const codeKey =
                data?.cancelPayDto?.isShowCancelPayButton === true
                  ? data.cancelPayDto.payCodeKey
                  : data.payCodes[0].codeKey;
              setPaySelected(codeKey);
            }
          } else {
            setLoading(false);
            setTipMsg(getResponseMessage(res));
          }
        }
      } catch (e: any) {
        setLoading(false);
        setTipMsg(e.message);
      }
    };
    fetchServiceFee();
  }, [txnId]);

  const tableData = useMemo(() => {
    const dataSource: ServiceFeeType[] = [];
    if (serviceFeeData && serviceFeeData.feeItems) {
      (serviceFeeData.feeItems ?? []).forEach((item, index) => {
        dataSource.push({
          key: (index + 1).toString(),
          title: getLangGroup(item.zh, item.pt),
          amount: item.priceTotal?.toLocaleString(),
          tax: item.taxPriceTotal?.toLocaleString(),
          subtotal: item.subtotal?.toLocaleString(),
        });
      });
    }
    return dataSource;
  }, [serviceFeeData]);

  useEffect(() => {
    setLoading(true);
    setShowClose(false);
    setShowTempStore(false);
    setShowCloseAndTempStore(true);
    setDisableNextStep(false);
    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }));
    contentForNextStep(1);
    return () => {
      setShowClose(true);
      setShowTempStore(true);
      setShowCloseAndTempStore(false);

      setNextStepText(intl.formatMessage({ id: 'next' }));
      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    const submit = async () => {
      setTipMsg('');
      setDisableNextStep(true);
      // 是否是取消支付
      if (cancelPayDto?.isShowCancelPayButton) {
        await handleCancelTxnPay();
        return;
      }

      let pageUrl = 'ovsap/web/vem/0033D/newcar-reg-apply?page=success';
      if (paySelected === 'BOCP' || paySelected === 'BOC' || paySelected === 'BNU') {
        pageUrl = baseUrl + '/' + pageUrl;
      }
      const data: completePaymentParams = {
        txnId: txnId!,
        payMethodCodeKey: paySelected,
        lang: 'zh_TW',
        ua: isMobile() ? 'MobileWeb' : 'PCWeb',
        remark: '',
        pageUrl,
      };

      try {
        const ret = await completePayment(data);
        setDisableNextStep(false);
        if (!ret) return;
        if (ret.code === '0') {
          const { data } = ret;
          if (data.payUrl) {
            location.href = data.payUrl;
          } else if (data.returnHTML) {
            document.write(data.returnHTML?.replace(/\\/, ''));
          }
        } else {
          setTipMsg(getResponseMessage(ret));
        }
      } catch (e: any) {
        setTipMsg(e.message);
        setDisableNextStep(false);
      }
    };

    if (contentNextStep > 0) {
      submit();
    }
  }, [contentNextStep]);

  return (
    <>
      {tipMsg && (
        <div style={{ padding: '0.2rem 0.24rem 0' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`}>
        <div className="pay-way-mobile">
          <p className={px('cost-title')}>{intl.formatMessage({ id: 'cost_details' })}</p>
          {tableData.map((item) => {
            return (
              <div className={px('cost-container')}>
                <div key={item.key} className={px('cost-content')}>
                  {/*缴费项*/}
                  <div className={px('cost-content-item')}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'fee_item' })}
                    </div>
                    <div className={px('cost-content-item-text-black')}>{item.title}</div>
                  </div>
                  {/*金额*/}
                  <div className={px('cost-content-item')}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'amount' })}
                    </div>
                    <div className={px('cost-content-item-text-black')}>{item.amount}</div>
                  </div>
                  {/*税款*/}
                  <div className={px('cost-content-item')}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'tax_payment' })}
                    </div>
                    <div className={px('cost-content-item-text-black')}>{item.tax}</div>
                  </div>
                  {/*小计*/}
                  <div className={`${px('cost-content-item')} ${px('cost-divide')}`}>
                    <div className={px('cost-content-item-label')}>
                      {intl.formatMessage({ id: 'subtotal' })}
                    </div>
                    <div className={px('cost-content-item-text-primary')}>{item.subtotal}</div>
                  </div>
                </div>
              </div>
            );
          })}
          <div className={px('cost-divide')} style={{ marginTop: '0.12rem' }}>
            <div className={px('cost-content-item')} style={{ padding: '0 0.28rem' }}>
              <div className={px('cost-content-item-label')}>
                {intl.formatMessage({ id: 'total' })}
              </div>
              <div className={px('cost-content-item-text-primary')}>
                <span style={{ marginRight: '0.1rem' }}>MOP</span>
                {serviceFeeData.total?.toLocaleString()}
              </div>
            </div>
          </div>
        </div>
        {/* <div className={px('pay-tip')}>

          {getLangGroup(
            serviceFeeData?.codeDTO?.codeCname,
            serviceFeeData?.codeDTO?.codePname,
            serviceFeeData?.codeDTO?.codeEname,
          )}
        </div> */}
        <div
          className={px('pay-tip')}
          dangerouslySetInnerHTML={{
            __html: getLangGroup(
              serviceFeeData?.codeDTO?.codeCname,
              serviceFeeData?.codeDTO?.codePname,
              serviceFeeData?.codeDTO?.codeEname,
            ),
          }}
        />
        <div className="pay-way-mobile">
          <p className={px('formTitle')}>
            {intl.formatMessage({ id: 'payment_channels' })}
            {cancelPayDto?.isShowCancelPayButton && (
              <span style={{ marginLeft: '10px', color: 'red' }}>
                (
                {getLangGroup(
                  cancelPayDto?.payMsgCodeDto.codeCname,
                  cancelPayDto?.payMsgCodeDto.codePname,
                  cancelPayDto?.payMsgCodeDto.codeEname,
                )}
                )
              </span>
            )}
          </p>
          {(serviceFeeData.payCodes ?? []).map((item) => (
            <div style={{ paddingLeft: '30px', marginTop: '5px' }} key={item.codeKey}>
              <Radio
                disabled={!item.available}
                checked={item.codeKey === paySelected}
                onChange={(e) => {
                  setPaySelected(item.codeKey);
                }}
              >
                {item.codeKey === 'GOV' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/GOVpay.png" alt="" />
                )}
                {item.codeKey === 'BOC' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCEPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BOCP' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCPPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BNU' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/BNU.png" alt="" />
                )}
              </Radio>
              {!item.available && (
                <div style={{ color: 'red', fontSize: '16px' }}>
                  {getLangGroup(
                    item?.payMaintenanceCodeDto?.codeCname,
                    item?.payMaintenanceCodeDto?.codePname,
                    item?.payMaintenanceCodeDto?.codeEname,
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default Payment;
