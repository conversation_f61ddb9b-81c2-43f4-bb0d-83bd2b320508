import React, { useState, useEffect, useRef } from 'react';
import Portal from '@ali-whale/portal';
import { Toast } from 'antd-mobile';
import { MoBaseContainer as BaseContainer } from '@gov-mo/components';
import { useIntl } from 'react-intl';
import usePersistFunc from '@/layouts/usePersistFunc';
import { receiveTypeEnum } from '@/pages/service-step/config';
import styles from './AddAddress.less';

type AddressValue = {
  title: string;
  address: string;
  contactPerson?: string;
  phone?: string;
};
export interface AddAddressProps {
  value?: AddressValue;
  onChange?: (v: AddressValue) => void;
  type: number;
  title?: string;
  detail?: boolean;
  className?: string;
}

interface SelectPortalProps {
  type: number;
  visible: boolean;
  onClose: (v?: AddressValue) => void;
  title: string;
}
const mailingAddressList = [
  {
    title: '公共行政大厦',
    address: '澳门xx街道1门牌',
    contactPerson: '黄跃才',
    phone: '153xxxx9277',
  },
  {
    title: '公共行政大厦',
    address: '澳门xx街道2门牌',
    contactPerson: '黄跃才',
    phone: '153xxxx9288',
  },
];
const personAddressList = [
  {
    title: '公共行政大厦',
    address: '澳门xx街道1门牌',
  },
  {
    title: '公共行政大厦',
    address: '澳门xx街道2门牌',
  },
];

const getMailingAddressList = () =>
  new Promise<AddressValue[]>((resolve) => {
    setTimeout(() => {
      resolve(mailingAddressList);
    }, Math.random() * 1000);
  });
const getPersonAddressList = () =>
  new Promise<AddressValue[]>((resolve) => {
    setTimeout(() => {
      resolve(personAddressList);
    }, Math.random() * 1000);
  });

const SelectPortal = (props: SelectPortalProps) => {
  const { type, visible, title, onClose } = props;
  const [addressList, setAddressList] = useState<AddressValue[]>([]);
  const [protalShow, setProtalShow] = useState<boolean>(false);
  const intl = useIntl();
  const _this = useRef<{ prevType: number }>({ prevType: -1 }).current;
  const ref = useRef<HTMLDivElement>(null);
  const { current } = ref;

  useEffect(() => {
    if (visible) {
      setProtalShow(true);
    }
  }, [visible]);
  useEffect(() => {
    if (protalShow && visible) {
      if (ref.current) {
        ref.current.style.transform = 'translateX(0)';
      }
    } else if (ref.current) {
      ref.current.style.transform = 'translateX(100%)';
    }
  }, [protalShow, visible, current]);
  useEffect(() => {
    if (visible && _this.prevType !== type) {
      _this.prevType = type;
      Toast.loading(intl.formatMessage({ id: '請稍候' }), 0);
      let service = getMailingAddressList;
      if (type === receiveTypeEnum.personal) {
        service = getPersonAddressList;
      }
      service().then((res) => {
        setAddressList(res);
        Toast.hide();
      });
    }
  }, [visible, type, intl, _this]);

  if (!protalShow) return null;

  return (
    <Portal>
      <div ref={ref} className={`${styles.portalRoot}`}>
        <BaseContainer
          type="custom"
          bodyContentClass={styles.bodyContent}
          titleContent={title}
          onLeftClick={() => {
            onClose();
          }}
        >
          {addressList.map((item) => {
            const { title: insideTitle, address, contactPerson, phone } = item;
            return (
              <div
                onClick={() => {
                  onClose(item);
                }}
                key={address}
                className={styles.addressArea}
              >
                <div className={styles.title}>{insideTitle}</div>
                <div className={styles.detailArea}>
                  <div>
                    <span className={styles.key}>{intl.formatMessage({ id: '地址：' })}</span>
                    <span>{address}</span>
                  </div>
                  {contactPerson && (
                    <div>
                      <span className={styles.key}>{intl.formatMessage({ id: '联系人：' })}</span>
                      <span>{contactPerson}</span>
                    </div>
                  )}
                  {phone && (
                    <div>
                      <span className={styles.key}>{intl.formatMessage({ id: '联系电话：' })}</span>
                      <span>{phone}</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </BaseContainer>
      </div>
    </Portal>
  );
};

function AddAddress(props: AddAddressProps) {
  const { value, onChange, title = '', type, detail = false, className = '' } = props;
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);

  const onClose = usePersistFunc((v?: AddressValue) => {
    if (v && onChange) {
      onChange(v);
    }
    setVisible(false);
  });

  return (
    <>
      {value ? (
        <div className={`${styles.valueArea} ${className}`}>
          <div className={styles.title}>{value.title}</div>
          <div className={styles.detialInfo}>
            {`${intl.formatMessage({ id: '地址：' })}${value.address}`}
          </div>
          {value.contactPerson && (
            <div className={styles.detialInfo}>
              {`${intl.formatMessage({ id: '联系人：' })}${value.contactPerson}`}
            </div>
          )}
          {value.phone && (
            <div className={styles.detialInfo}>
              {`${intl.formatMessage({ id: '联系电话：' })}${value.phone}`}
            </div>
          )}

          {!detail && (
            <div
              onClick={() => {
                setVisible(true);
              }}
              className={styles.modify}
            >
              {intl.formatMessage({ id: '修改' })}
            </div>
          )}
        </div>
      ) : (
        <div
          onClick={() => {
            setVisible(true);
          }}
          className={styles.addBtn}
        >
          <span>{`+${intl.formatMessage({ id: '选择领取地点' })}`}</span>
        </div>
      )}
      <SelectPortal title={title} onClose={onClose} type={type} visible={visible} />
    </>
  );
}

export default AddAddress;
