import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './FormOne.less';

import { useIntl } from 'umi';
import { Input, Select, Col, Button, Form } from 'antd';

const px = pc('form-one');

export interface FormOneProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;
}

const FormOne = (props: FormOneProps) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  return (
    <>
    <div className={`${px('root')} ${className}`} {...otherProps} style={{
      border: '1px solid #f0f0f0',
      padding: '20px',
      marginLeft: 0
    }}>
    <Form form={form} className={px('form')} layout="vertical">
      <Form.Item label={intl.formatMessage({ id: 'brand' })} required>
        <Select style={{ width: '100%' }} value={'1'} options={[{ label: 'B.M.W', value: '1' }]} placeholder={intl.formatMessage({ id: 'please_select' })} />
      </Form.Item>
      <Form.Item label={intl.formatMessage({ id: 'style' })} required>
        <Select style={{ width: '100%' }} placeholder={intl.formatMessage({ id: 'please_select' })} />
      </Form.Item>
      <Form.Item label={intl.formatMessage({ id: 'style_year' })} required>
        <Input />
      </Form.Item>
      <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })} required>
        <Select style={{ width: '100%' }} value={'1'} options={[{ label: intl.formatMessage({ id: 'light_vehicles' }), value: '1' }]} placeholder={intl.formatMessage({ id: 'please_select' })} />
      </Form.Item>
    </Form>
    </div>
    </>
  );
};







export default FormOne;
