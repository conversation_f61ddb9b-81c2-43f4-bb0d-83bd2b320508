import React, { useMemo } from 'react';
import { Breadcrumb as AntdBreadcrumb } from 'antd';
import { useIntl } from 'umi';
import styles from './Breadcrumb.less';

interface BreadcrumbComProps {
  itemList?: {
    title: string;
  }[];
}

const Breadcrumb = ({ itemList }: BreadcrumbComProps) => {
  const intl = useIntl();
  const shouldRender = useMemo(() => Array.isArray(itemList) && itemList.length, [itemList]);
  return shouldRender ? (
    <div className={styles['web-breadCrumbRoot']}>
      <AntdBreadcrumb className={styles['web-breadCrumb']}>
        {itemList.map(item => (
          <AntdBreadcrumb.Item key={item.title}>
            {item.title && intl.formatMessage({ id: item.title })}
          </AntdBreadcrumb.Item>
        ))}
      </AntdBreadcrumb>
    </div>
  ) : null;
};

Breadcrumb.defaultProps = {
  itemList: [],
};

export default Breadcrumb;
