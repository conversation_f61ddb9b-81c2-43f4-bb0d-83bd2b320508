import React from 'react';
import classPrefix from '@ali-whale/class-prefix';
import { Descriptions, Divider } from 'antd';

import './EntranceInfo.less';
import { useIntl } from 'umi';

const px = classPrefix('web-entrance-info');

const EntranceInfo = () => {
  const intl = useIntl();
  return (
    <div className={px('entranceInfoRoot')}>
      <div className={px('formTitle')} style={{ marginLeft: '-16px', paddingBottom: '26px' }}>
        {intl.formatMessage({ id: 'import_license_information' })}
      </div>
      <Descriptions column={2}>
        <Descriptions.Item label={intl.formatMessage({ id: 'import_license_number' })}>
          1/2024
        </Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'dsf' })}>
          <span style={{ fontWeight: 700, color: '#13a07b' }}>豁免</span>
        </Descriptions.Item>
        <Descriptions.Item label="M/4編號">2024010100101</Descriptions.Item>
        <Descriptions.Item label="M/4參考編號1">1</Descriptions.Item>
        <Descriptions.Item label="M/4參考編號2">1234</Descriptions.Item>
        <Descriptions.Item label="M/4參考編號3">1/2024</Descriptions.Item>
        <Descriptions.Item label="免稅申請編號">豁免</Descriptions.Item>
        <Descriptions.Item label="免稅申請年份">2024</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'tax_exemption_start_date' })}>2024-02-24 11:20</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'tax_exemption_end_date' })}>2024-02-24 11:20</Descriptions.Item>
        <Descriptions.Item label="免稅法律依據">Art4-1a</Descriptions.Item>
        <Descriptions.Item label="到岸日期">2024-02-24 11:20</Descriptions.Item>
        <Descriptions.Item label="免稅申請人中文名">陳大文</Descriptions.Item>
        <Descriptions.Item label="免稅申請人葡文名">CHAN DA WEN</Descriptions.Item>
      </Descriptions>
      <Divider />
      <Descriptions column={2}>
        <Descriptions.Item label={intl.formatMessage({ id: 'engine_no' })}>
          Engine 1
        </Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
          Engine 2
        </Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
          Engine 3
        </Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
          1
        </Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'vin' })}>VIN1234</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
          2024
        </Descriptions.Item>
        <Descriptions.Item label="進口者中文名">豁免</Descriptions.Item>
        <Descriptions.Item label="進口者葡文名">2024</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'importer_chinese_name' })}>2024-02-24 11:20</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'importer_portuguese_name' })}>2024-02-24 11:20</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'environmental_protection_category' })}>Art4-1a</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'environmental_status' })}>2024-02-24 11:20</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'start_date_environmental_protection' })}>陳大文</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'end_date_environmental_protection' })}>CHAN DA WEN</Descriptions.Item>
      </Descriptions>
      <Divider style={{ marginLeft: '-16px' }} />
      <div className={px('formTitle')} style={{ marginLeft: '-16px', paddingBottom: '26px' }}>
        {intl.formatMessage({ id: 'model_approval_documents' })}
      </div>
      <Descriptions column={2}>
        <Descriptions.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
          {intl.formatMessage({ id: 'light_vehicles' })}
        </Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
          載客
        </Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'brand' })}>特斯拉</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'style' })}>Model X</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'style_year' })}>2024</Descriptions.Item>
        <Descriptions.Item label={intl.formatMessage({ id: 'vehicle_origin' })}>美國</Descriptions.Item>
      </Descriptions>
      <Divider />
    </div>
  );
};
export default EntranceInfo;
