.cbd-mobile-base-form-text {
  // 多行文本框
  .am-list-item.am-textarea-item {
    padding: 12 * @hd !important;
  }
  .am-textarea-control {
    padding: 0;
  }
  .am-textarea-clear {
    margin-top: 0;
  }
  .am-input-disabled {
    background-color: #fafafa;
  }
  .am-list-item {
    .am-input-control input,
    .am-textarea-control textarea {
      font-size: 16 * @hd;
      color: #595959;
    }
    .am-input-control input {
      // 文字过长的省略号
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      // line-height: normal;
      &:disabled {
        background-color: transparent;
        color: @font-color-base;
      }
    }
  }
  .cbd-mobile-base-form-text-describe {
    color: #bfbfbf;
    font-size: 12 * @hd;
  }
}

.am-list-item.am-input-item {
  padding: 10 * @hd 0 10 * @hd 12 * @hd;
  height: 44 * @hd;
}

.am-list-item .am-input-control input,
.am-list-item .am-textarea-control textarea {
  font-size: 16 * @hd;
  color: #595959;
}
