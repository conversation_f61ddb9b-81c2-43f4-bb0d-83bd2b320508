import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Form2.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Collapse } from 'antd';

const px = pc('form2-33d-mobile');
const { Panel } = Collapse;
export interface Form2Props
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;
}

const Form2 = (props: Form2Props) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep } = otherProps;

  const [buyStatus, setBuyStatus] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const intl = useIntl();
  const [form] = Form.useForm();

  //使用购买车牌
  function buyCard() {
    setIsModalOpen(true);
  }
  function onSerach() {}

  function handleOk() {
    setBuyStatus(!buyStatus);
    setIsModalOpen(false);
  }
  function handleCancel() {
    setIsModalOpen(false);
  }
  useEffect(() => {
    setDisablePrevStep(false);
    setDisableNextStep(false);
    return () => {};
  }, []);

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'application_materials' })}</div>
      <div className={px('sectionBody')}>
        <div>
          <Form form={form} className={px('form')} layout="vertical">
            <Form.Item label={intl.formatMessage({ id: 'license_plate_number' })}>
              <div>EX-123</div>
            </Form.Item>
            <Form.Item label={intl.formatMessage({ id: 'ex_license_plate_validity' })}>
              <div>2024-11-30 ~ 2024-12-30</div>
            </Form.Item>
            <div style={{ width: '100%' }}>
              <Form.Item label={intl.formatMessage({ id: 'apply_date_last' })}>
                <div>2024-12-30</div>
              </Form.Item>
            </div>
            {/* <Form.Item label={intl.formatMessage({ id: 'th_license_plate_number' })}>
              <div>MA-12-34</div>
            </Form.Item> */}

            <Form.Item label={intl.formatMessage({ id: 'license_plate_lottery_status' })}>
              <div>已抽籤</div>
            </Form.Item>

            <Form.Item label={intl.formatMessage({ id: 'lottery_result' })}>
              <div>MA-12-34</div>
            </Form.Item>

            {buyStatus && (
              <Form.Item
                label={intl.formatMessage({ id: 'payment_after_buy' })}
                style={{ width: '100%' }}
              >
                <div>MA-99-99</div>
              </Form.Item>
            )}

            {/* <div style={{ width: '48%', marginBottom: '20px' }}><Button onClick={() => { history.push('/web/vem/0028F/vil-form') }} type='default'>修改車牌使用方式</Button></div> */}

            {/* <div style={{width:'48%',marginBottom:'20px'}}><Button onClick={()=>{history.push('/web/vem/0028F/vil-form?step=3')}} type='default'>{intl.formatMessage({ id: 'update_vehicle_information' })}</Button></div> */}
            <div style={{ width: '48%', marginBottom: '20px' }}>
              <Button
                onClick={() => {
                  buyCard();
                }}
                type="primary"
              >
                {!buyStatus ? '' : intl.formatMessage({ id: 'cancel' })}
                {intl.formatMessage({ id: 'use_license_plates' })}
              </Button>
            </div>
          </Form>
        </div>

        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
          <Collapse.Panel
            showArrow={false}
            header={<>{intl.formatMessage({ id: 'car_owner_information' })}</>}
            key={1}
          >
            <div>
              <p className="form-title">
                {intl.formatMessage({ id: 'car_owner_information' })} 1：
              </p>
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                  <div>澳門居民身份證</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'id_number' })}>
                  <div>12345678</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                  <div>陳大文</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                  <div>CHAN DA WEN</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'contact_address' })}>
                  <div>澳門xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'language' })}>
                  <div>中文</div>
                </Form.Item>
                <Form.Item label="">
                  {/* {posted ? (<span className='statusBg1'>{intl.formatMessage({ id: 'car_owner_identification_confirmation' })}</span>) : (<Button onClick={() => { setPosted(true) }} type="primary">{intl.formatMessage({ id: 'send_car_owner_identification_confirmation' })}</Button>)} */}
                </Form.Item>
              </Form>
            </div>

            <div>
              <p className="form-title">
                {intl.formatMessage({ id: 'car_owner_information' })} 2：
              </p>
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                  <div>澳門居民身份證</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'id_number' })}>
                  <div>12345678</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                  <div>陳大文</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                  <div>CHAN DA WEN</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'contact_address' })}>
                  <div>澳門xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'language' })}>
                  <div>中文</div>
                </Form.Item>
                <Form.Item label="">
                  <span className="statusBg2">{intl.formatMessage({ id: 'the_car_owner_identified' })}</span>
                </Form.Item>
              </Form>
            </div>
          </Collapse.Panel>
        </Collapse>

        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
          <Panel
            showArrow={false}
            header={<>{intl.formatMessage({ id: 'vehicle_information' })}</>}
            key={1}
          >
            {
              <div>
                <Form form={form} className={px('form')} layout="vertical">
                  <Form.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                    <div>I/1/2024</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                    <div>1/2024</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                    <div>{intl.formatMessage({ id: 'light_vehicles' })}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                    <div>私人</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                    <div>特斯特</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'style' })}>
                    <div>Model X</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'color' })}>
                    <div>白色</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                    <div>2024</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                    <div>載客</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                    <div>VIN1234</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                    <div>VIN1234</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'seating_apacity' })}>
                    <div></div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'tyre_f_r_s' })}>
                    <div>235/45-18 - 235/45-18 -</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'frontTyreQty' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'rearTyreQty' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'frontAxleQty' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'rearAxleQty' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehWeight' })}>
                    <div>1800 kg</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehGrossWeight' })}>
                    <div>xxx kg</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehLoadWeight' })}>
                    <div>xxx kg</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'car_size' })}>
                    <div>
                      ({intl.formatMessage({ id: 'length' })})5600 mm x (
                      {intl.formatMessage({ id: 'width' })})1000 mm x (
                      {intl.formatMessage({ id: 'height' })})1800 mm
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'cabinTypeDescCn' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_size' })}>
                    <div>
                      ({intl.formatMessage({ id: 'length' })})5600 mm x (
                      {intl.formatMessage({ id: 'width' })})1000 mm x (
                      {intl.formatMessage({ id: 'height' })})1800 mm
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehSourceCtryDescCn' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'maxPowerWithUnit' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'cylinderQty' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'cylinderVol' })}>
                    <div></div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'fuelTypeDescCn' })}>
                    <div></div>
                  </Form.Item>
                </Form>
              </div>
              /* <div>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'temporary_bidding_license_plates' })} style={{width: '100%'}}>
                <div>MA-12-34</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                <div>I/1/2024</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                <div>1/2024</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                <div>{intl.formatMessage({ id: 'light_vehicles' })}</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                <div>載客</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'dsf_ref_no' })}>
                <div>xxxxxxx</div>
              </Form.Item>
              <Form.Item label="IVM">
                <div>是</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                <div>特斯特</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'style' })}>
                <div>Model X</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'style_year' })}>
                <div>2023</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                <div>私人</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                <div>VIN1234</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                <div>Engine1234</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                <div>2024</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'ma_tyre_f_r_s' })}>
                <div>235/45-18 - 235/45-18 -</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'color' })}>
                <div>白色</div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'size_weight' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'car_body' })} style={{width:'100%'}} >
                <div style={{display:'flex'}}>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'length' })} 5600 mm</div>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'width' })} 1000 mm</div>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'height' })} 1800 mm</div>
                </div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'weight' })} style={{width:'33.3%'}} >
                <div>1800 kg</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehGrossWeight' })} style={{width:'33.3%'}} >
                <div>xxx kg</div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehLoadWeight' })} style={{width:'33.3%'}} >
                <div>xxx kg</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'max_weight' })} style={{width:'100%'}} >
                <div>xxx kg</div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'car_carriage' })} style={{width:'100%'}} >
                <div style={{display:'flex'}}>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'length' })} 5600 mm</div>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'width' })} 1000 mm</div>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'height' })} 1800 mm</div>
                </div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'side_card' })} style={{width:'100%'}} >
                <div style={{display:'flex'}}>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'length' })} 5600 mm</div>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'width' })} 1000 mm</div>
                <div style={{width:'33.3%'}}>{intl.formatMessage({ id: 'height' })} 1800 mm</div>
                </div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'side_card_desc' })} style={{width:'100%'}} >
                <div></div>
              </Form.Item>

            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'feature' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'vehicle_origin' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehSourceCtryDescCn' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'via' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'cabinTypeDescCn' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'frontTyreQty' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'rearTyreQty' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'car_feature' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'car_doors_number' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'passenger_pedal_type' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'steering_wheel_position' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'armrest_position' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'emergency_exit' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'seating_apacity' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'tour_guide_position' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'standing_position' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'disability_position' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'single_seat_size' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'independent_seat_size' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'motor' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'transmission_position' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'measure_revolutions' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'air_values' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'maxPowerWithUnit' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'cylinderQty' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'max_torque' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'caliber' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'stroke' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'stroke_2' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'obd' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'emission_report' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'exhaust_emission_regulations' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'regulations_exhaust_emissions' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'approval_date_exhaust_gas' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'standard_diagnostic_system' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'gasoline_direct_injection_engine' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'nox' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'emission_testing_program' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'co' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'thc' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'nmhc' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'noxx' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'pm' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'pn' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'thc_nox' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'n20' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>WHTC(JE05M)</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'co' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'thc' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'nmhc' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'noxx' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'pm' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'pn' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'thc_nox' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'n20' })} >
                <div></div>
              </Form.Item>
              <div style={{width:'100%'}}><hr style={{
                marginTop: '20px',
                marginBottom: '20px',
                border: 0,
                borderTop: '1px solid #eee'
              }} /></div>
              <Form.Item label={intl.formatMessage({ id: 'nmhc' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'co' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'noxx' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'no_pm' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'elr' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'testing_quality' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'fuel' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'fuel_efficiency_testing_procedure' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'fuelTypeDescCn' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'plug_in_type' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'oil_supply_method' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'air_intake_method' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'fuel_tank_capacity' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'fuel_efficiency' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'noise' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'vehicle_weight' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'transmission' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'transmission_type' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'number_gear_shifts' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'types_transmission' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'equipped_function' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'suspension' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'number_front_suspension_systems' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'number_end_suspension_systems' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'steel_plate_spring_suspension' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'front_suspension_system' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'end_suspension_system' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'parking_axle' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'other_braking_system_functions' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>其他</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'lamp' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'vehicle_transmission_system' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'vehicle_headlights' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'front_windshield' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'front_right_window' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'front_left_window' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'rear_windshield' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'rear_right_window' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'rear_left_window' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'frontAxleQty' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'rearAxleQty' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'battery_specifications' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div>

            <div>
            <p className='form-title2'>{intl.formatMessage({ id: 'environmental_status' })}</p>
            <Form form={form} className={px('form')} layout="vertical">
              <Form.Item label={intl.formatMessage({ id: 'environmental_regulations' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'environmental_protection_category' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'start_date_environmental_protection' })} >
                <div></div>
              </Form.Item>
              <Form.Item label={intl.formatMessage({ id: 'end_date_environmental_protection' })} >
                <div></div>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'environmental_status' })} >
                <div></div>
              </Form.Item>
            </Form>
            </div> */
            }
          </Panel>
        </Collapse>

        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
          <Collapse.Panel
            showArrow={false}
            header={<>{intl.formatMessage({ id: 'importer_information' })}</>}
            key={1}
          >
            <div>
              <Form form={form} className={px('form')} layout="vertical">
              
                <Form.Item label={intl.formatMessage({ id: 'importer_chinese_name' })}>
                  <div>ABC有限公司</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'importer_portuguese_name' })}>
                </Form.Item>
              </Form>
            </div>
          </Collapse.Panel>
        </Collapse>

        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
          <Collapse.Panel
            showArrow={false}
            header={<>{intl.formatMessage({ id: 'agent_information' })}</>}
            key={1}
          >
            <div>
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'agent_type' })}>
                  <div></div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'agent_no' })}>
                  <div></div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'agent_chinese_name' })}>
                  <div></div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'agent_portuguese_name' })}>
                  <div></div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'agent_phone' })}>
                  <div></div>
                </Form.Item>
              </Form>
            </div>
          </Collapse.Panel>
        </Collapse>

        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
          <Collapse.Panel
            showArrow={false}
            header={<>{intl.formatMessage({ id: 'vehicle_contact_information' })}</>}
            key={1}
          >
            <div>
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'contact_chinese_name' })}>
                  <div>陳大文</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'contact_portuguese_name' })}>
                  <div>CHAN DA WEN</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'local_mobile_phone' })}>
                  <div>66668888</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'language' })}>
                  <div>中文</div>
                </Form.Item>
              </Form>
            </div>
          </Collapse.Panel>
        </Collapse>

        <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
          <Collapse.Panel showArrow={false} header={<>{intl.formatMessage({ id: 'vehicle_registration_phone' })}</>} key={1}>
            <div>
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'local_mobile_phone' })}>
                  <div>66668888</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'language' })}>
                  <div>中文</div>
                </Form.Item>
                {/* <Form.Item label={intl.formatMessage({ id: 'other_phone' })}>
                  <div></div>
                </Form.Item> */}
              </Form>
            </div>
          </Collapse.Panel>
        </Collapse>
      </div>
    </div>
  );
};

export default Form2;
