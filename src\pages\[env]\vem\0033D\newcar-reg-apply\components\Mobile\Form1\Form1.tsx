import React, { useEffect, useState } from 'react';
import { useIntl } from 'umi';
import pc from 'prefix-classnames';
import './Form1.less';

import type { FormProps } from 'antd';
import { Alert, Form, Input, Modal, Select } from 'antd';

import { getSysCodesByCodeType } from '@/services/publicApi';
import { createVehRegTxn } from '@/services/0033D';
import { getLangGroup, getResponseMessage } from '@/locales/lang';

export interface FormOneProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setShowTempStore: (boolean) => void;
  setShowPrevStep: (boolean) => void;

  contentForNextStep: (number) => void;

  setEntranceInfo: (data) => void;
  entranceInfo: any;

  setCreateEXTxnParams: (data: createEXTxnParams) => void;
  createEXTxnParams: createEXTxnParams | undefined;
  setTxnId: (data) => void;
  txnId: string | undefined;
}

const FormOne = (props: FormOneProps) => {
  const px = pc('form1-33d-mobile');
  const { className = '', ...otherProps } = props;
  const {
    contentNextStep,
    setDisableNextStep,
    setShowTempStore,
    setShowPrevStep,
    contentForNextStep,

    setTxnId,
    txnId,
  } = otherProps;

  const [tipMsg, setTipMsg] = useState('');

  const intl = useIntl();
  const [form] = Form.useForm();

  const [isModalOpen, setIsModalOpen] = useState(false);

  const [vehTypeOpts, setVehTypeOpts] = useState<sysCodesByCodeTypeOpt[]>([]);

  // 表单数据
  const [vehType, setVehType] = useState('');
  const [exNo, setExNo] = useState('');
  const [vinPart, setVinPart] = useState('');

  const onFinish: FormProps<createVehRegTxnParams>['onFinish'] = async (values) => {
    setTipMsg('');

    if (txnId) return;

    let params: createVehRegTxnParams = values;

    console.log(params);
    const ret = await createVehRegTxn(params).catch((e) => setTipMsg(e.message));
    if (!ret) {
      return;
    }

    if (ret.code === '0') {
      setTxnId(ret.data.txnId);
      contentForNextStep(2);
    } else {
      setTipMsg(getResponseMessage(ret));
    }
  };

  const onFinishFailed: FormProps<createVehRegTxnParams>['onFinishFailed'] = (errorInfo) => {
    console.log('onFinishFailed', errorInfo);
  };

  useEffect(() => {
    if (vehType.length && exNo.length && vinPart.length === 4) {
      setDisableNextStep(false);
    } else {
      setDisableNextStep(true);
    }
  }, [vehType, exNo, vinPart]);

  // const showModal = () => {
  //   setIsModalOpen(true);
  // };
  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    window.location.href = '/web/index?tabIndex=2';
    setIsModalOpen(false);
  };
  // const search = (e) => {
  //   if (e.target.value.length == 4) setDisableNextStep(false);
  //   else setDisableNextStep(true);
  // };

  useEffect(() => {
    getSysCodesByCodeType('31201', 'A')
      .then((ret) => {
        if (ret?.code === '0') {
          setVehTypeOpts(ret.dataArr);
        }
      })
      .catch((e) => setTipMsg(e.message));

    contentForNextStep(1);

    setShowPrevStep(false);
    setShowTempStore(false);
    return () => {
      contentForNextStep(0);

      setShowPrevStep(true);
      setShowTempStore(true);

      setDisableNextStep(false);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      form.submit();
    }
  }, [contentNextStep]);

  return (
    <>
      {tipMsg && (
        <div style={{ padding: '0.2rem 0.24rem 0' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('sectionTitle')}>
          {intl.formatMessage({ id: 'car_license_information' })}
        </div>
        <Form
          form={form}
          className={px('form')}
          layout="vertical"
          preserve={false}
          scrollToFirstError={true}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          initialValues={{
            exNo: 'EX-',
          }}
        >
          <div className={px('sectionBody')}>
            <Form.Item
              label={intl.formatMessage({ id: 'vehicle_level' })}
              name="vehType"
              required
              rules={[{ required: true, message: intl.formatMessage({ id: 'please_select' }) }]}
            >
              <Select onChange={(v) => setVehType(v)}>
                {vehTypeOpts.map((item, index) => (
                  <Select.Option value={item.codeKey}>{getLangGroup(item.codeCname, item.codePname, item.codeEname)}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              label={intl.formatMessage({ id: 'ex_test_vehicle' })}
              name="exNo"
              required
              rules={[{ required: true, message: intl.formatMessage({ id: 'please_select' }) }]}
            >
              <Input value="" onChange={(e) => setExNo(e.target.value)} />
            </Form.Item>
            <Form.Item
              label={intl.formatMessage({ id: 'vin_4' })}
              name="vinPart"
              required
              rules={[{ required: true, message: intl.formatMessage({ id: 'please_select' }) }]}
            >
              <Input maxLength={4} onChange={(e) => setVinPart(e.target.value)} />
            </Form.Item>
          </div>
          <Modal
            title={intl.formatMessage({ id: 'warm_reminder' })}
            cancelText={intl.formatMessage({ id: 'exit' })}
            okText={intl.formatMessage({ id: 'continue' })}
            visible={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
          >
            <p>{intl.formatMessage({ id: 'application_notice' })}...</p>
          </Modal>
        </Form>
      </div>
    </>
  );
};

export default FormOne;
