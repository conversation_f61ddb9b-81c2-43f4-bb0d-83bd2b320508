import React, { useRef, useEffect } from 'react';
import { getImgByCode } from '@/services/publicApi';

export interface CodeImgProps extends React.HTMLAttributes<HTMLImageElement> {
  fileCode: string;
}

function CodeImg(props: CodeImgProps) {
  const { fileCode, className = '', ...otherProps } = props;
  const ref = useRef<HTMLImageElement>(null);

  useEffect(() => {
    getImgByCode(fileCode).then((res) => {
      if (ref.current) {
        ref.current.src = URL.createObjectURL(res);
      }
    });
  }, []);

  return <img className={className} {...otherProps} ref={ref} />;
}

export default CodeImg;
