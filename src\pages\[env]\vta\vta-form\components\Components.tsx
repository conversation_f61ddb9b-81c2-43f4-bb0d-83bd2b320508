import React from 'react';
import pc from 'prefix-classnames';
import './Components.less';

const px = pc('vta-form-components');

export interface ComponentsProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {

}

const Components = (props: ComponentsProps) => {
  const { className = '', ...otherProps } = props;
  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
  );
};

export default Components;
