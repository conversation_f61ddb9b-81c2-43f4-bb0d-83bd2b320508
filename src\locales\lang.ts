// 公共配置文件
import { setLocale, history, getLocale } from 'umi';
import enUS from 'antd-mobile/lib/locale-provider/en_US';
import zhTW from '@/locales/antd/zh-TW';
import pt from '@/locales/antd/pt-PT';
import { setLang } from '@/utils/storage';
import { FetchResponse } from '@/utils/fetch';

/**
 * url、jsapi返回的enum为 zh-Hant zh-Hans en pt
 * 项目中多语言enum为 zh-MO zh-CN en-US pt-PT
 * setLocale的enum为 zh-MO zh-CN en-US/en pt-PT/pt (parseLanguage方法前缀匹配)
 */

export enum Lang {
  简体中文 = 'zh-CN',
  English = 'en-US',
  繁体中文 = 'zh-MO',
  葡语 = 'pt-PT',
}

export enum GovLang {
  简体中文 = 'zh-Hans',
  English = 'en',
  繁体中文 = 'zh-Hant',
  葡语 = 'pt',
}

// 和native的映射
export const langMap: Record<string, string> = {
  'zh-CN': Lang.简体中文,
  'en-US': Lang.English,
  'zh-MO': Lang.繁体中文,
  'pt-PT': Lang.葡语,
};

export const msg: Record<string, unknown> = {
  [Lang.English]: enUS,
  [Lang.简体中文]: undefined,
  [Lang.葡语]: pt,
  [Lang.繁体中文]: zhTW,
};

export const langText: Record<string, string> = {
  'zh-Hans': '简体',
  en: 'English',
  'zh-Hant': '繁體',
  pt: 'Português',
};

/**
 * zh-Hant, zh-Hans
 */
export const MacaoGovLang = {
  'zh-Hant': 'zh-MO',
  'zh-Hans': 'zh-CN',
  pt: 'pt-PT',
  en: 'en-US',
};

/**
 * url中language字段(App返回的语言) 转换为 @gov-mo/components 组件库导出的Locale文件名
 */
export const getLocale2ComponentLocaleMap = {
  'zh-MO': 'zhHant',
  'zh-CN': 'zhHans',
  'pt-PT': 'pt',
  'en-US': 'en',
};

/**
 * 获取url上的language字段
 * @return {'zh-Hant'|"zh-Hans"|"en"|'pt'} language
 */
export const getLanguageFromUrl = () => {
  const language = history?.location?.query?.language ? decodeURIComponent(history?.location?.query?.language as string) : undefined
  return language?.includes(',') ? language?.substring(0, language?.indexOf(',')) : language as string | null | undefined;
};

export const transform2MoFormat = (locale: string) => (MacaoGovLang[locale] as string) || locale;

/**
 * 根据MacaoGovLang的value获取key
 */
export const transform2HantFormat = (v: string) => {
  for (const key in MacaoGovLang) {
    if (Object.prototype.hasOwnProperty.call(MacaoGovLang, key)) {
      const element = MacaoGovLang[key];
      if (element === v) {
        return key;
      }
    }
  }
  return v;
};

// //////////////////////////////////////

// 和 antd 对应
let lang: Lang = Lang.繁体中文;

export const getLang = function () {
  return lang;
};

export const updatelocale = function (l: string) {
  const changeLang = MacaoGovLang[l] as string;
  const antdLang = (langMap[changeLang] || lang) as Lang;
  // 更新自定义文案
  setLocale(antdLang, false);
  // 更新antd mobile 文案
  lang = antdLang;
  setLang(antdLang);
};

export const langGroup = (mo: string | undefined, pt?: string, en?: string) => {
  const defaultLang = mo || ''

  return {
    [Lang.繁体中文]: defaultLang,
    [Lang.葡语]: pt || '',
    [Lang.English]: en || '',
    '': defaultLang,
    undefined: defaultLang
  }
}

export const getLangGroup = (mo: string | undefined, pt?: string, en?: string): string  => {
  return langGroup(mo, pt, en)[getLocale()] || mo || ''
}

export const getResponseMessage = (res?: FetchResponse<any>): string => {
  const defaultValue = res?.externalMessageCn || res?.code || ''
  return {
    [Lang.繁体中文]: defaultValue,
    [Lang.葡语]: res?.externalMessagePt || res?.code || '',
    [Lang.English]: res?.externalMessageEn || res?.code || '',
    '': defaultValue,
    undefined: defaultValue
  }[getLocale()] || defaultValue
}

export const getLocaleMap = {
  'zh-MO': 'zh-Hant',
  'pt-PT': 'pt',
  'en-US': 'en',
};