import React from 'react';

function renderUncommonWord(text) {
  return <span className="mocaofont">{text}</span>;
}

const getNum = (tele: string): string => {
  if (tele === null || tele === undefined) return '';
  const reg = /^\+(86|853|852|886)/;
  if (reg.exec(tele)) {
    return tele.replace(reg, '($1)');
  }
  return `(853)${tele}`;
};

export const infoList: {
  key:
    | 'applicantLastnameZh'
    | 'applicantNameZh'
    | 'applicantLastnameFl'
    | 'applicantNameFl'
    | 'applicantCertTypeDesc'
    | 'applicantCertNo'
    | 'informLanguage'
    | 'mobilePhone'
    | 'acceptInformType';
  text: string;
  render?: (v) => React.ReactNode;
  renderKey?: (v, record) => React.ReactNode;
}[][] = [
  [
    {
      key: 'applicantLastnameZh',
      text: '姓',
      render: renderUncommonWord,
    },
    {
      key: 'applicantLastnameFl',
      text: ' ',
      renderKey: (text, record) => (record.applicantLastnameZh ? ' ' : '姓'),
    },
    {
      key: 'applicantNameZh',
      text: '名',
      render: renderUncommonWord,
    },
    {
      key: 'applicantNameFl',
      text: ' ',
      renderKey: (text, record) => (record.applicantNameZh ? ' ' : '名'),
    },
  ],
  [
    {
      key: 'applicantCertTypeDesc',
      text: '證件類型',
    },
    {
      key: 'applicantCertNo',
      text: '證件號碼',
    },
    // {
    //   key: 'informLanguage',
    //   text: '通知語言',
    // },
    {
      key: 'mobilePhone',
      text: '流動電話號碼',
      render: getNum,
    },
    // {
    //   key: 'acceptInformType',
    //   text: '接收訊息方式',
    // },
  ],
];
