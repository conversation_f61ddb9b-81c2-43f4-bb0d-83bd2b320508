@prefix: uploadFile-28f-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 0.2rem 0.12rem 0.4rem;
      padding-bottom: 0.2rem;
      background-color: #ffffff;
      font-size: 0.16rem;
      border-radius: 0.12rem;
      overflow: hidden;
      

      .ant-form {
        font-size: 0.16rem;
      }
      .ant-form-item-label > label {
        font-size: 0.16rem;
        color: #6c6c6c;
        flex-direction: row-reverse;
        display: flex;
        justify-content: flex-end;
      }
      .ant-select {
        font-size: 0.16rem;
      }
      .ant-radio-wrapper {
        font-size: 0.16rem;
      }
      .ant-input {
        font-size: 0.16rem;
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 700;
      padding: 0.05rem 0.2rem;
      min-height: 0.41rem;
      font-size: 0.18rem;
      color: #ffffff;
      background: @brand-primary;
      word-break: break-all;
    }
    &sectionBody {
      padding: 0.2rem 0.12rem;
    }
  }
}

.upload-box{
  border: 1px solid #d9d9d9;
  position: relative;
  vertical-align: top;
  width: 20.667vw;
  height: 20.667vw;
  border-radius: 1.6vw;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}