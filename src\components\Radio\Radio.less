.radioWrapper {
  display: inline-flex;
  align-items: center;
  .radio {
    width: 36px !important;
    height: 36px !important;
    appearance: none;
    outline: none !important;
    &::before {
      content: "";
      width: 36px;
      height: 36px;
      border: 1PX solid #CACACA;
      background-color: #fff;
      display: inline-block;
      border-radius: 50%;
    }
    &:checked::before {
      content: "";
      width: 36px;
      height: 36px;
      display: inline-block;
      border-radius: 50%;
      background-color: #fff;
      border: 10px solid #3eabdc;
    }
  }
  .label{
    margin-left: 16px;
    font-size: 28px;
    line-height: 1.5;
    color: #262626;
  }
}
