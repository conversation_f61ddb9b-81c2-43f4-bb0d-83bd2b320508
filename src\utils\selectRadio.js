/**
 * 获取已设置联动的组件
 * @param {object} formStructureData 表单结构数据
 * @return {Array} 已设置联动的组件集
 */
export const getCascaderComs = (formStructureData) => {
  const components = [];
  formStructureData.forEach((row) => {
    row.cols.forEach((com) => {
      if (com?.data?.options?.[0]?.showComp) {
        components.push(com);
      }
    });
  });
  return components;
};

/**
 * 获取选项对应的联动组件
 * @param {array} options 选项
 * @param {*} v 值
 * @return {string[]} 联动组件key
 */
function getOptionsShowComp(options, value) {
  if (!Array.isArray(value)) {
    return options.find(({ value: v }) => v === value)?.showComp ?? [];
  }
  const showComp = [];
  value.forEach((v) => {
    Array.prototype.push.apply(showComp, getOptionsShowComp(options, v));
  });
  return showComp;
}

/**
 * 处理联动状态
 * @param {objet} itemData 表单项props
 * @param {string} id 操作源-表单项key
 * @param {stirng[]} showComp 联动组件key集合
 */
function transformFormItemData(itemData, id, showComp) {
  const { group, rows = [], cols = [] } = itemData;
  if (!group) {
    return {
      ...itemData,
      cols: cols.map((colsItem) => {
        if (!colsItem) {
          return colsItem;
        }
        const { key, data: colsItemData = {}} = colsItem;
        if (key) {
          /* 展示当前表单项 */
          // eslint-disable-next-line eqeqeq
          if (key == id) {
            return {
              ...colsItem,
              data: { ...colsItemData, hide: false },
            };
          }

          /* 其他表单项根据showComp控制显示/隐藏状态 */
          // eslint-disable-next-line eqeqeq
          const hide = !showComp.some(comp => comp == key);
          return {
            ...colsItem,
            data: { ...colsItemData, hide },
          };
        }
        return colsItem;
      }),
    };
  }
  return {
    ...itemData,
    rows: rows.map(({ cols: rowCols = [] } = {}) => transformFormItemData(rowCols, id, showComp)),
  };
}

/**
 * 解析value
 * undefined -> undefined
 * { value: string, label: label} -> string
 * [{ value: string, label: label}] -> [string]
 */
export const parseValue = (v) => {
  switch (Object.prototype.toString.call(v)) {
    case '[object Array]':
      return v.map(parseValue);
    case '[object Number]':
    case '[object String]':
      return v;
    default:
      return v?.value;
  }
};

/**
 * 处理联动显示/隐藏状态
 * @param {object} obj
 * @param {object} obj.compProps 表单项props
 * @param {string} obj.compProps.id 表单项key
 * @param {array} obj.compProps.options 选项
 * @param {object} value 值
 * @param {function} setFormStructureJson 变更表单结构json的方法
 */
export const showHideComps = ({
  compProps: { id, options } = {},
  value,
  setFormStructureJson = () => undefined,
}) => {
  const showComp = getOptionsShowComp(options, parseValue(value));
  if (!showComp?.length) {
    return;
  }
  setFormStructureJson(propFormData =>
    propFormData.map(item => transformFormItemData(item, id, showComp)),
  );
};
