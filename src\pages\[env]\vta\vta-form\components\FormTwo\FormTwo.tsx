import React from 'react';
import pc from 'prefix-classnames';
import './FormTwo.less';

import { useIntl } from 'umi';
import { Row, Button, Form, Select, Input, Col } from 'antd';

const px = pc('form-two');

export interface FormTwoProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {

}

const FormTwo = (props: FormTwoProps) => {
  const { className = '', ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  return (
    <>
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
    </>
  );
};

export default FormTwo;
