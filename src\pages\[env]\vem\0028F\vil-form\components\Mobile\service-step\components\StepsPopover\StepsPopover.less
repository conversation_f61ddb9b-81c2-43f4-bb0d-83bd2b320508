.popoverTitle {
  height: 48px;
  line-height: 48px;
  border-radius: 30px;
  padding: 0 12px;
  border: 1px solid var(--borderColor);
  color: var(--thirdTextColor);
  flex-shrink: 0;
  font-size: 28px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.root {
  max-width: 174 * @hd;
  padding: 24px * @hd 16px * @hd 28px * @hd;
  .stepItem {
    display: flex;
    .stepNumber {
      flex: 0 0 auto;
      width: 24px * @hd;
      height: 24px * @hd;
      border-radius: 12px * @hd;
      background: var(--step-number);
      color: #fff;
      line-height: 24px * @hd;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .content {
      margin-left: 16px;
      height: 42px;
      line-height: 42px;
      // margin-top: 6px;
      // color: #bfbfbf;
      color: var(--step-number);
      .twoRowEllipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .before {
      color: @brand-success;
    }
    .active {
      color: #232323;
    }
  }
  .stepItemActive {
    .stepNumber {
      background-color: var(--primaryColor);
    }
    .content {
      color: var(--primaryColor);
    }
  }
  .stepItemFocus {
    .stepNumber {
      background-color: var(--step-focuse);
    }
    .content {
      color: var(--step-focuse);
    }
  }
  .tail {
    height: 42px;
    margin: 5px 0;
    background: #d9d9d9;
    width: 2px;
    margin-left: 12 * @hd;
  }
  .tailActive {
    background: @brand-success;
  }
}
