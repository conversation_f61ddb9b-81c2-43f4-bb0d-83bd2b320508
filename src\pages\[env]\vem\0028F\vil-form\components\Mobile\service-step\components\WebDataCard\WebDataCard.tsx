import React from 'react';
import classPrefix from '@ali-whale/class-prefix';
import './WebDataCard.less';

const px = classPrefix('web-card');

interface IWebDataCard extends React.HTMLAttributes<HTMLDivElement> {
  cardTitle: string | React.ReactNode;
  cardTitleClassName?: string;
  cardBodyClassName?: string;
}

function WebDataCard(props: IWebDataCard) {
  const {
    cardTitle,
    children,
    className = '',
    cardTitleClassName = '',
    cardBodyClassName = '',
  } = props;
  return (
    <div className={`${px('root')} ${className}`}>
      <div className={`${px('title')} ${cardTitleClassName}`}>{cardTitle}</div>
      <div className={`${px('body')} ${cardBodyClassName}`}>{children}</div>
    </div>
  );
}

WebDataCard.defaultProps = {
  cardTitleClassName: '',
  cardBodyClassName: '',
};

export default WebDataCard;
