import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './Preview.less';

import { useIntl } from 'umi';
import { Checkbox, Form } from 'antd';
import { getVehRegTaxTxnData } from '@/services/0033D-ii';
import { validateTxnIsNeedToPay } from '@/services/0028F';
import { getLangGroup } from '@/locales/lang';

const px = pc('preview-33d-ii-mobile');

export interface PreviewProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;
  setNextStepText: (boolean) => void;

  nextStep: () => void;

  setShowTempStore: (boolean) => void;
  setShowPrevStep: (boolean) => void;
  txnId: string;
  onSpeedUp: (isSpeedUp: string) => void;
  onIsNeedPay: (isNeedPay: boolean) => void;

  initStepOptions: StepOptionsItem[];
  setStepOptions: (opt: StepOptionsItem[]) => void;
}

const Preview = (props: PreviewProps) => {
  const { className = '', txnId, ...otherProps } = props;
  const {
    setDisableNextStep,
    setNextStepText,
    setShowPrevStep,
    setShowTempStore,
    onSpeedUp,
    onIsNeedPay,
    setStepOptions,
    initStepOptions,
  } = otherProps;

  const [vehRegTaxTxnData, setVehRegTaxTxnData] = useState<VehRegTaxTxnResType>(
    {} as VehRegTaxTxnResType,
  );
  const [speedUp, setSpeedUp] = useState<string>('N');

  const intl = useIntl();
  const [form] = Form.useForm();

  const [isNeedPay, setIsNeedPay] = useState<boolean>(true);
  const [fetchIsNeedPay, setFetchIsNeedPay] = useState<boolean>(true);
  const handleIsNeedPay = (state: boolean) => {
    setIsNeedPay(state);
    onIsNeedPay(state);
  };

  const fetchValidateTxnIsNeedToPay = async () => {
    if (!txnId) return;
    const res = await validateTxnIsNeedToPay(txnId);
    if (!res || res.code !== '0') {
      return;
    }

    setFetchIsNeedPay(res.data?.isNeedPay);
    if (speedUp === 'N') {
      handleIsNeedPay(res.data?.isNeedPay);
    } else {
      handleIsNeedPay(true);
    }
  };
  useEffect(() => {
    // 不需要支付
    if (!isNeedPay) {
      // 修改下一步按钮文案和步骤
      setNextStepText(intl.formatMessage({ id: 'submit_application' }));
      setShowTempStore(false);
      setStepOptions([...initStepOptions.filter((item) => item.sort !== 2)]);
    } else {
      // 修改下一步按钮文案和步骤
      setNextStepText(intl.formatMessage({ id: 'next' }));
      setShowTempStore(true);
      setStepOptions([...initStepOptions]);
    }
  }, [isNeedPay]);

  useEffect(() => {
    const fetchVehRegTaxTxnData = async () => {
      if (!txnId) return;
      const res = await getVehRegTaxTxnData(txnId);

      if (res && res.code === '0') {
        const { data } = res;
        setVehRegTaxTxnData(data ?? {});
        setSpeedUp(data?.speedUp ?? 'N');
        onSpeedUp(data?.speedUp ?? 'N');
        if (data?.txnStatus === 'F') {
          setDisableNextStep(true);
          setShowTempStore(false);
        }

        await fetchValidateTxnIsNeedToPay();
      }
    };
    fetchVehRegTaxTxnData();
  }, [txnId]);

  const handleSpeedUp = (value: boolean) => {
    if (value) {
      handleIsNeedPay(true);
      return;
    }
    handleIsNeedPay(fetchIsNeedPay);
  };

  useEffect(() => {
    setNextStepText(intl.formatMessage({ id: 'next' }));
    setShowPrevStep(false);
    return () => {
      setShowPrevStep(true);
    };
  }, []);

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      {/* <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'data_confirmation' })}</div> */}
      <div className={px('sectionBody')}>
        <div className="my-card-container">
          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'vehicle_register_information' })}
            </div>
            <div className="my-card-body">
              <Form form={form} className="owner-card-form-33d-ii-mobile" layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'ex_license_plate_number' })}>
                  <div>{vehRegTaxTxnData?.exNo}</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                  <div>
                    {getLangGroup(
                      vehRegTaxTxnData?.vehTypeDescCn,
                      vehRegTaxTxnData?.vehTypeDescPt,
                      vehRegTaxTxnData?.vehTypeDescEn,
                    )}
                  </div>
                </Form.Item>

                <Form.Item
                  label={intl.formatMessage({ id: 'temporary_license_plates' })}
                  style={{ width: '100%' }}
                >
                  <div>{vehRegTaxTxnData?.tempMountPlateNo}</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                  <div>{vehRegTaxTxnData?.vehBrandDescCn || vehRegTaxTxnData?.vehBrandDescPt}</div>
                </Form.Item>

                <Form.Item label={intl.formatMessage({ id: 'style' })}>
                  <div>{vehRegTaxTxnData?.vehModel}</div>
                </Form.Item>
                <Checkbox
                  className="preview-33d-ii-checkbox"
                  checked={speedUp === 'Y'}
                  onChange={(e) => {
                    handleSpeedUp(e.target.checked);
                    setSpeedUp(e.target.checked ? 'Y' : 'N');
                    onSpeedUp(e.target.checked ? 'Y' : 'N');
                  }}
                >
                  {intl.formatMessage({ id: 'accelerate_registration_documents' })}
                </Checkbox>
              </Form>
            </div>
          </div>
          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'car_owner_information' })}
            </div>
            <div className="owner-card-container-33d-ii-mobile">
              {vehRegTaxTxnData?.ownerDataList?.map((item, index) => {
                return (
                  <div className="owner-card-item-33d-ii-mobile" key={item.ownerId}>
                    <div className="owner-card-title">
                      {intl.formatMessage({ id: 'car_owner_information' })} {index + 1}：
                    </div>
                    <Form form={form} className="preview-card-form-33d-ii-mobile" layout="vertical">
                      <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                        <div>{item?.ownerCname}</div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                        <div>{item?.ownerPname}</div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                        <div>
                          {getLangGroup(
                            item.ownerIdentTypeCn,
                            item.ownerIdentTypePt,
                            item.ownerIdentTypeEn,
                          )}
                        </div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'id_number' })}>
                        <div>{item?.ownerIdentNo}</div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'contact_address' })}>
                        <div>{item.ownerAddrCn}</div>
                      </Form.Item>
                    </Form>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Preview;
