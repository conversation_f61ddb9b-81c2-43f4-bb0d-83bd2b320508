import React, { useRef, useState } from 'react';
import cn from 'classnames';
import { useClickAway } from 'ahooks';
import styles from './DraggableFloat.less';

interface Props {
  itemList: {
    title: string;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
  }[];
}

const DraggableFloat = ({ itemList = [] }: Props) => {
  const rootDomRef = useRef<any>(null);
  const listDomRef = useRef<any>(null);
  // 展开收缩状态
  const [isExpand, setIsExpand] = useState(false);

  const mineBtnPos = useRef({
    x: 0,
    // 注意：bottom 该值必须与DraggableFloat.less中.web-root的bottom一致，否则首次拖拽可能会错位。
    y: 300,
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 300,
  });

  /**
   * 靠右
   */
  const float2Right = () => {
    const $mineBtn = rootDomRef.current;
    mineBtnPos.current.x = 0; // 固定右吸附
    $mineBtn.style.right = '0px';
  };

  useClickAway(() => {
    isExpand &&
      setIsExpand(() => {
        float2Right();
        return false;
      });
  }, rootDomRef);

  /**
   * 展开/收起事件
   */
  const handleExpandChange = () => {
    setIsExpand((temp) => {
      const $mineBtn = rootDomRef.current;
      if (temp) {
        // 将要收缩
        float2Right();
      } else {
        // 将要展开
        $mineBtn.style.right = `${listDomRef.current.getBoundingClientRect().width}px`;
      }
      return !temp;
    });
  };

  const handleTouchStart = (e) => {
    if (isExpand) {
      return;
    }
    mineBtnPos.current.startX = e.touches[0].pageX;
    mineBtnPos.current.startY = e.touches[0].pageY;
  };

  const handleTouchEnd = (e) => {
    if (isExpand) {
      return;
    }
    mineBtnPos.current.y = mineBtnPos.current.endY;
    float2Right();
  };

  const handleTouchMove = (e) => {
    if (isExpand) {
      return;
    }
    const $mineBtn = rootDomRef.current;
    if (e.touches.length > 0) {
      // e.preventDefault();
      const offsetX = e.touches[0].pageX - mineBtnPos.current.startX;
      const offsetY = e.touches[0].pageY - mineBtnPos.current.startY;
      let x = mineBtnPos.current.x - offsetX;
      let y = mineBtnPos.current.y - offsetY; // check edge

      if (x + $mineBtn.offsetWidth > document.documentElement.offsetWidth) {
        x = document.documentElement.offsetWidth - $mineBtn.offsetWidth;
      }

      if (y + $mineBtn.offsetHeight > document.documentElement.offsetHeight - 80) {
        y = document.documentElement.offsetHeight - $mineBtn.offsetHeight - 80;
      }

      if (x < 0) {
        x = 0;
      }

      if (y < 0) {
        y = 0;
      }

      $mineBtn.style.right = `${x}px`;
      $mineBtn.style.bottom = `${y}px`;
      mineBtnPos.current.endX = x;
      mineBtnPos.current.endY = y;
    }
  };

  return (
    <div
      className={cn({
        [styles['web-root']]: true,
        [styles['web-root-isFold']]: !isExpand,
      })}
      ref={rootDomRef}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onTouchMove={handleTouchMove}
    >
      <div className={styles['web-icon']} onClick={handleExpandChange}>
        <img
          src={isExpand ? `/ovsap/svg/reverseIcon.svg` : `/ovsap/svg/icon.svg`}
          alt=""
          role="button"
          aria-label={isExpand ? '收起' : '展开'}
        />
      </div>
      <div className={styles['web-list']} ref={listDomRef}>
        {itemList.map(({ title, onClick }) => (
          <div role="button" className={styles['norem-list-item']} onClick={onClick} key={title}>
            {title}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DraggableFloat;
