import React from 'react';
import pc from 'prefix-classnames';
import './Success.less';

import { history, useIntl } from 'umi';
import { Col, Button } from 'antd';

const px = pc('success');

export interface FormTenProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {

}

const FormTen = (props: FormTenProps) => {
  const intl = useIntl();
  const { className = '', ...otherProps } = props;

  function gotoIndex() {
    history.push('/web/vem')
  }

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <div className={px('body')}>
        <div className={px('sectionTitle')}>
          <img style={{width:'50px',height:'auto'}} src="/ba/vil/correct-c.png" alt="" />
          <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
        </div>
        <div className={px('sectionBody')}>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'service_procedure_name' })}</div>
            <div className='value'>{intl.formatMessage({ id: 'vehicle_registration_application' })}</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'import_license_number' })}</div>
            <div className='value'>xxxxxx</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'invoice_number' })}</div>
            <div className='value'>TESTCU2024021419205772386</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'trading_time' })}</div>
            <div className='value'>2024-02-14 00:00:00</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'transaction_channel' })}</div>
            <div className='value'>政付通</div>
          </Col>

          <Col>
            <div className='label'>{intl.formatMessage({ id: 'payment_method' })}</div>
            <div className='value'>MPGS</div>
          </Col>
          {/* <Col>
            <div className='label'>收費項</div>
            <div className='value'>{intl.formatMessage({ id: 'light_vehicles_register' })}</div>
          </Col> */}
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'is_accelerating' })}</div>
            <div className='value'>{intl.formatMessage({ id: 'no' })}</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'total_transaction_amount' })}</div>
            <div className='value'>澳門元200.00</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'payment_status' })}</div>
            <div className='value'>支付成功</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'submitted_documents' })}</div>
            <div className='value'>第044-DV-試驗車牌申請表表格<br />進口准照副本 (表格 -- E聯)，須蓋上公司印章<br />申請表第048/DDAVC/DV號表格</div>
          </Col>
          <Col>
            <div className='label'>{intl.formatMessage({ id: 'application_status' })}</div>
            <div className='value'>申請成功</div>
          </Col>
        </div>
      </div>
      <div className={px('footer')}>
        <Button type="default" onClick={() => {gotoIndex()}}>{intl.formatMessage({ id: 'return' })}</Button>
        <Button type="default">{intl.formatMessage({ id: 'download_receipt' })}</Button>
        {/* <Button type="default">{intl.formatMessage({ id: 'download_ex_card' })}</Button> */}
      </div>
    </div>
  );
};

export default FormTen;
