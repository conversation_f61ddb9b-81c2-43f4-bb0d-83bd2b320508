import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Form3.less';

import { useIntl, useLocation } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Upload, message, Divider, Alert } from 'antd';
import type { RadioChangeEvent, UploadProps, UploadFile, UploadFileStatus, FormProps } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

import {
  getServiceDocs,
  serviceItemFileUpload,
  checkServiceItemFileUpload,
} from '@/services/0028F';
import { getResponseMessage } from '@/locales/lang';

const px = pc('form3-33d');

export interface Form3Props extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setDisablePrevStep: (boolean) => void;

  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void
  setDisableNextStep: (boolean) => void;

  txnId: string|undefined;
}

const Form3 = (props: Form3Props) => {
  const { className = '', ...otherProps } = props;
  const { setDisablePrevStep } = otherProps;
  const {
    step,
    contentNextStep,

    nextStep,
    contentForNextStep,
    setDisableNextStep,

    txnId,
  } = otherProps;

  const [tipMsg, setTipMsg] = useState('');

  const intl = useIntl();
  const [form] = Form.useForm();
  const location = useLocation()

  const [list, setList] = useState<getServiceDocsRes[]>([])
  const [uploadfileList, setUploadfileList] = useState<Record<string, UploadFile[]>>()
  const [fileList, setFileList] = useState<{[key:string]:File}>()

  const getUploadfileList = () => {
    return uploadfileList
  }

  const getFileList = () => {
    return fileList
  }


  const onFinish: FormProps['onFinish'] = async (values) => {
    setTipMsg('')

    if(!txnId) {
      return
    }

    const ret = await checkServiceItemFileUpload(txnId).catch(e => setTipMsg(e.message))
    if(!ret) {
      return
    }
    if(ret.code === '0') {
      contentForNextStep(2);
    } else {
      setTipMsg(getResponseMessage(ret));
    }

  }

  const onFinishFailed: FormProps['onFinishFailed'] = (errorInfo) => {
    console.log('onFinishFailed', errorInfo)
  }


  useEffect(() => {
    if(txnId) {
      getServiceDocs(txnId).then(ret => {
        if(ret.code === '0') {
          const { dataArr } = ret
          setList(dataArr)
        }
      }).catch(e => setTipMsg(e.message))
    }
  }, [txnId])


  useEffect(() => {
    contentForNextStep(1)

    if(location.query.txnId) {
      setDisablePrevStep(true)
    }
    return () => {
      contentForNextStep(0)

      if(location.query.txnId) {
        setDisablePrevStep(false)
      }
    }
  }, [])


  useEffect(() => {
    if(contentNextStep>0) {
      form.submit()
    }
  }, [contentNextStep])

  return (<>
    {tipMsg?<div style={{paddingLeft:'0.24rem',paddingBottom:'0.24rem'}}><Alert message={tipMsg} type="error" showIcon /></div>:<></>}
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <Form form={form} className={px('form')} layout="vertical"
        preserve={false}
        scrollToFirstError={true}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
      >
        {/* 支持png, jpeg, jpg, pdf格式上傳，文件大小不超過5MB */}
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'submit_size_type_tips' })}</div>
        <div className={px('sectionBody')}>
        {list.map((item, index) => (
            <Form.Item label={`${index+1}. ${item.nameZh}`} name={String(item.serviceDocumentID)}>
              <Upload
                fileList={uploadfileList?uploadfileList[String(item.serviceDocumentID)]:undefined}
                beforeUpload={(file)=>{
                  const uploadfileList = getUploadfileList()
                  const fileList = getFileList()

                  let data = {...uploadfileList}
                  data[String(item.serviceDocumentID)] = [{
                    uid: String(item.serviceDocumentID),
                    name: file.name,
                    status: 'uploading',
                  }]
                  setUploadfileList(data)

                  let files = {...fileList}
                  files[String(item.serviceDocumentID)] = file
                  setFileList(files)

                  return false
                }}
                onRemove={(file)=>{
                }}
                onChange={(e) => {
                  const uploadfileList = getUploadfileList()
                  const fileList = getFileList()
                  if('removed'===e.file.status) {
                    let data = {...uploadfileList}
                    data[e.file.uid] = []
                    setUploadfileList(data)

                    let files = {...fileList}
                    delete files[e.file.uid]
                    setFileList(files)
                    return
                  }

                  if(!txnId) {
                    return
                  }

                  const file = e.fileList[0]

                  if(!(fileList && fileList[file.uid])) {
                    return
                  }
                  const formData = new FormData()
                  formData.append('txnId', txnId)
                  formData.append('code', item.code??'')
                  formData.append('spServiceDocId', item.serviceDocumentID)
                  formData.append('nameZh', item.nameZh)
                  formData.append('namePt', item.namePt)


                  formData.append('file', fileList[file.uid])

                  serviceItemFileUpload({
                    txnId,
                    code: item.code,
                    spServiceDocId: item.serviceDocumentID,
                    nameZh: item.nameZh,
                    namePt: item.namePt
                  }, formData).then(ret => {
                    const uid = String(item.serviceDocumentID)
                    const uploadfileList = getUploadfileList()
                    let data = {...uploadfileList}

                    if(!(data[uid] && data[uid].length)) {
                      return
                    }
                    let f = data[uid][0]

                    if(ret.code !== '0') {
                      f.status = 'error'
                    } else {
                      f.status = 'done'
                      //f.url = ret.data.url
                    }
                    data[uid] = [f]
                    setUploadfileList(data)
                  }).catch(e => {
                    message.error(e.message)

                    const uid = String(item.serviceDocumentID)
                    const uploadfileList = getUploadfileList()
                    let data = {...uploadfileList}

                    if(!(data[uid] && data[uid].length)) {
                      return
                    }
                    let f = data[uid][0]

                    f.status = 'error'
                    data[uid] = [f]
                    setUploadfileList(data)
                  })
                }}
              >
                <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
              </Upload>
              <Divider />
            </Form.Item>
          ))}
        </div>
      </Form>
    </div>
    </>
  );
};

export default Form3;
