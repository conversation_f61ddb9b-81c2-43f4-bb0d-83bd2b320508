@prefix: form4-28f-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 0.2rem 0.12rem;
      font-size: 0.16rem;
      .ant-descriptions-item-label {
        width: 50%;
        font-size: 0.16rem;
        &::after{
          display: none;
        }
      }
 
      .am-list-item.mo-input-mobile-middle
      {
        height: 35px !important;
      }
      
      .am-list-extra
      {
        font-size: 0.16rem !important;
      }
      .am-list-item.mo-input-mobile-middle input
      {
        font-size: 0.16rem;
       
      }
      .ant-form-item-label>label
      {
        font-size: 0.16rem;
      }
      .ant-descriptions-item-content {
        width: 50%;
        font-size: 0.16rem;
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 700;
      padding: 0.05rem 0.2rem;
      min-height: 0.41rem;
      font-size: 0.18rem;
      color: #ffffff;
      line-height: 0.21rem;
      background: @brand-primary;
      border-radius: 0.1rem 0.1rem 0 0;
    }
  }
}
