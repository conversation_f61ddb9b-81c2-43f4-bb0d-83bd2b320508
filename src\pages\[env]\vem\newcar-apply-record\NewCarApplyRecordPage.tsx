import { ExclamationCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { NavBar } from 'antd-mobile';
import { omit } from 'lodash';
import pc from 'prefix-classnames';
import React, { useEffect, useState } from 'react';
import { getLocale, history, useIntl } from 'umi';

import FooterButton from '@/components/FooterButton';
import type { FormProps } from 'antd';
import {
  Button,
  Checkbox,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Pagination,
  Popconfirm,
  Select,
  Spin,
} from 'antd';
import dayjs from 'dayjs';
import './NewCarApplyRecordPage.less';

import { Empty, Popup } from 'antd-mobile-v5';

import { getLangGroup, getLocaleMap, getResponseMessage, GovLang } from '@/locales/lang';
import {
  batchPayValidateData,
  getExReportPreviewUrl,
  getReceiptFile,
  getServiceOptions,
  printExReport,
  receiptFileUrl,
  validateGetBatchExTxn,
} from '@/services/0028F';
import {
  batchCompleteOvsapTxn,
  checkBatch33Dii,
  createVehRegTaxTxn,
  getSubRegReportPreviewUrl,
  printSubRegReport,
} from '@/services/0033D-ii';
import { getTxnList, getTxnStatusCount } from '@/services/apply';
import { checkServiceAvailable, getBookingInfo, getSysCodesByCodeType } from '@/services/publicApi';
import { isMobile } from '@/utils';
import { FetchResponse, get } from '@/utils/fetch';

import { URL_HEADER_HIDE_SUBFIX } from '@/utils/const';
import { getStatusBarHeight } from '@/utils/hooks';
import { File, Page } from '@gov-mo/mpaas-js-bridge';

const { RangePicker } = DatePicker;

const classPrefix = isMobile() ? 'newcar-apply-record-mobile' : 'newcar-apply-record';

const px = pc(classPrefix);

const NewCarApplyRecordPage = () => {
  const intl = useIntl();

  const nextStepText = intl.formatMessage({ id: 'next' });

  const [step, setStep] = useState(1);
  const [showPrevStep, setShowPrevStep] = useState(false);
  const [showNextStep, setShowNextStep] = useState(false);
  const [showClose, setShowClose] = useState(true);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);

  const [btnActive, setBtnActive] = useState(6);

  const [checkServiceModal, checkServiceContextHolder] = Modal.useModal();

  useEffect(() => {
    checkServiceAvailable('VEM').then((res) => {
      console.log('checkServiceAvailable', res);

      if (!res || !res.data) {
        return;
      }

      if (res?.data?.available === false) {
        history.push('/web/error');
        return;
      }

      if (isMobile()) {
        Modal.warning({
          icon: null,
          centered: true,
          className: 'system-maintenance-mobile',
          title: intl.formatMessage({ id: 'warm_reminder' }),
          content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
          okText: intl.formatMessage({ id: 'confirm' }),
        });
        return;
      }

      checkServiceModal.warning({
        title: intl.formatMessage({ id: 'warm_reminder' }),
        icon: <ExclamationCircleOutlined />,
        content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
        okText: intl.formatMessage({ id: 'confirm' }),
      });
    });
  }, []);

  const prevStep = () => {
    setStep(step - 1);
  };

  const nextStep = () => {
    setStep(step + 1);
  };

  const handleClose = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const [tipMsg, setTipMsg] = useState('');

  const [form] = Form.useForm();

  // 下拉选项
  const [vehTypeOpts, setVehTypeOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
  const [applyStatusOpts, setApplyStatusOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
  const [certificateOpts, setCertificateOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
  const [serviceOptions, setServiceOptions] = useState<getServiceOptions[]>();

  const [txnStatusCount, setTxnStatusCount] = useState<getTxnStatusCountRes>();

  const [list, setList] = useState<getTxnListRes[]>([]);
  const [listLoading, setListLoading] = useState(false);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [startForm, setStartForm] = useState(0);

  const [selections, setSelections] = useState<getTxnListRes[]>([]);

  async function handleBatch() {
    if (!selections || selections?.length === 0) {
      return;
    }

    const values = (selections || []).map((item) =>
      item?.buttonStatusDTO?.showSpeedUpCheckbox
        ? {
            txnId: item.nextTxnId,
            speedUp: item.speedUp ?? 'N',
          }
        : {
            txnId: item.txnId,
            speedUp: 'N',
          },
    );

    const res = await batchPayValidateData({
      txnList: values,
    });

    if (!res) return;

    if (res?.code === '0') {
      const jsonString = encodeURIComponent(JSON.stringify(res?.data?.txnIds));
      const url = `/web/vem/batch-payment?txnList=${jsonString}`;
      isMobile() || process.env.NODE_ENV !== 'production'
        ? history.push(url)
        : window.open(`/ovsap${url}`, '_blank');
    } else {
      setTipMsg(getLangGroup(getResponseMessage(res)));
    }
  }

  async function handleBatchGet() {
    if (!selections || selections?.length === 0) {
      return;
    }
    const txnIds = (selections || []).map((item) => item.txnId);
    const res = await validateGetBatchExTxn({
      txnIds,
    });
    if (!res) return;
    if (res?.code === '0') {
      const url = `/web/vem/batch-get?txnIds=${txnIds.join()}`;
      isMobile() || process.env.NODE_ENV !== 'production'
        ? history.push(url)
        : window.open(`/ovsap${url}`, '_blank');
    } else {
      setTipMsg(getLangGroup(getResponseMessage(res)));
    }
  }

  async function receiptFile(txnId: string) {
    setTipMsg('');

    if (!txnId) {
      return;
    }

    if (isMobile()) {
      const res = await receiptFileUrl(txnId);
      if (!res) return;
      res?.code === '0' &&
        File.previewPdfWithDownload({ url: `${res?.data}#.pdf`, fileName: `${txnId}.pdf` });
      res?.code !== '0' && setTipMsg(getResponseMessage(res));
      return;
    }

    const ret = await getReceiptFile(txnId).catch((e) => {
      setTipMsg(e.message);
    });

    if (!ret) {
      return;
    }

    if ((ret as FetchResponse<string>).code) {
      setTipMsg(getResponseMessage(ret as FetchResponse<string>));
      return;
    }

    const blob = new Blob([ret as BlobPart], { type: 'application/pdf' });
    blob && window.open(URL.createObjectURL(blob), '_blank');
  }

  async function exReport(txnId: string) {
    setTipMsg('');

    if (!txnId) {
      return;
    }

    if (isMobile()) {
      const res = await getExReportPreviewUrl(txnId);
      if (!res) return;
      res?.code === '0' &&
        File.previewPdfWithDownload({ url: `${res?.data}#.pdf`, fileName: `${txnId}.pdf` });
      res?.code !== '0' && setTipMsg(getResponseMessage(res));
      return;
    }

    const ret = await printExReport(txnId).catch((e) => {
      setTipMsg(e.message);
    });
    if (!ret) {
      return;
    }

    if ((ret as FetchResponse<string>).code) {
      setTipMsg(getResponseMessage(ret as FetchResponse<string>));
      return;
    }

    const blob = new Blob([ret as BlobPart], { type: 'application/pdf' });
    blob && window.open(URL.createObjectURL(blob), '_blank');
  }

  async function handlePrintSubRegReport(txnId: string) {
    setTipMsg('');

    if (!txnId) {
      return;
    }

    try {
      if (isMobile()) {
        const res = await getSubRegReportPreviewUrl(txnId);
        if (!res) return;
        res?.code === '0' &&
          File.previewPdfWithDownload({ url: `${res?.data}#.pdf`, fileName: `${txnId}.pdf` });
        res?.code !== '0' && setTipMsg(getResponseMessage(res));
        return;
      }

      const ret = await printSubRegReport(txnId);

      if (!ret) return;

      if ((ret as FetchResponse<string>)?.code) {
        setTipMsg(getResponseMessage(ret as FetchResponse<string>));
        return;
      }

      const blob = new Blob([ret as BlobPart], { type: 'application/pdf' });
      if (isMobile()) {
        blob &&
          File.previewPdfWithDownload({
            url: `${URL.createObjectURL(blob)}#.pdf`,
            fileName: `${txnId}.pdf`,
          });
        return;
      }

      blob && window.open(URL.createObjectURL(blob), '_blank');
    } catch (e: any) {
      setTipMsg(e.message);
    }
  }

  function bookingInfo(spNo: string, vehId: string) {
    getBookingInfo(spNo, vehId, getLocaleMap[getLocale()] || GovLang.繁体中文).then((res) => {
      if (res?.code !== '0') {
        return;
      }

      isMobile() && Page.openPage(res?.data);
      !isMobile() && window.open(res?.data, '_blank');
    });
  }

  const onFinish: FormProps<getTxnListParams>['onFinish'] = async (values) => {
    getList();
  };

  const onFinishFailed: FormProps<getTxnListParams>['onFinishFailed'] = (errorInfo) => {
    console.log('onFinishFailed', errorInfo);
    tipMsg && console.log('tipMsg', 'tipMsg');
  };

  const deleteTxn = (txnId: string) => {
    get<FetchResponse<string>>(`/oa/vem/deleteTxn?txnId=${txnId}`).then((res) => {
      if (!res) return;
      if (res.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }

      getList();
    });
  };

  const getList = (formFields?: any) => {
    setTipMsg('');
    setListLoading(true);

    const formData = formFields || form.getFieldsValue();

    const { dateRange } = formData;
    const formKey = Object.keys(formData);
    const params: getTxnListParams = {} as getTxnListParams;
    formKey.forEach((key) => {
      if (
        formData[key] !== undefined &&
        formData[key] !== null &&
        formData[key] !== '' &&
        key !== 'dateRange'
      ) {
        params[key] = formData[key];
      }
    });
    params.start = startForm;
    params.length = pageSize;
    if (dateRange && dateRange.length && dateRange[0] && dateRange[1]) {
      params.startDate = dayjs(dateRange[0]).format('YYYY-MM-DD');
      params.endDate = dayjs(dateRange[1]).format('YYYY-MM-DD');
    }

    getTxnList(params)
      .then(async (ret) => {
        if (!ret) return;
        if (ret.code !== '0') {
          setTipMsg(getResponseMessage(ret));
          return;
        }
        const { data, pageSize, startForm, total } = ret.data;
        setList(data);
        setPageSize(pageSize);
        setStartForm(startForm);
        setTotal(total);
        // setSelections((data || [])?.filter((item) => item?.speedUp === 'Y'));
        setSpeedUpSelections(
          data?.filter((item) => item?.speedUp === 'Y')?.map((item) => item.txnId) ?? [],
        );
        setSelections([]);
        setBatchVehRegSelections([]);
        setListLoading(false);
        fetchTxnStatusCount(params);
      })
      .catch((e) => {
        setTipMsg(e.message);
        setListLoading(false);
      });
  };

  const fetchTxnStatusCount = (params: getTxnListParams | undefined) => {
    getTxnStatusCount(omit(params || {}, 'start', 'length') as getTxnListParams)
      .then((ret) => {
        if (!ret) return;
        if (ret.code === '0') {
          setTxnStatusCount(ret.data);
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });
  };

  useEffect(() => {
    getSysCodesByCodeType('31201', 'A')
      .then((ret) => {
        if (!ret) return;
        if (ret.code === '0') {
          setVehTypeOpts(ret.dataArr);
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });
    getSysCodesByCodeType('20003', 'A')
      .then((ret) => {
        if (!ret) return;
        if (ret.code === '0') {
          setApplyStatusOpts(ret.dataArr);
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });
    getSysCodesByCodeType('21001', 'A')
      .then((ret) => {
        if (!ret) return;
        if (ret.code === '0') {
          setCertificateOpts(ret.dataArr);
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });

    getServiceOptions()
      .then((res) => {
        if (!res) return;
        setServiceOptions(res.dataArr || []);
      })
      .catch((e) => {
        setTipMsg(e.message);
      });
  }, []);

  useEffect(() => {
    getList();
  }, [startForm]);

  useEffect(() => {
    setStartForm((page - 1) * pageSize);
  }, [page]);

  const handleCreateVehRegTaxTxn = async (record: getTxnListRes) => {
    if (record.nextTxnId) {
      history.push(
        `/web/vem/0033D-ii/newcar-reg-pay?txnId=${record.nextTxnId}${
          URL_HEADER_HIDE_SUBFIX ? `&${URL_HEADER_HIDE_SUBFIX}` : ''
        }`,
      );
      return;
    }

    try {
      const res = await createVehRegTaxTxn(record.txnId);
      if (!res || res?.code !== '0') {
        return;
      }

      res.data?.txnId &&
        history.push(
          `/web/vem/0033D-ii/newcar-reg-pay?txnId=${res.data.txnId}${
            URL_HEADER_HIDE_SUBFIX ? `&${URL_HEADER_HIDE_SUBFIX}` : ''
          }`,
        );
    } catch (e: any) {
      message.error(e?.message);
    }
  };
  const [visible, setVisible] = useState(false);

  const [batchVehRegSelections, setBatchVehRegSelections] = useState<getTxnListRes[]>([]);
  const [speedUpSelections, setSpeedUpSelections] = useState<string[]>([]);
  const [batchVehRegBtnLoading, setBatchVehRegBtnLoading] = useState<boolean>(false);
  const [batchVehRegModal, batchVehRegModalContextHolder] = Modal.useModal();

  const renderBatchVehRegModal = (data: CheckBatch33DiiModel) => {
    const { plateNoDTOList, isNeedToPay, txnIds } = data;
    const needPayList = plateNoDTOList.filter((item) => item.payType);
    const notPayList = plateNoDTOList.filter((item) => !item.payType);
    const isNeedSpeedUp = plateNoDTOList.some((item) => item.speedUp === 'Y');
    const renderListItem = (list: CheckBatch33DiiModel['plateNoDTOList']) => {
      return (
        <div className={px('batch-veh-reg-modal-list-content')}>
          {list.map((item) => {
            return (
              <div
                className={`${px('batch-veh-reg-modal-list-item')} ${
                  item.speedUp === 'Y' && px('batch-veh-reg-modal-tip')
                }`}
              >
                {getLangGroup(item.vehTypeDescCn, item.vehTypeDescPt, item.vehTypeDescEn)}{' '}
                {item.plateNo} ({item.exNo}) {item.speedUp === 'Y' && '*'}
              </div>
            );
          })}
        </div>
      );
    };
    const content = () => {
      return (
        <div className={px('batch-veh-reg-modal')}>
          <div className={px('batch-veh-reg-modal-title')}>
            {intl.formatMessage({ id: 'batch_complete_vehicle_modal_tip' })}
          </div>
          {needPayList.length > 0 && (
            <div className={px('batch-veh-reg-modal-list')}>
              <div className={px('batch-veh-reg-modal-list-title')}>
                {intl.formatMessage({ id: 'batch_complete_vehicle_payment_title' })}：
              </div>
              {renderListItem(needPayList)}
            </div>
          )}
          {notPayList.length > 0 && (
            <div className={px('batch-veh-reg-modal-list')}>
              <div className={px('batch-veh-reg-modal-list-title')}>
                {intl.formatMessage({ id: 'batch_complete_vehicle_not_payment_title' })}：
              </div>
              {renderListItem(notPayList)}
            </div>
          )}
          {isNeedSpeedUp && (
            <div className={px('batch-veh-reg-modal-tip')}>
              *{intl.formatMessage({ id: 'accelerate_registration_documents' })}
            </div>
          )}
        </div>
      );
    };
    const toPay = () => {
      const jsonString = encodeURIComponent(JSON.stringify(txnIds));
      const url = `/web/vem/batch-payment?txnList=${jsonString}`;
      isMobile() || process.env.NODE_ENV !== 'production'
        ? history.push(url)
        : window.open(`/ovsap${url}`, '_blank');
    };
    const toSuccess = async () => {
      const res = await batchCompleteOvsapTxn(txnIds);
      if (!res || res.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }
      const url = `/web/vem/batch-success?reserved1=${res.data.batchId}`;
      isMobile() || process.env.NODE_ENV !== 'production'
        ? history.push(url)
        : window.open(`/ovsap${url}`, '_blank');
    };
    batchVehRegModal.confirm({
      icon: undefined,
      width: 500,
      content: content(),
      okText: intl.formatMessage({ id: 'confirm' }),
      onOk: async () => {
        if (isNeedToPay) {
          toPay();
        } else {
          await toSuccess();
        }
      },
    });
  };
  const handleBatchCompleteVehReg = async () => {
    const params: CheckBatch33DiiParams[] = batchVehRegSelections.map((item) => {
      return {
        txnId: item.txnId,
        speedUp: speedUpSelections.includes(item.txnId) ? 'Y' : 'N',
      };
    });

    try {
      setBatchVehRegBtnLoading(true);
      const res = await checkBatch33Dii(params);
      setTipMsg(getResponseMessage(res));
      if (!res || res.code !== '0') {
        return;
      }
      renderBatchVehRegModal(res.data);
    } finally {
      setBatchVehRegBtnLoading(false);
    }
  };
  const renderBatchCompleteVehRegCheckbox = (item: getTxnListRes) => {
    if (btnActive !== 5) {
      return <></>;
    }
    const checked = batchVehRegSelections.some((selection) => item?.txnId === selection?.txnId);
    const onChecked = (item: getTxnListRes) => {
      setBatchVehRegSelections([...batchVehRegSelections, item]);
    };
    const onUnChecked = (item: getTxnListRes) => {
      setBatchVehRegSelections(
        batchVehRegSelections.filter((selection) => item?.txnId !== selection?.txnId),
      );
    };
    return (
      <Checkbox
        disabled={!item.isCanComplete}
        checked={checked}
        onChange={(e) => {
          e.target.checked ? onChecked(item) : onUnChecked(item);
        }}
      />
    );
  };
  const renderBatchCompleteVehRegBtn = () => {
    if (btnActive !== 5) {
      return <></>;
    }
    return (
      <div className={px('batch-veh-reg-btn')}>
        <Button
          type="primary"
          className="pure"
          loading={batchVehRegBtnLoading}
          disabled={batchVehRegSelections.length === 0}
          onClick={() => {
            handleBatchCompleteVehReg().then();
          }}
        >
          {intl.formatMessage({ id: 'batch_complete_vehicle_registration' })}
        </Button>
      </div>
    );
  };

  const renderSearchForm = () => {
    return (
      <>
        <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })} name="vehType">
          <Select>
            {vehTypeOpts.map((item, index) => (
              <Select.Option value={item.codeKey}>
                {getLangGroup(item.codeCname, item.codePname, item.codeEname)}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={intl.formatMessage({ id: 'license_plate_number' })} name="plateNo">
          <Input value="" />
        </Form.Item>
        <Form.Item label={intl.formatMessage({ id: 'service_processing_date' })} name="dateRange">
          <RangePicker
            style={{ width: '100%' }}
            // onChange={(date, dateString) => {
            //   console.log(dateString);
            //   const data = { ...form.getFieldsValue() };
            //   data.startDate = dateString[0];
            //   data.endDate = dateString[1];
            //   form.setFieldsValue(data);
            // }}
          />
        </Form.Item>

        <Form.Item label={intl.formatMessage({ id: 'query_number' })} name="spNo">
          <Input value="" />
        </Form.Item>
        <Form.Item
          label={intl.formatMessage({ id: 'service_application_status' })}
          name="txnStatus"
        >
          <Select>
            {applyStatusOpts.map((item, index) => (
              <Select.Option value={item.codeKey}>
                {getLangGroup(item.codeCname, item.codePname, item.codeEname)}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={intl.formatMessage({ id: 'service_procedure_name' })} name="serviceCode">
          <Select>
            {(serviceOptions || []).map((item, index) => (
              <Select.Option value={item.serviceCode}>
                {getLangGroup(item.serviceTitleCn, item.serviceTitlePt, item.serviceTitleEn)}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label={intl.formatMessage({ id: 'import_license_type' })} name="importType">
          <Input value="" />
        </Form.Item>
        <Form.Item label={intl.formatMessage({ id: 'import_license_number' })} name="importNo">
          <Input value="" />
        </Form.Item>
        <Form.Item label={intl.formatMessage({ id: 'import_license_year' })} name="importYear">
          <Input value="" />
        </Form.Item>
        <Form.Item
          label={intl.formatMessage({ id: 'vehicle_owner_id_type' })}
          name="ownerIdentType"
        >
          <Select>
            {certificateOpts.map((item, index) => (
              <Select.Option value={item.codeKey}>
                {getLangGroup(item.codeCname, item.codePname, item.codeEname)}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={intl.formatMessage({ id: 'vehicle_owner_id_number' })}
          name="ownerIdentNo"
        >
          <Input value="" />
        </Form.Item>
        {!isMobile() && <Form.Item></Form.Item>}

        <Form.Item noStyle name="txnStatusButton">
          <Input type="hidden" value="" />
        </Form.Item>
        <Form.Item noStyle name="payStatus">
          <Input type="hidden" value="" />
        </Form.Item>
        <Form.Item noStyle name="exStatus">
          <Input type="hidden" value="" />
        </Form.Item>
        <Form.Item noStyle name="alreadyReceivedExStatus">
          <Input type="hidden" value="" />
        </Form.Item>
        <Form.Item noStyle name="waitVehRegStatus">
          <Input type="hidden" value="" />
        </Form.Item>
        <Form.Item noStyle name="myTxnApplyStatus">
          <Input type="hidden" value="" />
        </Form.Item>
        <div className={px('search-query-btn')}>
          <div className={px('search-query-btn-box')}>
            <Button
              type="default"
              className={btnActive == 0 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(0);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = '';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList();
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'all' })}({txnStatusCount?.allCount || 0})
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 1 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(1);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = 'A';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList();
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'in_progress' })}({txnStatusCount?.activeCount || 0})
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 2 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(2);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = 'P';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList();
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'pending_payment' })}({txnStatusCount?.waitPayCount || 0})
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 7 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(7);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = 'S';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList();
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'pending_payment_approval' })}(
              {txnStatusCount?.waitApprovalCount || 0})
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 4 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(4);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = '';
                data.payStatus = '';
                data.exStatus = 'T';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList();
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'waiting_ex_cards' })}({txnStatusCount?.waitGetExCount || 0}
              )
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 8 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(8);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = '';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = 'Y';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList();
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'got_ex_cards' })}(
              {txnStatusCount?.alreadyReceivedExCount || 0})
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 5 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(5);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = '';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = 'Y';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList(data);
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'pending_vehicle_registration' })}(
              {txnStatusCount?.waitVehRegCount || 0})
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 6 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(6);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = '';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = 'Y';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList(data);
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'my_create_record' })}(
              {txnStatusCount?.myTxnApplyCount || 0})
            </Button>
            &nbsp;
            <Button
              type="default"
              className={btnActive == 3 ? 'btn-active pure' : 'pure'}
              onClick={() => {
                setBtnActive(3);

                const data = { ...form.getFieldsValue() };
                data.txnStatusButton = 'F';
                data.payStatus = '';
                data.exStatus = '';
                data.alreadyReceivedExStatus = '';
                data.waitVehRegStatus = '';
                data.myTxnApplyStatus = '';
                form.setFieldsValue(data);

                page != 1 ? setPage(1) : getList();
                isMobile() && setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'application_completed' })}(
              {txnStatusCount?.finishCount || 0})
            </Button>
          </div>
          <div className="search-reset-btn" style={{ flexShrink: 0 }}>
            <Button
              style={{ marginRight: '10px', order: isMobile() ? 2 : 1 }}
              type="primary"
              onClick={() => {
                page != 1 ? setPage(1) : form.submit();
                setVisible(false);
              }}
            >
              {intl.formatMessage({ id: 'search' })}
            </Button>
            <Button
              style={{ marginRight: '10px', order: isMobile() ? 1 : 2 }}
              type="default"
              onClick={() => {
                setBtnActive(0);
                form.resetFields();
              }}
            >
              {intl.formatMessage({ id: 'reset' })}
            </Button>
          </div>
        </div>
      </>
    );
  };
  const renderSearchFormModal = () => {
    return (
      // @ts-ignore
      <Popup
        visible={visible}
        maskStyle={
          {
            // top: 45 + getStatusBarHeight(),
          }
        }
        bodyStyle={{
          // top: 45 + getStatusBarHeight(),
          paddingTop: getStatusBarHeight(),
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          borderBottomLeftRadius: '10px',
          borderBottomRightRadius: '10px',
        }}
        onMaskClick={() => {
          setVisible(false);
        }}
        getContainer={null}
        position="top"
      >
        <div style={{ height: '70vh', overflowY: 'scroll', padding: '20px' }}>
          <Form
            form={form}
            layout="vertical"
            preserve={false}
            scrollToFirstError
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
          >
            <div className={px('sectionBody')}>{renderSearchForm()}</div>
          </Form>
        </div>
      </Popup>
    );
  };

  const color = ['#084ab8', '#e9b745', '#13a07b', '#f33b40'];
  return (
    <>
      {isMobile() ? (
        <NavBar
          style={{ paddingTop: getStatusBarHeight() }}
          mode="light"
          leftContent={<LeftOutlined onClick={() => history.push('/web/vem')} />}
          rightContent={
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <svg
                onClick={() => setVisible(true)}
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="4270"
                width="20"
                height="20"
              >
                <path
                  d="M568.552727 954.181818a86.341818 86.341818 0 0 1-41.425454-10.472727l-114.269091-62.836364a86.574545 86.574545 0 0 1-44.916364-76.101818V442.181818a16.989091 16.989091 0 0 0-6.981818-13.730909L97.047273 218.298182 93.090909 214.807273A85.876364 85.876364 0 0 1 155.927273 69.818182h711.214545A86.574545 86.574545 0 0 1 930.909091 214.807273l-3.490909 3.490909-266.24 213.410909a17.454545 17.454545 0 0 0-6.050909 13.730909v422.4A86.341818 86.341818 0 0 1 568.552727 954.181818zM141.963636 166.632727l262.283637 209.454546A85.178182 85.178182 0 0 1 437.061818 442.181818v362.589091a17.92 17.92 0 0 0 8.610909 15.592727l114.269091 62.138182a17.687273 17.687273 0 0 0 17.687273 0 17.221818 17.221818 0 0 0 8.378182-15.36V445.44a85.876364 85.876364 0 0 1 31.883636-67.258182l263.214546-211.549091a17.687273 17.687273 0 0 0 1.62909-18.152727 17.454545 17.454545 0 0 0-15.592727-8.843636H155.927273a17.221818 17.221818 0 0 0-13.963637 27.694545zM920.669091 512h-188.974546a34.443636 34.443636 0 0 1 0-69.818182h188.974546a34.443636 34.443636 0 0 1 0 69.818182z m0 128.930909h-188.974546a34.443636 34.443636 0 0 1 0-69.818182h188.974546a34.443636 34.443636 0 0 1 0 69.818182z m0 128h-188.974546a34.443636 34.443636 0 0 1 0-69.818182h188.974546a34.443636 34.443636 0 0 1 0 69.818182z"
                  p-id="4271"
                  fill="#084ab8"
                ></path>
              </svg>
            </div>
          }
        >
          <div className="mobile-title">
            {intl.formatMessage({ id: 'application_record_query' })}
          </div>
        </NavBar>
      ) : null}

      {/* {tipMsg && (
        <div style={{ width: '80%', margin: '0 auto', paddingBottom: '0.24rem', }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )} */}

      <div
        style={{
          paddingTop: isMobile() ? 45 + getStatusBarHeight() : 0,
          height: isMobile() && !list.length ? '100%' : 'auto',
        }}
        className={`${px('root')} `}
      >
        {/* <h2 className={px('title')}>{intl.formatMessage({ id: 'application_record_query' })}</h2> */}
        <Spin spinning={listLoading}>
          <div className={px('stepinfo')}>
            {isMobile() && renderSearchFormModal()}
            <div className={px('form')}>
              <Form
                form={form}
                layout="vertical"
                preserve={false}
                scrollToFirstError
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
              >
                <div className={px('sectionBody')}>
                  {!isMobile() && renderSearchForm()}

                  {btnActive === 2 && txnStatusCount?.waitPayCount ? (
                    <div className={px('batch-payment-btn')}>
                      <Button
                        type="primary"
                        className="pure"
                        disabled={!selections || selections.length === 0}
                        onClick={handleBatch}
                      >
                        {intl.formatMessage({ id: 'batch_payment' })}
                      </Button>
                    </div>
                  ) : null}

                  {btnActive === 4 && (
                    <div className={px('batch-payment-btn')}>
                      <Button
                        type="primary"
                        className="pure"
                        disabled={!selections || selections.length === 0}
                        onClick={handleBatchGet}
                      >
                        {intl.formatMessage({ id: 'batch_get' })}
                      </Button>

                      <Button
                        style={{ marginLeft: '8px' }}
                        type="default"
                        className="pure"
                        onClick={() => {
                          const url = `/web/vem/batch-get/list`;
                          isMobile() || process.env.NODE_ENV !== 'production'
                            ? history.push(url)
                            : window.open(`/ovsap${url}`, '_blank');
                        }}
                      >
                        {intl.formatMessage({ id: 'get_car_list' })}
                      </Button>
                    </div>
                  )}

                  {renderBatchCompleteVehRegBtn()}

                  {list.length ? (
                    <>
                      <div style={{ width: '100%', marginTop: 20 }}>
                        {list.map((item, index) => {
                          const checked = (selections || []).some(
                            (selection) => item?.txnId === selection?.txnId,
                          );

                          return (
                            <div className="card">
                              <div
                                className="card-header"
                                style={{
                                  backgroundColor: color[index % 4],
                                }}
                              >
                                {(btnActive === 2 || btnActive === 4) && (
                                  <Checkbox
                                    disabled={
                                      !!item?.batchId || (btnActive === 2 && !item?.isNeedToPay)
                                    }
                                    checked={checked}
                                    onChange={(e) => {
                                      e.target.checked
                                        ? setSelections([...(selections || []), item])
                                        : setSelections(
                                            (selections || []).filter(
                                              (selection) => item?.txnId !== selection?.txnId,
                                            ),
                                          );
                                    }}
                                  />
                                )}
                                {renderBatchCompleteVehRegCheckbox(item)}
                                <div className="card-title">
                                  {getLangGroup(
                                    item.serviceTitleNameCn,
                                    item.serviceTitleNamePt,
                                    item.serviceTitleNameEn,
                                  )}
                                  {/* {item.spOperationCode == '0028F'
                                ? intl.formatMessage({ id: 'ex_test_license_plate_application' })
                                : item.spOperationCode == '0033D'
                                ? intl.formatMessage({ id: 'vehicle_registration_application' })
                                : intl.formatMessage({ id: 'unknown_type' })}
                              {item.spNo
                                ? `(${intl.formatMessage({ id: 'query_number' })}：${item.spNo})`
                                : ''} */}
                                  {/* {
                                item.txnStatus == 'F' && (
                                  <SystemQRcodeOutline onClick={() => openQRCode(item.spNo)} />
                                )
                              } */}
                                </div>

                                <div
                                  className={`status ${
                                    item.txnStatus === 'P' ? 'status-paying' : 'status-paied'
                                  }`}
                                >
                                  {`${getLangGroup(
                                    item?.txnStatusCn,
                                    item?.txnStatusPt,
                                    item?.txnStatusEn,
                                  )}${
                                    item?.batchId
                                      ? ` (${getLangGroup(
                                          item?.detailStatusCn,
                                          item?.detailStatusPt,
                                          item?.detailStatusEn,
                                        )})`
                                      : ''
                                  }`}
                                </div>
                              </div>
                              <div className="card-body">
                                {item.ownerDataList.length ? (
                                  <div style={{ width: '100%' }}>
                                    {item.ownerDataList.map((ownerData, index2) => (
                                      <div
                                        className="item"
                                        style={{ display: 'flex', width: '100%' }}
                                      >
                                        <div
                                          className="label"
                                          style={{ width: isMobile() ? '40%' : '15%' }}
                                        >
                                          {index2 == 0 ? (
                                            <>
                                              {intl.formatMessage({ id: 'car_owner_information' })}
                                            </>
                                          ) : (
                                            <></>
                                          )}
                                        </div>
                                        <div className="value">
                                          {getLangGroup(
                                            ownerData.ownerIdentTypeCn,
                                            ownerData.ownerIdentTypePt,
                                            ownerData.ownerIdentTypeEn,
                                          )}{' '}
                                          {ownerData.ownerIdentNo}
                                          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                          <span className={isMobile() ? 'flex' : ''}>
                                            {(ownerData.ownerLastNameCn ?? '') +
                                              (ownerData.ownerFirstNameCn ?? '')}
                                            &nbsp;&nbsp;
                                            {`${ownerData.ownerLastNamePt ?? ''} ${
                                              ownerData.ownerFirstNamePt ?? ''
                                            }`}
                                          </span>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <></>
                                )}
                                <div className="item">
                                  <div className="label">
                                    {item.spOperationCode == '0028F'
                                      ? intl.formatMessage({ id: 'ex_license_plate_no' })
                                      : intl.formatMessage({ id: 'license_plate_number' })}
                                  </div>
                                  <div className="value">
                                    {item.plateNo ||
                                      (item.spOperationCode == '0028F' &&
                                        getLangGroup(
                                          item?.noPlateNoMsgDTO?.codeCname,
                                          item?.noPlateNoMsgDTO?.codePname,
                                          item?.noPlateNoMsgDTO?.codeEname,
                                        ))}
                                  </div>
                                </div>
                                <div className="item">
                                  <div className="label">
                                    {intl.formatMessage({ id: 'vehicle_level' })}
                                  </div>
                                  <div className="value">
                                    {getLangGroup(
                                      item.vehTypeDescCn,
                                      item.vehTypeDescPt,
                                      item.vehTypeDescEn,
                                    )}
                                  </div>
                                </div>
                                <div className="item">
                                  <div className="label">
                                    {intl.formatMessage({ id: 'model_approval_number' })}
                                  </div>
                                  <div className="value">{item.vtaNoFull || item.vtaNo}</div>
                                </div>
                                <div className="item">
                                  <div className="label">
                                    {intl.formatMessage({ id: 'import_license_number' })}
                                  </div>
                                  <div className="value">{item.importNoFull || item.importNo}</div>
                                </div>
                                <div className="item">
                                  <div className="label">{intl.formatMessage({ id: 'brand' })}</div>
                                  <div className="value">{item.vehBrandDescCn}</div>
                                </div>
                                <div className="item">
                                  <div className="label">{intl.formatMessage({ id: 'style' })}</div>
                                  <div className="value">{item.vehModel}</div>
                                </div>
                                <div className="item">
                                  <div className="label">
                                    {intl.formatMessage({ id: 'vehicle_number' })}
                                  </div>
                                  <div className="value">{item.vin}</div>
                                </div>
                                {(item?.promptMsgCn || item?.promptMsgPt || item?.promptMsgEn) && (
                                  <div className={px('owner-time 123')}>
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: getLangGroup(
                                          item?.promptMsgCn,
                                          item?.promptMsgPt,
                                          item?.promptMsgEn,
                                        ),
                                      }}
                                    ></div>
                                  </div>
                                )}
                              </div>
                              <div className="card-footer">
                                <div className="card-footer-checkbox">
                                  {item.buttonStatusDTO.showSpeedUpCheckbox && (
                                    <Checkbox
                                      checked={item.speedUp === 'Y'}
                                      onChange={(e) => {
                                        if (e.target.checked) {
                                          setSpeedUpSelections([...speedUpSelections, item.txnId]);
                                        } else {
                                          setSpeedUpSelections(
                                            speedUpSelections.filter(
                                              (selection) => item.txnId !== selection,
                                            ),
                                          );
                                        }

                                        if (e.target.checked) {
                                          !checked &&
                                            setSelections([
                                              ...(selections || []),
                                              { ...item, speedUp: e.target.checked ? 'Y' : 'N' },
                                            ]);
                                        }

                                        setList(
                                          list.map((element, idx) =>
                                            index === idx
                                              ? {
                                                  ...element,
                                                  speedUp: e.target.checked ? 'Y' : 'N',
                                                }
                                              : element,
                                          ),
                                        );
                                      }}
                                    >
                                      {intl.formatMessage({
                                        id: 'accelerate_registration_documents',
                                      })}
                                    </Checkbox>
                                  )}
                                </div>
                                <div className="right flex">
                                  {/*查看詳情*/}
                                  {item.buttonStatusDTO.detailButton &&
                                    item.txnStatus !== 'I' &&
                                    item.txnStatus !== 'D' && (
                                      <Button
                                        type="default"
                                        onClick={() => {
                                          const url =
                                            item.spOperationCode == '0033D'
                                              ? `/web/vem/0033D/newcar-reg-apply?showSuccess=true&step=2&txnId=${
                                                  item.txnId
                                                }${
                                                  URL_HEADER_HIDE_SUBFIX
                                                    ? `&${URL_HEADER_HIDE_SUBFIX}`
                                                    : ''
                                                }`
                                              : `/web/vem/0028F/vil-form?showSuccess=true&step=7&txnId=${
                                                  item.txnId
                                                }${
                                                  URL_HEADER_HIDE_SUBFIX
                                                    ? `&${URL_HEADER_HIDE_SUBFIX}`
                                                    : ''
                                                }`;

                                          isMobile() || process.env.NODE_ENV !== 'production'
                                            ? history.push(url)
                                            : window.open(`/ovsap${url}`, '_blank');
                                        }}
                                      >
                                        {intl.formatMessage({ id: 'view_details' })}
                                      </Button>
                                    )}

                                  {/*下載收據*/}
                                  {item.buttonStatusDTO.downloadReceiptButton && (
                                    <Button type="default" onClick={() => receiptFile(item.txnId)}>
                                      {intl.formatMessage({ id: 'download_receipt' })}
                                    </Button>
                                  )}
                                  {/*下載收據(完成車輛註冊)*/}
                                  {item.buttonStatusDTO.download33DiiReceiptButton && (
                                    <Button
                                      type={'default'}
                                      onClick={() => receiptFile(item.nextTxnId)}
                                    >
                                      {intl.formatMessage({
                                        id: 'download_complete_vehicle_registration_receipt',
                                      })}
                                    </Button>
                                  )}

                                  {/*下載EX試驗車牌臨時准照*/}
                                  {item.buttonStatusDTO.downloadExButton && (
                                    <Button
                                      type="default"
                                      style={{
                                        whiteSpace: 'normal',
                                        wordBreak: 'break-word',
                                        height: 'auto',
                                      }}
                                      onClick={() => exReport(item.txnId)}
                                    >
                                      {intl.formatMessage({ id: 'download_ex_card' })}
                                    </Button>
                                  )}
                                  {/*下載代替登記摺之憑證*/}
                                  {item.buttonStatusDTO.downloadRegDocButton && (
                                    <Button
                                      type="default"
                                      onClick={() => handlePrintSubRegReport(item.nextTxnId)}
                                    >
                                      {intl.formatMessage({ id: 'download_booklet' })}
                                    </Button>
                                  )}
                                  {/*終止*/}
                                  {item.buttonStatusDTO.deleteButton && (
                                    <Popconfirm
                                      title={intl.formatMessage({ id: 'terminate_record' })}
                                      onConfirm={() => {
                                        deleteTxn(item.txnId);
                                      }}
                                      okText={intl.formatMessage({ id: 'yes' })}
                                      cancelText={intl.formatMessage({ id: 'no' })}
                                    >
                                      <Button type="default">
                                        {intl.formatMessage({ id: 'termination' })}
                                      </Button>
                                    </Popconfirm>
                                  )}

                                  {/*更新資料*/}
                                  {item.buttonStatusDTO.updateButton && (
                                    <Button
                                      type="default"
                                      onClick={() => {
                                        const url =
                                          item.spOperationCode == '0033D'
                                            ? `/web/vem/0033D/newcar-reg-apply?txnId=${item.txnId}${
                                                URL_HEADER_HIDE_SUBFIX
                                                  ? `&${URL_HEADER_HIDE_SUBFIX}`
                                                  : ''
                                              }`
                                            : `/web/vem/0028F/vil-form?txnId=${item.txnId}${
                                                URL_HEADER_HIDE_SUBFIX
                                                  ? `&${URL_HEADER_HIDE_SUBFIX}`
                                                  : ''
                                              }`;

                                        isMobile() || process.env.NODE_ENV !== 'production'
                                          ? history.push(url)
                                          : window.open(`/ovsap${url}`, '_blank');
                                      }}
                                    >
                                      {intl.formatMessage({ id: 'update_information' })}
                                    </Button>
                                  )}

                                  {/*預約/修改驗車*/}
                                  {item.buttonStatusDTO.appVehButton && (
                                    <Button
                                      type="primary"
                                      onClick={() => bookingInfo(item?.spNo, item?.vehId)}
                                    >
                                      {intl.formatMessage({ id: 'appointment_vehicle_inspection' })}
                                    </Button>
                                  )}
                                  {/*完成車輛註冊*/}
                                  {item.buttonStatusDTO.completeButton && (
                                    <Popconfirm
                                      title={getLangGroup(
                                        item.buttonStatusDTO?.popWinMsg33DIICn,
                                        item.buttonStatusDTO?.popWinMsg33DIIEn,
                                        item.buttonStatusDTO?.popWinMsg33DIIPt,
                                      )}
                                      onConfirm={() => {
                                        handleCreateVehRegTaxTxn(item);
                                      }}
                                      okText={intl.formatMessage({ id: 'yes' })}
                                      cancelText={intl.formatMessage({ id: 'no' })}
                                    >
                                      <Button type={'primary'}>
                                        {intl.formatMessage({
                                          id: 'complete_vehicle_registration',
                                        })}
                                      </Button>
                                    </Popconfirm>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      <div className={px('pagination-box')}>
                        <Pagination
                          showSizeChanger={false}
                          current={page}
                          pageSize={pageSize}
                          total={total}
                          onChange={(e) => {
                            setPage(e);
                          }}
                        />
                      </div>
                    </>
                  ) : (
                    <div className="data-empty">
                      <Empty description={intl.formatMessage({ id: 'no_data' })}></Empty>
                    </div>
                  )}
                </div>
              </Form>
            </div>
          </div>
        </Spin>
      </div>

      {!isMobile() && (
        <FooterButton
          // handleClose={handleClose}
          className={px('footer-button')}
          step={step}
          stepType="progress"
          nextStepText={nextStepText}
          showPrevStep={showPrevStep}
          showNextStep={showNextStep}
          showClose={showClose}
          setShowPrevStep={() => {
            setShowPrevStep;
          }}
          setShowNextStep={() => {
            setShowNextStep;
          }}
          setShowClose={() => {
            setShowClose;
          }}
          disablePrevStep={disablePrevStep}
          disableNextStep={disableNextStep}
          disableClose={disableClose}
          setDisablePrevStep={(flag) => {
            setDisablePrevStep(flag);
          }}
          setDisableNextStep={(flag) => {
            setDisableNextStep(flag);
          }}
          setDisableClose={() => {
            setDisableClose;
          }}
          handlePrevStep={() => {
            prevStep();
          }}
          handleNextStep={() => {
            nextStep();
          }}
          handleClose={() => {
            handleClose();
          }}
        />
      )}
      {!isMobile() && checkServiceContextHolder}
      {batchVehRegModalContextHolder}
    </>
  );
};

export default NewCarApplyRecordPage;
