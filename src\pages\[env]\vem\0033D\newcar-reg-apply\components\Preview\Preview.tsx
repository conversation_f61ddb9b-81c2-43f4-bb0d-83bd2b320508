import React, { useContext, useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import dayjs from 'dayjs';
import './Preview.less';

import { Alert, Button, Form, Input, message, Modal, Popconfirm, Select } from 'antd';

import {
  getTxnVehicle,
  getVTATyreList,
  saveTxnVehicle,
  sendFacialRecognitionECNotice,
  validateTxnForPayment,
  validateTxnIsNeedToPay,
} from '@/services/0028F';

import {
  cancelCustomPlateNo,
  getVehRegTxnData,
  saveCustomPlateNo,
  validateCustomPlateNo,
} from '@/services/0033D';
import { history, Location, useIntl, useLocation } from 'umi';
import { getSysCodesByCodeType } from '@/services/publicApi';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import { useCountDown } from '@/utils/hooks';
import SpinContext from '@/components/SpinContext';
import { completeOvsapTxn } from '@/services/0033D-ii';
import Watermark from '@/components/Watermark';
import { useToggle } from 'ahooks';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

type FormPreviewLocation = {
  query: {
    showSuccess?: boolean;
    update?: string;
  };
};

const px = pc('preview-33d');

export interface PreviewProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setNextStepText: (string) => void;
  setCloseText?: (value: string) => void;
  setShowCloseAndTempStore: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowClose: (boolean) => void;

  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;
  setDisablePrevStep: (disabled: boolean) => void;
  setShowPrevStep: (isShow: boolean) => void;
  setShowNextStep: (isShow) => void;
  onApplied: (applied: boolean) => void;
  txnId: string | undefined;
  onTxnStatus: (status: string) => void;
  initStepOptions: StepOptionsItem[];
  setStepOptions: (opt: StepOptionsItem[]) => void;
}

// 倒數計時
const CountdownButton = ({
  item,
  noticeStatus,
  setNoticeStatus,
  setTipMsg,
  txnId,
  setDisableNextStep,
  fetchVehRegTxnData,
}) => {
  const onExpire = async () => {
    sendFacialRecognitionECNotice({
      txnId: txnId!,
      ownerId: item.txnOwnerId,
      channelType: '2',
      identType: item.ownerIdentType,
      identNo: item.ownerIdentNo,
    })
      .then((ret) => {
        if (ret.code === '0') {
          start();
        } else {
          setTipMsg(getResponseMessage(ret));
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });
  };
  const { seconds, isRunning, start } = useCountDown({
    expiryTime: 60,
    onExpire: () => {
      const status = noticeStatus!.map((notice) => {
        if (notice.ownerId == item.txnOwnerId) {
          notice.status = 1;
          item.verficationStatus = 'P';
        }
        return notice;
      });

      setDisableNextStep(true);
      setNoticeStatus(status);
      fetchVehRegTxnData();
    },
  });

  return (
    <Button
      disabled={isRunning}
      onClick={onExpire}
      type="primary"
      style={{ whiteSpace: 'normal', maxWidth: '350px', height: 'auto' }}
    >
      {getLangGroup(
        item?.agentVerficationStatusCn,
        item?.agentVerficationStatusPt,
        item?.agentVerficationStatusEn,
      )}
      {isRunning ? `(${seconds}s)` : ''}
    </Button>
  );
};
const Preview = (props: PreviewProps) => {
  const { className = '', ...otherProps } = props;
  const {
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    contentNextStep,
    contentForNextStep,
    setDisableNextStep,
    setDisablePrevStep,
    setShowPrevStep,
    setShowNextStep,
    txnId,
    onApplied,
    onTxnStatus,
    setNextStepText,
    setStepOptions,
    initStepOptions,
  } = otherProps;

  const [messageApi, contextHolder] = message.useMessage();

  const [tipMsg, setTipMsg] = useState('');
  const intl = useIntl();

  const [buyStatus, setBuyStatus] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [vehForm] = Form.useForm();
  const [editCarStatus, setEditCarStatus] = useState(false);

  const [vehUsageOpts, setVehUsageOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
  const [colorOpts, setColorOpts] = useState<sysCodesByCodeTypeOpt[]>([]);
  const [tyreOpts, setTyreOpts] = useState<vehTyreOpt[]>([]);

  const [customPlateNo, setCustomPlateNo] = useState<string>('');
  const [validatePlateNoStatus, setValidatePlateNoStatus] = useState<string>('');
  const [validatePlateNoMsg, setValidatePlateNoMsg] = useState<string>('');

  const [ownerDataList, setOwnerDataList] = useState<saveTxnOwnersParams[]>([]);
  const [ovsapVehDataDTO, setOvsapVehDataDTO] = useState<ovsapVehDataDTO>();
  const [ovsapAgentCompData, setOvsapAgentCompData] = useState<saveAgentCompParams>();
  const [vehRegTopDataDTO, setVehRegTopDataDTO] = useState<VehRegTopDataDTOType>();
  const [completeResultDTO, setCompleteResultDTO] = useState<CompleteResultDTOType | undefined>(
    undefined,
  );
  const [vehReqTaxTxnCompleteDataDTO, setVehReqTaxTxnCompleteDataDTO] = useState<
    CompleteResultDTOType | undefined
  >(undefined);
  const [noticeStatus, setNoticeStatus] = useState<{ ownerId: string; status: number }[]>();
  const [txnStatus, setTxnStatus] = useState<string>('');
  const { setLoading } = useContext(SpinContext);
  const location = useLocation() as Location & FormPreviewLocation;

  const { showSuccess } = location.query;

  const [dspaFpDataDto, setDspaFpDataDto] = useState<{
    dspaFpNo: string;
    dspaFpFeeCodeDto: Record<string, any>;
  }>({ dspaFpNo: '', dspaFpFeeCodeDto: {} });

  const [_, setIsNeedPay] = useState<boolean>(true);
  const fetchValidateTxnIsNeedToPay = async () => {
    if (!txnId) return;
    const { data, code } = await validateTxnIsNeedToPay(txnId);
    if (code !== '0') return;

    setIsNeedPay(data.isNeedPay);
    // 不需要支付
    if (!data.isNeedPay) {
      // 修改下一步按钮文案和步骤
      setNextStepText(intl.formatMessage({ id: 'submit_application' }));
      setStepOptions([...initStepOptions.filter((item) => item.sort !== 4)]);
    }
  };

  const fetchVehRegTxnData = async () => {
    if (!txnId) return setLoading(false);
    const res = await getVehRegTxnData(txnId);
    setLoading(false);
    if (res && res.code === '0') {
      const { data } = res;
      setTxnStatus(data.txnStatus);
      onTxnStatus(data.txnStatus);
      setOwnerDataList(data.ownerDataList);
      setOvsapVehDataDTO(data.ovsapVehDataDTO);
      setOvsapAgentCompData(data.ovsapAgentCompData);
      setVehRegTopDataDTO(data.vehRegTopDataDTO);
      setCompleteResultDTO(data.completeResultDTO);
      setVehReqTaxTxnCompleteDataDTO(data.vehRegTaxTxnCompleteDataDTO);
      setDspaFpDataDto(data?.dspaFpDataDTO || {});

      const status: { ownerId: string; status: number }[] = (data.ownerDataList ?? []).map(
        (item) => ({
          ownerId: item.txnOwnerId,
          status: item.verficationStatus !== 'W' ? 1 : 0,
        }),
      );

      setNoticeStatus(status || []);
      setBuyStatus(!!data.vehRegTopDataDTO?.tempMountPlateNo);

      if (data.txnStatus === 'E' || data.txnStatus === 'F' || data.txnStatus === 'S') {
        setShowPrevStep(false);
        setShowNextStep(false);
        onApplied(true);
        props.setCloseText && props.setCloseText(intl.formatMessage({ id: 'turn_off' }));
      } else if (data.txnStatus === 'P') {
        setDisableNextStep(false);
        setDisablePrevStep(false);
      } else if (data.txnStatus === 'A') {
        setDisableNextStep(true);
        setDisablePrevStep(false);
      }
      if (data.txnStatus !== 'E' && data.txnStatus !== 'F' && data.txnStatus !== 'S') {
        fetchValidateTxnIsNeedToPay();
      }
      if (txnId) {
        props.step === 2 && setDisablePrevStep(true);
      }
    }
  };

  useEffect(() => {
    let timer;
    fetchVehRegTxnData();
    timer = setInterval(() => {
      // 非 P E F才60s刷新一次
      if (!['P', 'E', 'F', 'S'].includes(txnStatus)) {
        fetchVehRegTxnData();
      }
    }, 60 * 1000);
    return () => {
      clearInterval(timer);
    };
  }, [txnId]);

  // 使用购买车牌
  const cancelBuyCard = async () => {
    const res = await cancelCustomPlateNo(txnId ?? '');
    if (res && res.code === '0') {
      fetchVehRegTxnData();
    }
  };

  const onSerach = async () => {
    if (txnId) {
      const res = await validateCustomPlateNo(txnId, customPlateNo);
      setValidatePlateNoStatus(res.code === '0' ? 'success' : 'error');
      setValidatePlateNoMsg(
        res.code === '0'
          ? getLangGroup(res?.data?.msgCname, res?.data?.msgPname, res?.data?.msgEname)
          : getResponseMessage(res),
      );
    }
  };

  const handleCancel = () => {
    setCustomPlateNo('');
    setValidatePlateNoStatus('');
    setValidatePlateNoMsg('');
    setIsModalOpen(false);
  };

  const handleOk = async () => {
    if (txnId) {
      const res = await saveCustomPlateNo(txnId, customPlateNo);
      if (res.code === '0') {
        handleCancel();
        fetchVehRegTxnData();
      }
    }
  };

  const handleCarOk = () => {
    vehForm.validateFields().then(async (values) => {
      try {
        const ret = await saveTxnVehicle({
          ...values,
          txnId: txnId ?? '',
        });

        if (ret.code !== '0') {
          messageApi.error(getResponseMessage(ret));
          return;
        }

        fetchVehRegTxnData();
        setEditCarStatus(false);
      } catch (err) {
        console.log(err);
      }
    });
  };

  function handleCarCancel() {
    setEditCarStatus(false);
  }

  // function editCar() {
  //   setEditCarStatus(true);
  // }
  useEffect(() => {
    setLoading(true);
    setShowPrevStep(false);
    contentForNextStep(1);
    setShowCloseAndTempStore(true);
    setShowClose(false);
    setShowTempStore(false);

    setDisableNextStep(true);
    return () => {
      contentForNextStep(0);
      setShowCloseAndTempStore(false);
      setShowTempStore(true);
      setShowClose(true);
      setDisableNextStep(false);
      // 清除是否支付副作用
      setNextStepText(intl.formatMessage({ id: 'next' }));
      setStepOptions([...initStepOptions]);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      async function fn() {
        setDisableNextStep(true);
        setTipMsg('');

        if (!txnId) {
          return;
        }

        const ret = await validateTxnForPayment(txnId).catch((e) => {
          setTipMsg(e.message);
        });

        if (!ret) {
          setDisableNextStep(false);
          return;
        }
        if (ret.code !== '0') {
          setTipMsg(getResponseMessage(ret));
          setDisableNextStep(false);
          return;
        }

        if (ret?.data?.isNeedPay === false) {
          const res = await completeOvsapTxn(txnId);
          if (res?.code !== '0') {
            setDisableNextStep(false);
            return;
          }
          history.push(`/web/vem/0033D/newcar-reg-apply?page=success&txnId=${txnId}`);
          return;
        }

        contentForNextStep(2);
        setDisableNextStep(false);
      }

      fn();
    }
  }, [contentNextStep]);

  useEffect(() => {
    const fetchVehOptions = async () => {
      if (editCarStatus && txnId) {
        getSysCodesByCodeType('20001', 'A')
          .then((ret) => {
            if (ret.code === '0') {
              setVehUsageOpts(ret.dataArr);
            }
          })
          .catch((err) => {
            console.log(err);
          });

        getSysCodesByCodeType('20002', 'A')
          .then((ret) => {
            if (ret.code === '0') {
              setColorOpts(ret.dataArr);
            }
          })
          .catch((e) => {
            setTipMsg(e.message);
          });

        getTxnVehicle(txnId)
          .then((ret) => {
            if (ret.code === '0') {
              const { data } = ret;
              vehForm.setFieldsValue({
                vehUsageCode: data.vehUsageCode,
                tyreId: data.tyreId,
                colorCode: data.colorCode,
              });

              getVTATyreList(data.vtaNo, data.vtaYear)
                .then((ret) => {
                  if (ret.code === '0') {
                    setTyreOpts(ret.dataArr);
                  }
                })
                .catch((e) => {
                  setTipMsg(e.message);
                });
            }
          })
          .catch((e) => {
            setTipMsg(e.message);
          });
      }
    };
    fetchVehOptions();
  }, [editCarStatus, txnId]);
  const [state, { toggle }] = useToggle();

  return (
    <Watermark text={dayjs().format('YYYY-MM-DD HH:mm:ss')}>
      {contextHolder}
      {tipMsg && (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'data_confirmation' })}</div>

        <div className="my-card-container">
          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'vehicle_register_information' })}
            </div>
            <div className="my-card-body">
              <Form form={form} className="owner-card-form" layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'license_plate_number' })}>
                  <div>{vehRegTopDataDTO?.plateNo}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'ex_license_plate_validity' })}>
                  <div>{vehRegTopDataDTO?.exRegDuration}</div>
                </Form.Item>
                <div style={{ width: '100%' }}>
                  <Form.Item label={intl.formatMessage({ id: 'apply_date_last' })}>
                    <div>{vehRegTopDataDTO?.lastVehRegDate}</div>
                  </Form.Item>
                </div>

                {vehRegTopDataDTO?.isShowDrawResult ? (
                  <>
                    <Form.Item label={intl.formatMessage({ id: 'license_plate_lottery_status' })}>
                      <div>
                        {getLangGroup(
                          vehRegTopDataDTO?.vehTodrawCn,
                          vehRegTopDataDTO?.vehTodrawPt,
                          vehRegTopDataDTO?.vehTodrawEn,
                        )}
                      </div>
                      {vehRegTopDataDTO?.drawMsgCodeDto && (
                        <div style={{ color: '#f00', marginTop: '10px' }}>
                          {getLangGroup(
                            vehRegTopDataDTO?.drawMsgCodeDto?.codeCname,
                            vehRegTopDataDTO?.drawMsgCodeDto?.codePname,
                            vehRegTopDataDTO?.drawMsgCodeDto?.codeEname,
                          )}
                        </div>
                      )}
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'lottery_result' })}>
                      <div>{vehRegTopDataDTO?.drawResultNo}</div>
                    </Form.Item>
                  </>
                ) : null}
                {buyStatus && (
                  <Form.Item
                    label={intl.formatMessage({ id: 'payment_after_buy' })}
                    style={{ width: '100%' }}
                  >
                    <div>{vehRegTopDataDTO?.tempMountPlateNo}</div>
                  </Form.Item>
                )}
                {txnStatus === 'E' || txnStatus === 'F' ? null : (
                  <div style={{ width: '48%', marginBottom: '20px' }}>
                    {vehRegTopDataDTO?.canTempMountPlate === 'Y' && (
                      <Button type="primary" onClick={() => setIsModalOpen(true)}>
                        {intl.formatMessage({ id: 'use_license_plates' })}
                      </Button>
                    )}
                    {vehRegTopDataDTO?.canCancelTempMountPlate === 'Y' && (
                      <Popconfirm
                        title={intl.formatMessage({ id: 'confirm_cancel_license_plates' })}
                        onConfirm={() => {
                          cancelBuyCard();
                        }}
                        okText={intl.formatMessage({ id: 'yes' })}
                        cancelText={intl.formatMessage({ id: 'no' })}
                      >
                        <Button type="primary">
                          {intl.formatMessage({ id: 'cancel_license_plates' })}
                        </Button>
                      </Popconfirm>
                    )}
                  </div>
                )}
              </Form>
            </div>
          </div>

          {dspaFpDataDto?.dspaFpNo ? (
            <div className="my-card-item">
              <div className="my-card-title">
                {intl.formatMessage({ id: 'old_motorcycle_replace_subsidy' })}
              </div>
              <div className="my-card-body">
                <Form form={form} className="owner-card-form" layout="vertical" colon={false}>
                  <Form.Item label={intl.formatMessage({ id: 'fp_no' })} style={{ width: '100%' }}>
                    <div>{dspaFpDataDto?.dspaFpNo}</div>
                    <div style={{ color: '#084ab8' }}>{`${getLangGroup(
                      dspaFpDataDto?.dspaFpFeeCodeDto.codeCname,
                      dspaFpDataDto?.dspaFpFeeCodeDto.codePname,
                      dspaFpDataDto?.dspaFpFeeCodeDto.codeEname,
                    )}`}</div>
                  </Form.Item>
                </Form>
              </div>
            </div>
          ) : (
            ''
          )}
          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'car_owner_information' })}
            </div>
            <div className="owner-card-container-33d">
              {ownerDataList.map((item, index, arr) => (
                <div
                  className={
                    dspaFpDataDto?.dspaFpNo
                      ? 'owner-card-item-33d owner-card-3'
                      : 'owner-card-item-33d owner-card-2'
                  }
                  key={index}
                >
                  <div className="owner-card-title">
                    {intl.formatMessage({ id: 'car_owner_information' })}
                    {arr.length > 1 ? index + 1 : ''}
                  </div>
                  <Form form={form} className="owner-card-form" layout="vertical">
                    <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                      <div>
                        {item.ownerLastNameCn}
                        {item.ownerFirstNameCn}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                      <div>
                        {item.ownerLastNamePt} {item.ownerFirstNamePt}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                      <div>
                        {getLangGroup(
                          item.ownerIdentTypeCn,
                          item.ownerIdentTypePt,
                          item.ownerIdentTypeEn,
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'id_number' })}>
                      <div>{item.ownerIdentNo}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'contact_address' })}>
                      <div>{item.ownerAddr}</div>
                    </Form.Item>
                    <Form.Item></Form.Item>

                    {item.isShowFacialLabel ? (
                      <Form.Item label="">
                        <span
                          className={
                            item.verficationStatus == 'P'
                              ? 'statusBg1'
                              : item.verficationStatus == 'Y'
                              ? 'statusBg2'
                              : 'statusBg1'
                          }
                        >
                          {getLangGroup(
                            item?.verficationStatusCn,
                            item?.verficationStatusPt,
                            item?.verficationStatusEn,
                          )}
                        </span>
                      </Form.Item>
                    ) : null}

                    {item.isShowFacialButton ? (
                      <Form.Item label="">
                        <CountdownButton
                          item={item}
                          noticeStatus={noticeStatus}
                          setNoticeStatus={setNoticeStatus}
                          setTipMsg={setTipMsg}
                          txnId={txnId}
                          setDisableNextStep={setDisableNextStep}
                          fetchVehRegTxnData={fetchVehRegTxnData}
                        />
                      </Form.Item>
                    ) : null}
                  </Form>
                </div>
              ))}
            </div>
          </div>

          <div className="my-card-item">
            <div className="my-card-title flex-c-s">
              <div>{intl.formatMessage({ id: 'vehicle_information' })}</div>
              {!state ? (
                <CaretDownOutlined onClick={() => toggle()} />
              ) : (
                <CaretUpOutlined onClick={() => toggle()} />
              )}
            </div>
            <div className="my-card-body">
              <Form form={form} className="owner-card-form" layout="vertical">
                {false && (
                  <>
                    <Form.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                      <div>{ovsapVehDataDTO?.importNoFull}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                      <div>{ovsapVehDataDTO?.vtaNoFull}</div>
                    </Form.Item>
                  </>
                )}
                <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehBrandDescCn,
                      ovsapVehDataDTO?.vehBrandDescPt,
                      ovsapVehDataDTO?.vehBrandDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'style' })}>
                  <div>{ovsapVehDataDTO?.vehModel}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                  <div>{ovsapVehDataDTO?.vehBuildYear}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'color' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.colorDescCn,
                      ovsapVehDataDTO?.colorDescPt,
                      ovsapVehDataDTO?.colorDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehTypeDescCn,
                      ovsapVehDataDTO?.vehTypeDescPt,
                      ovsapVehDataDTO?.vehTypeDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehUsageDescCn,
                      ovsapVehDataDTO?.vehUsageDescPt,
                      ovsapVehDataDTO?.vehUsageDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                  <div>{ovsapVehDataDTO?.vin}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehSourceCtryDescCn' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehSourceCtryDescCn,
                      ovsapVehDataDTO?.vehSourceCtryDescPt,
                      ovsapVehDataDTO?.vehSourceCtryDescEn,
                    )}
                  </div>
                </Form.Item>

                <div
                  style={{ height: state ? 'auto' : '0px', overflow: 'hidden' }}
                  className="flex-c-s"
                >
                  <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehCateGoryDescCn,
                        ovsapVehDataDTO?.vehCateGoryDescPt,
                        ovsapVehDataDTO?.vehCateGoryDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                    <div>{ovsapVehDataDTO?.engineNo}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                    <div>{ovsapVehDataDTO?.engineNo2}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                    <div>{ovsapVehDataDTO?.engineNo3}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                    <div>{ovsapVehDataDTO?.engineNo4}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'cylinderQty' })}>
                    <div>{ovsapVehDataDTO?.cylinderQty}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'cylinderVol' })}>
                    <div>{`${ovsapVehDataDTO?.cylinderVol || 0} c.c`}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'maxPowerWithUnit' })}>
                    <div>{ovsapVehDataDTO?.maxPowerWithUnit}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'fuelTypeDescCn' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.fuelTypeDescCn,
                        ovsapVehDataDTO?.fuelTypeDescPt,
                        ovsapVehDataDTO?.fuelTypeDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehicle_size' })}>
                    {(ovsapVehDataDTO?.vehLength ||
                      ovsapVehDataDTO?.vehWidth ||
                      ovsapVehDataDTO?.vehHeight) && (
                      <div>
                        ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.vehLength} mm x (
                        {intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.vehWidth} mm x (
                        {intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.vehHeight} mm
                      </div>
                    )}
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'cabinTypeDescCn' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.cabinTypeDescCn,
                        ovsapVehDataDTO?.cabinTypeDescPt,
                        ovsapVehDataDTO?.cabinTypeDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'car_size' })}>
                    {(ovsapVehDataDTO?.cabinLength ||
                      ovsapVehDataDTO?.cabinWidth ||
                      ovsapVehDataDTO?.cabinHeight) && (
                      <div>
                        ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.cabinLength} mm x
                        ({intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.cabinWidth} mm x (
                        {intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.cabinHeight} mm
                      </div>
                    )}
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'seating_apacity' })}>
                    <div>{ovsapVehDataDTO?.seatQty}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'tyre_f_r_s' })}>
                    <div>{ovsapVehDataDTO?.tyreDesc}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'frontTyreQty' })}>
                    <div>{ovsapVehDataDTO?.frontTyreQty}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'rearTyreQty' })}>
                    <div>{ovsapVehDataDTO?.rearTyreQty}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'frontAxleQty' })}>
                    <div>{ovsapVehDataDTO?.frontAxleQty}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'rearAxleQty' })}>
                    <div>{ovsapVehDataDTO?.rearAxleQty}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehWeight' })}>
                    <div>
                      {ovsapVehDataDTO?.vehWeight} {ovsapVehDataDTO?.vehWeight && `kg`}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehLoadWeight' })}>
                    <div>
                      {ovsapVehDataDTO?.vehLoadWeight} {ovsapVehDataDTO?.vehLoadWeight && `kg`}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehGrossWeight' })}>
                    <div>
                      {ovsapVehDataDTO?.vehGrossWeight} {ovsapVehDataDTO?.vehGrossWeight && `kg`}
                    </div>
                  </Form.Item>{' '}
                </div>
              </Form>
            </div>
          </div>

          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'importer_information' })}
            </div>
            <div className="my-card-body">
              <Form form={form} className="owner-card-form" layout="vertical">
                <Form.Item label={intl.formatMessage({ id: 'importer_chinese_name' })}>
                  <div>{ovsapAgentCompData?.vtaCompCname}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'importer_portuguese_name' })}>
                  <div>{ovsapAgentCompData?.vtaCompPname}</div>
                </Form.Item>
              </Form>
            </div>
          </div>

          <div className="my-card-item">
            <div className="my-card-title">{intl.formatMessage({ id: 'agent_information' })}</div>
            <div className="my-card-body">
              <Form form={form} className="owner-card-form" layout="vertical">
                {/* <Form.Item label={intl.formatMessage({ id: 'agent_type' })}>
                    {getLangGroup(
                      ovsapAgentCompData?.agentCompIdentTypeDescCn,
                      ovsapAgentCompData?.agentCompIdentTypeDescPt,
                    )}
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'agent_no' })}>
                    <div>{ovsapAgentCompData?.agentCompIdentNo}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'agent_chinese_name' })}>
                    <div>{ovsapAgentCompData?.agentCompCname}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'agent_portuguese_name' })}>
                    <div>{ovsapAgentCompData?.agentCompPname}</div>
                  </Form.Item> */}
                <Form.Item label={intl.formatMessage({ id: 'agent_phone' })}>
                  <div>{ovsapAgentCompData?.agentContactPhone}</div>
                </Form.Item>
              </Form>
            </div>
          </div>
          {showSuccess && (txnStatus === 'E' || txnStatus === 'F') && (
            <div className="my-card-item">
              <div className="my-card-title">
                {intl.formatMessage({ id: 'submit_application_materials' })}
              </div>
              <div className="my-card-body">
                <Form form={form} className="owner-card-form" layout="vertical">
                  <Form.Item label={intl.formatMessage({ id: 'query_number' })}>
                    <div>{completeResultDTO?.spNo}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'service_procedure_name' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.serviceTitleRespDTO.serviceTitleCn,
                        completeResultDTO?.serviceTitleRespDTO.serviceTitlePt,
                        completeResultDTO?.serviceTitleRespDTO.serviceTitleEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'service_remarks' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.remarkCn,
                        completeResultDTO?.remarkPt,
                        completeResultDTO?.remarkEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'invoice_number' })}>
                    <div>{completeResultDTO?.invoiceNumber}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'trading_time' })}>
                    <div>
                      {completeResultDTO?.payDate
                        ? dayjs(completeResultDTO?.payDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'transaction_channel' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.payMethodCn,
                        completeResultDTO?.payMethodPt,
                        completeResultDTO?.payMethodEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'payment_method' })}>
                    <div>{completeResultDTO?.payMode}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'total_transaction_amount' })}>
                    <div>{completeResultDTO?.payAmount?.toLocaleString()}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'payment_status' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.payStatusCn,
                        completeResultDTO?.payStatusPt,
                        completeResultDTO?.payStatusEn,
                      )}
                    </div>
                  </Form.Item>
                  {/* <Form.Item label={intl.formatMessage({ id: 'submitted_documents' })}>
                    <div>
                      {(completeResultDTO?.docNameListCn ?? []).map((item) => (
                        <>
                          {item}
                          <br />
                        </>
                      ))}
                    </div>
                  </Form.Item> */}
                  <Form.Item label={intl.formatMessage({ id: 'application_status' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.txnStatusCn,
                        completeResultDTO?.txnStatusPt,
                        completeResultDTO?.txnStatusEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'establishment_time' })}>
                    <div>
                      {completeResultDTO?.txnStatusDate
                        ? dayjs(completeResultDTO?.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'newcar_owner_confirmation_time' })}>
                    <div>
                      {completeResultDTO?.verficationStatusDate
                        ? dayjs(completeResultDTO?.verficationStatusDate).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : ''}
                    </div>
                  </Form.Item>
                  {/* <Form.Item label={intl.formatMessage({ id: 'collection_ex_time' })}>
                    <div>
                      {completeResultDTO?.exRegDate
                        ? dayjs(completeResultDTO?.exRegDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item> */}
                  <Form.Item label={intl.formatMessage({ id: 'flow_creation_time' })}>
                    <div>
                      {completeResultDTO?.createDate
                        ? dayjs(completeResultDTO?.createDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                </Form>
              </div>
            </div>
          )}
          {vehReqTaxTxnCompleteDataDTO && (
            <div className="my-card-item">
              <div className="my-card-title">
                {intl.formatMessage({ id: '33Dii—submit_application_materials' })}
              </div>
              <div className="my-card-body">
                <Form form={form} className="owner-card-form" layout="vertical">
                  <Form.Item label={intl.formatMessage({ id: 'query_number' })}>
                    <div>{vehReqTaxTxnCompleteDataDTO?.spNo}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'service_procedure_name' })}>
                    <div>
                      {getLangGroup(
                        vehReqTaxTxnCompleteDataDTO?.serviceTitleRespDTO.serviceTitleCn,
                        vehReqTaxTxnCompleteDataDTO?.serviceTitleRespDTO.serviceTitlePt,
                        vehReqTaxTxnCompleteDataDTO?.serviceTitleRespDTO.serviceTitleEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'service_remarks' })}>
                    <div>
                      {getLangGroup(
                        vehReqTaxTxnCompleteDataDTO?.remarkCn,
                        vehReqTaxTxnCompleteDataDTO?.remarkPt,
                        vehReqTaxTxnCompleteDataDTO?.remarkEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'invoice_number' })}>
                    <div>{vehReqTaxTxnCompleteDataDTO?.invoiceNumber}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'trading_time' })}>
                    <div>
                      {vehReqTaxTxnCompleteDataDTO?.payDate
                        ? dayjs(vehReqTaxTxnCompleteDataDTO?.payDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'transaction_channel' })}>
                    <div>
                      {getLangGroup(
                        vehReqTaxTxnCompleteDataDTO?.payMethodCn,
                        vehReqTaxTxnCompleteDataDTO?.payMethodPt,
                        vehReqTaxTxnCompleteDataDTO?.payMethodEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'payment_method' })}>
                    <div>{vehReqTaxTxnCompleteDataDTO?.payMode}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'total_transaction_amount' })}>
                    <div>{vehReqTaxTxnCompleteDataDTO?.payAmount?.toLocaleString()}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'payment_status' })}>
                    <div>
                      {getLangGroup(
                        vehReqTaxTxnCompleteDataDTO?.payStatusCn,
                        vehReqTaxTxnCompleteDataDTO?.payStatusPt,
                        vehReqTaxTxnCompleteDataDTO?.payStatusEn,
                      )}
                    </div>
                  </Form.Item>
                  {/* <Form.Item label={intl.formatMessage({ id: 'submitted_documents' })}>
                    <div>
                      {(vehReqTaxTxnCompleteDataDTO?.docNameListCn ?? []).map(() => (item) => (
                        <>
                          {item}
                          <br />
                        </>
                      ))}
                    </div>
                  </Form.Item> */}
                  <Form.Item label={intl.formatMessage({ id: 'application_status' })}>
                    <div>
                      {getLangGroup(
                        vehReqTaxTxnCompleteDataDTO?.txnStatusCn,
                        vehReqTaxTxnCompleteDataDTO?.txnStatusPt,
                        vehReqTaxTxnCompleteDataDTO?.txnStatusEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'establishment_time' })}>
                    <div>
                      {vehReqTaxTxnCompleteDataDTO?.txnStatusDate
                        ? dayjs(vehReqTaxTxnCompleteDataDTO?.txnStatusDate).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : ''}
                    </div>
                  </Form.Item>
                  {/* <Form.Item label={intl.formatMessage({ id: 'newcar_owner_confirmation_time' })}>
                    <div>
                      {vehReqTaxTxnCompleteDataDTO?.verficationStatusDate
                        ? dayjs(vehReqTaxTxnCompleteDataDTO?.verficationStatusDate).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'collection_ex_time' })}>
                    <div>
                      {vehReqTaxTxnCompleteDataDTO?.exRegDate
                        ? dayjs(vehReqTaxTxnCompleteDataDTO?.exRegDate).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : ''}
                    </div>
                  </Form.Item> */}
                  <Form.Item label={intl.formatMessage({ id: 'flow_creation_time' })}>
                    <div>
                      {vehReqTaxTxnCompleteDataDTO?.createDate
                        ? dayjs(vehReqTaxTxnCompleteDataDTO?.createDate).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : ''}
                    </div>
                  </Form.Item>
                </Form>
              </div>
            </div>
          )}
        </div>

        <Modal
          title={intl.formatMessage({ id: 'use_license_plates' })}
          visible={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={[
            <Button key="close" onClick={handleCancel}>
              {intl.formatMessage({ id: 'turn_off' })}
            </Button>,
            <Button
              key="nextStep"
              type="primary"
              disabled={validatePlateNoStatus !== 'success'}
              onClick={handleOk}
            >
              {intl.formatMessage({ id: 'ok' })}
            </Button>,
          ]}
        >
          <div style={{ padding: '30px' }}>
            <div style={{ display: 'flex' }}>
              <Input
                value={customPlateNo}
                onChange={(e) => {
                  setCustomPlateNo(e.target.value);
                }}
              />
              &nbsp;
              <Button
                className="pure"
                type="primary"
                onClick={() => {
                  onSerach();
                }}
              >
                {intl.formatMessage({ id: 'search' })}
              </Button>
            </div>
            {validatePlateNoStatus === 'success' && (
              <p style={{ marginTop: '10px', color: '#13a07b' }}>{validatePlateNoMsg}</p>
            )}
            {validatePlateNoStatus === 'error' && (
              <p style={{ marginTop: '10px', color: '#ff4d4f' }}>{validatePlateNoMsg}</p>
            )}

            {/* {isRight ? (
              <p style={{ marginTop: '10px', color: '#13a07b' }}>{intl.formatMessage({ id: 'license_plate_matches' })}</p>
            ) : (
              <></>
            )} */}
          </div>
        </Modal>

        <Modal
          title={intl.formatMessage({ id: 'vehicle_information' })}
          cancelText={intl.formatMessage({ id: 'exit' })}
          okText="保存"
          visible={editCarStatus}
          onOk={handleCarOk}
          onCancel={handleCarCancel}
        >
          <Form form={vehForm} className={px('form')} layout="vertical">
            {/* <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'vehicle_information' })}</div> */}
            <div className={px('sectionBody')}>
              <Form.Item
                label={intl.formatMessage({ id: 'vehicle_usage' })}
                required
                name="vehUsageCode"
              >
                <Select>
                  {vehUsageOpts.map((item) => (
                    <Select.Option value={item.codeKey}>{item.codeCname}</Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'tyre_f_r_s' })} required name="tyreId">
                <Select>
                  {tyreOpts.map((item, index) => (
                    <Select.Option value={item.tyreId}>{item.tyreDesc}</Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label={intl.formatMessage({ id: 'color' })} required name="colorCode">
                <Select
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  options={colorOpts.map((item) => ({
                    label: item.codeCname,
                    value: item.codeKey,
                  }))}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </div>
    </Watermark>
  );
};

export default Preview;
