import { get, post } from '@/utils/fetch';
import type { FetchResponse } from '@/utils/fetch';

// 查詢車輛資料
export const validateVehReg = (params: validateVehRegParams) =>
  post<FetchResponse<string>>('/oa/vem/validateVehReg', { params });

// 創建0033D流水
export const createVehRegTxn = (params: createVehRegTxnParams) =>
  post<FetchResponse<createVehRegTxnRes>>('/oa/vem/createVehRegTxn', params);


// 加載資料
export const getVehRegTxnData = (txnId: string) =>
  get<FetchResponse<getEXTxnDataRes>>('/oa/vem/getVehRegTxnData', { txnId });

// 查詢購買車牌
export const validateCustomPlateNo = (txnId: string, customPlateNo: string) =>
  get<FetchResponse<validateCustomPlateNo>>('/oa/txn/validateCustomPlateNo', { txnId, customPlateNo });

// 保存購買車牌
export const saveCustomPlateNo = (txnId: string, customPlateNo: string) =>
  get<FetchResponse<unknown>>('/oa/txn/saveCustomPlateNo', { txnId, customPlateNo });

// 取消購買車牌
export const cancelCustomPlateNo = (txnId: string) =>
  get<FetchResponse<unknown>>('/oa/txn/cancelCustomPlateNo', { txnId });
