@prefix: vta-record;

.@{prefix} {
  &- {
    &root {
      padding: 0 10% 88px;
      height: calc(100% - 68px);
      overflow-y: auto;


      .ant-form {

      }
      .ant-form-item-label>label {

      }
      .ant-select {

      }
    }

    &title {
        font-size: 20px;
        font-weight: 700;
        padding: 20px 24px;
        border-bottom: 1px solid rgba(0,0,0,.15)
    }

    &search-query-btn {
      width: 100%;
      display: flex;
      margin-bottom: 20px;
      justify-content: space-between;
    }

    &stepinfo {
        display: flex
    }

    &step {
        margin-top: 20px;
        padding: 0 22px;
        width: 240px;
        min-width: -webkit-fit-content;
        min-width: -moz-fit-content;
        min-width: fit-content;
        border-right: 1px solid rgba(0,0,0,.15)
    }

    &step h3 {
        font-size: 16px;
        line-height: 22px;
        font-weight: 700;
        padding: 0 2px 16px
    }

    &step .ant-steps-small {
        font-size: 16px
    }

    &step .ant-steps-small .ant-steps-item {
        height: 70px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon {
        background-color: rgba(8,74,184,.25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-icon {
        background-color: @brand-primary
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-tail:after {
        background-color: @brand-primary;
        height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-icon {
        background-color: rgba(255,193,7,.25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-icon {
        background-color: #ffc107
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-tail:after {
        background-color: #c4c4c4;
        height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-icon {
        background-color: hsla(0,0%,76.9%,.25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-icon {
        background-color: #c4c4c4
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-tail:after {
        background-color: #c4c4c4;
        height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait:last-child .ant-steps-item-tail:after {
        display: none
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container {
        height: 40px;
        min-height: 40px;
        display: flex;
        align-items: center
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-tail {
        bottom: 0;
        top: 40px;
        left: 14px;
        padding: 0
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        margin-right: 6px;
        cursor: pointer;
        flex-shrink: 0
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon .ant-steps-icon {
        height: 18px;
        min-height: 18px;
        display: flex;
        align-items: center;
        width: 18px;
        line-height: 18px;
        left: 5px;
        top: 5px;
        font-size: 14px;
        justify-content: center;
        color: #fff;
        border-radius: 50%
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content {
        height: 40px;
        min-height: 40px;
        display: flex;
        align-items: center;
        line-height: 16px
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content .ant-steps-item-title {
        font-size: 16px;
        color: #333
    }

    &form {
        flex: 1 1;
        margin-top: 20px
    }

    &form h3 {
        font-size: 16px;
        color: rgba(0,0,0,.85);
        font-weight: 700;
        margin-left: 24px
    }

    &form>.pc-pay-ui-root {
        margin-left: 24px
    }

    &form .pc-pay-ui-top-amount {
        line-height: 65px
    }

    &form .pc-pay-ui-top-amount span {
        position: relative
    }

    &form .pc-pay-ui-top-amount span:first-child {
        font-size: 16px;
        color: #666;
        top: -2px
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
        font-size: 24px;
        top: -2px
    }

    &formTitle {
        display: flex;
        align-items: center;
        font-weight: 700;
        margin-left: 24px;
        font-size: 18px;
        color: #232323;
        padding-bottom: 10px
    }

    &formTitle:before {
        display: block;
        content: "";
        width: 4px;
        height: 16px;
        background: @brand-primary;
        margin-right: 8px
    }

    &spin {
        width: 100%;
        height: 100%;
        padding: 45%
    }

    &footer-button {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 68px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 0 10%;
        background: #fff;
        box-shadow: 0 -2px 8px 0 rgba(0,0,0,.1)
    }

    &footer-button .ant-btn {
        min-width: 128px;
        height: 48px;
        line-height: 48px;
        border-radius: 24px;
        margin-left: 12px;
        font-size: 16px;
        padding: 0 5px
    }

    &footer-button .ant-btn.ant-btn-default {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary
    }






    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px
    }

    &sectionTitle:before {
      display: block;
      content: "";
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px
    }

    &sectionBody {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .ant-form-item {
        width: 30%;
      }

      .ant-btn.ant-btn-default {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;
        &.pure {
          border-radius: 2px;
          color: rgba(0, 0, 0, .85);
          border: 1px solid #f0f0f0;
          font-size: 14px;
        }

        &.btn-active {
          background-color: @brand-primary;
          color: #fff;
        }
      }


      .card {
        box-shadow: 0 5px 15px 0 rgba(0, 0, 0, .12);
        margin-bottom: 24px;
        border-radius: 8px;
        overflow: hidden;
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 58px;
          padding: 0 20px;
          color: #ffffff;
          .card-title {
            flex: 1;
            padding: 0 12px;
            font-size: 18px;
            font-weight: 600;
          }
          .status {
            background: #fff;
            border-radius: 15px;
            padding: 0 10px;
            line-height: 30px;
            font-size: 14px;
            text-align: center;
          }
          .status-paying {
            color: #E9B745;
          }
          .status-paied {
            color: #084AB8;
          }
          .status-passed {
            color: #13A07B;
          }
        }
        .card-body {
          min-height: 58px;
          padding: 18px 20px 0;
          display: flex;
          flex-wrap: wrap;
          .item {
            width: 50%;
            display: flex;
            margin-bottom: 16px;
            .label {
              width: 30%;
              font-size: 16px;
              color: #6c6c6c;
              overflow-wrap: anywhere;
            }
            .value {
              flex: 1 1;
              color: #232323;
              font-size: 16px;
              font-family: "PingFang SC", "Microsoft YaHei", "Ming-MSCS-DSI";
              overflow-wrap: anywhere;
            }
          }
        }
        .card-footer {
          height: 62px;
          padding: 13px 0;
          display: flex;
          justify-content: space-between;
          margin: 0 24px;
          border-top: 1px solid #f0f0f0;
          .right {
            .ant-btn {
              margin-right: 8px;
            }
          }
        }
      }
    }



  }
}
