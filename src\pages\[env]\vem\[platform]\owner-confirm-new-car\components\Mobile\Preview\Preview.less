@prefix: owner-confirm-new-car-preview-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 20px 12px;
      padding-bottom: 20px;
      font-size: 0.16rem;
      border-radius: 10px 10px 0px 0px;
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 700;
      padding: 5px 20px;
      min-height: 41px;
      font-size: 0.18rem;
      color: #ffffff;
      line-height: 21px;
      background: @brand-primary;
      border-radius: 10px 10px 0px 0px;
      display: none;
    }
    &sectionBody {
      .ant-collapse {
        color: white;
        border: 1px solid #f0f0f0;
        margin-bottom: 20px;
        border-radius: 12px 12px;
        overflow: hidden;
        &:nth-child(4n + 1) {
          background: #d9ebe7;
        }
        &:nth-child(4n + 2) {
          background: #dfeaf5;
        }
        &:nth-child(4n + 3) {
          background: #faedd9;
        }
        &:nth-child(4n + 4) {
          background: #f7e4da;
        }
        .ant-collapse-header {
          padding: 12px 16px 12px 40px !important;
          color: #000;
          font-size: 16px;
          cursor: pointer;
        }
        .ant-collapse-content {
          color: rgba(0, 0, 0, 0.85);
          background-color: #fff;
          border-top: 1px solid #f0f0f0;
        }
        .form-title {
          font-size: 16px;
          color: @brand-primary;
        }
        .form-title2 {
          display: flex;
          align-items: center;
          font-weight: 700;
          font-size: 16px;
          color: #232323;
          margin: 0 24px;
          padding-bottom: 10px;
        }
        .form-title2:before {
          display: block;
          content: '';
          width: 4px;
          height: 16px;
          background: @brand-primary;
          margin-right: 8px;
        }
        .disAgree {
          padding-top: 20px;
          padding-bottom: 20px;
        }
      }
      .ant-form {
        padding: 0 24px;
        .ant-form-item {
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
              // color: @brand-primary;
            }
          }

          .statusBg1 {
            padding: 0 2px;
            color: #f33b40;
          }
          .statusBg2 {
            padding: 0 2px;
            color: @brand-primary;
          }
          
        }
      }
    }
  }
}
.owner-title-mobile{
  height: 50px;
  background-color: @brand-primary;
  font-size: 0.16rem;
  display: flex;
  align-items: center;
  padding-left: 40px;
  margin-bottom: 20px;
  border-radius: 12px;
  background-color: #d9ebe7;
  color: #000;
}