@prefix: page-batch-payment;

.@{prefix} {
  &- {
    &root {
      padding: 0 10% 88px;
      height: calc(100% - 68px);
      overflow-y: auto;

      .ant-table {
        font-size: 16px !important;
        border-radius: 12px;
        border: 1px solid #f0f0f0;
        overflow: hidden;
      }

      
    }

    
    &pay-way-mobile {
      border-radius: 12px;
border: 1px solid #f0f0f0;
overflow: hidden;
padding-bottom: 20px;
margin-top: 50px;
.ant-radio-checked {
  &:after {
    border-color: #084ab8;
  }
  .ant-radio-inner {
    border-color: #084ab8;
    &::after {
      background-color: #084ab8;
    }
  }
}
    }

    &title {
      font-size: 20px;
      font-weight: 700;
      padding: 20px 24px;
      border-bottom: 1px solid rgba(0,0,0,.15)
    }

    &stepinfo {
      display: flex
    }


    &form {
      flex: 1 1;
      margin-top: 20px
    }

    &body-root {
      margin-left: 24px;
      .ant-table-thead>tr>th {
        background: @brand-primary;
        color: #fff;
      }
    }

    &formTitle {
      padding: 16px;
      background: #E9B745;
      color: #fff;
      font-size: 16px;
    }


    &footer-button {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10%;
      background: #fff;
      box-shadow: 0 -2px 8px 0 rgba(0,0,0,.1)
    }

    &footer-button .ant-btn {
      min-width: 128px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
      margin-left: 12px;
      font-size: 16px;
      padding: 0 5px
    }

    &footer-button .ant-btn.ant-btn-default {
      background: #fff;
      color: @brand-primary;
      border: 1px solid @brand-primary
    }

  }
}
