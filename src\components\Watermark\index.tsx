// Watermark.tsx
import React, { useEffect, useMemo } from 'react';

interface WatermarkProps {
  text?: string;
  fontSize?: number;
  color?: string;
  gap?: number;
  angle?: number;
  zIndex?: number;
  children?: React.ReactNode;
}

const Watermark: React.FC<WatermarkProps> = ({
  text = 'Confidential',
  fontSize = 16,
  color = 'rgba(0, 0, 0, 0.1)',
  gap = 200,
  angle = -20,
  zIndex = 9999,
  children,
}) => {
  const base64Url = useMemo(() => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const width = gap;
    const height = gap;
    canvas.width = width;
    canvas.height = height;

    if (ctx) {
      ctx.clearRect(0, 0, width, height);
      ctx.fillStyle = color;
      ctx.font = `${fontSize}px sans-serif`;
      ctx.translate(width / 2, height / 2);
      ctx.rotate((Math.PI / 180) * angle);
      ctx.fillText(text, -ctx.measureText(text).width / 2, fontSize / 2);
    }

    return canvas.toDataURL();
  }, [text, fontSize, color, gap, angle]);

  useEffect(() => {
    const watermarkDiv = document.createElement('div');
    watermarkDiv.setAttribute('id', 'watermark-container');
    watermarkDiv.style.position = 'fixed';
    watermarkDiv.style.top = '0';
    watermarkDiv.style.left = '0';
    watermarkDiv.style.width = '100vw';
    watermarkDiv.style.height = '100vh';
    watermarkDiv.style.pointerEvents = 'none';
    watermarkDiv.style.zIndex = zIndex.toString();
    watermarkDiv.style.backgroundImage = `url(${base64Url})`;
    watermarkDiv.style.backgroundRepeat = 'repeat';

    document.body.appendChild(watermarkDiv);

    return () => {
      const dom = document.getElementById('watermark-container');
      if (dom) document.body.removeChild(dom);
    };
  }, [base64Url, zIndex]);

  return <>{children}</>;
};

export default Watermark;
