import React, {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import pc from 'prefix-classnames';
import './Form2.less';

import { getLocale, useIntl } from 'umi';
import type { FormProps } from 'antd';
import { Alert, Button, Checkbox, Col, Form, Input, message, Modal, Row, Select } from 'antd';
import { MoAddress } from '@gov-mo/components';
import zhHant from '@gov-mo/components/esm/MoAddress/locales/zh-Hant';
import pt from '@gov-mo/components/esm/MoAddress/locales/pt';

import { deleteTxnOwners, getTxnOwners, saveTxnOwners } from '@/services/0028F';
import { LANGUAGE_TYPE } from '@/utils/const';
import { getSysCodesByCodeType } from '@/services/publicApi';
import { getLangGroup, getResponseMessage, Lang } from '@/locales/lang';
import SpinContext from '../../../../../../../components/SpinContext';

const px = pc('form2-28f');

const generateRandom = () => Math.floor(Math.random() * 9000000000) + 1000000000;

export interface FormTwoProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setEntranceInfo: (data) => void;
  setDisablePrevStep: (boolean) => void;

  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;

  entranceInfo: getRivmByDSFM4Res | undefined;
  txnId: string | undefined;
}

export type FormTwoOwnerRef = {
  tempStore: () => Promise<void>;
};

const FormTwo = forwardRef((props: FormTwoProps, ref: React.ForwardedRef<FormTwoOwnerRef>) => {
  useImperativeHandle(ref, () => ({
    // 暫存
    async tempStore() {
      try {
        await form.validateFields();
        await submit(form.getFieldsValue(), false);
      } catch {}
    },
  }));

  const { className = '', setDisableNextStep, ...otherProps } = props;
  const { contentNextStep, contentForNextStep, txnId } = otherProps;

  const [tipMsg, setTipMsg] = useState('');
  const [quotaNumeratorTip, setQuotaNumeratorTip] = useState('');

  const [messageApi, contextHolder] = message.useMessage();
  const intl = useIntl();
  const [form] = Form.useForm();

  const [isModalOpen, setModalOpen] = useState(false);
  const [currModalDataIdx, setCurrModalDataIdx] = useState(-1);
  const [currModalDataField, setCurrModalDataField] = useState('');
  const [vehType, setVehType] = useState<string>();

  const [formKeys, setFormKeys] = useState<number[]>([]);
  // 表单数据
  const saveTxnOwnersParamsInit = useMemo(
    () => ({
      ownerId: '', // PK
      txnOwnerId: '', // ownerId 的替换
      txnId: '', // FK of OVSAP_TXN.TXN_ID

      ownerIdentType: 'BM', // 車主證件類型
      ownerIdentNo: '', // {intl.formatMessage({ id: 'vehicle_owner_id_number' })}
      ownerLastNameCn: '', // 車主中文姓
      ownerLastNamePt: '', // 車主葡文姓
      ownerFirstNameCn: '', // 車主中文名
      ownerFirstNamePt: '', // 車主葡文名

      region: '', // 地區
      streetCode: '', // 大廈/街道
      bldingNo: '', // 門牌
      bldingFloor: '', // 樓層
      bldingBlock: '', // 座/室
      ownerAddr: '', // 中文詳細地址
      addrType: 'F', // 选择澳门为U,不是澳门就是F

      addrLang: LANGUAGE_TYPE[getLocale()] || 'C', // 地址語言（使用一戶通登錄的語言）C / P / E

      checkBox: false, // 各共有人所佔的份額相同
      quotaNumerator: '', // DSAJ業權分子數
      quotaDenominator: '', // DSAJ所有權分母
      dsajAddrTicked: 'Y', // 選中：以聯絡地址作為汽車登記上的居所/住所
      dsajAddrTickedChecked: true,

      dsajRegion: '', // 所有權地址：地區
      dsajStreetCode: '', // 所有權地址：大廈/街道
      dsajBldingNo: '', // 所有權地址：門牌
      dsajBldingFloor: '', // 所有權地址：樓層
      dsajBldingBlock: '', // 所有權地址：座/室
      dsajAddr: '', // 所有權地址

      verficationNoticeId: '1', // 若有發送車主人面識別提示信息，記錄其對應的noticeId
      verficationStatus: 'Y', // 是否通過人面識別（Y=是；N=否；W=waiting）
      ownerStatus: 'A', // 車主狀態: A=有效; D=删除; H=歷史; U=Undefined
    }),
    [],
  );

  const [tempRegion, setTempRegion] = useState('澳門');
  const [tempStreetCode, setTempStreetCode] = useState('');
  const [tempBldingNo, setTempBldingNo] = useState('');
  const [tempBldingFloor, setTempBldingFloor] = useState('');
  const [tempBldingBlock, setTempBldingBlock] = useState('');
  const [tempRemark, setTempRemark] = useState('');

  const [isAverageQuotaNumerator, setIsAverageQuotaNumerator] = useState<boolean>(false); // 各共有人所佔的份額是否相同
  const [dsajAddrTickedCheckedInfos, setDsajAddrTickedCheckedInfos] = useState<
    Record<string, boolean>
  >({});
  const [certificateOpts, setCertificateOpts] = useState<sysCodesByCodeTypeOpt[]>();
  const [ownerSeqInfo, setOwnerSeqInfo] = useState<Record<string, number>>({});
  const { setLoading } = useContext(SpinContext);
  const onChangeBydsajAddrTicked = (v: any, formKey: number) => {
    const data = form.getFieldsValue();
    setDsajAddrTickedCheckedInfos((prev) => ({ ...prev, [formKey]: v.target.checked }));
    if (v.target.checked) {
      form.setFieldsValue({
        ...data,
        [formKey]: { ...data[formKey], dsajAddr: data[formKey].ownerAddr },
      });
      return;
    }

    form.setFieldsValue({ ...data, [formKey]: { ...data[formKey], dsajAddr: '' } });
  };

  const maxOwnerSeq = useMemo(() => {
    let maxSeq = 0;
    Object.keys(ownerSeqInfo).forEach((key) => {
      maxSeq = Math.max(ownerSeqInfo[key] ?? 0, maxSeq);
    });
    return maxSeq;
  }, [ownerSeqInfo]);

  const onFinish: FormProps<saveTxnOwnersParams>['onFinish'] = async (values) => {
    submit(values)
      .then(() => {})
      .finally(() => {
        setDisableNextStep(false);
      });
  };

  const submit = async (values: saveTxnOwnersParams, next: boolean = true) => {
    return new Promise(async (resolve, reject) => {
      const keys = Object.keys(values);
      // 檢驗所佔份額是否為1
      let isQuotaNumerator = 0;
      keys.forEach((key) => {
        const data = values[key];
        if (data?.quotaNumerator && data?.quotaDenominator) {
          isQuotaNumerator += Number(data?.quotaNumerator) / Number(data?.quotaDenominator);
        }
      });
      if (isQuotaNumerator !== 1 && ['L', 'P'].includes(vehType || '') && keys.length > 1) {
        messageApi.open({
          type: 'error',
          content: intl.formatMessage({ id: 'must_1' }),
        });
        return reject();
      }

      const dataArr: saveTxnOwnersParams[] = [];
      keys.forEach((key, index) => {
        const data = values[key];
        data.txnOwnerId = data.txnOwnerId ?? saveTxnOwnersParamsInit.txnOwnerId;
        data.txnId = data.txnId ?? txnId;
        data.addrLang = data.addrLang ?? saveTxnOwnersParamsInit.addrLang;
        data.addrType = data.addrType ?? saveTxnOwnersParamsInit.addrType;
        data.verficationNoticeId =
          data.verficationNoticeId ?? saveTxnOwnersParamsInit.verficationNoticeId;
        data.verficationStatus =
          data.verficationStatus ?? saveTxnOwnersParamsInit.verficationStatus;
        data.ownerStatus = data.ownerStatus ?? saveTxnOwnersParamsInit.ownerStatus;
        data.dsajAddrTicked = data.dsajAddrTickedChecked ? 'Y' : 'N';
        // data.ownerSeq = index + 1;
        data.ownerSeq = ownerSeqInfo[key];
        data.checkBox = isAverageQuotaNumerator ? 'Y' : 'N';
        if (data?.quotaNumerator || data?.quotaNumerator === 0) {
          data.quotaNumerator = Number(data?.quotaNumerator);
        }
        if (data?.quotaDenominator || data?.quotaDenominator === 0) {
          data.quotaDenominator = Number(data?.quotaDenominator);
        }
        dataArr.push(data);
      });

      const isValid = dataArr.every(
        (item) =>
          (item.ownerLastNameCn && item.ownerFirstNameCn) ||
          (item.ownerLastNamePt && item.ownerFirstNamePt),
      );

      if (!isValid) {
        messageApi.open({
          type: 'error',
          content: intl.formatMessage({ id: 'cp_must_enter' }),
        });
        return reject();
      }

      const ret = await saveTxnOwners(dataArr).catch((e) => {
        setTipMsg(e.message);
      });
      if (!ret) {
        return reject();
      }

      if (ret.code !== '0') {
        setTipMsg(getResponseMessage(ret));
        return reject();
      }

      if (!next) {
        message.success(intl.formatMessage({ id: 'cp_save_success' }));
        fn();
      }
      next && contentForNextStep(2);
      resolve('');
    });
  };

  const onFinishFailed: FormProps<saveTxnOwnersParams>['onFinishFailed'] = (errorInfo) => {
    setDisableNextStep(false);
  };

  function handleOk() {
    if (tempRegion && tempStreetCode && tempBldingNo && tempBldingFloor && tempBldingBlock) {
      const addr =
        tempRegion +
        tempStreetCode +
        (tempBldingNo ? `${tempBldingNo}號` : '') +
        (tempBldingFloor ? `${tempBldingFloor}樓` : '') +
        (tempBldingBlock ? `${tempBldingBlock}室` : '') +
        (tempRemark ? tempRemark : '');

      const data = form.getFieldValue(currModalDataIdx);
      if (data) {
        if (currModalDataField === 'ownerAddr') {
          data.region = tempRegion;
          data.streetCode = tempStreetCode;
          data.bldingNo = tempBldingNo;
          data.bldingFloor = tempBldingFloor;
          data.bldingBlock = tempBldingBlock;
          data.ownerAddr = addr;
          // if (isReadonly) {
          //   data.dsajAddr = addr
          // }
          if (data.dsajAddrTicked === '1') {
            data.dsajRegion = tempRegion;
            data.dsajStreetCode = tempStreetCode;
            data.dsajBldingNo = tempBldingNo;
            data.dsajBldingFloor = tempBldingFloor;
            data.dsajBldingBlock = tempBldingBlock;
            data.dsajAddr = addr;
          }
        } else {
          data.dsajRegion = tempRegion;
          data.dsajStreetCode = tempStreetCode;
          data.dsajBldingNo = tempBldingNo;
          data.dsajBldingFloor = tempBldingFloor;
          data.dsajBldingBlock = tempBldingBlock;
          data.dsajAddr = addr;
        }

        form.setFieldsValue({ currModalDataIdx: { ...data } });
      }

      setTempStreetCode('');
      setTempBldingFloor('');
      setTempBldingBlock('');
      setTempRemark('');

      setModalOpen(false);
    } else {
      messageApi.open({
        type: 'error',
        content: intl.formatMessage({ id: 'enter_address' }),
      });
    }
  }

  const handleResAddrSplit = (v, formKey: string, field: string) => {
    const data = form.getFieldValue(formKey);
    if (data) {
      if (field === 'ownerAddr') {
        data.addrType = v?.areaCode === 'MO' ? 'U' : 'F';
        data.region = v?.region;
        data.streetCode = `${v?.streetName} ${v?.building}`;
        data.bldingNo = v?.streetNo;
        data.bldingFloor = v?.floor;
        data.bldingBlock = v?.unit;
        data.ownerAddr = v?.fullAddr;
        if (data.dsajAddrTicked === '1') {
          data.dsajRegion = v?.region;
          data.dsajStreetCode = `${v?.streetName} ${v?.building}`;
          data.dsajBldingNo = v?.streetNo;
          data.dsajBldingFloor = v?.floor;
          data.dsajBldingBlock = v?.unit;
          data.dsajAddr = v?.fullAddr;
        }
        if (dsajAddrTickedCheckedInfos[formKey]) {
          data.dsajAddr = v?.fullAddr;
        }
      } else {
        data.dsajRegion = v?.region;
        data.dsajStreetCode = `${v?.streetName} ${v?.building}`;
        data.dsajBldingNo = v?.streetNo;
        data.dsajBldingFloor = v?.floor;
        data.dsajBldingBlock = v?.unit;
        data.dsajAddr = v?.fullAddr;
      }
      form.setFieldsValue({ [formKey]: { ...data } });
    }
  };

  function handleCancel() {
    setModalOpen(false);
  }

  useEffect(() => {
    setLoading(true);
    contentForNextStep(1);

    return () => {
      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    if (!txnId) {
      return;
    }
    if (contentNextStep > 0) {
      const fn = async () => {
        setTipMsg('');
        form.submit();
      };
      fn();
    }
  }, [txnId, contentNextStep]);

  useEffect(() => {
    setDisableNextStep(true);
    getSysCodesByCodeType('21001', 'A')
      .then((ret) => {
        if (ret?.code === '0') {
          setCertificateOpts(ret.dataArr);
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      })
      .finally(() => {
        setDisableNextStep(false);
      });
  }, []);

  const fn = useCallback(async () => {
    if (!txnId) return setLoading(false);
    const ret = await getTxnOwners(txnId)
      .catch((e) => {
        setTipMsg(e.message);
      })
      .finally(() => {
        setLoading(false);
      });

    if (!ret) {
      return;
    }
    if (ret.code !== '0') {
      setTipMsg(getResponseMessage(ret));
      return;
    }

    const fields = {
      C: 'dsajQuotaMsgCname',
      E: 'dsajQuotaMsgEname',
      P: 'dsajQuotaMsgPname',
    };

    const { dataArr, data = {} as saveTxnOwnersParams } = ret;
    setQuotaNumeratorTip(data[fields[saveTxnOwnersParamsInit.addrLang]] || '');
    setVehType(data.vehType);

    const txnKeys = Object.keys(saveTxnOwnersParamsInit);
    const keys: number[] = [];
    const values = {};
    if (!dataArr.length) {
      const key = generateRandom();
      setFormKeys([key]);

      values[key] = { ...saveTxnOwnersParamsInit, dsajAddrTicked: '1' };
      form.setFieldsValue(values);
      setOwnerSeqInfo((prev) => ({ ...prev, [key]: 1 }));
      return;
    }

    const dsajAddrTickedCheckedObj: Record<string, boolean> = {};

    dataArr.forEach((item, index) => {
      const key = generateRandom();
      keys.push(key);
      dsajAddrTickedCheckedObj[key] = item.dsajAddrTicked === 'Y';
      const data = { dsajAddrTickedChecked: item.dsajAddrTicked === 'Y' };
      txnKeys.forEach((txnKey) => {
        if (undefined !== item[txnKey]) {
          data[txnKey] = item[txnKey];
        }
      });
      values[key] = { ...data };
      setOwnerSeqInfo((prev) => ({ ...prev, [key]: item.ownerSeq }));
    });
    setDsajAddrTickedCheckedInfos((prev) => ({ ...prev, ...dsajAddrTickedCheckedObj }));
    setFormKeys([...keys]);
    form.setFieldsValue(values);
    setIsAverageQuotaNumerator(dataArr[0]?.checkBox === 'Y');
  }, [txnId, saveTxnOwnersParamsInit, form]);

  useEffect(() => {
    fn();
  }, [fn]);

  useEffect(() => {
    const len = formKeys.length;
    if (len > 0 && isAverageQuotaNumerator) {
      const val = form.getFieldsValue();
      let values = { ...val };
      formKeys.map((formKey) => {
        values = {
          ...values,
          [formKey]: { ...values[formKey], quotaNumerator: 1, quotaDenominator: len },
        };
      });
      form.setFieldsValue(values);
    }
  }, [isAverageQuotaNumerator, formKeys, form]);

  const handleDelete = (data, formKey: number) => {
    const keys = formKeys.filter((_item, _index) => _item !== formKey);
    setFormKeys([...keys]);
    const copyData = { ...data };
    delete copyData[formKey];
    form.setFieldsValue(copyData);
    setOwnerSeqInfo((prev) => {
      delete prev[formKey];
      return { ...prev };
    });
  };

  return (
    <>
      {tipMsg && (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      {contextHolder}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('sectionTitle')}>
          <span>{intl.formatMessage({ id: 'apply_information' })}</span>
        </div>

        {formKeys.length > 1 && ['L', 'P'].includes(vehType || '') ? (
          <div style={{ marginBottom: 12, lineHeight: 1 }}>
            <Checkbox
              className="preview-28f-checkbox"
              checked={isAverageQuotaNumerator}
              onChange={(e) => {
                setIsAverageQuotaNumerator(e.target.checked);
              }}
              style={{ fontSize: '18px' }}
            >
              {intl.formatMessage({ id: 'each_same_share' })}
            </Checkbox>
          </div>
        ) : null}

        <Form
          form={form}
          className="root"
          layout="vertical"
          preserve={false}
          scrollToFirstError
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
        >
          <div className="my-card-container">
            {formKeys.map((formKey, index, arr) => (
              <div className="my-card-item" key={formKey}>
                <div>
                  {/* <Form.Item
                  label={intl.formatMessage({ id: 'ownerId' })}
                  name={[formKey, 'ownerId']}
                  hidden
                >
                  <Input />
                </Form.Item> */}
                  <Form.Item
                    label={intl.formatMessage({ id: 'txnOwnerId' })}
                    name={[formKey, 'txnOwnerId']}
                    hidden
                  >
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label={intl.formatMessage({ id: 'area' })}
                    name={[formKey, 'addrType']}
                    hidden
                  />
                  <Form.Item
                    label={intl.formatMessage({ id: 'region' })}
                    name={[formKey, 'region']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: 'building_street' })}
                    name={[formKey, 'streetCode']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: 'door_number' })}
                    name={[formKey, 'bldingNo']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: 'floor' })}
                    name={[formKey, 'bldingFloor']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: '座/室' })}
                    name={[formKey, 'bldingBlock']}
                    hidden
                  >
                    <Input />
                  </Form.Item>

                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: 'region' })}
                    name={[formKey, 'dsajRegion']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: 'building_street' })}
                    name={[formKey, 'dsajStreetCode']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: 'door_number' })}
                    name={[formKey, 'dsajBldingNo']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: 'floor' })}
                    name={[formKey, 'dsajBldingFloor']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item<saveTxnOwnersParams[]>
                    label={intl.formatMessage({ id: '座/室' })}
                    name={[formKey, 'dsajBldingBlock']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                </div>

                <div className="my-card-title">
                  <div className="my-card-title-action">
                    <span>
                      {intl.formatMessage({ id: 'car_owner_information' })}
                      {arr.length > 1 ? `: ${index + 1}` : ''}
                    </span>
                    {index > 0 || formKeys.length > 1 ? (
                      <Button
                        danger
                        type="default"
                        onClick={async () => {
                          const data = form.getFieldsValue();
                          const { txnOwnerId } = data[formKey];

                          if (txnOwnerId) {
                            const ret = await deleteTxnOwners(txnOwnerId).catch((e) =>
                              messageApi.open({
                                type: 'error',
                                content: e.message,
                              }),
                            );
                            if (!ret) {
                              return;
                            }
                            if (ret.code !== '0') {
                              return;
                            }
                            handleDelete(data, formKey);
                            // fn();
                            // setOwnerSeqInfo({});
                          } else {
                            // const keys = formKeys.filter((_item, _index) => _item !== formKey);
                            // setFormKeys([...keys]);
                            // const copyData = { ...data };
                            // delete copyData[formKey];
                            // form.setFieldsValue(copyData);
                            // setOwnerSeqInfo((prev) => {
                            //   delete prev[formKey];
                            //   return { ...prev };
                            // });
                            handleDelete(data, formKey);
                          }

                          // const keys = formKeys.filter((_item, _index) => _item !== formKey);
                          // setFormKeys([...keys]);
                          // setOwnerSeqInfo((prev) => {
                          //   const val = {};
                          //   Object.keys(prev).forEach((key, i) => {
                          //     if (Number(key) !== formKey) {
                          //       val[key] = index > i ? prev[key] : i;
                          //     }
                          //   });
                          //   return { ...val };
                          // });
                        }}
                      >
                        {intl.formatMessage({ id: 'delete' })}
                      </Button>
                    ) : null}
                  </div>
                </div>

                <div className="my-card-body">
                  <Row className={px('row')}>
                    <Col span={11}>
                      <Form.Item<saveTxnOwnersParams[]>
                        label={intl.formatMessage({ id: 'chinese_surname' })}
                        name={[formKey, 'ownerLastNameCn']}
                      >
                        <Input
                          className="input"
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={1} />
                    <Col span={11}>
                      <Form.Item<saveTxnOwnersParams[]>
                        label={intl.formatMessage({ id: 'chinese_sur_name' })}
                        name={[formKey, 'ownerFirstNameCn']}
                      >
                        <Input
                          className="input"
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row className={px('row')}>
                    <Col span={11}>
                      <Form.Item<saveTxnOwnersParams[]>
                        label={intl.formatMessage({ id: 'portuguese_surname' })}
                        name={[formKey, 'ownerLastNamePt']}
                        rules={[
                          {
                            required: true,
                            message: intl.formatMessage({ id: 'please_enter' }),
                          },
                          {
                            pattern: /^[0-9a-zA-ZÀ-ÖØ-öø-ÿ\s]*$/,
                            message: intl.formatMessage({ id: 'enter_only_pt_char' }),
                          },
                        ]}
                      >
                        <Input
                          className="input"
                          onChange={(e) => {
                            const data = form.getFieldsValue();
                            form.setFieldsValue({
                              ...data,
                              [formKey]: {
                                ...data[formKey],
                                ownerLastNamePt: e.target.value.toUpperCase(),
                              },
                            });
                          }}
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={1} />
                    <Col span={11}>
                      <Form.Item<saveTxnOwnersParams[]>
                        label={intl.formatMessage({ id: 'portuguese_sur_name' })}
                        name={[formKey, 'ownerFirstNamePt']}
                        rules={[
                          {
                            required: true,
                            message: intl.formatMessage({ id: 'please_enter' }),
                          },
                          {
                            pattern: /^[0-9a-zA-ZÀ-ÖØ-öø-ÿ\s]*$/,
                            message: intl.formatMessage({ id: 'enter_only_pt_char' }),
                          },
                        ]}
                      >
                        <Input
                          className="input"
                          onChange={(e) => {
                            const data = form.getFieldsValue();
                            form.setFieldsValue({
                              ...data,
                              [formKey]: {
                                ...data[formKey],
                                ownerFirstNamePt: e.target.value.toUpperCase(),
                              },
                            });
                          }}
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row className={px('row')}>
                    <Col span={11}>
                      <Form.Item<saveTxnOwnersParams[]>
                        label={intl.formatMessage({ id: 'certificate_type' })}
                        required
                        rules={[
                          {
                            required: true,
                            message: intl.formatMessage({ id: 'please_select' }),
                          },
                        ]}
                        name={[formKey, 'ownerIdentType']}
                        initialValue="BM"
                      >
                        <Select
                          defaultValue="BM"
                          options={(certificateOpts || []).map((item) => ({
                            label: getLangGroup(item.codeCname, item.codePname, item.codeEname),
                            value: item.codeKey,
                          }))}
                          placeholder={intl.formatMessage({ id: 'please_select' })}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={1} />
                    <Col span={11}>
                      <Form.Item<saveTxnOwnersParams[]>
                        label={intl.formatMessage({ id: 'id_number' })}
                        dependencies={[formKey, 'ownerIdentType']}
                        required
                        rules={[
                          ({ getFieldsValue }) => ({
                            validator(_, value) {
                              if (!value) {
                                return Promise.reject(
                                  `${intl.formatMessage({ id: 'please_enter' })}`,
                                );
                              } else if (getFieldsValue()[formKey].ownerIdentType === 'BM') {
                                if (!/^\d{8}$/.test(value)) {
                                  return Promise.reject(
                                    `${intl.formatMessage({ id: 'id_number_check' })}`,
                                  );
                                }
                              }
                              return Promise.resolve();
                            },
                          }),
                        ]}
                        name={[formKey, 'ownerIdentNo']}
                      >
                        <Input
                          className="input"
                          placeholder={intl.formatMessage({ id: 'please_enter' })}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row className={px('row')}>
                    <Col span={23}>
                      <Form.Item<saveTxnOwnersParams[]>
                        label={intl.formatMessage({ id: 'contact_address' })}
                        required
                        rules={[
                          { required: true, message: intl.formatMessage({ id: 'please_enter' }) },
                        ]}
                        name={[formKey, 'ownerAddr']}
                      >
                        <MoAddress
                          onResAddrSplit={(v) => {
                            handleResAddrSplit(v, `${formKey}`, 'ownerAddr');
                          }}
                          placeholder={intl.formatMessage({ id: 'please_enter_the_address' })}
                          locale={getLocale() === Lang.葡语 ? pt : zhHant}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  {/* <Row className={px('row')}>
                  <Col span={23}>
                    <Form.Item<saveTxnOwnersParams[]>
                      label={intl.formatMessage({ id: 'contact_address' })}
                      required
                      rules={[{ required: true, message: intl.formatMessage({ id: 'please_enter' }) }]}
                      name={[formKey, 'ownerAddr']}
                    >
                      <Input
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                        onFocus={(e) => {
                          e.target.blur()
                          setCurrModalDataIdx(formKey)
                          setCurrModalDataField('ownerAddr')

                          const data = form.getFieldValue(formKey)
                          if(data) {
                            setTempRegion(data.region??'澳門')
                            setTempStreetCode(data.streetCode??'')
                            setTempBldingNo(data.bldingNo??'')
                            setTempBldingFloor(data.bldingFloor??'')
                            setTempBldingBlock(data.bldingBlock??'')
                            setTempRemark(data.bldingRemark??'')
                          } else {
                            setTempRegion('澳門')
                            setTempStreetCode('')
                            setTempBldingNo('')
                            setTempBldingFloor('')
                            setTempBldingBlock('')
                            setTempRemark('')
                          }

                          setModalOpen(true)
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row> */}
                  {['L', 'P'].includes(vehType || '') && formKeys.length > 1 && (
                    <>
                      <Row>
                        <Col span={23}>
                          <Form.Item<saveTxnOwnersParams[]>
                            label={intl.formatMessage({ id: 'registered_residence' })}
                            required
                            rules={[
                              {
                                required: true,
                                message: intl.formatMessage({ id: 'please_enter' }),
                              },
                            ]}
                            name={[formKey, 'dsajAddr']}
                            dependencies={['ownerAddr']}
                            shouldUpdate={(prevValues, curValues) =>
                              dsajAddrTickedCheckedInfos[formKey]
                            }
                          >
                            <MoAddress
                              disabled={dsajAddrTickedCheckedInfos[formKey]}
                              className={
                                dsajAddrTickedCheckedInfos[formKey] ? px('moAddressDisabled') : ''
                              }
                              onResAddrSplit={(v) => {
                                handleResAddrSplit(v, `${formKey}`, 'dsajAddr');
                              }}
                              placeholder={intl.formatMessage({ id: 'please_enter_the_address' })}
                              locale={getLocale() === Lang.葡语 ? pt : zhHant}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      {/* <Row>
                        <Col span={23}>
                          <Form.Item<saveTxnOwnersParams[]>
                            label={intl.formatMessage({ id: 'registered_residence' })}
                            required
                            rules={[{ required: true, message: intl.formatMessage({ id: 'please_enter' }) }]}
                            name={[formKey, 'dsajAddr']}
                          >
                            <Input
                              readOnly={isReadonly}
                              style={{ width: '100%' }}
                              placeholder={intl.formatMessage({ id: 'please_enter' })}
                              onFocus={(e) => {
                                if (isReadonly) return;
                                const data = form.getFieldValue(formKey)

                                e.target.blur()
                                setCurrModalDataIdx(formKey)
                                setCurrModalDataField('dsajAddr')

                                if(data) {
                                  setTempRegion(data.dsajRegion??'澳門')
                                  setTempStreetCode(data.dsajStreetCode??'')
                                  setTempBldingNo(data.dsajBldingNo??'')
                                  setTempBldingFloor(data.dsajBldingFloor??'')
                                  setTempBldingBlock(data.dsajBldingBlock??'')
                                  setTempRemark(data.dsajBldingRemark??'')
                                } else {
                                  setTempRegion('澳門')
                                  setTempStreetCode('')
                                  setTempBldingNo('')
                                  setTempBldingFloor('')
                                  setTempBldingBlock('')
                                  setTempRemark('')
                                }

                                setModalOpen(true)
                              }}
                            />
                          </Form.Item>
                        </Col>
                      </Row> */}
                      <Row style={{ marginBottom: 20 }}>
                        <Col span={24}>
                          <Form.Item<saveTxnOwnersParams[]>
                            noStyle
                            name={[formKey, 'dsajAddrTickedChecked']}
                            valuePropName="checked"
                          >
                            <Checkbox
                              className="preview-28f-checkbox"
                              onClick={(v: unknown) => {
                                onChangeBydsajAddrTicked(v, formKey);
                              }}
                            >
                              {intl.formatMessage({ id: 'dsaj_addr_ticked' })}
                            </Checkbox>
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  )}
                  {['L', 'P'].includes(vehType || '') && formKeys.length > 1 && (
                    <Row className={px('row')}>
                      <Col span={23}>
                        <Form.Item<saveTxnOwnersParams[]>
                          label={intl.formatMessage({ id: 'share_held' })}
                          required
                        >
                          <div style={{ display: 'flex' }}>
                            <Form.Item
                              rules={[
                                {
                                  required: true,
                                  message: intl.formatMessage({ id: 'please_enter' }),
                                },
                              ]}
                              name={[formKey, 'quotaNumerator']}
                            >
                              <Input
                                readOnly={isAverageQuotaNumerator}
                                style={{ width: '140px' }}
                                placeholder={intl.formatMessage({ id: 'please_enter' })}
                              />
                            </Form.Item>
                            <div className={px('seperator')}>/</div>
                            <Form.Item
                              rules={[
                                {
                                  required: true,
                                  message: intl.formatMessage({ id: 'please_enter' }),
                                },
                              ]}
                              name={[formKey, 'quotaDenominator']}
                            >
                              <Input
                                readOnly={isAverageQuotaNumerator}
                                style={{ width: '140px' }}
                                placeholder={intl.formatMessage({ id: 'please_enter' })}
                              />
                            </Form.Item>
                          </div>

                          {quotaNumeratorTip}
                        </Form.Item>
                      </Col>
                    </Row>
                  )}
                </div>
              </div>
            ))}

            <div className="add-owner-btn">
              <Button
                type="primary"
                onClick={() => {
                  const key = generateRandom();
                  setFormKeys([...formKeys, key]);
                  const getOneForm = form.getFieldsValue()[formKeys[0]];
                  const values = {};
                  values[key] = {
                    ...saveTxnOwnersParamsInit,
                    ownerAddr: getOneForm?.ownerAddr ?? '',
                    dsajAddr: getOneForm?.ownerAddr ?? '',
                  };
                  form.setFieldsValue(values);
                  setDsajAddrTickedCheckedInfos((prev) => ({
                    ...prev,
                    [key]: saveTxnOwnersParamsInit.dsajAddrTickedChecked,
                  }));
                  setOwnerSeqInfo((prev) => ({ ...prev, [key]: maxOwnerSeq + 1 }));
                }}
              >
                {intl.formatMessage({ id: 'new_car_owner' })}
              </Button>
            </div>
          </div>
        </Form>
        <Modal
          title={intl.formatMessage({ id: 'address' })}
          visible={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          okText="確 認"
          width={600}
          cancelButtonProps={{ type: 'default' }}
        >
          <Form labelCol={{ span: 4 }}>
            <Form.Item label={intl.formatMessage({ id: 'fill_in_type' })} required>
              <Select
                style={{ width: '100%' }}
                value="1"
                options={[{ label: '通過地址搜索', value: '1' }]}
                placeholder={intl.formatMessage({ id: 'please_select' })}
              />
            </Form.Item>
            <Form.Item label={intl.formatMessage({ id: 'select_region' })} required>
              <Select
                style={{ width: '100%' }}
                value={tempRegion}
                options={[{ label: '澳門', value: '澳門' }]}
                placeholder={intl.formatMessage({ id: 'please_select' })}
                onChange={(val) => {
                  setTempRegion(val);
                }}
              />
            </Form.Item>
            <Form.Item label={intl.formatMessage({ id: 'building_street' })} required>
              <Input
                value={tempStreetCode}
                onChange={(e) => {
                  setTempStreetCode(e.target.value);
                }}
              />
            </Form.Item>
            <Form.Item label=" " colon={false}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Select
                  value={tempBldingNo}
                  options={[{ label: '23', value: '23' }]}
                  placeholder={intl.formatMessage({ id: 'please_select' })}
                  style={{ width: '82px', marginRight: '8px' }}
                  onChange={(val) => {
                    setTempBldingNo(val);
                  }}
                />
                <span style={{ marginRight: '8px' }}>
                  {intl.formatMessage({ id: 'door_number' })}
                </span>
                <Input
                  placeholder={intl.formatMessage({ id: 'please_enter' })}
                  style={{ width: '82px', marginRight: '8px' }}
                  value={tempBldingFloor}
                  onChange={(e) => {
                    setTempBldingFloor(e.target.value);
                  }}
                />
                <span style={{ marginRight: '8px' }}>{intl.formatMessage({ id: 'floor' })}</span>
                <Input
                  placeholder={intl.formatMessage({ id: 'please_enter' })}
                  style={{ width: '82px', marginRight: '8px' }}
                  value={tempBldingBlock}
                  onChange={(e) => {
                    setTempBldingBlock(e.target.value);
                  }}
                />
                <span>{intl.formatMessage({ id: 'room_unit' })}</span>
              </div>
            </Form.Item>
            <Form.Item label={intl.formatMessage({ id: 'notes' })}>
              <Input.TextArea
                placeholder={intl.formatMessage({ id: 'please_enter' })}
                value={tempRemark}
                onChange={(e) => {
                  setTempRemark(e.target.value);
                }}
              />
            </Form.Item>
            <Form.Item label={intl.formatMessage({ id: 'address_content' })}>
              <div>
                {tempStreetCode
                  ? tempRegion +
                    tempStreetCode +
                    (tempBldingNo ? `${tempBldingNo}號` : '') +
                    (tempBldingFloor ? `${tempBldingFloor}樓` : '') +
                    (tempBldingBlock ? `${tempBldingBlock}室` : '') +
                    (tempRemark ? tempRemark : '')
                  : ''}
              </div>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </>
  );
});

export default FormTwo;
