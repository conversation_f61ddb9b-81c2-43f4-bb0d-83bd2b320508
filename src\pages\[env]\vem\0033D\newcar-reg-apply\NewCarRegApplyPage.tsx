import React, {useContext, useEffect, useState} from 'react';
import pc from 'prefix-classnames';
import {history, Location, useIntl, useLocation} from 'umi';
import {Modal, Spin} from 'antd';

import FooterButton from '@/components/FooterButton';
import GuideButton from '@/components/GuideButton';

import FormBaseMobile from './components/Mobile/FormBase';
import FooterButtonMobile from '@/components/FooterButtonMobile';

import {checkServiceAvailable, getServiceTitle} from '@/services/publicApi';
import Success from './components/Success';
import SuccessMobile from './components/Mobile/Success';

import FormBase from './components/FormBase';
import {isMobile} from '@/utils';
import {Page} from '@gov-mo/mpaas-js-bridge';
import {getLangGroup} from '@/locales/lang';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import {CODE_VERSION} from '@/utils/auth';
import {omit} from 'lodash';
import SpinContext from '../../../../../components/SpinContext';

const classPrefix = isMobile() ? 'page-33d-mobile' : 'page-33d';
const px = pc(classPrefix);

type NewCarLocation = {
  query: {
    step?: number;
    txnId?: string;
    reserved1?: string;
    update?: string;
    requestId: string;
    ID: string;
    code?: string;
  };
};

const NewCarSignPage = () => {
  const ComFormBase = isMobile() ? FormBaseMobile : FormBase;
  const ComFooterButton = isMobile() ? FooterButtonMobile : FooterButton;
  const intl = useIntl();
  const location = useLocation() as Location & NewCarLocation;
  const [contentNextStep, setContentNextStep] = useState(0);

  // todo 有待于验证location.query?.txnId 时候 step 应该返回
  const [step, setStep] = useState(() => {
    if (location.query.step && Number(location.query.step) > 1) {
      return Number(location.query.step);
    }
    if (location.query.txnId) {
      return 2;
    }
    return 1;
  });

  const [nextStepText, setNextStepText] = useState(intl.formatMessage({id: 'next'}));
  const [closeText, setCloseText] = useState('');

  const [showPrevStep, setShowPrevStep] = useState(true);
  const [showNextStep, setShowNextStep] = useState(true);
  const [showClose, setShowClose] = useState(true);
  const [showTempStore, setShowTempStore] = useState(true);
  const [showCloseAndTempStore, setShowCloseAndTempStore] = useState(false);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);
  const [disableTempStore, setDisableTempStore] = useState(false);
  const [disableCloseAndTempStore, setDisableCloseAndTempStore] = useState(false);

  const [title, setTitle] = useState(
    intl.formatMessage({id: 'vehicle_registration_application'}),
  );
  const [companyName, setCompanyName] = useState('');

  const [nextStepByContent, setNextStepByContent] = useState(0);

  const [createEXTxnParams, setCreateEXTxnParams] = useState<createEXTxnParams>();
  const [txnId, setTxnId] = useState<string>('');

  const [checkServiceModal, checkServiceContextHolder] = Modal.useModal();
  const requestId = location.query?.requestId || ''; // BOC 支付会携带这个参数requestId (就是txnId)
  const ID = location.query?.ID || ''; // BUN 支付会携带这个参数ID

  useEffect(() => {
    checkServiceAvailable('VEM').then((res) => {
      console.log('checkServiceAvailable', res);

      if (!res || !res.data) {
        return;
      }

      if (res?.data?.available === false) {
        history.push('/web/error');
        return;
      }

      if (isSuccessPage()) {
        return;
      }

      if (isMobile()) {
        Modal.warning({
          icon: null,
          centered: true,
          className: 'system-maintenance-mobile',
          title: intl.formatMessage({id: 'warm_reminder'}),
          content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
          okText: intl.formatMessage({id: 'confirm'}),
        });
        return;
      }

      checkServiceModal.warning({
        title: intl.formatMessage({id: 'warm_reminder'}),
        icon: <ExclamationCircleOutlined/>,
        content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
        okText: intl.formatMessage({id: 'confirm'}),
      });
    });
  }, []);

  useEffect(() => {
    if (location.search.startsWith('?page=success') || location.search.startsWith('page=success')) {
      return;
    }

    let query = txnId
      ? {...location.query, step: `${step}`, txnId: txnId || ''}
      : {...location.query, step: `${step}`};

    if (isMobile()) {
      query = {...query, hideNavigationBar: true} as any;
    }

    query = query[CODE_VERSION] && query?.code ? {...omit(query, CODE_VERSION, 'code')} : query;

    history.replace({
      pathname: location?.pathname,
      query: query,
    });
  }, [step]);

  const prevStep = () => {
    if (step <= 1) {
      isMobile() && history.push('/web/vem');
      return;
    }

    setStep(step - 1);
  };

  useEffect(() => {
    if (nextStepByContent == 2) {
      setContentNextStep(0);
      setStep(step + 1);
    }
  }, [nextStepByContent]);

  const nextStep = () => {
    if (nextStepByContent == 1) {
      setContentNextStep(contentNextStep + 1);
    } else if (nextStepByContent == 0) {
      setContentNextStep(0);
      setStep(step + 1);
    }
    // 滚动到页面顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth', // 平滑滚动
    });
  };

  const handleClose = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const handleTempStore = () => {
  };

  const handleCloseAndTempStore = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  useEffect(() => {
    if (location.query.txnId) {
      setDisablePrevStep(step <= 2);
    } else {
      setDisablePrevStep(step <= 1);
    }
  }, [step]);

  useEffect(() => {
    const txnId = location.query.txnId! || location.query.reserved1!;
    if (txnId) {
      setTxnId(txnId);
      return;
    }

    getServiceTitle('0033D')
      .then((res) => {
        setTitle(getLangGroup(res?.data?.serviceTitleCn, res?.data?.serviceTitlePt, res?.data?.serviceTitleEn))
        setCompanyName(getLangGroup(res?.data?.companyCname, res?.data?.companyPname, res?.data?.companyEname))
      })
      .catch((e) => {
        console.log('error-- ', e);
      });

    if (location.query.txnId) {
      setTxnId(location.query.txnId);
    }
  }, []);

  const isSuccessPage = () => location.query.page === 'success' || location.search?.includes('page=success')

  if (isSuccessPage()) {
    return isMobile() ? (
      <SuccessMobile txnId={txnId} requestId={requestId} ID={ID}/>
    ) : (
      <Success txnId={txnId} requestId={requestId} ID={ID}/>
    );
  }

  const {loading} = useContext(SpinContext);
  return (
    <>
      <Spin
        spinning={loading}
        size="large"
        className="spin-vil"
        style={{
          maxHeight: 'none',
          position: 'fixed',
          bottom: '0px',
        }}
      >
        {' '}
      </Spin>
      <ComFormBase
        classPrefix={classPrefix}
        setCreateEXTxnParams={(data) => {
          setCreateEXTxnParams(data);
        }}
        createEXTxnParams={createEXTxnParams}
        setTxnId={(txnId) => {
          setTxnId(txnId);
        }}
        txnId={txnId}
        title={title}
        companyName={companyName}
        contentNextStep={contentNextStep}
        step={step}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        nextStep={nextStep}
        setNextStepText={(text) => {
          setNextStepText(text);
        }}
        setCloseText={(value: string) => setCloseText(value)}
        setShowClose={(flag) => {
          setShowClose(flag);
        }}
        setShowTempStore={(flag) => {
          setShowTempStore(flag);
        }}
        setShowCloseAndTempStore={(flag) => {
          setShowCloseAndTempStore(flag);
        }}
        setShowPrevStep={setShowPrevStep}
        setShowNextStep={setShowNextStep}
        contentForNextStep={(flag) => {
          setNextStepByContent(flag);
        }}
        handlePrevStep={() => {
          prevStep();
        }}
      />
      <GuideButton itemId="ApplicationNotice33D"/>
      <ComFooterButton
        // handleClose={handleClose}
        className={px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        closeText={closeText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        showTempStore={showTempStore}
        showCloseAndTempStore={showCloseAndTempStore}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        setShowTempStore={() => {
          setShowTempStore;
        }}
        setShowCloseAndTempStore={() => {
          setShowCloseAndTempStore;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        disableTempStore={disableTempStore}
        disableCloseAndTempStore={disableCloseAndTempStore}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        setDisableTempStore={() => {
          setDisableTempStore;
        }}
        setDisableCloseAndTempStore={() => {
          setDisableCloseAndTempStore;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
        handleTempStore={() => {
          handleTempStore();
        }}
        handleCloseAndTempStore={() => {
          handleCloseAndTempStore();
        }}
      />
      {!isMobile() && checkServiceContextHolder}
    </>
  );
};

export default NewCarSignPage;
