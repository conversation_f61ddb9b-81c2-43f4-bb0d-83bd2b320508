import React, { forwardRef, useMemo } from 'react';
import { Select } from 'antd';
import type { SelectProps } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';

/**
 * 仅封装：完全继承 antd Select 的所有属性，透传 ref。
 * 不添加 Form.Item、不改动受控/非受控语义、不加默认行为。
 */
export const VtaSelect = forwardRef(
  <ValueType, OptionType extends DefaultOptionType = DefaultOptionType>(
    props: SelectProps<ValueType, OptionType>,
    ref: React.Ref<any>,
  ) => {
    let realProps = useMemo(() => {
      return {
        ...props,
        optionFilterProp: 'children',
        showSearch: true,
        filterOption: (input, option) =>
          ((option?.label ?? '') as string).toLowerCase().includes(input.toLowerCase()),
      };
    }, [props]);
    return <Select<ValueType, OptionType> ref={ref} {...realProps} />;
  },
);
