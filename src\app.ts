import { Modal } from 'antd';
import * as queryString from 'query-string';
import { getIntl, getLocale } from 'umi';
import { transform2HantFormat } from '@/locales/lang';
import { isMobile } from '@/utils';
import { getModalConfig } from './components/Modal/useModalMethod';
import locale from './parseLocale';
import { logout } from './services/UserApi';
// import { login, signature } from './services/UserApi';

// eslint-disable-next-line wrap-iife
// (async function () {
//   const logined = await login();
//   if (logined) {
//     const s = await signature();
//     // eslint-disable-next-line no-console
//     console.log(s);
//   }
// })();

export const dva = {
  config: {
    onError(err: ErrorEvent, next, { effectArgs, key }) {
      err.preventDefault();

      console.error('Error while dispatching effect:', key);
      effectArgs[0].__dva_reject(err);
    },
  },
};

(function () {
  // 开发模式
  // @ts-expect-error
  // if (IS_OFFLINE) return;
  // loadJS('eruda.js', () => {
  //   (window as any).eruda.init();
  // });
  if (window.eruda) {
    (window as any).eruda.init();
  }
}());

const showCloseModal = (handler) => {
  const intl = getIntl(getLocale());
  Modal.confirm({
    ...getModalConfig(intl),
    title: intl.formatMessage({ id: 'warm_reminder' }),
    content: intl.formatMessage({ id: 'confirm_logout' }),
    onOk() {
      handler?.();
    },
    okText: intl.formatMessage({ id: 'confirm' }),
    cancelText: intl.formatMessage({ id: 'cancel' }),
  });
};
const initHeader = () => {
  // @ts-expect-error
  const { yiwangheader } = window;
  if (yiwangheader && !isMobile()) {
    yiwangheader.init(
      ENV,
      (height) => {
        const rootElem = document.getElementById('root');
        if (!rootElem) return;
        rootElem.style.paddingTop = `${height}px`;
        rootElem.style.transition = 'padding-top 0.5s ease';
      },
      {
        onHandleReplaceUrl: (e) => {
          showCloseModal(() => {
            window.location.href = e.data.url;
          });
        },
        onHandleChangeLanguage: (e) => {
          const { language } = e.data;
          if (language === transform2HantFormat(getLocale())) return;
          showCloseModal(() => {
            yiwangheader.postChangeLanguage?.(e.data.language);
            const param = queryString.parseUrl(window.location.href);
            const newUrl = queryString.stringifyUrl({
              ...param,
              query: {
                ...param.query,
                language: e.data.language,
              },
            });
            window.location.replace(newUrl);
          });
        },
        onHandleLogout: (e) => {
          showCloseModal(() => {
            logout();

            window.console.log('~~~', e);
            window.location.href = e.data.url;
            yiwangheader.postLogout(e);
            setTimeout(() => {
              window.location.href = e.data.url;
            }, 1500);
          });
        },
      },
      {
        platform: PLATFORM,
        displayedLanguages: ['zh-Hant', 'pt'],
      }
    );
  }
};
initHeader();

(function () {
  const elements = document.getElementsByTagName('html');
  if (elements.length) {
    const pageWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    if (pageWidth < 1200 && isMobile()) {
      elements[0].style.fontSize = `${100 * (pageWidth / 338)}px`;
    }
  }
}());

export { locale };
