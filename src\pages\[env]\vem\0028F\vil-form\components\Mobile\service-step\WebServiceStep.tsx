import React, { useEffect } from 'react';
import classPrefix from '@ali-whale/class-prefix';
import { Steps, Modal } from 'antd';
import { Toast } from 'antd-mobile';
import { history, useSelector, useIntl, useDispatch, PaymentModelsState } from 'umi';
import { useMount, usePersistFn } from 'ahooks';
import { ServiceInfo } from '@/conf/ServiceConf';
import FooterButton from '@/components/FooterButton';
import type {
  StepModelsState,
  FormModelsState,
  receiveResultModelsState,
  ApplicantInformationsModelsState,
} from 'umi';
import GuideButton from '@/components/GuideButton';
import type { StepInfoType, ReceiveResultType } from './types/defs';
import StepRender from './components/StepRender';
import './WebServiceStep.less';
import { stepType, modelsType, resultTypeEnum } from './config';
import useHandleStep from './components/StepRender/HandleStep';
import useStep from './useStep';

const px = classPrefix('web-service-step');

const { Step } = Steps;

function WebServiceStep() {
  const intl = useIntl();
  const {
    location: { query = {} },
  } = history;
  const allModels = useSelector(
    ({
      applicantInformationsModels,
      stepModels,
      formModels,
      receiveResultModels,
      paymentModels,
    }: {
      applicantInformationsModels: ApplicantInformationsModelsState;
      stepModels: StepModelsState;
      formModels: FormModelsState;
      receiveResultModels: receiveResultModelsState;
      paymentModels: PaymentModelsState;
    }) => ({
      applicantInformationsModels,
      stepModels,
      formModels,
      receiveResultModels,
      paymentModels,
    }),
  );
  const {
    stepModels: { step, maxStep, itemSecurityLevel },
    formModels: { formData },
    receiveResultModels: { receiveResult },
    applicantInformationsModels: { applicantInformations },
    paymentModels: { webPaymentOrder },
  } = allModels;

  const dispatch = useDispatch();

  const { itemCode = '' } = query as Record<string, string>;
  const service = ServiceInfo.ServiceList.find((item) => item.itemCode === itemCode);
  const currentStep = service?.stepInfo[step - 1];
  const stepInfoList = service?.stepInfo.filter(
    (item) =>
      item.stepType === stepType.form ||
      item.stepType === stepType.resultReceive ||
      item.stepType === stepType.upload,
  );

  // 更新/初始化models
  const updataModels = usePersistFn((payload, type) => {
    dispatch({
      type: `${type}/update`,
      payload,
    });
  });

  function createReceiveResult(stepInfo: StepInfoType): ReceiveResultType {
    return stepInfo.itemResultReceiveMobieRespList.map((item) => ({
      // cartType: item.cartType,
      efficetiveTimeInterval: item.efficetiveTimeInterval,
      normalTimeInterval: item.normalTimeInterval,
      premiumAble: -1,
      premiumTimeInterval: item.premiumTimeInterval,
      // bad code TODO：解决初始化时领取方式全选的问题
      receiveType: [Number(item.receiveType?.split(',')[0])],
      resultLanguage: (item.resultLanguage ?? '').split(',')[0],
      resultNameZhMO: item.resultName,
      resultReceiveId: item.resultReceiveId,
      resultType: Number(item.resultType?.split(',')[0]),
      surrogateAble: item.surrogateAble,
      timeUnit: item.timeUnit,
    }));
  }

  function createShowOnly(stepInfo: StepInfoType): boolean {
    return stepInfo.itemResultReceiveMobieRespList.every((item) => {
      const { resultType, premiumAble, resultLanguage } = item;
      const resultLanguageList = (resultLanguage ?? '').split(',');
      return (
        resultType === String(resultTypeEnum.electronic) &&
        premiumAble === 0 &&
        resultLanguageList.length <= 1
      );
    });
  }

  const { checkQuaFn } = useStep();

  const checkQuality = async () => {
    Toast.loading(intl.formatMessage({ id: '加载中' }), 0);
    const res = await checkQuaFn({
      idNumber: applicantInformations.applicantCertNo,
      idType: applicantInformations.applicantCertType,
      serviceCode: itemCode,
    });
    Toast.hide();
    if (!res.success) {
      Modal.info({
        title: intl.formatMessage({ id: '温馨提示' }),
        content: res.message,
        okText: intl.formatMessage({ id: '确认' }),
        onOk: () => {
          window.top?.close();
        },
      });
    }
    return res.success;
  };

  useMount(async () => {
    await checkQuality();
    updataModels(
      {
        itemName: service?.name ?? '',
        itemCode: service?.itemCode,
      },
      modelsType.stepModels,
    );
    service?.stepInfo.forEach((stepInfo: StepInfoType) => {
      if (stepInfo.stepType === stepType.resultReceive || stepInfo.stepType === stepType.form) {
        const currentIsShowOnly = createShowOnly(stepInfo);
        updataModels(
          {
            resultReceiveConfig: stepInfo.itemResultReceiveMobieRespList,
            isShowOnly: currentIsShowOnly,
            receiveResult: receiveResult.length ? receiveResult : createReceiveResult(stepInfo),
          },
          modelsType.receiveResultModels,
        );
      } else if (stepInfo.stepType === stepType.upload) {
        updataModels({ materialConfig: stepInfo.itemMaterialRespList ?? [] }, modelsType.upload);
      }
    });
  });

  useEffect(() => {
    // 记录当前到过的最大步骤数，用于步骤间跳转
    if (maxStep < step) {
      updataModels({ maxStep: step }, modelsType.stepModels);
    }
  }, [step]);

  // 上一步按钮点击
  const handlePrevStep = usePersistFn(async () => {
    updataModels({ step: step - 1 }, modelsType.stepModels);
  });

  // 步骤条按钮点击
  // const handleStepItemClick = usePersistFn((stepNum) => {
  //   // setPayShow(false);
  //   maxStep >= stepNum
  //     ? updataModels({ step: stepNum }, modelsType.stepModels)
  //     : message.warn(intl.formatMessage({ id: '步骤没到' }));
  // });

  const handle = useHandleStep(currentStep);

  /**
   * 计算步骤条状态
   * @var step 当前步骤 从1开始
   * @var maxStep 最大到达步骤 从1开始
   * @param index 索引 从0开始
   */
  const getStepStatus = usePersistFn((index: number) => {
    if (index + 1 === step) {
      return 'process';
    }
    // if (index + 1 <= maxStep - 1) {
    //   return 'finish';
    // }
    // status配置为undefined时，交由Steps的current来自动指定状态。
    return undefined;
  });

  return (
    <>
      <div className={px('root')}>
        {/* 面包屑 */}
        {/* <Breadcrumb itemList={getDefaultBreadcrumbItems(service?.name)} /> */}
        {/* 为统一修改，在此加intl.formatMessage */}
        <h2 className={px('title')}>{intl.formatMessage({ id: service?.name })}</h2>
        <div className={px('stepinfo')}>
          <div className={px('step')}>
            <h3>{intl.formatMessage({ id: '申请步骤' })}</h3>
            <Steps direction="vertical" size="small" current={step - 1}>
              {service?.stepInfo.map((info: StepInfoType, index) => (
                <Step
                  key={info.stepType}
                  title={intl.formatMessage({ id: info.stepName })}
                  icon={index + 1}
                  // onClick={() => {
                  //   handleStepItemClick(index + 1);
                  // }}
                  status={getStepStatus(index)}
                />
              ))}
            </Steps>
          </div>
          <div className={px('form')}>
            {!webPaymentOrder && (
              <div className={px('formTitle')}>
                {currentStep?.stepName ? intl.formatMessage({ id: currentStep.stepName }) : ''}
              </div>
            )}
            <StepRender
              currentStep={currentStep}
              formData={formData}
              itemSecurityLevel={itemSecurityLevel}
              stepInfoList={stepInfoList}
              handleNext={handle}
              mobile={false}
            />
          </div>
        </div>
      </div>
      <GuideButton itemId="" />
      <FooterButton
        className={px('footer-button')}
        step={step}
        stepType={currentStep?.stepType}
        handleNextStep={handle}
        handlePrevStep={handlePrevStep}
      />
    </>
  );
}

export default WebServiceStep;
