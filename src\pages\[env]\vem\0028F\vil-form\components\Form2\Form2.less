@prefix: form2-28f;

.@{prefix} {
  &- {
    &root {
      margin-left: 0.24rem;

      .ant-form {
        font-size: 0.16rem;
      }
      .ant-form-item-label > label {
        font-size: 0.16rem;
      }
      .ant-select {
        font-size: 0.16rem;
      }
      .ant-input {
        font-size: 0.16rem;
      }

      .line-flex {
        display: flex;
      }
      .ant-btn {
        min-width: 0.62rem;
        height: 0.36rem;
        border-radius: 0.24rem;
        margin-left: 0.12rem;
        font-size: 0.16rem;
        line-height: 0.36rem;
      }
      .ant-btn.ant-btn-dangerous {
        background: #fff;
        color: #f33b40;
        border: 1px solid #f33b40;
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 0.18rem;
      color: #232323;
      padding-bottom: 0.1rem;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 0.04rem;
      height: 0.16rem;
      background: @brand-primary;
      margin-right: 0.08rem;
    }

    &seperator {
      width: 20px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
    }

    &moAddressDisabled {
      .ant-input[disabled] {
        color: rgba(0, 0, 0, 0.85);
        background-color: #fff;
        pointer-events: none;
      }
    }
  }
}

.my-card-title-action {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.add-owner-btn {
  display: flex;
  justify-content: flex-end;
}
.preview-28f-checkbox{
  font-size: 0.16rem;
  display: flex;
  align-items: center;
  .ant-checkbox-inner{
    width: 20px;
    height: 20px;
    border: 1px solid #ccc;
  }
  .ant-checkbox+span{
    font-size: 16px;
  }
}