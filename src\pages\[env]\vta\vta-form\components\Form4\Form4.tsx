import React, { useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Form4.less';

import { useIntl } from 'umi';
import {
  Button,
  Checkbox,
  Col,
  Empty,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Space,
} from 'antd';
import { findIndex, groupBy, keys, map, merge } from 'lodash';
import { CarFuelSupplyType, CarLevel, CarType, useGetEmu } from '../../hooks';
import { useFormAction } from '../../hooks/useFormAction';
import { getVTAVehBodyByTxnId, saveVTAVehBody } from '@/services/vta';
import { VtaRadioGroup } from '@/components/VtaRadioGroup/VtaRadioGroup';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';
import { setTimeout } from 'timers';

const px = pc('vta-form-form-4');

export interface FormFourProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId?: string;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

type FormListItem = {
  type?: 'input' | 'checkbox' | 'select';
  category?: string;
  label?: string;
  name?: string;
  required?: boolean;
  span?: number;
  component?: React.ReactNode;
  hide?: boolean;
  children?: FormListItem[];
};

const FormFour = (props: FormFourProps) => {
  const { className = '', txnId, ...otherProps } = props;
  const intl = useIntl();
  const [form] = Form.useForm();
  const [formLunTai] = Form.useForm();

  let [handBrake, setHandBrake] = useState();
  let [sights, setSights] = useState<any[]>([]);

  const handleValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {
    setHandBrake(allValues['handBrake']);
  };
  let { emuObj } = useGetEmu({
    indepSuspension: 22021,
    frontBrakeType: 31222,
    rearBrakeType: 31222,
    hasAbsBrake: 22014,
    handBrake: 50903,
    parkingAxle: 50106,
    tyreUnit: 22015,
    frontAxleQty: 22016,
    rearAxleQty: 22016,
    dualFrontAxle: 50902,
    batterySwapMode: 22016,
  });
  let { vehType, vtaCategory, powerSource, canEditable } = useFormAction(
    txnId,
    form,
    getVTAVehBodyByTxnId,
    saveVTAVehBody,
    props,
  );
  let carFormItemList: FormListItem[] = useMemo(
    () => [
      {
        type: 'input',
        category: intl.formatMessage({ id: 'vin_number' }),
        label: intl.formatMessage({ id: 'identifier' }),
        name: 'vin',
        span: 12,
        component: (
          <Input
            disabled={canEditable['vin']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'vin_number' }),
        label: intl.formatMessage({ id: 'serial_number' }),
        name: 'vinPattern',
        span: 12,
        component: (
          <Input
            disabled={canEditable['vinPattern']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'suspension_system' }),
        label: intl.formatMessage({ id: 'front_suspension' }),
        name: 'frontSuspension',
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['frontSuspension']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'suspension_system' }),
        label: intl.formatMessage({ id: 'front_suspension_qty' }),
        name: 'frontSuspensionQty',
        required: true,
        hide: [CarLevel.半掛車, CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['frontSuspensionQty']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'suspension_system' }),
        label: intl.formatMessage({ id: 'independent_suspension' }),
        name: 'indepSuspension',
        required: true,
        hide: [CarLevel.半掛車, CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <VtaRadioGroup
            disabled={canEditable['indepSuspension']}
            options={emuObj.indepSuspension}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'suspension_system' }),
        label: intl.formatMessage({ id: 'rear_suspension' }),
        name: 'rearSuspension',
        required: true,
        component: (
          <Input
            disabled={canEditable['rearSuspension']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'checkbox',
        category: intl.formatMessage({ id: 'suspension_system' }),
        label: intl.formatMessage({ id: 'rear_suspension_qty' }),
        name: 'rearSuspensionQty',
        required: true,
        hide: [CarLevel.半掛車, CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <Input
            disabled={canEditable['rearSuspensionQty']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'checkbox',
        category: intl.formatMessage({ id: 'brake_system' }),
        label: intl.formatMessage({ id: 'frontBrakeType' }),
        name: 'frontBrakeType',
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaRadioGroup disabled={canEditable['frontBrakeType']} options={emuObj.frontBrakeType} />
        ),
      },
      {
        type: 'checkbox',
        category: intl.formatMessage({ id: 'brake_system' }),
        label: intl.formatMessage({ id: 'rearBrakeType' }),
        name: 'rearBrakeType',
        required: true,
        component: (
          <VtaRadioGroup disabled={canEditable['rearBrakeType']} options={emuObj.rearBrakeType} />
        ),
      },
      {
        type: 'checkbox',
        category: intl.formatMessage({ id: 'brake_system' }),
        label: intl.formatMessage({ id: 'abs_system' }),
        name: 'hasAbsBrake',
        required: true,
        component: (
          <VtaRadioGroup disabled={canEditable['hasAbsBrake']} options={emuObj.hasAbsBrake} />
        ),
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'parking_brake' }),
        label: intl.formatMessage({ id: 'type' }),
        name: 'handBrake',
        required: true,
        component: (
          <Select
            placeholder={intl.formatMessage({ id: 'please_select' })}
            disabled={canEditable['handBrake']}
            options={emuObj.handBrake}
          />
        ),
      },
      {
        type: 'checkbox',
        category: intl.formatMessage({ id: 'parking_brake' }),
        label: intl.formatMessage({ id: 'parking_axle' }),
        name: 'parkingAxle',
        required: true,
        // hide:[].includes(handBrake),
        component: (
          <VtaRadioGroup disabled={canEditable['parkingAxle']} options={emuObj.parkingAxle} />
        ),
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'qty_front' }),
        name: 'frontAxleQty',
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_enter' })}
            disabled={canEditable['frontAxleQty']}
            options={emuObj.frontAxleQty}
          />
        ),
      },
      {
        type: 'select',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'qty_rear' }),
        name: 'rearAxleQty',
        required: true,
        component: (
          <VtaSelect
            placeholder={intl.formatMessage({ id: 'please_enter' })}
            disabled={canEditable['rearAxleQty']}
            options={emuObj.rearAxleQty}
          />
        ),
      },
      {
        type: 'checkbox',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'steering_dual_axle' }),
        name: 'dualFrontAxle',
        required: true,
        hide: [CarLevel.半掛車, CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <VtaRadioGroup disabled={canEditable['dualFrontAxle']} options={emuObj.dualFrontAxle} />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label:intl.formatMessage({ id: 'axle_load_1' }),
        name: 'axleLoad1',
        required: ![CarType.其它].includes(vtaCategory),
        hide: [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['axleLoad1']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'axle_load_2' }),
        name: 'axleLoad2',
        required: ![CarType.其它].includes(vtaCategory),
        hide: [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['axleLoad2']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'axle_load_3' }),
        name: 'axleLoad3',
        required: ![CarType.其它].includes(vtaCategory),
        hide: [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['axleLoad3']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'axle_load_4' }),
        name: 'axleLoad4',
        required: ![CarType.其它].includes(vtaCategory),
        hide: [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['axleLoad4']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'axle_load_5' }),
        name: 'axleLoad5',
        required: ![CarType.其它].includes(vtaCategory),
        hide: [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['axleLoad5']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'axle_load_other' }),
        name: 'otherAxleLoad',
        required: ![CarType.其它].includes(vtaCategory),
        hide: [CarLevel.輕型摩托車, CarLevel.重型摩托車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['otherAxleLoad']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'axle_load_drive' }),
        name: 'axleLoadTraction',
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['axleLoadTraction']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'axle' }),
        label: intl.formatMessage({ id: 'fuel_tank_capacity' }),
        name: 'fuelTankVol',
        required: true,
        hide: [CarFuelSupplyType.電力].includes(powerSource) || [CarLevel.半掛車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['fuelTankVol']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'wheel' }),
        label: intl.formatMessage({ id: 'qty_front' }),
        name: 'frontTyreQty',
        required: true,
        hide: [CarLevel.半掛車].includes(vehType),
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['frontTyreQty']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
      {
        type: 'input',
        category: intl.formatMessage({ id: 'wheel' }),
        label: intl.formatMessage({ id: 'qty_rear' }),
        name: 'rearTyreQty',
        required: true,
        component: (
          <InputNumber
            style={{ width: '100%' }}
            disabled={canEditable['rearTyreQty']}
            placeholder={intl.formatMessage({ id: 'please_enter' })}
          />
        ),
      },
    ],
    [emuObj, vehType, canEditable, handBrake, vtaCategory, powerSource],
  );

  const [show, setShow] = useState(false);

  const [isModalOpen, setModalOpen] = useState(false);
  function upsertById(arr, id, payload) {
    // 找索引
    const idx = findIndex(arr, { id });
    if (idx !== -1) {
      // 存在 → 更新
      arr[idx] = merge({}, arr[idx], payload);
    } else {
      // 不存在 → 追加
      arr.push({ id, ...payload });
    }

    return arr;
  }
  function handleOk() {
    return new Promise((resolve) => {
      formLunTai.validateFields().then((values) => {
        let sights = form.getFieldsValue().sights;
        let arr =upsertById(sights,values.id,values)
        setSights(arr);
        form.setFieldsValue({ sights: arr });
        formLunTai.resetFields();
        setModalOpen(false);
        setShow(true);
        resolve(values);
      });
    });
  }

  function handleCancel() {
    setModalOpen(false);
  }
  const [typeUnit, setTypeUnit] = useState(0);
  return (
    <div className={`${px('root')} ${className} multi-card-container`} {...otherProps}>
      <Form
        form={form}
        className={px('form')}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <Form.Item name="txnId" hidden initialValue={txnId}>
          <Input />
        </Form.Item>
        {keys(groupBy(carFormItemList, 'category')).map((category, index) => (
          <div className="multi-card-item" key={index}>
            <div className="multi-card-title">
              <span>{category}</span>
            </div>
            <div className="multi-card-body">
              <Row gutter={16}>
                {(groupBy(carFormItemList, 'category')[category] as FormListItem[]).map(
                  (item, index) => (
                    <>
                      <Col span={12} key={index}>
                        <Form.Item
                          labelCol={{ span: item.span ?? 24 }}
                          label={item.label}
                          name={item.name}
                          rules={[{ required: item.required }]}
                        >
                          {item.component}
                        </Form.Item>
                      </Col>
                      {item?.children &&
                        keys(groupBy(item?.children, 'category')).map((childCategory) => (
                          <div className="multi-card-item-sub">
                            <div className="multi-card-title">
                              <span>{childCategory}</span>
                            </div>
                            <div className="multi-card-body">
                              <Row gutter={16}>
                                {groupBy(item?.children, 'category')
                                  [childCategory].filter((ite) => !ite.hide)
                                  ?.map((child, childIndex) => (
                                    <Col span={12} key={childIndex}>
                                      <Form.Item
                                        labelCol={{ span: 12 }}
                                        label={child.label}
                                        name={child.name}
                                        rules={[{ required: item.required }]}
                                      >
                                        {child.component}
                                      </Form.Item>
                                    </Col>
                                  ))}
                              </Row>
                            </div>
                          </div>
                        ))}
                    </>
                  ),
                )}
              </Row>
            </div>
          </div>
        ))}
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>{intl.formatMessage({ id: 'tire_data' })}</span>
            <div className="multi-card-title-action">
              <Space>
                <Button onClick={() => setModalOpen(true)}>{intl.formatMessage({ id: 'add_tire_data' })}</Button>
              </Space>
            </div>
          </div>
          <Form.Item name="sights" hidden initialValue={[]}>
            <Input />
          </Form.Item>
          {show ? (
            <>
              <div className="multi-card-item-sub">
                {sights.map((item: any, index) => {
                  return (
                    <>
                      <div className="multi-card-title">
                        <span>{intl.formatMessage({ id: 'tire_spec' })}{index + 1}</span>
                        <div className="multi-card-title-action">
                          <Space>
                            <Button
                              onClick={() => {
                                setModalOpen(true);
                                setTimeout(() => {
                                  setTypeUnit(item.typeUnit);
                                  formLunTai.setFieldsValue(item);
                                }, 0);
                              }}
                            >
                             {intl.formatMessage({ id: 'update' })}
                            </Button>
                            <Button danger onClick={()=>{
                              let sg = form.getFieldsValue().sights;
                              sg.splice(index,1)
                              setSights([...sg]);
                              form.setFieldsValue({ sights: [...sg] });
                            }}>{intl.formatMessage({ id: 'delete' })}</Button>
                          </Space>
                        </div>
                      </div>
                      <div className="multi-card-body">
                        <Form form={form} className={px('form')} layout="inline">
                          <Form.Item label={intl.formatMessage({ id: 'imperial_metric' })} style={{ width: '20%' }}>
                            <div> {intl.formatMessage({ id: 'imperial' })}</div>
                          </Form.Item>
                          <Form.Item label={intl.formatMessage({ id: 'tire_spec_front' })} style={{ width: '20%' }}>
                            <div>
                              {item.tyreFSpec1}
                              {item.tyreFSpec2 ? `/${item.tyreFSpec2}` : ''}-{item.tyreFSpec3}
                            </div>
                          </Form.Item>
                          <Form.Item label={intl.formatMessage({ id: 'tire_spec_rear' })} style={{ width: '20%' }}>
                            <div>
                              {item.tyreRSpec1}
                              {item.tyreRSpec2 ? `/${item.tyreRSpec2}` : ''}-{item.tyreRSpec3}
                            </div>
                          </Form.Item>
                          <Form.Item label={intl.formatMessage({ id: 'tire_spec_side' })} style={{ width: '20%' }}>
                            <div>
                              {item.tyreSSpec1}
                              {item.tyreSSpec2 ? `/${item.tyreSSpec2}` : ''}-{item.tyreSSpec3}
                            </div>
                          </Form.Item>
                        </Form>
                      </div>
                    </>
                  );
                })}
              </div>
            </>
          ) : (
            <Empty
              description={intl.formatMessage({ id: 'no_data' })}
              style={{ margin: '20px 0' }}
            />
          )}
        </div>
      </Form>

      <Modal
        title={intl.formatMessage({ id: 'input_tire_data' })}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={intl.formatMessage({ id: 'confirm' })}
        width={600}
        destroyOnClose
        cancelButtonProps={{ type: 'default' }}
      >
        <Form layout="vertical" form={formLunTai}>
          <Form.Item hidden initialValue={Date.now()} name={'id'}>
            <Input />
          </Form.Item>
          <Form.Item label={intl.formatMessage({ id: 'imperial_metric' })} required name={'typeUnit'} initialValue={0}>
            <Radio.Group
              options={['英制', '公制'].map((item, index) => ({
                label: item,
                value: Number(index),
              }))}
              onChange={(e) => {
                setTypeUnit(e.target.value);
              }}
            />
          </Form.Item>
          <Form.Item label={intl.formatMessage({ id: 'tire_spec_front' })} required>
            <Row>
              <Col span={typeUnit === 0 ? 11 : 7}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  name={'tyreFSpec1'}
                  rules={[{ required: true,}]}
                >
                  <Input placeholder={intl.formatMessage({ id: 'tire_width' })} />
                </Form.Item>
              </Col>
              <Col
                span={2}
                style={{
                  background: '#eee',
                  height: 35,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                -
              </Col>
              {typeUnit === 0 ? (
                <></>
              ) : (
                <>
                  <Col span={7}>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      name={'tyreFSpec2'}
                      rules={[{ required: true, }]}
                    >
                      <Input placeholder={intl.formatMessage({ id: 'tire_aspect_ratio' })} />
                    </Form.Item>
                  </Col>
                  <Col
                    span={2}
                    style={{
                      background: '#eee',
                      display: 'flex',
                      height: 35,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    -
                  </Col>
                </>
              )}
              <Col span={typeUnit === 0 ? 11 : 6}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  name={'tyreFSpec3'}
                  rules={[{ required: true,  }]}
                >
                  <Input placeholder={intl.formatMessage({ id: 'tire_diameter' })} />
                </Form.Item>
              </Col>
            </Row>
          </Form.Item>
          <Form.Item label={intl.formatMessage({ id: 'tire_spec_rear' })} required>
            <Row>
              <Col span={typeUnit === 0 ? 11 : 7}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  name={'tyreRSpec1'}
                  rules={[{ required: true, }]}
                >
                  <Input placeholder={intl.formatMessage({ id: 'tire_width' })} />
                </Form.Item>
              </Col>
              <Col
                span={2}
                style={{
                  background: '#eee',
                  display: 'flex',
                  height: 35,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                -
              </Col>
              {typeUnit === 0 ? (
                <></>
              ) : (
                <>
                  <Col span={7}>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      name={'tyreRSpec2'}
                      rules={[{ required: true, }]}
                    >
                      <Input placeholder={intl.formatMessage({ id: 'tire_aspect_ratio' })} />
                    </Form.Item>
                  </Col>
                  <Col
                    span={2}
                    style={{
                      background: '#eee',
                      display: 'flex',
                      height: 35,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    -
                  </Col>
                </>
              )}
              <Col span={typeUnit === 0 ? 11 : 6}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  name={'tyreRSpec3'}
                  rules={[{ required: true, }]}
                >
                  <Input placeholder={intl.formatMessage({ id: 'tire_diameter' })} />
                </Form.Item>
              </Col>
            </Row>
          </Form.Item>
          <Form.Item label={intl.formatMessage({ id: 'tire_spec_side' })} required>
            <Row>
              <Col span={typeUnit === 0 ? 11 : 7}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  name={'tyreSSpec1'}
                  rules={[{ required: true,  }]}
                >
                  <Input placeholder={intl.formatMessage({ id: 'tire_width' })} />
                </Form.Item>
              </Col>
              <Col
                span={2}
                style={{
                  background: '#eee',
                  height: 35,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                -
              </Col>
              {typeUnit === 0 ? (
                <></>
              ) : (
                <>
                  <Col span={7}>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      name={'tyreSSpec2'}
                      rules={[{ required: true,  }]}
                    >
                      <Input placeholder={intl.formatMessage({ id: 'tire_aspect_ratio' })} />
                    </Form.Item>
                  </Col>
                  <Col
                    span={2}
                    style={{
                      background: '#eee',
                      height: 35,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    -
                  </Col>
                </>
              )}
              <Col span={typeUnit === 0 ? 11 : 6}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  name={'tyreSSpec3'}
                  rules={[{ required: true,}]}
                >
                  <Input placeholder={intl.formatMessage({ id: 'tire_diameter' })} />
                </Form.Item>
              </Col>
            </Row>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FormFour;
