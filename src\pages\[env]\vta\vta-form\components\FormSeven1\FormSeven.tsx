import React from 'react';
import pc from 'prefix-classnames';
import './FormSeven.less';

import { useIntl } from 'umi';
import { Form, Button, Upload, Divider  } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

const px = pc('form-seven1');

// const props = {
//   name: 'file',
//   action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
//   headers: {
//     authorization: 'authorization-text',
//   },
//   onChange(info) {
//     if (info.file.status !== 'uploading') {
//       console.log(info.file, info.fileList);
//     }
//     if (info.file.status === 'done') {
//       message.success(`${info.file.name} file uploaded successfully`);
//     } else if (info.file.status === 'error') {
//       message.error(`${info.file.name} file upload failed.`);
//     }
//   },
// };

export interface FormSevenProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
    txnId?: string
}

const FormSeven = (props: FormSevenProps) => {
  const { className = '', ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <Form form={form} className={px('form')} layout="vertical">
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'submit_size_type_tips_size' }, { size: '30MB' })}</div>
        <div className={px('sectionBody')}>
          <Form.Item required label="i. 零件供應聲明書">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="ii. 車輛稅務價格聲明書">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="iii. 供應商供應車輛之聲明信或車輛買賣合約證明">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="iv. 製造商發出的車輛技術規格證明書（或供應商提供的技術規格資料）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="v.	原廠說明書（或載有車輛技術資料的原廠使用手冊）">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="vi.	標示車輛尺寸的側視、正視及後視圖">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="vii.	提交汽車尾氣排放及車載自我診斷系統(OBD)之聲明書">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="viii. 提交摩托車尾氣排放之聲明書">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="ix. 車輛尾氣排放報告或證明文件">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="x.	電力安全測試報告或證明文件">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item required label="xi. 純電續駛里程測試證明文件">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
          <Form.Item label="xii. 其他補充資料">
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>{intl.formatMessage({ id: 'upload_files' })}</Button>
            </Upload>
            <Divider />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default FormSeven;
