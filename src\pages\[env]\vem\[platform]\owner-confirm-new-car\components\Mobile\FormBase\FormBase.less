@prefix: owner-confirm-new-car-mobile;

.@{prefix} {
  &- {
    &root {
      display: flex;
      flex-direction: column;
      padding-top: 45px;
      border-bottom: 100px solid #eeeeee;
      overflow-y: auto;
      background-color: #ffffff;

      font-size: 16px;
    }

    &title {
      font-size: 0.18rem;
      color: #363636;
      line-height: 21px;
      text-align: center;
      font-weight: 700;
      padding: 0 0.24rem 0.2rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    }

    &stepinfo {
      flex: 1;
      flex-direction: column;
      display: flex;
    }

    &mobile-step {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 25px;

      .step-item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 33px;
        height: 33px;
        font-size: 14px;
        color: #ffffff;
        background-color: rgba(193, 193, 193, 0.25);
        border-radius: 50%;

        span {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 25px;
          height: 25px;
          background-color: #c1c1c1;
          border-radius: 50%;
        }

        &.finish {
          background-color: rgba(8, 74, 184, 0.25);

          span {
            background-color: @brand-primary;
          }
        }

        &.progress {
          background-color: rgba(255, 193, 7, 0.25);

          span {
            background-color: #ffc107;
          }
        }
      }

      .line {
        flex: 1;
        margin: 0 16px;
        min-width: 0.1rem;
        max-width: 1rem;
        height: 2px;
        background-color: #c1c1c1;
      }
    }

    &step {
      min-width: -webkit-fit-content;
      min-width: -moz-fit-content;
      min-width: fit-content;
    }

    &step h3 {
      font-size: 16px;
      line-height: 22px;
      font-weight: 700;
      padding: 0 2px 16px;
    }

    &step .ant-steps-small {
      font-size: 16px;
    }

    &step .ant-steps-small .ant-steps-item {
      height: 70px;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon {
      background-color: rgba(8, 74, 184, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-icon {
      background-color: @brand-primary;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-tail:after {
      background-color: @brand-primary;
      height: 30px;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-icon {
      background-color: rgba(255, 193, 7, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-icon {
      background-color: #ffc107;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 30px;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-icon {
      background-color: hsla(0, 0%, 76.9%, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-icon {
      background-color: #c4c4c4;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 30px;
    }

    &step
    .ant-steps-small
    .ant-steps-item.ant-steps-item-wait:last-child
    .ant-steps-item-tail:after {
      display: none;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container {
      height: 40px;
      min-height: 40px;
      display: flex;
      align-items: center;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-tail {
      bottom: 0;
      top: 40px;
      left: 14px;
      padding: 0;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      margin-right: 6px;
      cursor: pointer;
      flex-shrink: 0;
    }

    &step
    .ant-steps-small
    .ant-steps-item
    .ant-steps-item-container
    .ant-steps-item-icon
    .ant-steps-icon {
      height: 18px;
      min-height: 18px;
      display: flex;
      align-items: center;
      width: 18px;
      line-height: 18px;
      left: 5px;
      top: 5px;
      font-size: 14px;
      justify-content: center;
      color: #fff;
      border-radius: 50%;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content {
      height: 40px;
      min-height: 40px;
      display: flex;
      align-items: center;
      line-height: 16px;
    }

    &step
    .ant-steps-small
    .ant-steps-item
    .ant-steps-item-container
    .ant-steps-item-content
    .ant-steps-item-title {
      font-size: 16px;
      color: #333;
    }

    &form {
      display: flex;
      flex-direction: column;
      margin-top: 0;
      background-color: #eeeeee;
      flex: 1;
    }

    &form h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 24px;
    }

    &form > .pc-pay-ui-root {
      margin-left: 24px;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 65px;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 16px;
      color: #666;
      top: -2px;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 24px;
      top: -2px;
    }

    &formTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      margin-left: 24px;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px;
    }

    &formTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }

    &spin {
      width: 100%;
      height: 100%;
      padding: 45%;
    }

    &footer-button {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 20px;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
      height: 100px;
      gap: 4%;

      .ant-btn {
        flex: 1;
        height: 52px;
        line-height: 52px;
        border-radius: 26px;
        font-size: 16px;
        padding: 0 5px;

        &.ant-btn-default {
          background: #fff;
          color: @brand-primary;
          border: 1px solid @brand-primary;
        }
      }
    }


    &pt-footer-button {
      flex-direction: column;
      height: auto;
      padding: 8px 20px;

      .ant-btn {
        width: 100%;
      }
    }

    &pt-footer-button > :not([hidden]) ~ :not([hidden]) {
      margin-top: 8px;
    }
  }
}
