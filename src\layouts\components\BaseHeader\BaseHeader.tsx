import React from 'react';
import { getLocale } from 'umi';
import classPrefix from 'prefix-classnames';
import logo from '@/assets/logo.png';
import './BaseHeader.less';

export interface LogoProps {
  className?: string;
  src?: string;
  onClick: () => void;
}

export interface TitleProps {
  className?: string;
  children: React.ReactNode;
  onClick: () => void;
}

export interface BaseHeaderProps {
  className?: string;
  children: React.ReactNode;
}

const PREFIX = 'web-header';
const px = classPrefix(PREFIX) as (param: string) => string;

const Logo = (props: LogoProps) => {
  const { className = '', src = logo, ...otherProps } = props;
  return (
    <div className={`${px('logo')} ${className}`} {...otherProps}>
      <img alt="logo" src={src} />
    </div>
  );
};

const Title = (props: TitleProps) => {
  const locale = getLocale();
  const { className, children, ...otherProps } = props;
  const needSmallTitle = locale === 'pt-PT' || locale === 'en-US';
  return (
    <div
      className={`${px('title')} ${className ?? ''}`}
      {...otherProps}
      style={{ fontSize: needSmallTitle ? '18px' : '26px' }}
    >
      {children}
    </div>
  );
};

const BaseHeader = (props: BaseHeaderProps) => {
  const { className, children, ...otherProps } = props;
  return (
    <div className={`${px('main')} ${className ?? ''}`} {...otherProps}>
      {children}
    </div>
  );
};

export { Logo, Title };

export default BaseHeader;
