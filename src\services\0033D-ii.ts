import type { FetchResponse } from '@/utils/fetch';
import { get, post } from '@/utils/fetch';

// 加載資料
export const getVehRegTaxTxnData = (txnId: string) =>
  get<FetchResponse<VehRegTaxTxnResType>>('/oa/vem/getVehRegTaxTxnData', { txnId });

// 创建流水(点击完成车辆註冊)
export const createVehRegTaxTxn = (txnId: string) =>
  get<FetchResponse<CreateVehRegTaxTxn>>('/oa/vem/createVehRegTaxTxn', { txnId });

// 確認資料下一步
export const validateTxnForCompleteVehReg = (txnId: string, speedUp: string) =>
  get<FetchResponse<ValidateTxnForCompleteVehReg>>('/oa/vem/validateTxnForCompleteVehReg', {
    txnId,
    speedUp,
  });

// 下載代替登記摺
export const printSubRegReport = (txnId: string) =>
  post<FetchResponse<string> | BlobPart>(
    '/oa/vem/printSubRegReport',
    { txnId },
    {
      responseType: 'blob',
    },
  );

// 下載代替登記摺 獲取預覽地址
export const getSubRegReportPreviewUrl = (txnId: string) =>
  post<FetchResponse<string>>(`/oa/vem/getSubRegReportPreviewUrl`, { txnId });

// 服務完成接口
export const completeOvsapTxn = (txnId: string) =>
  get<FetchResponse<string>>('/oa/service/completeOvsapTxn', { txnId });

export const saveVehRegTaxTxn = (params: SaveVehRegTaxTxnParams) =>
  post<FetchResponse<string>>('/oa/vem/saveVehRegTaxTxn', params);

export const checkBatch33Dii = (params: CheckBatch33DiiParams[]) => {
  return post<FetchResponse<CheckBatch33DiiModel>>('/oa/vem/checkBatch33Dii', params);
};

export const batchCompleteOvsapTxn = (params: string[]) => {
  return post<FetchResponse<BatchCompleteOvsapTxnModel>>(
    '/oa/service/batchCompleteOvsapTxn',
    params,
  );
};
