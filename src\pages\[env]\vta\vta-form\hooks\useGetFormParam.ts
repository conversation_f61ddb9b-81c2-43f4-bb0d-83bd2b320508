import { getVTAFeaturesByTxnId, getVTAPowerDeviceByTxnId, getVTAVehBrandByTxnId } from '@/services/vta';
import { keys } from 'lodash';
import { useEffect, useState } from 'react';
/**
 * Request
 *
 * OvsapVTAMainTxnParamsDTO
 */
export interface Request {
  /**
   * 型號編碼
   */
  customModelCode?: string;
  /**
   * 評稅編號
   */
  dsfRefNo?: string;
  /**
   * 變速器 codeType:31223
   */
  gearboxType?: string;
  /**
   * 是否有評稅編號 有為Y，沒有為N
   */
  hasDsfRefNo?: string;
  /**
   * 軚盤位置Ｅ＝左盤, D=右盤 codeType:50118
   */
  steeringWheelPosition?: string;
  /**
   * txnId
   */
  txnId?: string;
  /**
   * 傳動方式
   */
  txType?: string;
  /**
   * 檔數
   */
  varSpeedQty?: number;
  /**
   * 商標code codeType:31204
   */
  vehBrandCode?: string;
  /**
   * 型號
   */
  vehModel?: string;
  /**
   * 款式年份
   */
  vehModelYear?: number;
  /**
   * 車輛級別 codeType:31201
   */
  vehType?: string;
  [property: string]: any;
}
export const useGetFormParam = (txnId) => {
  let [formData, setFormData] = useState<Request>();
  useEffect(() => {
    txnId &&
      getVTAVehBrandByTxnId({ txnId: txnId }).then((res: any) => {
        let obj = {};
        keys(res.data).forEach((item) => {
          obj[item] = res.data[item].value;
        }, {});
        setFormData(obj);
      });
  }, []);
  return { formData };
};
export const useGetForm1Param = (txnId) => {
  let [formData, setFormData] = useState<Request>();
  useEffect(() => {
    txnId &&
      getVTAFeaturesByTxnId({ txnId: txnId }).then((res: any) => {
        let obj = {};
        keys(res.data).forEach((item) => {
          obj[item] = res.data[item].value;
        }, {});
        console.log(obj, 'cabinType');
        setFormData(obj);
      });
  }, []);
  return { formData };
};
export const useGetForm3Param = (txnId) => {
  let [formData, setFormData] = useState<Request>();
  useEffect(() => {
    txnId &&
      getVTAPowerDeviceByTxnId({ txnId: txnId }).then((res: any) => {
        let obj = {};
        keys(res.data).forEach((item) => {
          obj[item] = res.data[item].value;
        }, {});
        setFormData(obj);
      });
  }, []);
  return { formData };
};
