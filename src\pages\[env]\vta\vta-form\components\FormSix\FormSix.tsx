import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './FormSix.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button } from 'antd';
import type { RadioChangeEvent } from 'antd';

const px = pc('form-six');

export interface FormSixProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;
}

const FormSix = (props: FormSixProps) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  const [val, setVal] = useState(0)
  const [more, setMore] = useState(false)
  const [search, setSearch] = useState(false)


  const onChange = (e: RadioChangeEvent) => {
    setVal(e.target.value);
  };

  useEffect(() => {
    setDisableNextStep(false)
  }, [])


  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
  );
};

export default FormSix;
