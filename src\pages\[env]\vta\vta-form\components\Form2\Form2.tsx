import React, { useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Form2.less';

import { useIntl } from 'umi';
import { Col, Form, Input, InputNumber, Radio, Row } from 'antd';
import { useVtaDict } from '@/hook/useVtaDict';
import { useGetForm1Param, useGetFormParam } from '../../hooks/useGetFormParam';
import { getVTASizeWeightByTxnId, saveVTASizeWeight } from '@/services/vta';
import { CarLevel, CarType, useGetRes } from '../../hooks';
import { VtaSelect } from '@/components/VtaSelect/VtaSelect';

const px = pc('vta-form-form-2');

export interface FormTwoProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
  txnId?: string;
}

console.log('Component:Form2');
const FormTwo = (props: FormTwoProps) => {
  const { className = '', nextStepTrigger, txnId, handleNextStep, ...otherProps } = props;
  const { dict } = useVtaDict([22004, 22005, 22006] as const);

  const intl = useIntl();
  const [form] = Form.useForm();

  const [cabinType, setCabinType] = useState();
  const [cabin_code_remark, setCabin_code_remark] = useState();
  const [cabinCode, setCabinCode] = useState();

  const handleValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {
    if ('cabinType' in changedValues) {
      setCabinType(changedValues.cabinType);
    }
    if ('cabin_code_remark' in changedValues) {
      setCabin_code_remark(changedValues.cabinType);
    }
    if ('cabinCode' in changedValues) {
      setCabinCode(changedValues.cabinType);
    }

    if (
      (changedValues.vehGrossWeight || changedValues.vehWeight) &&
      allValues.vehGrossWeight &&
      allValues.vehWeight
    ) {
      const vehLoadWeight = allValues.vehGrossWeight - allValues.vehWeight;
      form.setFieldsValue({ ...form.getFieldsValue(), vehLoadWeight });
    }
  };
  const flag = useMemo(() => {
    return cabinCode || cabin_code_remark;
  }, [cabinCode, cabin_code_remark]);

  let { getFormData } = useGetRes();
  let { formData } = useGetFormParam(txnId);
  let { formData: formData1 } = useGetForm1Param(txnId);
  let vehType: any = useMemo(() => {
    return formData?.vehType || '';
  }, [formData]);
  let vtaCategory: any = useMemo(() => {
    return formData1?.vtaCategory || '';
  }, [formData1]);
  const [canEditable, setCanEditable] = useState({});
  const getRecordData = async () => {
    if (!txnId) return;
    const res = await getVTASizeWeightByTxnId({ txnId: txnId });
    let { formData, canEditable } = getFormData(res.data);
    setCanEditable(canEditable);
    form.setFieldsValue({ ...formData });
  };
  useEffect(() => {
    form.setFieldsValue({ txnId });
    getRecordData();
  }, [txnId]);

  const submit = async () => {
    try {
      const data = await form.validateFields();
      let res = await saveVTASizeWeight(data);
      if (res.error) {
        return false;
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  useEffect(() => {
    if (!nextStepTrigger || nextStepTrigger === 0) {
      return;
    }
    handleNextStep(() => {
      return submit();
    });
  }, [nextStepTrigger]);
  const cabinTypeRadio = useMemo(() => {
    console.log(dict, 'dict', vtaCategory, cabinType);
    if (CarType.客車 === vtaCategory && cabinType === 'A') {
      return dict?.[22005];
    } else if (CarType.客車 === vtaCategory && cabinType === 'F') {
      return dict?.[22006];
    } else {
      return [];
    }
  }, [cabinType, vtaCategory, dict]);
  return (
    <div className={`${px('root')} ${className} multi-card-container`} {...otherProps}>
      <Form
        form={form}
        className={px('form')}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <Form.Item hidden name="txnId">
          <Input />
        </Form.Item>
        <div className="multi-card-item">
          <div className="multi-card-title">
            <span>{intl.formatMessage({ id: 'size_weight' })}</span>
          </div>
          <div className="multi-card-item-sub">
            <div className="multi-card-title">
              <span>{intl.formatMessage({ id: 'vehicle_size' })} (mm)</span>
            </div>
            <div className="multi-card-body">
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vehLength' })}
                    name="vehLength"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['vehLength']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vehWidth' })}
                    name="vehWidth"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['vehWidth']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vehHeight' })}
                    name="vehHeight"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['vehHeight']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'axleDistance' })}
                    name="axleDistance"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['axleDistance']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
                {![CarLevel['輕型摩托車'], CarLevel['重型摩托車']].includes(vehType) ? (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'rearWheelWidth' })}
                      name="rearWheelWidth"
                      rules={[
                        {
                          required: CarLevel.輕型汽車 === vehType ? false : true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <InputNumber
                        disabled={canEditable['rearWheelWidth']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                ) : (
                  <></>
                )}
                {[CarLevel.輕型汽車, CarLevel.重型汽車, CarLevel.工業機器].includes(vehType) ? (
                  <Col span={12}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'cabinType' })}
                      name="cabinType"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_select' }),
                        },
                      ]}
                    >
                      <VtaSelect
                        options={dict?.[22004] ?? []}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  </Col>
                ) : (
                  <></>
                )}
                <Col span={12}>
                  {CarType.客車 === vtaCategory ? (
                    <Form.Item
                      label={intl.formatMessage({ id: 'cabinCode' })}
                      name="cabinCode"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_select' }),
                        },
                      ]}
                    >
                      <VtaSelect
                        options={cabinTypeRadio}
                        placeholder={intl.formatMessage({ id: 'please_select' })}
                      />
                    </Form.Item>
                  ) : (
                    <Form.Item
                      label={intl.formatMessage({ id: 'cabinCode' })}
                      name="cabin_code_remark"
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <Input placeholder={intl.formatMessage({ id: 'please_enter' })} />
                    </Form.Item>
                  )}
                </Col>
              </Row>
            </div>
          </div>
          {flag ? (
            <div className="multi-card-item-sub">
              <div className="multi-card-title">
                <span>{intl.formatMessage({ id: 'car_size' })} (mm)</span>
              </div>
              <div className="multi-card-body">
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'cabinLength' })}
                      name="cabinLength"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <InputNumber
                        disabled={canEditable['cabinLength']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'cabinWidth' })}
                      name="cabinWidth"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <InputNumber
                        disabled={canEditable['cabinWidth']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'cabinHeight' })}
                      name="cabinHeight"
                      required
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'please_enter' }),
                        },
                      ]}
                    >
                      <InputNumber
                        disabled={canEditable['cabinHeight']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </div>
          ) : (
            <></>
          )}
          {CarLevel.重型摩托車 === vehType ? (
            <div className="multi-card-item-sub">

              <div className="multi-card-title">
                <span>{intl.formatMessage({ id: 'sidecard_size' })} (mm)</span>
              </div>
              <div className="multi-card-body">
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'sidecarLength' })}
                      name="sidecarLength"
                    >
                      <InputNumber
                        disabled={canEditable['sidecarLength']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'sidecarWidth' })}
                      name="sidecarWidth"
                    >
                      <InputNumber
                        disabled={canEditable['sidecarWidth']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'sidecarHeight' })}
                      name="sidecarHeight"
                    >
                      <InputNumber
                        disabled={canEditable['sidecarHeight']}
                        style={{ width: '100%' }}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </div>
          ) : (
            <></>
          )}
          <div className="multi-card-item-sub">
            <div className="multi-card-title">
              <span>{intl.formatMessage({ id: 'weight' })} (kg)</span>
            </div>
            <div className="multi-card-body">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vehWeight' })}
                    name="vehWeight"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['vehWeight']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vehGrossWeight' })}
                    name="vehGrossWeight"
                    required
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['vehGrossWeight']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vehLoadWeight' })}
                    name="vehLoadWeight"
                    required
                    tooltip={intl.formatMessage({ id: 'payload_formula' })}
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['vehLoadWeight']}
                      style={{ width: '100%' }}
                      placeholder=""
                      readOnly
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={intl.formatMessage({ id: 'vehMaxGrossWeight' })}
                    name="vehMaxGrossWeight"
                    tooltip={intl.formatMessage({ id: 'max_load' })}
                    rules={[
                      {
                        required: [
                          CarLevel.輕型汽車,
                          CarLevel.輕型摩托車,
                          CarLevel.重型摩托車,
                        ].includes(vehType)
                          ? true
                          : false,
                        message: intl.formatMessage({ id: 'please_enter' }),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={canEditable['vehMaxGrossWeight']}
                      style={{ width: '100%' }}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default FormTwo;
