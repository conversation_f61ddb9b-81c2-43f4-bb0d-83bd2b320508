import { MoPhone } from '@gov-mo/components';
import { Descriptions, Form } from 'antd';
import pc from 'prefix-classnames';
import React, {
  forwardRef,
  useImperativeHandle
} from 'react';
import { useIntl } from 'umi';

const px = pc('form4-28f');

export interface FormFourProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setDisableNextStep: (boolean: boolean) => void;
  txnId?: string
}

export type FormFourAgentCompRef = {
  tempStore: () => void;
};

const FormFour = forwardRef(
  (props: FormFourProps, ref: React.ForwardedRef<FormFourAgentCompRef>) => {
    useImperativeHandle(ref, () => ({
      // 暫存
      tempStore() {},
    }));

    const { className = '', setDisableNextStep, ...otherProps } = props;
    const intl = useIntl();
    const [form] = Form.useForm();
    return (
      <>
        <div className={`${px('root')} ${className}`} {...otherProps}>
          <Form form={form} className={px('form')} layout="vertical">
            <div className="my-card-container">
              <div className="my-card-item">
                <div className="my-card-title">
                  {intl.formatMessage({ id: 'importer_information' })}
                </div>
                <div className="my-card-body">
                  <Descriptions layout="vertical">
                    <Descriptions.Item
                      label={intl.formatMessage({ id: 'importer_chinese_name' })}
                      span={1}
                    >
                      進口商 62950
                    </Descriptions.Item>

                    <Descriptions.Item
                      label={intl.formatMessage({ id: 'importer_portuguese_name' })}
                      span={1}
                    >
                      IMPORTER 62950
                    </Descriptions.Item>
                  </Descriptions>
                   <Form.Item
                    style={{ width: '50%' }}
                    label={intl.formatMessage({ id: 'jks_phone' })}
                    name="agentContactPhone"
                    rules={[
                      { required: true, message: intl.formatMessage({ id: 'please_enter' }) },
                      {
                        pattern: /^6\d{7}$/,
                        message: intl.formatMessage({ id: 'phone_tips' }),
                      },
                    ]}
                  >
                    <div className="mophone-user">
                      <MoPhone
                        onlyAreaGroup={['+853']}
                        areaProps={{
                          disabled: true,
                          placeholder: intl.formatMessage({ id: 'please_select' }),
                          formItemProps: {
                            initialValue: '+853',
                          },
                        }}
                        defaultValue={'66131153'}
                        placeholder={intl.formatMessage({ id: 'please_enter' })}
                      />
                    </div>
                  </Form.Item>
                </div>
              </div>
            </div>
          </Form>
        </div>
      </>
    );
  },
);

export default FormFour;
