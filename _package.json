{"name": "gov-mo-demo", "version": "0.1.0", "private": true, "dependencies": {"@ali-whale/class-prefix": "^0.8.42", "@ali-whale/open-box": "^0.8.42", "@ali-whale/portal": "^0.8.43", "@ant-design/icons": "^4.6.2", "@umijs/preset-react": "^1.8.6", "antd-mobile-v5": "npm:antd-mobile@5.0.0-beta.18", "core-js": "3.6.5", "postcss-plugin-px2rem": "^0.8.1", "query-string": "^7.0.1", "react-redux": "^7.2.6"}, "scripts": {"start": "koi dev", "start:pre": "cross-env UMI_ENV=pre koi dev", "build": "koi build && npm run pack", "build:pre": "cross-env UMI_ENV=pre koi build && npm run pack", "test": "umi-test", "eject": "react-scripts eject", "publish": "koi publish", "eslint": "eslint --fix --ext .js --ext .jsx --ext .ts --ext .tsx ./src", "lint-staged": "lint-staged", "test:coverage": "umi-test --coverage", "analyze": "cross-env NODE_ENV=production ANALYZE=1 npm run build", "pack": "node scripts/pack.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@ali-dayu/koi": "^1.0.3", "@ali-whale/eslint-config": "^1.1.1", "@gov-mo/framework": "0.1.13", "@types/react-transition-group": "^4.4.0", "@umijs/test": "^3.2.27", "cross-env": "^5.2.1", "cross-spawn": "^7.0.3", "eslint": "^7.18.0", "husky": "^4.3.0", "lint-staged": "^10.5.0", "postcss-px-to-viewport": "^1.1.1", "shelljs": "^0.8.4", "tar": "^6.0.5", "typescript": "^4.1.3", "umi-plugin-convention-routes": "^0.2.1", "yorkie": "^2.0.0"}, "lint-staged": {"*.{js,jsx}": ["eslint --fix --quiet"], "*.ts?(x)": ["eslint --fix --quiet"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}