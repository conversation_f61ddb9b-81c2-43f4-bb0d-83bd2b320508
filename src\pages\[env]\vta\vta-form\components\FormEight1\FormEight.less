@prefix: form-eight1;

.@{prefix} {
  &- {
    &root {
      margin-left: 24px;
    }

    &check-container {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
      padding: 20px;
      background-color: #FEF5D1;

      .check-item {
        padding: 5px 0;
      }

      .ant-checkbox-inner {
        border-color: @brand-primary;
      }

      .ant-checkbox-wrapper {
        display: flex;
        align-items: start;

        .ant-checkbox {
          margin-top: 6px;
        }
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px
    }

    &sectionTitle:before {
      display: block;
      content: "";
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px
    }

    &sectionBody {
      .form-title {
        font-size: 18px;
        color: @brand-primary;
      }

      .form-title2 {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 18px;
        color: #232323;
        margin: 0 24px;
        padding-bottom: 10px
      }

      .form-title2:before {
        display: block;
        content: "";
        width: 4px;
        height: 16px;
        background: @brand-primary;
        margin-right: 8px
      }

      .ant-form {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .ant-form-item {
          width: 48%;
          font-size: 16px;

          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
            }
          }

          .bg {
            padding: 0 2px;
            color: #ffffff;
            background-color: @brand-primary;
          }
        }
      }

    }
  }
}
