/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import React, { useEffect } from 'react';
import { useDispatch, useIntl, useSelector } from 'umi';
import type { ApplicantInformationsModelsState } from 'umi';
import { Toast } from 'antd-mobile';
import { Row, Col } from 'antd';
import { Infos, Language } from '@gov-mo/mpaas-js-bridge';
import type { FetchResponse } from '@/utils/fetch';
// import { langText } from '@/locales/lang';
import getCertType from '@/utils/getCertType';
import { formatForeignName, formatName } from '@/utils';
import { infoList } from './config';
import WebDataCard from '../WebDataCard';
import styles from './ApplicantInformations.less';

export interface ApplicantInformationsProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  mobile: boolean;
}
function ApplicantInformations(props: ApplicantInformationsProps) {
  const { className = '', mobile, ...othersProps } = props;
  const { applicantInformations } = useSelector(
    ({
      applicantInformationsModels,
    }: {
      applicantInformationsModels: ApplicantInformationsModelsState;
    }) => applicantInformationsModels,
  );
  const dispatch = useDispatch();
  // const [dictMap] = useState<Record<string, Record<string, string>>>({ langText });
  const intl = useIntl();
  const title = intl.formatMessage({ id: '申请人资料' });

  useEffect(() => {
    Infos.requestProfile().then((res: FetchResponse<Record<string, string>>) => {
      if (res.success) {
        // 当用户不存在中文/英文名称时，nameCn/namePt字段不返回
        const [lastnameCn, fistnameCn] = formatName(res.data?.nameCn);
        const [lastnamePt, fistnamePt] = formatForeignName(res.data?.namePt);
        Language.getLanguage().then((langRes: FetchResponse<Record<string, string>>) => {
          const { data, success, message } = langRes;
          if (success) {
            const { language } = data;
            dispatch({
              type: 'applicantInformationsModels/update',
              payload: {
                applicantInformations: {
                  ...applicantInformations,
                  applicantLastnameZh: lastnameCn,
                  applicantNameZh: fistnameCn,
                  applicantLastnameFl: lastnamePt,
                  applicantNameFl: fistnamePt,
                  applicantCertType: res.data?.identityType,
                  applicantCertTypeDesc:
                    getCertType(res.data.identityType, language) || res.data.identityTypeDesc,
                  applicantCertNo: res.data?.identityNo,
                  mobilePhone: res.data?.mobile,
                  acceptInformType: intl.formatMessage({ id: '短讯' }),
                  informLanguage: language,
                },
              },
            });
          } else {
            Toast.fail(message);
          }
        });
      }
    });
  }, []);

  // const getNum = (tele: string): string => {
  //   if (tele === null || tele === undefined) return '';
  //   const reg = /^\+(86|853|852|886)/;
  //   if (reg.exec(tele)) {
  //     return tele.replace(reg, '($1)');
  //   }
  //   return `(853)${tele}`;
  // };

  // eslint-disable-next-line arrow-body-style
  const isShowFloat = (key: string): boolean => {
    // if (mobile || key === 'applicantLastnameFl' || key === 'applicantNameFl') return false;
    // return true;
    return false;
  };

  const content = (
    <div className={styles.infoContainer}>
      {infoList.map((group, index) => (
        <Row className={styles.infoArea} key={index}>
          {group.map((item) => {
            const { key, text, render = (v) => v, renderKey = (v, record) => v } = item;
            const renderText = applicantInformations[key];
            return (
              renderText && (
                <Col role="text" key={key} className={styles.listItem} span={mobile ? 24 : 12}>
                  <span className={`${styles.itemKey} ${mobile ? '' : styles.webItem}`}>
                    {intl.formatMessage({ id: renderKey(text, applicantInformations) })}
                    {isShowFloat(key) && <span className={styles.colon}>：</span>}
                  </span>
                  <span className={styles.itemValue}>{render(renderText)}</span>
                </Col>
              )
            );
          })}
        </Row>
      ))}
    </div>
  );

  if (!mobile) {
    return (
      <div className={`${styles.webRoot} ${className}`} {...othersProps}>
        <WebDataCard cardTitle={title}>{content}</WebDataCard>
      </div>
    );
  }

  return (
    <div className={`${styles.root} ${className}`} {...othersProps}>
      <div className={styles.titleArea}>
        <span className={styles.title}>{title}</span>
      </div>
      {content}
    </div>
  );
}

export default ApplicantInformations;
