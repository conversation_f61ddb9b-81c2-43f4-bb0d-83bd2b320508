@classprefix: form-one;
// @import '../../../。/../styles/listItemMixins.less';

.@{classprefix} {
  &-root {


  }

  &-text {
    font-size: 16px;
    line-height: 22px;
    white-space: pre-wrap;
    align-items: baseline;
    height: auto;
  }

  &-form {
    padding-bottom: 0;

  }

  &-row {
    font-size: 16px;
    margin-top: 8px;
    margin-bottom: 18px;
    //  justify-content: center;
    align-items: center;

    .span-t {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    .ant-btn {
      min-width: 62px;
      height: 36px;
      border-radius: 24px;
      margin-left: 12px;
      font-size: 16px;
      background-color: @brand-primary !important;
      color: #fff;


      &.ant-btn-default {
        background: #ffffff !important;
        color: #fff;
        border: 1px solid @brand-primary;
      }
    }
  }
}
