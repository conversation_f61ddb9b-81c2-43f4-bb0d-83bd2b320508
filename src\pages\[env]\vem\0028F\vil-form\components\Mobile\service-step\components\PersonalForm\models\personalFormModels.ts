export interface PersonalFormModelsState {
  projectDict: {
    label: string;
    value: string;
    disbled?: boolean;
  }[];
  usageDict: {
    label: string;
    value: string;
    disbled?: boolean;
  }[];
}

export const initPersonalFormModelsState = {
  projectDict: [],
  usageDict: [],
};

export default {
  namespace: 'personalFormModels',
  state: initPersonalFormModelsState,

  reducers: {
    update: (state: PersonalFormModelsState, { payload }: { payload: Partial<PersonalFormModelsState> }) => ({
      ...state,
      ...payload,
    }),
    init: () => initPersonalFormModelsState,
  },
};
