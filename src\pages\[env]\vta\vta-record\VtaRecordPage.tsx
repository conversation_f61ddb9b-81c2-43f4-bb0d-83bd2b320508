import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import { history, useIntl } from 'umi';
import './VtaRecordPage.module.less';
import FooterButton from '@/components/FooterButton';

import { Button, DatePicker, Form, Input, Pagination, Select } from 'antd';
import { getVTATxnList } from '@/services/vta';

const { RangePicker } = DatePicker;

const classPrefix = 'vta-record';
const px = pc(classPrefix);

const NewcarApplyRecordPage = () => {
  const intl = useIntl();

  const [step, setStep] = useState(1);
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));
  const [btnActive, setBtnActive] = useState(0);

  const [showPrevStep, setShowPrevStep] = useState(false);
  const [showNextStep, setShowNextStep] = useState(false);
  const [showClose, setShowClose] = useState(true);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);

  const prevStep = () => {
    setStep(step - 1);
  };

  const nextStep = () => {
    setStep(step + 1);
  };

  const handleClose = () => {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const [form] = Form.useForm();

  const getList = async (formFields?: any) => {
    const formData = formFields || form.getFieldsValue();
    console.log(formData);
    await getVTATxnList({ length: 10, start: 0 });
  };

  useEffect(() => {
    getList().then();
  }, []);

  return (
    <>
      <div className={`${px('root')} `}>
        <h2 className={px('title')}>{intl.formatMessage({ id: 'application_record_query' })}</h2>
        <div className={px('stepinfo')}>
          <div className={px('form')}>
            <Form form={form} layout="vertical">
              <div className={px('sectionBody')}>
                <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                  <Input value="" />
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'query_number' })}>
                  <Input value="" />
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'service_processing_date' })}>
                  <RangePicker style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                  <Select value={'1'}>
                    <Select.Option value="1">
                      {intl.formatMessage({ id: 'please_select' })}
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'style' })}>
                  <Select value={'1'}>
                    <Select.Option value="1">
                      {intl.formatMessage({ id: 'please_select' })}
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'service_application_status' })}>
                  <Select value={'1'}>
                    <Select.Option value="1">
                      {intl.formatMessage({ id: 'please_select' })}
                    </Select.Option>
                  </Select>
                </Form.Item>

                <div className={px('search-query-btn')}>
                  <div className={px('search-query-btn-box')}>
                    <Button
                      type="default"
                      className={btnActive == 0 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(0);
                      }}
                    >
                      {intl.formatMessage({ id: 'all' })}({12})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 1 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(1);
                      }}
                    >
                      {intl.formatMessage({ id: 'in_progress' })}({0})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 2 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(2);
                      }}
                    >
                      {intl.formatMessage({ id: 'pending_payment' })}({0})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 3 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(2);
                      }}
                    >
                      {intl.formatMessage({ id: 'pending_payment_approval' })}({0})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 4 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(2);
                      }}
                    >
                      {'認可階段-待審批'}({0})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 5 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(2);
                      }}
                    >
                      {'認可階段-待補正資料'}({0})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 6 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(2);
                      }}
                    >
                      {'待實物檢驗'}({0})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 7 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(2);
                      }}
                    >
                      {'商標及型號已獲核准'}({0})
                    </Button>
                    &nbsp;
                    <Button
                      type="default"
                      className={btnActive == 8 ? 'btn-active pure' : 'pure'}
                      onClick={() => {
                        setBtnActive(2);
                      }}
                    >
                      {intl.formatMessage({ id: 'my_create_record' })}({0})
                    </Button>
                  </div>
                  <div className="search-reset-btn">
                    <Button type="primary" style={{ marginRight: '10px' }}>
                      {intl.formatMessage({ id: 'search' })}
                    </Button>
                    <Button type="default">{intl.formatMessage({ id: 'reset' })}</Button>
                  </div>
                </div>

                {/* <div style={{width:'100%', display:'flex', justifyContent:'flex-end'}}>
                  <Button type='primary' style={{marginRight: '10px'}}>{intl.formatMessage({ id: 'search' })}</Button>
                  <Button type='default'>{intl.formatMessage({ id: 'reset' })}</Button>
                </div> */}

                <div style={{ width: '100%', marginBottom: '24px' }}></div>
                <div style={{ width: '100%' }}>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#E9B745' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                        {intl.formatMessage({ id: 'query_number' })}：230000101 (型號認可 - 申請)
                      </div>
                      <div className="status status-paying">
                        {intl.formatMessage({ id: 'pending_payment' })}
                      </div>
                      {/* <div>
                        <SystemQRcodeOutline />
                      </div> */}
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">--</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">
                          L - {intl.formatMessage({ id: 'light_vehicles' })}
                        </div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'brand' })}</div>
                        <div className="value">B.M.W</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'style' })}</div>
                        <div className="value">M5</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'style_year' })}</div>
                        <div className="value">2023</div>
                      </div>
                      {/* <div className="item">
                        <div className="label">建立時間</div>
                        <div className="value">2024-03-20 11:01</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'service_processing_date' })}</div>
                        <div className="value">2024-02-24 11:20</div>
                      </div>
                      <div className="item">
                        <div className="label">更新時間</div>
                        <div className="value">2024-03-20 13:01</div>
                      </div>
                      <div className="item">
                        <div className="label">總金額</div>
                        <div className="value">1500 MOP</div>
                      </div> */}
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        <Button type="default">{intl.formatMessage({ id: 'delete' })}</Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'update_information' })}
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#084AB8' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                        {intl.formatMessage({ id: 'query_number' })}：230000201 (型號認可 - 申請)
                      </div>
                      <div className="status status-paied">
                        {intl.formatMessage({ id: 'in_progress' })}
                      </div>
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">1/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">
                          L - {intl.formatMessage({ id: 'light_vehicles' })}
                        </div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'brand' })}</div>
                        <div className="value">B.M.W</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'style' })}</div>
                        <div className="value">M5</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'style_year' })}</div>
                        <div className="value">2023</div>
                      </div>
                      {/* <div className="item">
                        <div className="label">建立時間</div>
                        <div className="value">2024-03-20 11:01</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'service_processing_date' })}</div>
                        <div className="value">2024-02-24 11:20</div>
                      </div>
                      <div className="item">
                        <div className="label">更新時間</div>
                        <div className="value">2024-03-20 13:01</div>
                      </div>
                      <div className="item">
                        <div className="label">總金額</div>
                        <div className="value">1500 MOP</div>
                      </div> */}
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        <Button type="default">{intl.formatMessage({ id: 'delete' })}</Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'update_information' })}
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                        <Button type="default">
                          {intl.formatMessage({ id: 'download_receipt' })}
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#13A07B' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                        {intl.formatMessage({ id: 'query_number' })}：230000301 (型號認可 - 申請)
                      </div>
                      <div className="status status-passed">已付款</div>
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">1/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">
                          L - {intl.formatMessage({ id: 'light_vehicles' })}
                        </div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'brand' })}</div>
                        <div className="value">B.M.W</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'style' })}</div>
                        <div className="value">M5</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'style_year' })}</div>
                        <div className="value">2023</div>
                      </div>
                      {/* <div className="item">
                        <div className="label">建立時間</div>
                        <div className="value">2024-03-20 11:01</div>
                      </div> */}
                      {/* <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'service_processing_date' })}</div>
                        <div className="value">2024-02-24 11:20</div>
                      </div> */}
                      {/* <div className="item">
                        <div className="label">更新時間</div>
                        <div className="value">2024-03-20 13:01</div>
                      </div>
                      <div className="item">
                        <div className="label">總金額</div>
                        <div className="value">1500 MOP</div>
                      </div> */}
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        <Button type="default">{intl.formatMessage({ id: 'delete' })}</Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                        <Button type="default">
                          {intl.formatMessage({ id: 'download_receipt' })}
                        </Button>
                        <Button type="default">下載驗車合格紙</Button>
                        <Button type="default">相關進口准照</Button>
                      </div>
                    </div>
                  </div>
                </div>
                <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                  <Pagination defaultCurrent={1} total={60} />
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
      <FooterButton
        // handleClose={handleClose}
        className={px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
      />
    </>
  );
};

export default NewcarApplyRecordPage;
