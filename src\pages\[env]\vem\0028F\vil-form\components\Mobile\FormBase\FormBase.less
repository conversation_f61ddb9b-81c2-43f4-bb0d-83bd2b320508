@prefix: page-28f-mobile;

.@{prefix} {
  &- {
    &root {
      display: flex;
      flex-direction: column;
      padding-top: 0.45rem;
      border-bottom: 1rem solid #eeeeee;
      overflow-y: auto;
      background-color: #ffffff;
    }
    .ant-form-item-label > label {
      font-size: 0.16rem;
    }

    font-size: 0.16rem;
    .ant-form {
      font-size: 0.16rem;
    }
    .ant-form-item-label > label {
      font-size: 0.16rem;
    }
    .ant-select {
      font-size: 0.16rem;
    }
    .ant-input {
      font-size: 0.16rem;
    }
    &title {
      color: #363636;
      line-height: 0.21rem;
      text-align: center;
      font-weight: 700;
      padding: 0 0.24rem 0.2rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      font-size: 0.16rem;
      font-weight: 400;
    }

    &stepinfo {
      flex: 1;
      flex-direction: column;
      display: flex;
    }
    &mobile-step {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.2rem 0.25rem;
      .step-item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 0.33rem;
        height: 0.33rem;
        font-size: 0.14rem;
        color: #ffffff;
        background-color: rgba(193, 193, 193, 0.25);
        border-radius: 50%;
        span {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 0.25rem;
          height: 0.25rem;
          background-color: #c1c1c1;
          border-radius: 50%;
        }
        &.finish {
          background-color: rgba(8, 74, 184, 0.25);
          span {
            background-color: @brand-primary;
          }
        }
        &.progress {
          background-color: rgba(255, 193, 7, 0.25);
          span {
            background-color: #ffc107;
          }
        }
      }
      .line {
        flex: 1;
        margin: 0 0.16rem;
        min-width: 0.1rem;
        max-width: 1rem;
        height: 2px;
        background-color: #c1c1c1;
      }
    }

    &step {
      min-width: -webkit-fit-content;
      min-width: -moz-fit-content;
      min-width: fit-content;
    }

    &step h3 {
      font-size: 0.16rem;
      line-height: 0.22rem;
      font-weight: 700;
      padding: 0 0.02rem 0.16rem;
    }

    &step .ant-steps-small {
      font-size: 0.16rem;
    }

    &step .ant-steps-small .ant-steps-item {
      height: 0.5rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon {
      background-color: rgba(8, 74, 184, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-icon {
      background-color: @brand-primary;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-tail:after {
      background-color: @brand-primary;
      height: 0.2rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-icon {
      background-color: rgba(255, 193, 7, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-icon {
      background-color: #ffc107;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 0.3rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-icon {
      background-color: hsla(0, 0%, 76.9%, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-icon {
      background-color: #c4c4c4;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 0.3rem;
    }

    &step
      .ant-steps-small
      .ant-steps-item.ant-steps-item-wait:last-child
      .ant-steps-item-tail:after {
      display: none;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container {
      height: 0.4rem;
      min-height: 0.4rem;
      display: flex;
      align-items: center;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-tail {
      bottom: 0;
      top: 0.35rem;
      left: 0.14rem;
      padding: 0;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon {
      width: 0.28rem;
      height: 0.28rem;
      border-radius: 50%;
      margin-right: 0.06rem;
      cursor: pointer;
      flex-shrink: 0;
    }

    &step
      .ant-steps-small
      .ant-steps-item
      .ant-steps-item-container
      .ant-steps-item-icon
      .ant-steps-icon {
      height: 0.18rem;
      min-height: 0.18rem;
      display: flex;
      align-items: center;
      width: 0.18rem;
      line-height: 0.18rem;
      left: 0.05rem;
      top: 0.05rem;
      font-size: 0.14rem;
      justify-content: center;
      color: #fff;
      border-radius: 50%;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content {
      height: 0.4rem;
      min-height: 0.4rem;
      display: flex;
      align-items: center;
      line-height: 0.16rem;
    }

    &step
      .ant-steps-small
      .ant-steps-item
      .ant-steps-item-container
      .ant-steps-item-content
      .ant-steps-item-title {
      font-size: 0.16rem;
      color: #333;
    }

    &form {
      display: flex;
      flex-direction: column;
      margin-top: 0;
      background-color: #eeeeee;
      flex: 1;
    }

    &form h3 {
      font-size: 0.16rem;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 0.24rem;
    }

    &form > .pc-pay-ui-root {
      margin-left: 0.24rem;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 0.65rem;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 0.16rem;
      color: #666;
      top: -0.02rem;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 0.24rem;
      top: -0.02rem;
    }

    &formTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      margin-left: 0.24rem;
      font-size: 0.18prem;
      color: #232323;
      padding-bottom: 0.1rem;
    }

    &spin {
      width: 100%;
      height: 100%;
      padding: 45%;
    }

    &footer-button {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 0.2rem;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
      height: 1rem;
      gap: 4%;
      .ant-btn {
        flex: 1;
        height: 0.52rem;
        line-height: 0.52rem;
        border-radius: 0.26rem;
        font-size: 0.16rem;
        padding: 0 0.05rem;
        &.ant-btn-default {
          background: #fff;
          color: @brand-primary;
          border: 1px solid @brand-primary;
        }
      }
    }
  }
}

.am-navbar {
  position: fixed;
  top: 0;
  z-index: 99;
  width: 100%;
  color: #000000;
  padding-bottom: 0.1rem;
  height: auto;
  .select {
    position: relative;
    margin-right: 0.1rem;
    padding: 0.04rem 0.08rem;
    line-height: 1em;
    border: 1px solid #ccc;
    border-radius: 0.3rem;
    color: #666;
    font-size: 0.14rem;
    white-space: nowrap;
    box-shadow: 0 0 0.05rem 0 rgba(0, 0, 0, 0.06);
    .opt-wrap {
      position: absolute;
      z-index: 1;
      left: 50%;
      top: 100%;
      margin-top: 0.05rem;
      border-radius: 0.05rem;
      background-color: #ffffff;
      box-shadow: 0 0 0.05rem 0 rgba(0, 0, 0, 0.1);
      transform: translate(-50%, 0);

      font-size: 0.16rem;
      .item {
        display: flex;
        align-items: center;
        white-space: nowrap;
        .serial {
          margin-right: 0.05rem;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 0.25rem;
          height: 0.25rem;
          background-color: #c1c1c1;
          border-radius: 50%;
          color: #ffffff;
        }
        &.progress {
          color: #ffc107;
          .serial {
            background-color: #ffc107;
          }
        }
        &.finish {
          color: @brand-primary;
          .serial {
            background-color: @brand-primary;
          }
        }
      }
      .line {
        margin: 0.03rem 0.11rem;
        height: 0.2rem;
        width: 2px;
        background-color: #c1c1c1;
      }
    }
  }
}

// mobile card style

.my-card-container-mobile {
  .my-card-item-mobile {
    border-radius: 0.12rem;
    margin-bottom: 0.2rem;
    &:nth-of-type(4n + 1) {
      background-color: @brand-primary;
    }
    &:nth-of-type(4n + 2) {
      background-color: #e9b745;
    }
    &:nth-of-type(4n + 3) {
      background-color: #13a07b;
    }
    &:nth-of-type(4n + 4) {
      background-color: #f33b40;
    }
    .my-card-title-mobile {
      height: 0.44rem;
      padding: 0 0.2rem;
      color: #fff;
      font-size: 0.16rem;
      display: flex;
      align-items: center;
    }
    .my-card-body-mobile {
      background: #fff;
      border: 1px solid #f0f0f0;
      padding: 0.2rem;
      border-radius: 0 0 0.12rem 0.12rem;
    }
  }
}

.step-h {
  padding: 0 0.12rem;
  .ant-steps-small .ant-steps-item-title:after {
    top: 0;
  }
  .ant-steps-item-process
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-title:after {
    background-color: #c4c4c4;
  }
  .ant-steps-item-wait
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-title:after {
    background-color: #c4c4c4;
  }
  .ant-steps-item-finish
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-title:after {
    background-color: @brand-primary;
  }
}
