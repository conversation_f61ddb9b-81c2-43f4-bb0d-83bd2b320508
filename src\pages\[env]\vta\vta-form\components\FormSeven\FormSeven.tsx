import React from 'react';
import pc from 'prefix-classnames';
import './FormSeven.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Upload, message, Divider  } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

const px = pc('form-seven');

const props = {
  name: 'file',
  action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
  headers: {
    authorization: 'authorization-text',
  },
  onChange(info) {
    if (info.file.status !== 'uploading') {
      console.log(info.file, info.fileList);
    }
    if (info.file.status === 'done') {
      message.success(`${info.file.name} file uploaded successfully`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed.`);
    }
  },
};

export interface FormSevenProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {

}

const FormSeven = (props: FormSevenProps) => {
  const { className = '', ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>

    </div>
  );
};

export default FormSeven;
