import React, { useContext, useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import dayjs from 'dayjs';
import './Preview.less';
import { <PERSON><PERSON>, Button, Form } from 'antd';
import { getLocale, history, Location, useIntl, useLocation } from 'umi';

import {
  getEXTxnData,
  sendFacialRecognitionECNotice,
  validateTxnForPayment,
  validateTxnIsNeedToPay,
} from '@/services/0028F';
import { getLangGroup, getResponseMessage, Lang } from '@/locales/lang';
import { useCountDown } from '@/utils/hooks';
import { handleContactName } from '@/utils';
import SpinContext from '../../../../../../../components/SpinContext';
import { completeOvsapTxn } from '@/services/0033D-ii';
import { useToggle } from 'ahooks';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import Watermark from '@/components/Watermark';

const px = pc('preview-28f');

export interface FormEightProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setNextStepText: (string) => void;
  setCloseText?: (value: string) => void;
  setShowCloseAndTempStore: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowClose: (boolean) => void;

  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;
  setDisablePrevStep: (boolean) => void;
  setShowNextStep: (boolean) => void;
  setShowPrevStep: (boolean) => void;
  onApplied: (isApplied: boolean) => void;
  txnId: string | undefined;
  onTxnStatus: (string) => void;
  initStepOptions: StepOptionsItem[];
  setStepOptions: (opt: StepOptionsItem[]) => void;
}

type FormPreviewLocation = {
  query: {
    showSuccess?: string;
  };
};

// 倒數計時
const CountdownButton = ({
  item,
  noticeStatus,
  setNoticeStatus,
  setTipMsg,
  txnId,
  setDisableNextStep,
  getData,
}) => {
  const onExpire = async () => {
    sendFacialRecognitionECNotice({
      txnId: txnId || '',
      ownerId: item.txnOwnerId,
      channelType: '2',
      identType: item.ownerIdentType,
      identNo: item.ownerIdentNo,
    })
      .then((ret) => {
        if (ret.code === '0') {
          start();
        } else {
          setTipMsg(getResponseMessage(ret));
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });
  };
  const { seconds, isRunning, start } = useCountDown({
    expiryTime: 60,
    onExpire: () => {
      const status = noticeStatus!.map((notice) => {
        if (notice.ownerId == item.txnOwnerId) {
          notice.status = 1;
          item.verficationStatus = 'P';
        }
        return notice;
      });
      setNoticeStatus(status);
      setDisableNextStep(true);
      getData(txnId as string);
    },
  });

  return (
    <Button
      disabled={isRunning}
      onClick={onExpire}
      type="primary"
      style={{ whiteSpace: 'normal', maxWidth: '350px', height: 'auto' }}
    >
      {getLangGroup(
        item?.agentVerficationStatusCn,
        item?.agentVerficationStatusPt,
        item?.agentVerficationStatusEn,
      )}
      {isRunning ? `(${seconds}s)` : ''}
    </Button>
  );
};

const FormEight = (props: FormEightProps) => {
  const { className = '', ...otherProps } = props;
  const intl = useIntl();
  const {
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    contentNextStep,
    contentForNextStep,
    setDisableNextStep,
    setDisablePrevStep,
    setShowNextStep,
    setShowPrevStep,
    onApplied,
    txnId,
    onTxnStatus,
    setNextStepText,
    setStepOptions,
    initStepOptions,
  } = otherProps;

  const [tipMsg, setTipMsg] = useState('');
  const [form] = Form.useForm();
  const [ownerDataList, setOwnerDataList] = useState<saveTxnOwnersParams[]>([]);
  const [ovsapVehDataDTO, setOvsapVehDataDTO] = useState<ovsapVehDataDTO>();
  const [ovsapAgentCompData, setOvsapAgentCompData] = useState<saveAgentCompParams>();
  const [ovsapTxnContactDTO, setOvsapTxnContactDTO] = useState<getTxnContactRes>();
  const [completeResultDTO, setCompleteResultDTO] = useState<CompleteResultDTOType | undefined>(
    undefined,
  );
  const [noticeStatus, setNoticeStatus] = useState<{ ownerId: string; status: number }[]>();
  const [txnStatus, setTxnStatus] = useState<string>('');

  const location = useLocation() as Location & FormPreviewLocation;
  const { setLoading } = useContext(SpinContext);
  const { showSuccess } = location.query;

  const [dspaFpDataDto, setDspaFpDataDto] = useState<{
    dspaFpNo: string;
    dspaFpFeeCodeDto: Record<string, any>;
  }>({ dspaFpNo: '', dspaFpFeeCodeDto: {} });

  const [_, setIsNeedPay] = useState<boolean>(true);
  const fetchValidateTxnIsNeedToPay = async () => {
    if (!txnId) return;
    const { data, code } = await validateTxnIsNeedToPay(txnId);
    if (code !== '0') return;

    setIsNeedPay(data.isNeedPay);
    // 不需要支付
    if (!data.isNeedPay) {
      // 修改下一步按钮文案和步骤
      setNextStepText(intl.formatMessage({ id: 'submit_application' }));
      setStepOptions([...initStepOptions.filter((item) => item.sort !== 5)]);
    }
  };

  const getData = (txnId: string) => {
    getEXTxnData(txnId)
      .then((ret) => {
        setDisableNextStep(false);
        if (ret?.code === '0') {
          const { data } = ret;
          setTxnStatus(data?.txnStatus);
          onTxnStatus(data?.txnStatus);
          setOwnerDataList(data.ownerDataList);
          setDspaFpDataDto(data?.dspaFpDataDTO || {});
          setOvsapVehDataDTO(data.ovsapVehDataDTO);
          setOvsapAgentCompData(data.ovsapAgentCompData);
          setOvsapTxnContactDTO(data.ovsapTxnContactRequDTO);
          setCompleteResultDTO(data.completeResultDTO);

          const status: { ownerId: string; status: number }[] = (data?.ownerDataList || [])?.map(
            (item) => ({
              ownerId: item.txnOwnerId,
              status: item.verficationStatus != 'W' ? 1 : 0,
            }),
          );

          setNoticeStatus(status);

          if (data.txnStatus === 'E' || data.txnStatus === 'F' || data.txnStatus === 'S') {
            setShowNextStep(false);
            setShowPrevStep(false);
            onApplied(true);
            props.setCloseText && props.setCloseText(intl.formatMessage({ id: 'turn_off' }));
          } else if (data.txnStatus === 'A') {
            setDisablePrevStep(false);
            setDisableNextStep(true);
          } else if (data.txnStatus === 'P') {
            setDisablePrevStep(false);
            setDisableNextStep(false);
          }
          if (data.txnStatus !== 'E' && data.txnStatus !== 'F' && data.txnStatus !== 'S') {
            fetchValidateTxnIsNeedToPay();
          }
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    let timer;
    if (txnId) {
      getData(txnId);
      timer = setInterval(() => {
        // 非 P E F S 才60s刷新一次
        if (!['P', 'E', 'F', 'S'].includes(txnStatus)) {
          getData(txnId);
        }
      }, 60 * 1000);
    } else {
      setLoading(false);
    }
    return () => {
      clearInterval(timer);
    };
  }, [txnId]);

  useEffect(() => {
    setDisableNextStep(true);
    setLoading(true);
    contentForNextStep(1);
    setShowCloseAndTempStore(true);
    setShowClose(false);
    setShowTempStore(false);
    return () => {
      contentForNextStep(0);
      setShowCloseAndTempStore(false);
      setShowClose(true);
      setShowTempStore(true);
      // 清除是否支付副作用
      setNextStepText(intl.formatMessage({ id: 'next' }));
      setStepOptions([...initStepOptions]);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      async function fn() {
        setTipMsg('');

        if (!txnId) {
          return;
        }

        const ret = await validateTxnForPayment(txnId).catch((e) => {
          setTipMsg(e.message);
        });

        if (!ret) {
          setDisableNextStep(false);
          return
        };
        if (ret.code !== '0') {
          setTipMsg(getResponseMessage(ret));
          setDisableNextStep(false);
          return;
        }

        if (ret?.data?.isNeedPay === false) {
          const res = await completeOvsapTxn(txnId);
          if (res?.code !== '0') {
            setDisableNextStep(false);
            return;
          }
          history.push(`/web/vem/0028F/vil-form?page=success&txnId=${txnId}`);
          return;
        }

        contentForNextStep(2);
        setDisableNextStep(false);
      }

      fn();
    }
  }, [contentNextStep]);

  const [state, { toggle }] = useToggle();
  return (
    <Watermark text={dayjs().format('YYYY-MM-DD HH:mm:ss')}>
      {tipMsg && (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('sectionTitle')}>{intl.formatMessage({ id: 'data_confirmation' })}</div>
        <div className="my-card-container">
          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'usage_license_plates' })}
            </div>
            <div className="my-card-body">
              <Form className="owner-card-form">
                <Form.Item style={{ width: '100%' }}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.plateNoUserCn,
                      ovsapVehDataDTO?.plateNoUserPt,
                      ovsapVehDataDTO?.plateNoUserEn,
                    )}
                  </div>
                  <div>{ovsapVehDataDTO?.relatedPlateNo || ''}</div>
                </Form.Item>
              </Form>
            </div>
          </div>

          {dspaFpDataDto?.dspaFpNo ? (
            <div className="my-card-item">
              <div className="my-card-title">
                {intl.formatMessage({ id: 'old_motorcycle_replace_subsidy' })}
              </div>
              <div className="my-card-body">
                <Form
                  colon={false}
                  labelAlign="left"
                  form={form}
                  className="owner-card-form"
                  layout="vertical"
                >
                  <Form.Item label={intl.formatMessage({ id: 'fp_no' })} style={{ width: '100%' }}>
                    <div>{dspaFpDataDto?.dspaFpNo}</div>
                    <div style={{ color: '#084ab8' }}>
                      {`${getLangGroup(
                        dspaFpDataDto?.dspaFpFeeCodeDto.codeCname,
                        dspaFpDataDto?.dspaFpFeeCodeDto.codePname,
                        dspaFpDataDto?.dspaFpFeeCodeDto.codeEname,
                      )}`}
                    </div>
                  </Form.Item>
                </Form>
              </div>
            </div>
          ) : (
            ''
          )}

          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'car_owner_information' })}
            </div>
            <div className="owner-card-container-28f">
              {ownerDataList.map((item, index, arr) => (
                <div
                  className={
                    dspaFpDataDto?.dspaFpNo
                      ? 'owner-card-item-28f owner-card-3'
                      : 'owner-card-item-28f owner-card-2'
                  }
                >
                  <div className="owner-card-title">
                    {intl.formatMessage({ id: 'car_owner_information' })}
                    {arr.length > 1 ? index + 1 : ''}
                  </div>
                  <Form
                    colon={false}
                    labelAlign="left"
                    form={form}
                    className="owner-card-form"
                    layout="vertical"
                  >
                    <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                      <div>
                        {item.ownerLastNameCn}
                        {item.ownerFirstNameCn}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                      <div>
                        {item.ownerLastNamePt} {item.ownerFirstNamePt}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                      <div>
                        {getLangGroup(
                          item.ownerIdentTypeCn,
                          item.ownerIdentTypePt,
                          item.ownerIdentTypeEn,
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'id_number' })}>
                      <div>{item.ownerIdentNo}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'contact_address' })}>
                      <div>{item.ownerAddr}</div>
                    </Form.Item>
                    <Form.Item></Form.Item>
                    {(ownerDataList || []).length > 1 &&
                      ['L', 'P'].includes(ovsapVehDataDTO?.vehType || '') && (
                        <>
                          <Form.Item label={intl.formatMessage({ id: 'registered_residence' })}>
                            <div>{item.dsajAddr}</div>
                          </Form.Item>
                          <Form.Item label={intl.formatMessage({ id: 'share' })}>
                            <div>
                              {item.quotaNumerator} / {item.quotaDenominator}
                            </div>
                          </Form.Item>
                        </>
                      )}

                    {item.isShowFacialLabel ? (
                      <Form.Item label="">
                        <span
                          className={
                            item.verficationStatus == 'P'
                              ? 'statusBg1'
                              : item.verficationStatus == 'Y'
                              ? 'statusBg2'
                              : 'statusBg1'
                          }
                        >
                          {getLangGroup(
                            item?.verficationStatusCn,
                            item?.verficationStatusPt,
                            item?.verficationStatusEn,
                          )}
                        </span>
                      </Form.Item>
                    ) : null}

                    {item.isShowFacialButton ? (
                      <Form.Item label="">
                        <CountdownButton
                          item={item}
                          noticeStatus={noticeStatus}
                          setNoticeStatus={setNoticeStatus}
                          setTipMsg={setTipMsg}
                          txnId={txnId}
                          setDisableNextStep={setDisableNextStep}
                          getData={getData}
                        />
                      </Form.Item>
                    ) : null}
                  </Form>
                </div>
              ))}
            </div>
          </div>

          <div className="my-card-item">
            <div className="my-card-title flex-c-s">
              <div>
                {intl.formatMessage({ id: 'vehicle_information' })}{' '}
                {/* <span style={{ color: '#f00', marginLeft: '20px' }}>
                {getLangGroup(
                    ovsapVehDataDTO?.newCheckResultCodeDto.codeCname,
                    ovsapVehDataDTO?.newCheckResultCodeDto.codeEname,
                    ovsapVehDataDTO?.newCheckResultCodeDto.codePname,
                  )}
                </span> */}
              </div>
              {!state ? (
                <CaretDownOutlined onClick={() => toggle()} />
              ) : (
                <CaretUpOutlined onClick={() => toggle()} />
              )}
            </div>
            <div className="my-card-body" style={{ paddingBottom: 0 }}>
              <Form
                colon={false}
                labelAlign="left"
                form={form}
                className="owner-card-form"
                layout="vertical"
              >
                {false && (
                  <>
                    <Form.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                      <div>{ovsapVehDataDTO?.importNoFull}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                      <div>{ovsapVehDataDTO?.vtaNoFull}</div>
                    </Form.Item>
                  </>
                )}
                <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehBrandDescCn,
                      ovsapVehDataDTO?.vehBrandDescPt,
                      ovsapVehDataDTO?.vehBrandDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'style' })}>
                  <div>{ovsapVehDataDTO?.vehModel}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                  <div>{ovsapVehDataDTO?.vehBuildYear}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'color' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.colorDescCn,
                      ovsapVehDataDTO?.colorDescPt,
                      ovsapVehDataDTO?.colorDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehTypeDescCn,
                      ovsapVehDataDTO?.vehTypeDescPt,
                      ovsapVehDataDTO?.vehTypeDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehUsageDescCn,
                      ovsapVehDataDTO?.vehUsageDescPt,
                      ovsapVehDataDTO?.vehUsageDescEn,
                    )}
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                  <div>{ovsapVehDataDTO?.vin}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehSourceCtryDescCn' })}>
                  <div>
                    {getLangGroup(
                      ovsapVehDataDTO?.vehSourceCtryDescCn,
                      ovsapVehDataDTO?.vehSourceCtryDescPt,
                      ovsapVehDataDTO?.vehSourceCtryDescEn,
                    )}
                  </div>
                </Form.Item>

                <div
                  style={{ height: state ? 'auto' : '0px', overflow: 'hidden' }}
                  className="flex-c-s"
                >
                  <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehCateGoryDescCn,
                        ovsapVehDataDTO?.vehCateGoryDescPt,
                        ovsapVehDataDTO?.vehCateGoryDescEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                    <div>{ovsapVehDataDTO?.engineNo}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                    <div>{ovsapVehDataDTO?.engineNo2}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                    <div>{ovsapVehDataDTO?.engineNo3}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                    <div>{ovsapVehDataDTO?.engineNo4}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'cylinderQty' })}>
                    <div>{ovsapVehDataDTO?.cylinderQty}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'cylinderVol' })}>
                    <div>{`${ovsapVehDataDTO?.cylinderVol || 0} c.c`}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'maxPowerWithUnit' })}>
                    <div>{ovsapVehDataDTO?.maxPowerWithUnit}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'fuelTypeDescCn' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.fuelTypeDescCn,
                        ovsapVehDataDTO?.fuelTypeDescPt,
                        ovsapVehDataDTO?.fuelTypeDescEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_size' })}>
                    {(ovsapVehDataDTO?.vehLength ||
                      ovsapVehDataDTO?.vehWidth ||
                      ovsapVehDataDTO?.vehHeight) && (
                      <div>
                        ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.vehLength} mm x (
                        {intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.vehWidth} mm x (
                        {intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.vehHeight} mm
                      </div>
                    )}
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'cabinTypeDescCn' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.cabinTypeDescCn,
                        ovsapVehDataDTO?.cabinTypeDescPt,
                        ovsapVehDataDTO?.cabinTypeDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'car_size' })}>
                    {(ovsapVehDataDTO?.cabinLength ||
                      ovsapVehDataDTO?.cabinWidth ||
                      ovsapVehDataDTO?.cabinHeight) && (
                      <div>
                        ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.cabinLength} mm x
                        ({intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.cabinWidth} mm x (
                        {intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.cabinHeight} mm
                      </div>
                    )}
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'seating_apacity' })}>
                    <div>{ovsapVehDataDTO?.seatQty}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'tyre_f_r_s' })}>
                    <div>{ovsapVehDataDTO?.tyreDesc}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'frontTyreQty' })}>
                    <div>{ovsapVehDataDTO?.frontTyreQty}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'rearTyreQty' })}>
                    <div>{ovsapVehDataDTO?.rearTyreQty}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'frontAxleQty' })}>
                    <div>{ovsapVehDataDTO?.frontAxleQty}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'rearAxleQty' })}>
                    <div>{ovsapVehDataDTO?.rearAxleQty}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehWeight' })}>
                    <div>
                      {ovsapVehDataDTO?.vehWeight} {ovsapVehDataDTO?.vehWeight && `kg`}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehLoadWeight' })}>
                    <div>
                      {ovsapVehDataDTO?.vehLoadWeight} {ovsapVehDataDTO?.vehLoadWeight && `kg`}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehGrossWeight' })}>
                    <div>
                      {ovsapVehDataDTO?.vehGrossWeight} {ovsapVehDataDTO?.vehGrossWeight && `kg`}
                    </div>
                  </Form.Item>
                </div>
              </Form>
            </div>
          </div>
          <div className="my-card-item">
            <div className="my-card-title">
              {intl.formatMessage({ id: 'importer_information' })}
            </div>
            <div className="my-card-body">
              <Form
                colon={false}
                labelAlign="left"
                form={form}
                className="owner-card-form"
                layout="vertical"
              >
                <Form.Item label={intl.formatMessage({ id: 'importer_chinese_name' })}>
                  <div>{ovsapAgentCompData?.vtaCompCname}</div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'importer_portuguese_name' })}>
                  <div>{ovsapAgentCompData?.vtaCompPname}</div>
                </Form.Item>
              </Form>
            </div>
          </div>
          <div className="my-card-item">
            <div className="my-card-title">{intl.formatMessage({ id: 'agent_information' })}</div>
            <div className="my-card-body">
              <Form
                colon={false}
                labelAlign="left"
                form={form}
                className="owner-card-form"
                layout="vertical"
              >
                <Form.Item label={intl.formatMessage({ id: 'agent_phone' })}>
                  <div>{ovsapAgentCompData?.agentContactPhone}</div>
                </Form.Item>
              </Form>
            </div>
          </div>
          <div className="my-card-item">
            <div className="my-card-title">{intl.formatMessage({ id: 'contact_information' })}</div>
            <div className="my-card-body">
              {ovsapTxnContactDTO?.contactAgreest === 'Y' ? (
                <Form
                  colon={false}
                  labelAlign="left"
                  form={form}
                  className="owner-card-form"
                  layout="vertical"
                >
                  <Form.Item label={intl.formatMessage({ id: 'car_owner_view' })}>
                    <div>{handleContactName(ovsapTxnContactDTO)}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'local_mobile_phone' })}>
                    <div>{ovsapTxnContactDTO.contactMobilePhone}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'language' })}>
                    {getLangGroup(
                      ovsapTxnContactDTO?.contactLanguageCn,
                      ovsapTxnContactDTO?.contactLanguagePt,
                      ovsapTxnContactDTO?.contactLanguageEn,
                    )}
                  </Form.Item>
                  <Form.Item style={{ width: '100%' }}>
                    {getLangGroup(
                      ovsapTxnContactDTO?.agreeCodeDto?.codeCname,
                      ovsapTxnContactDTO?.agreeCodeDto?.codePname,
                      ovsapTxnContactDTO?.agreeCodeDto?.codeEname,
                    )}
                  </Form.Item>
                </Form>
              ) : (
                <div className="disAgree">
                  {getLangGroup(
                    ovsapTxnContactDTO?.disAgreeCodeDto?.codeCname,
                    ovsapTxnContactDTO?.disAgreeCodeDto?.codePname,
                    ovsapTxnContactDTO?.disAgreeCodeDto?.codeEname,
                  )}
                </div>
              )}
            </div>
          </div>
          {showSuccess && (txnStatus === 'E' || txnStatus === 'F') && (
            <div className="my-card-item">
              <div className="my-card-title">
                {intl.formatMessage({ id: 'submit_application_materials' })}
              </div>
              <div className="my-card-body">
                <Form
                  colon={false}
                  labelAlign="left"
                  form={form}
                  className="owner-card-form"
                  layout="vertical"
                >
                  <Form.Item label={intl.formatMessage({ id: 'query_number' })}>
                    <div>{completeResultDTO?.spNo}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'service_procedure_name' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.serviceTitleRespDTO.serviceTitleCn,
                        completeResultDTO?.serviceTitleRespDTO.serviceTitlePt,
                        completeResultDTO?.serviceTitleRespDTO.serviceTitleEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'service_remarks' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.remarkCn,
                        completeResultDTO?.remarkPt,
                        completeResultDTO?.remarkEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'invoice_number' })}>
                    <div>{completeResultDTO?.invoiceNumber}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'trading_time' })}>
                    <div>
                      {completeResultDTO?.payDate
                        ? dayjs(completeResultDTO?.payDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'transaction_channel' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.payMethodCn,
                        completeResultDTO?.payMethodPt,
                        completeResultDTO?.payMethodEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'payment_method' })}>
                    <div>{completeResultDTO?.payMode}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'total_transaction_amount' })}>
                    <div>{completeResultDTO?.payAmount?.toLocaleString()}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'payment_status' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.payStatusCn,
                        completeResultDTO?.payStatusPt,
                        completeResultDTO?.payStatusEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'submitted_documents' })}>
                    <div>
                      {(
                        (getLocale() === Lang.葡语
                          ? completeResultDTO?.docNameListPt
                          : completeResultDTO?.docNameListCn) ?? []
                      ).map((item) => (
                        <>
                          {item}
                          <br />
                        </>
                      ))}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'application_status' })}>
                    <div>
                      {getLangGroup(
                        completeResultDTO?.txnStatusCn,
                        completeResultDTO?.txnStatusPt,
                        completeResultDTO?.txnStatusEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'establishment_time' })}>
                    <div>
                      {completeResultDTO?.txnStatusDate
                        ? dayjs(completeResultDTO?.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'newcar_owner_confirmation_time' })}>
                    <div>
                      {completeResultDTO?.verficationStatusDate
                        ? dayjs(completeResultDTO?.verficationStatusDate).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'collection_ex_time' })}>
                    <div>
                      {completeResultDTO?.exRegDate
                        ? dayjs(completeResultDTO?.exRegDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'flow_creation_time' })}>
                    <div>
                      {completeResultDTO?.createDate
                        ? dayjs(completeResultDTO?.createDate).format('YYYY-MM-DD HH:mm:ss')
                        : ''}
                    </div>
                  </Form.Item>
                </Form>
              </div>
            </div>
          )}
        </div>
      </div>
    </Watermark>
  );
};

export default FormEight;
