export interface PaymentModelsState {
  webPaymentOrder;
  commodityList: {
    payItemId: string;
    price: number;
    quantity: number;
    unit: string;
    total: 0;
    isExemption: number;
    billId: string;
  };
  total: number;
}

export const initPaymentModelsState = {
  webPaymentOrder: null,
  commodityList: {
    payItemId: '',
    price: 0,
    quantity: 0,
    unit: '',
    money: 0,
    isExemption: 0,
  },
  total: 0,
};

export default {
  namespace: 'paymentModels',
  state: initPaymentModelsState,

  /* 更新状态（通用） */
  reducers: {
    update: (state: PaymentModelsState, { payload }: { payload: Partial<PaymentModelsState> }) => ({
      ...state,
      ...payload,
    }),
    init: () => initPaymentModelsState,
  },
};
