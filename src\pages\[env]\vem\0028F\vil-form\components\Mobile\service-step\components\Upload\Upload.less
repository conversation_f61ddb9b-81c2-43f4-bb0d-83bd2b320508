.addEntryRender {
  width: 155px;
  height: 155px;
  margin-right: 20px;
  margin-bottom: 24px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
}
.renderItem {
  width: 155px;
  height: 155px;
  margin-right: 20px;
  margin-bottom: 24px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  color: #ffffff;
  position: relative;
  .delButton {
    // width: 36px;
    // height: 36px;
    position: absolute;
    top: 0;
    right: 0;
    background: #00000020;
    border-top-right-radius: 8px;
    border-bottom-left-radius: 12px;
  }
  img {
    border-radius: 8px;
    object-fit: contain;
  }
}
.moUpload {
  :global {
    .mo-upload-mobile-add-btn-root,
    .mo-upload-mobile-delete-btn-root,
    .mo-upload-pc-file-list-item-main,
    .mo-upload-pc-file-list-item-main > span {
      display: none;
    }
  }
}

.uploadContent {
  // height: 680px;
  overflow-y: scroll;
  white-space: pre-wrap;
  text-align: left;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 0.06rem; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 0.06rem;
    background: #d8d8d8;
    background-clip: padding-box;
    -webkit-border-radius: 0.06rem;
    -moz-border-radius: 0.06rem;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #d8d8d8;
    -webkit-border-radius: 0.06rem;
    -moz-border-radius: 0.06rem;
    border-radius: 0.06rem;
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    border-radius: 0.06rem;
    background: #ffffff;
  }
  p {
    font-size: 28px;
    line-height: 44px;
  }
}

.webUploadBtn {
  min-width: 128px;
  height: 48px;
  border: 1px solid #d9d9d9 !important;
  background-color: #fff !important;
  color: #6c6c6c !important;
}
