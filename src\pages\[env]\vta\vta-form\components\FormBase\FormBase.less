@prefix: vta-form-page;

.@{prefix} {
  &- {
    &title {
      display: flex;
    }
    &root {
      padding: 0 10% 88px;
      height: calc(100% - 68px);
      overflow-y: auto;

      .tab-wrap {
        font-size: 16px;

        .tab-header {
          border-bottom: 1px solid #d9d9d9;
          margin-bottom: 16px;

          ul {
            display: flex;

            font-size: 16px;
            line-height: 1.5;
            position: relative;
            white-space: nowrap;
            margin-bottom: -1px;

            li {
              position: relative;
              padding: 20px 20px;
              transition: color .3s cubic-bezier(.645, .045, .355, 1);
              display: block;
              cursor: pointer;
              text-decoration: none;

              border-bottom: 1px solid #d9d9d9;
              border-top: none !important;
              border-left: none !important;
              border-right: none !important;
            }

            li.active, li:hover {
              color: var(--primary-btn-fill);
              border-bottom: 2px solid @brand-primary !important;
            }
          }
        }
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px
    }

    &sectionTitle:before {
      display: block;
      content: "";
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px
    }

    &form {
      padding-bottom: 0;

    }

    &body-root {
      margin-left: 24px;
      justify-content: center;
    }


    &title {
      font-size: 20px;
      font-weight: 700;
      padding: 20px 24px;
      border-bottom: 1px solid rgba(0, 0, 0, .15)
    }

    &stepinfo {
      display: flex
    }

    &step {
      margin-top: 20px;
      padding: 0 22px;
      width: 240px;
      min-width: -webkit-fit-content;
      min-width: -moz-fit-content;
      min-width: fit-content;
      border-right: 1px solid rgba(0, 0, 0, .15)
    }

    &step h3 {
      font-size: 16px;
      line-height: 22px;
      font-weight: 700;
      padding: 0 2px 16px
    }

    &step .ant-steps-small {
      font-size: 16px
    }

    &step .ant-steps-small .ant-steps-item {
      height: 70px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon {
      background-color: rgba(8, 74, 184, .25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-icon {
      background-color: @brand-primary
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-tail:after {
      background-color: @brand-primary;
      height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-icon {
      background-color: rgba(255, 193, 7, .25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-icon {
      background-color: #ffc107
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-icon {
      background-color: hsla(0, 0%, 76.9%, .25)
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-icon {
      background-color: #c4c4c4
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 30px
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait:last-child .ant-steps-item-tail:after {
      display: none
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container {
      height: 40px;
      min-height: 40px;
      display: flex;
      align-items: center
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-tail {
      bottom: 0;
      top: 40px;
      left: 14px;
      padding: 0
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      margin-right: 6px;
      cursor: pointer;
      flex-shrink: 0
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon .ant-steps-icon {
      height: 18px;
      min-height: 18px;
      display: flex;
      align-items: center;
      width: 18px;
      line-height: 18px;
      left: 5px;
      top: 5px;
      font-size: 14px;
      justify-content: center;
      color: #fff;
      border-radius: 50%
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content {
      height: 40px;
      min-height: 40px;
      display: flex;
      align-items: center;
      line-height: 16px
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content .ant-steps-item-title {
      font-size: 16px;
      color: #333
    }

    &form {
      flex: 1 1;
      margin-top: 20px
    }

    &form h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, .85);
      font-weight: 700;
      margin-left: 24px
    }

    &form > .pc-pay-ui-root {
      margin-left: 24px
    }

    &form .pc-pay-ui-top-amount {
      line-height: 65px
    }

    &form .pc-pay-ui-top-amount span {
      position: relative
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 16px;
      color: #666;
      top: -2px
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 24px;
      top: -2px
    }

    &formTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      margin-left: 24px;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px
    }

    &formTitle:before {
      display: block;
      content: "";
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px
    }

    &spin {
      width: 100%;
      height: 100%;
      padding: 45%
    }

    &footer-button {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10%;
      background: #fff;
      box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, .1)
    }

    &footer-button .ant-btn {
      min-width: 128px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
      margin-left: 12px;
      font-size: 16px;
      padding: 0 5px
    }

    &footer-button .ant-btn.ant-btn-default {
      background: #fff;
      color: @brand-primary;
      border: 1px solid @brand-primary
    }
  }
}

.ant-form {
  .ant-form-item {
    input[type=checkbox], input[type=radio] {
      height: 16px;
      width: 16px;
    }

    .ant-radio-wrapper,
    .ant-select,
    .ant-input,
    .ant-select-selection-item,
    .ant-input-group-addon {
      font-size: 16px;
    }

    .ant-form-item-label {
      label {
        font-size: 16px;
        color: #6c6c6c;
      }
    }
  }
}

.multi-card-container {
  .multi-card-item {
    margin-bottom: 20px;
    border-radius: 12px;

    .multi-card-item-sub {
      background: #fff;
    }

    &:nth-of-type(4n + 1) {
      .multi-card-title {
        border-radius: 12px 12px 0 0;
        background-color: @brand-primary;
      }

      .multi-card-item-sub .multi-card-title {
        color: rgba(0, 0, 0, 0.85);
        background-color: #dfeaf5;
        border-radius: 12px 12px 0 0;
        margin-top: 20px;
      }
    }

    &:nth-of-type(4n + 2) {
      .multi-card-title {
        border-radius: 12px 12px 0 0;
        background-color: #e9b745;
      }

      .multi-card-item-sub .multi-card-title {
        color: rgba(0, 0, 0, 0.85);
        background-color: #fdf8f0;
        border-radius: 12px 12px 0 0;
        margin-top: 20px;
      }
    }

    &:nth-of-type(4n + 3) {
      .multi-card-title {
        border-radius: 12px 12px 0 0;
        background-color: rgb(19, 160, 123);
      }

      .multi-card-item-sub .multi-card-title {
        color: rgba(0, 0, 0, 0.85);
        background-color: rgba(19, 160, 123,0.1);
        border-radius: 12px 12px 0 0;
        margin-top: 20px;
      }
    }

    &:nth-of-type(4n + 4) {
      .multi-card-title {
        border-radius: 12px 12px 0 0;
        background-color: rgb(243, 59, 64);
      }

      .multi-card-item-sub .multi-card-title {
        color: rgba(0, 0, 0, 0.85);
        background-color: rgba(243, 59, 64,0.1);
        border-radius: 12px 12px 0 0;
        margin-top: 20px;
      }
    }

    .multi-card-title {
      height: 44px;
      padding: 0 20px;
      color: #fff;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .multi-card-title-action {
      display: flex;
      align-items: center;

      .ant-btn {
        background: #fff;
        color: @brand-primary;
        border: 1px solid @brand-primary;
      }

      .ant-btn.ant-btn-dangerous {
        background: #fff;
        color: #f33b40;
        border: 1px solid #f33b40;
      }
    }

    .multi-card-body {
      background: #fff;
      border: 1px solid #f0f0f0;
      padding: 20px;
      border-radius: 0 0 12px 12px;
    }

  }
}
