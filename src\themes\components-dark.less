.am-list-item {
  background: var(--secondBgColor);
  &.am-list-item-active {
    background: var(--thirdBgColor);
  }
}
// 收银台
.mobile-pay-ui-title {
  background-color: var(--secondBgColor);
  .mobile-pay-ui-total {
    color: var(--firstTextColor);
  }
  .mobile-pay-ui-tip {
    color: var(--thirdTextColor);
  }
}
.mobile-pay-ui-root {
  color: var(--thirdTextColor);
  .mobile-pay-ui-label {
    color: var(--firstTextColor);
    background: var(--loopColor1);
  }
}
.mobile-pay-selector-root {
  background-color: var(--secondBgColor);
}
.mobile-pay-selector-list {
  background-color: var(--secondBgColor);
}
.mobile-pay-selector-list:not(:last-child) {
  border-color: var(--firstBgColor);
}
.mobile-pay-selector-title {
  color: var(--firstTextColor);
}
.mobile-pay-ui-footer {
  background-color: var(--firstBgColor);
}
.mo-baseContainer-mobile-bodyContent.mobile-pay-ui-container {
  background-color: var(--firstBgColor);
}
// 卡片
.am-card {
  background-color: var(--secondBgColor);
}
// 下拉框
.mo-select-list-item {
  background: var(--secondBgColor);
}
.mo-select-list-item:not(.mo-select-list-item-selected) {
  .am-list-extra {
    color: var(--thirdTextColor);
  }
}
.mo-select-list-item-selected.mo-select-list-item .am-list-extra {
  color: @selected-value-color;
}
// 表单
.ant-form-item-has-error .mo-select-list-item.am-list-item {
  border-color: var(--error);
}
// MoDatePicker
.ant-picker-panel {
  background-color: var(--secondBgColor);
}
.ant-picker-cell,
.ant-picker-header,
.ant-picker-content th,
.ant-picker-cell-inner {
  color: var(--firstTextColor);
}
.ant-picker-header button {
  color: var(--thirdTextColor);
}
.ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end)
  .ant-picker-cell-inner {
  background: var(--firstBgColor);
}
// MoDatePicker disabled
.ant-picker-cell-disabled .ant-picker-cell-inner {
  color: var(--thirdTextColor);
}
.ant-picker-cell-disabled:before {
  background: var(--thirdBgColor);
}
.ant-picker-decade-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
.ant-picker-month-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
.ant-picker-quarter-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
.ant-picker-year-panel .ant-picker-cell-disabled .ant-picker-cell-inner .am-popover-inner-wrapper,
.am-popover-arrow {
  background: var(--thirdBgColor);
}
// 步骤条
.am-popover-inner-wrapper,
.am-popover-arrow {
  background: var(--secondBgColor);
}

// 时间
.mo-mrangepicker-separator {
  color: var(--borderColor);
}

// 地址
.mo-address-dark-inputitem-ph,
.mo-address-inputitem-ph {
  background: var(--secondBgColor);
  color: var(--firstTextColor);
  border: 1px solid var(--borderColor);
}

.mo-address-dark-inputitem-ph-text,
.mo-address-inputitem-ph-text {
  color: var(--firstTextColor);
}

.mo-address-addressmain {
  background: var(--firstBgColor);
  .mo-address-addressmaincontainer,
  .address-mo-area-main,
  .mo-search-input,
  .mo-search-main {
    background: var(--secondBgColor);
  }
  .mo-search-inputarea {
    background: var(--secondBgColor);
  }
  .am-list-header {
    color: var(--firstTextColor) !important;
  }
  .am-list-extra {
    color: var(--firstTextColor) !important;
  }
  .am-list-content {
    color: var(--firstTextColor) !important;
  }
  input {
    color: var(--firstTextColor) !important;
  }
}

.address-mo-area-dropdown,
.mo-search-wrapper {
  background: var(--firstBgColor);
}
.mo-search-dropitem {
  background: var(--firstBgColor);
  color: var(--firstTextColor) !important;
}
.mo-search-selectclose {
  color: var(--secondTextColor);
}
