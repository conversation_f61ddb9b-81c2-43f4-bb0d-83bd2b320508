import type { ReceiveConfigType, ReceiveResultType } from '../types/defs';

export interface receiveResultModelsState {
  receiveResult: ReceiveResultType;
  resultReceiveConfig: ReceiveConfigType;
  hasForm: boolean;
  isShowOnly: boolean;
}

export const initReceiveResultModelsState = {
  receiveResult: [],
  resultReceiveConfig: [],
  hasForm: true,
  isShowOnly: false,
};

export default {
  namespace: 'receiveResultModels',
  state: initReceiveResultModelsState,

  /* 更新状态（通用） */
  reducers: {
    update: (
      state: receiveResultModelsState,
      { payload }: { payload: Partial<receiveResultModelsState> },
    ) => ({
      ...state,
      ...payload,
    }),
    init: () => initReceiveResultModelsState,
  },
};
