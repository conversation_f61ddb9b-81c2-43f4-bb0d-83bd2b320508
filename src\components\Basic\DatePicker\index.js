import React, { useState } from 'react';
import { Modal, DatePickerView } from 'antd-mobile';
import { CloseCircleFilled } from '@ant-design/icons';
import moment from 'moment';
import styles from './index.less';

const formatArr = {
  date: 'YYYY-MM-DD',
  year: 'YYYY',
  month: 'YYYY-MM',
  datetime: 'YYYY-MM-DD HH:mm',
  time: 'HH:mm',
};

const DatePicker = (props) => {
  const {
    placeholder = '請選擇',
    value,
    onOk,
    mode = 'date',
    format,
    style = {},
    className,
    ...restProps
  } = props;
  const [visible, setVisible] = useState(false);
  const [dateValue, setDateValue] = useState(value ? new Date(value) : '');
  return (
    <div className={`${styles.container} ${className}`} style={style}>
      <div
        className={styles.inputItem}
        onClick={() => {
          setVisible(true);
        }}
      >
        <span className={!value ? styles.placeholder : styles.value}>
          {value ? moment(value).format(format || formatArr[mode]) : placeholder}
        </span>

        {value && (
          <span
            className={styles.close}
            onClick={(e) => {
              e.stopPropagation();
              onOk('');
            }}
          >
            <CloseCircleFilled />
          </span>
        )}
      </div>
      <Modal
        popup
        visible={visible}
        animationType="slide-up"
        wrapClassName={styles.modal}
        afterClose={() => {
          if (value) {
            setDateValue(value);
          }
        }}
      >
        <div className={styles.topBtn}>
          <span
            className={styles.btnText}
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </span>
          <span
            className={styles.btnText}
            onClick={() => {
              setVisible(false);
              onOk(dateValue);
            }}
          >
            確定
          </span>
        </div>
        <DatePickerView
          value={new Date(dateValue)}
          mode={mode}
          onChange={(v) => {
            setDateValue(v);
          }}
          {...restProps}
        />
      </Modal>
    </div>
  );
};

export default DatePicker;
