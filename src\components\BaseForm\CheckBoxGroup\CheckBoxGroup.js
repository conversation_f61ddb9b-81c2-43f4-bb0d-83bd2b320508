import React, { useMemo, useState, useContext, useEffect } from 'react';
import { Checkbox as MCheck<PERSON> } from 'antd-mobile';
import { Radio, Checkbox } from 'antd';
import cn from 'classnames';
import classPrefix from '@ali-whale/class-prefix';
import './CheckBoxGroup.less';
import FormContext from '../FormContext';
import { MOBILE_CLASS_PREFIX, PC_CLASS_PREFIX } from '../constant';

const CheckBoxGroupWeb = (props) => {
  const { multiple = false, options, onItemClick, onChange, value, ...otherProps } = props;
  const CheckItem = multiple ? Checkbox : Radio;
  const [_value, setValue] = useState([]);

  useEffect(() => {
    setValue(value);
  }, [value]);
  const handleItemClick = (item) => {
    onItemClick && onItemClick(item);
  };

  if (onItemClick && !multiple) {
    return (
      <>
        {options.map(({ value, label, disabled }) => (
          <CheckItem
            checked={_value === value}
            key={value}
            value={value}
            disabled={disabled}
            onChange={() => onChange(value)}
            onClick={() => handleItemClick({ value, label })}
          >
            <span>{label}</span>
          </CheckItem>
        ))}
      </>
    );
  }

  return (
    <CheckItem.Group
      value={value}
      onChange={(e) => {
        const newVal = multiple ? e : e.target.value;
        onChange(newVal);
      }}
      {...otherProps}
    >
      {options.map(({ value, label, disabled, required }) => (
        <CheckItem
          key={value}
          className={cn({
            'mo-checkbox-wrapper-required': required,
          })}
          value={value}
          disabled={disabled}
        >
          <span onClick={handleItemClick}>{label}</span>
        </CheckItem>
      ))}
    </CheckItem.Group>
  );
};

const CheckBoxGroup = (props) => {
  const { mobile, isDetail } = useContext(FormContext);
  const {
    value,
    onChange,
    multiple = false,
    border = false,
    options,
    onItemClick,
    // layout = mobile ? 'horizontal' : 'vertical',
    layout = 'horizontal',
    ...otherProps
  } = props;
  const [_value, setValue] = useState([]);
  const px = mobile ? classPrefix(MOBILE_CLASS_PREFIX) : classPrefix(PC_CLASS_PREFIX);

  useEffect(() => {
    setValue(value);
  }, [value]);

  const handleItemClick = (item) => {
    onItemClick && onItemClick(item);
  };

  const checkboxItems = useMemo(() => {
    /* web端 */
    if (!mobile) {
      return (
        <CheckBoxGroupWeb
          {...props}
          className={cn(px('checkbox'), {
            [px('checkbox-horizontal')]: layout === 'horizontal',
            [px('checkbox-vertical')]: layout === 'vertical',
          })}
        />
      );
    }

    /* 多选 */
    if (multiple) {
      return options.map(({ value: v, label, disabled, required }) => (
        <MCheckbox
          key={v}
          className={cn({
            [px('checkbox-border')]: border,
            [px('checkbox-disabled')]: disabled,
            [px('checkbox-required')]: required,
          })}
          disabled={disabled}
          checked={_value?.includes(v)}
          onClick={() => handleItemClick({ value: v, label })}
          onChange={(e) => {
            const { checked } = e.target;
            const preValue = Array.isArray(_value) ? _value : [_value];
            const transformValue = checked
              ? preValue.concat(v) // 勾选
              : preValue.filter(element => element !== v); // 取消勾选
            onChange(transformValue);
            setValue(transformValue);
          }}
          {...otherProps}
        >
          {label}
        </MCheckbox>
      ));
    }
    // 单选
    return options.map(({ value: v, label, disabled }) => (
      <MCheckbox
        checked={v === _value}
        key={v}
        disabled={disabled}
        className={cn(px('checkbox-radio'), {
          [px('checkbox-disabled')]: disabled,
        })}
        {...otherProps}
        onClick={() => handleItemClick({ value: v, label })}
        onChange={() => {
          onChange(v);
          setValue(v);
        }}
      >
        {label}
      </MCheckbox>
    ));
  }, [multiple, options, border, otherProps, _value]);

  if (isDetail) {
    let showText = '';
    if (Array.isArray(value)) {
      showText = options
        .filter(v => value.includes(v.value))
        .map(v => v.label)
        .join('，');
    } else {
      showText = options.filter(v => v.value === value)[0]?.label;
    }
    return <div>{showText}</div>;
  }

  return (
    <div
      className={cn(px('checkbox'), {
        [px('checkbox-horizontal')]: layout === 'horizontal',
        [px('checkbox-vertical')]: layout === 'vertical',
        [px('checkbox-multiple')]: multiple,
      })}
    >
      {checkboxItems}
    </div>
  );
};

export default CheckBoxGroup;
