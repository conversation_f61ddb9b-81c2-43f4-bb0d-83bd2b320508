import React from 'react';
import { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import { history } from 'umi';
import { useIntl } from 'umi';
import './OwnerRecordPage.module.less';
import { MoPhone } from '@gov-mo/components';
import FooterButton from '@/components/FooterButton';

import { Input, Row, Col, Button, Form, Select, DatePicker, Pagination } from 'antd';
import { isMobile } from '@/utils';

const { RangePicker } = DatePicker;

const classPrefix = isMobile() ? 'owner-record-mobile':'owner-record';
const px = pc(classPrefix);

const NewcarApplyRecordPage = () => {
  const intl = useIntl();
  const [step, setStep] = useState(1);
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));

  const [showPrevStep, setShowPrevStep] = useState(false);
  const [showNextStep, setShowNextStep] = useState(false);
  const [showClose, setShowClose] = useState(true);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);

  const prevStep = () => {
    setStep(step - 1);
  };

  const nextStep = () => {
    setStep(step + 1);
  };

  const handleClose = () => {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const [form] = Form.useForm();

  return (
    <>
      <div className={`${px('root')} `}>
        <h2 className={px('title')}>{intl.formatMessage({ id: 'my_car_record' })}</h2>
        <div className={px('stepinfo')}>
          <div className={px('form')}>
            <Form form={form} layout="vertical">
              <div className={px('sectionBody')}>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                  <Select value={'1'}>
                    <Select.Option value="1">
                      {intl.formatMessage({ id: 'please_select' })}
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'license_plate_number' })}>
                  <Input value="" />
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'service_processing_date' })}>
                  <RangePicker style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'query_number' })}>
                  <Input value="" />
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'service_application_status' })}>
                  <Select value={'1'}>
                    <Select.Option value="1">
                      {intl.formatMessage({ id: 'please_select' })}
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'service_procedure_name' })}>
                  <Select value={'1'}>
                    <Select.Option value="1">
                      {intl.formatMessage({ id: 'please_select' })}
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_contact_phone_number' })}>
                  <div className="mophone-user">
                    <MoPhone
                      onlyAreaGroup={['+853']}
                      placeholder={intl.formatMessage({ id: 'please_enter' })}
                      areaProps={{ placeholder: intl.formatMessage({ id: 'please_select' }) }}
                    />
                  </div>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_owner_id_type' })}>
                  <Select value={'1'}>
                    <Select.Option value="1">
                      {intl.formatMessage({ id: 'please_select' })}
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label={intl.formatMessage({ id: 'vehicle_owner_id_number' })}>
                  <Input value="" />
                </Form.Item>
                <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                  <Button type="primary" style={{ marginRight: '10px' }}>
                  {intl.formatMessage({ id: 'search' })}
                  </Button>
                  <Button type="default">{intl.formatMessage({ id: 'reset' })}</Button>
                </div>

                <div style={{ width: '100%', marginBottom: '24px' }}></div>
                <div style={{ width: '100%' }}>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#13A07B' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                      {intl.formatMessage({ id: 'query_number' })}：230000101 (
                        {intl.formatMessage({ id: 'ex_test_license_plate_application' })})
                      </div>
                      <div className="status status-paying"></div>
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'license_plate_number' })}
                        </div>
                        <div className="value">EX-123</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">L - {intl.formatMessage({ id: 'light_vehicles' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'import_license_number' })}
                        </div>
                        <div className="value">I/2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_application_status' })}
                        </div>
                        <div className="value">{intl.formatMessage({ id: 'pending_payment' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_processing_date' })}
                        </div>
                        <div className="value">2024-02-24 11:20</div>
                      </div>
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        {/* <Button type='default'>{intl.formatMessage({ id: 'delete' })}</Button>*/}
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'update_information' })}
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/web/vem/personal/owner-confirm');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#13A07B' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                        {intl.formatMessage({ id: 'query_number' })}：230000011 (
                        {intl.formatMessage({ id: 'vehicle_registration_application' })})
                      </div>
                      <div className="status status-paying"></div>
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'license_plate_number' })}
                        </div>
                        <div className="value">EX-111</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">L - {intl.formatMessage({ id: 'light_vehicles' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'import_license_number' })}
                        </div>
                        <div className="value">I/2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_application_status' })}
                        </div>
                        <div className="value">{intl.formatMessage({ id: 'in_progress' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_processing_date' })}
                        </div>
                        <div className="value">2024-02-24 11:20</div>
                      </div>
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        {/* <Button type='default'>{intl.formatMessage({ id: 'delete' })}</Button> */}
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'update_information' })}
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/web/vem/personal/owner-confirm');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#13A07B' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                        {intl.formatMessage({ id: 'query_number' })}：230000011 (
                        {intl.formatMessage({ id: 'ex_test_license_plate_application' })})
                      </div>
                      <div className="status status-paying"></div>
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'license_plate_number' })}
                        </div>
                        <div className="value">EX-234</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">L - {intl.formatMessage({ id: 'light_vehicles' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'import_license_number' })}
                        </div>
                        <div className="value">I/2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_application_status' })}
                        </div>
                        <div className="value">待車主確定</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_processing_date' })}
                        </div>
                        <div className="value">2024-02-24 11:20</div>
                      </div>
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        {/* <Button type='default'>{intl.formatMessage({ id: 'delete' })}</Button>*/}
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'update_information' })}
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/web/vem/personal/owner-confirm');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#13A07B' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                        {intl.formatMessage({ id: 'query_number' })}：230000002 (
                        {intl.formatMessage({ id: 'vehicle_registration_application' })})
                      </div>
                      <div className="status status-paied"></div>
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'license_plate_number' })}
                        </div>
                        <div className="value">MA-12-34</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">L - {intl.formatMessage({ id: 'light_vehicles' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'import_license_number' })}
                        </div>
                        <div className="value">I/2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_application_status' })}
                        </div>
                        <div className="value">{intl.formatMessage({ id: 'application_completed' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_processing_date' })}
                        </div>
                        <div className="value">2024-02-24 11:20</div>
                      </div>
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        {/* <Button type='default'>{intl.formatMessage({ id: 'delete' })}</Button>*/}
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'update_information' })}
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/web/vem/personal/owner-confirm');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                        <Button type="default">{intl.formatMessage({ id: 'download_booklet' })}</Button>
                        {/* <Button type='default'>{intl.formatMessage({ id: 'download_receipt' })}</Button> */}
                      </div>
                    </div>
                  </div>
                  <div className="card">
                    <div className="card-header" style={{ backgroundColor: '#13A07B' }}>
                      <div>
                        <input type="checkbox" />
                      </div>
                      <div className="card-title">
                        {intl.formatMessage({ id: 'query_number' })}：230000001 (
                        {intl.formatMessage({ id: 'ex_test_license_plate_application' })})
                      </div>
                      <div className="status"></div>
                    </div>
                    <div className="card-body">
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'license_plate_number' })}
                        </div>
                        <div className="value">EX-234</div>
                      </div>
                      <div className="item">
                        <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                        <div className="value">L - {intl.formatMessage({ id: 'light_vehicles' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'model_approval_number' })}
                        </div>
                        <div className="value">2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'import_license_number' })}
                        </div>
                        <div className="value">I/2/2024</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_application_status' })}
                        </div>
                        <div className="value">{intl.formatMessage({ id: 'application_completed' })}</div>
                      </div>
                      <div className="item">
                        <div className="label">
                          {intl.formatMessage({ id: 'service_processing_date' })}
                        </div>
                        <div className="value">2024-02-24 11:20</div>
                      </div>
                    </div>
                    <div className="card-footer">
                      <div></div>
                      <div className="right">
                        {/* <Button type='default'>{intl.formatMessage({ id: 'delete' })}</Button>*/}
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/vta-form');
                          }}
                        >
                          {intl.formatMessage({ id: 'update_information' })}
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            history.push('/web/vem/personal/owner-confirm');
                          }}
                        >
                          {intl.formatMessage({ id: 'view_details' })}
                        </Button>
                        <Button type="default">{intl.formatMessage({ id: 'download_ex_card' })}</Button>
                        {/* <Button type='default'>{intl.formatMessage({ id: 'download_receipt' })}</Button> */}
                      </div>
                    </div>
                  </div>
                </div>
                <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                  <Pagination defaultCurrent={1} total={60} />
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
      <FooterButton
        // handleClose={handleClose}
        className={px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
      />
    </>
  );
};

export default NewcarApplyRecordPage;
