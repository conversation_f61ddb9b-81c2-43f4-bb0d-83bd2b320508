.root {
  flex-shrink: 0;
  margin-left: 32px;
  margin-right: 32px;
  margin-bottom: 24px;
  .titleArea {
    padding: 16px 0;
    position: relative;
    .cardTitleMixins(@color1);
    .title {
      color: var(--firstTextColor);
      font-size: 36px;
      font-weight: bold;
    }
  }
  .infoContainer {
    background-color: var(--secondBgColor);
    padding-top: 32px;
    border-bottom-left-radius: 10 * @hd;
    border-bottom-right-radius: 10 * @hd;
    .infoArea {
      margin: 32px;
      margin-top: 0;
      padding: 0;
      border-bottom: 1px solid var(--borderColor);
      .listItem {
        margin-bottom: 32px;
        display: flex;
        font-size: 32px;
        .itemKey {
          .itemKeyMixins(@hd);
          .colon {
            margin-left: 2 * @hd;
            margin-right: 8 * @hd;
          }
        }
        .itemValue {
          .itemValueMixins(@hd);
        }
      }
      .webItem {
        flex: 0 0 25%;
      }
    }
    .infoArea:last-child {
      border-bottom: 0;
    }
  }
}
.webRoot {
  flex-shrink: 0;
  margin-left: 20px;
  margin-right: 32px;
  margin-bottom: 24px;
  .infoContainer {
    .infoArea {
      margin: 0;
      padding-top: 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      &:last-child {
        bottom: none;
      }
      .listItem {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 20px;
        display: flex;
        .webItem {
          .itemKeyMixins();
        }
        .itemValue {
          .itemValueMixins();
        }
      }
    }
  }
}
