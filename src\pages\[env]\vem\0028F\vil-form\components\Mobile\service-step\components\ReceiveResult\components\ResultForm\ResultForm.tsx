/* eslint-disable indent */
import React, { useState } from 'react';
import { useIntl, useSelector } from 'umi';
import type { receiveResultModelsState } from 'umi';
import Stepper from '@/components/Stepper';
import { CheckBoxGroup, LayoutForm as Form, Select } from '@/components/BaseForm';
import { receiveTypeEnum, resultTypeEnum } from '@/pages/service-step/config';
import { Modal } from 'antd-mobile';
import { isMobile } from '@/utils';
import useModalMethod from '@/components/Modal/useModalMethod';
import vDocTipIcon from '@/assets/virtualDocTip.svg';
import { resultMediumTypeDict, resultReceiveTypeDict, addressOpts } from '../../config';
import styles from './ResultForm.less';

const PureText = (props: { value?: string }) => {
  const { value } = props;
  return <span className={styles.name}>{value}</span>;
};
PureText.defaultProps = {
  value: '',
};
export interface FormLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  required?: boolean;
  label?: string;
  type?: 'horizontal' | 'vertical';
}

function ResultForm(props: { mobile: boolean }) {
  const { resultReceiveConfig } = useSelector(
    ({ receiveResultModels }: { receiveResultModels: receiveResultModelsState }) =>
      receiveResultModels,
  );
  const intl = useIntl();
  const { mobile, form } = props;

  // 模態框是否顯示
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const [PcModal] = useModalMethod();

  const ModalAlert = () => {
    isMobile() ? setModalVisible(true) : PcModal.electronicDocInfo();
  };

  const handleDisabled = (curReceiveType, code) => {
    if (curReceiveType === receiveTypeEnum.personal) {
      return code !== resultTypeEnum.paper;
    }
    return code === resultTypeEnum.paper;
  };

  // 根据下拉框改变单选框的值
  const handleReceiveTypeChange = ([{ value: tempValue }]) => {
    const fieldData = form.getFieldsValue();
    form.setFieldsValue({
      ...fieldData,
      receiveResult: [
        {
          ...fieldData.receiveResult[0],
          resultType:
            tempValue === receiveTypeEnum.personal
              ? resultTypeEnum.paper
              : resultTypeEnum.electronic,
          // 选择网上领取后重置份数。
          serviceResultCount: 1,
        },
      ],
    });
  };

  /**
   * 获取费用文案
   */
  const getFeeExtra = () => {
    const cost = 15;
    const day = 0;
    return (
      <div className={styles.feeContainer}>
        <div role="text">
          <p>{`${intl.formatMessage({ id: '费用' })}：`}</p>
          <p>{intl.formatMessage({ id: '类别费用说明' }, { cost: cost.toFixed(2) })}</p>
        </div>
        <div role="text">
          <p>{`${intl.formatMessage({ id: '预计领取日期' })}：`}</p>
          <p>
            {Number(day) === 0
              ? intl.formatMessage({ id: '即日或下个工作日' }, { day })
              : intl.formatMessage({ id: '多个工作日' }, { day })}
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.root}>
      {resultReceiveConfig.length === 0 ? null : (
        <Form.List name="receiveResult">
          {fields => (
            <>
              {fields.map(({ name: fieldName, fieldKey }) => {
                const resultTypeConfig = (resultReceiveConfig[fieldName]?.resultType || '').split(
                  ',',
                );
                const receiveTypeConfig = (resultReceiveConfig[fieldName]?.receiveType || '').split(
                  ',',
                );
                return (
                  <React.Fragment key={fieldKey}>
                    <Form.Item
                      name={[fieldName, 'receiveType']}
                      label={intl.formatMessage({ id: '领取方式' })}
                      rules={[{ required: true, message: intl.formatMessage({ id: '請選擇' }) }]}
                      required
                    >
                      <Select
                        options={receiveTypeConfig.map(code => ({
                          label: intl.formatMessage({ id: resultReceiveTypeDict[code] }),
                          value: Number(code),
                        }))}
                        placeholder={intl.formatMessage({ id: '請選擇' })}
                        onChange={handleReceiveTypeChange}
                      />
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.receiveResult[fieldName].receiveType !==
                        currentValues.receiveResult[fieldName].receiveType}
                    >
                      {({ getFieldValue }) => {
                        const curReceiveType = getFieldValue([
                          'receiveResult',
                          fieldName,
                          'receiveType',
                        ])[0].value;
                        return (
                          <Form.Item
                            name={[fieldName, 'resultType']}
                            fieldKey={[fieldKey, 'resultType']}
                            label={intl.formatMessage({ id: '服务结果类型' })}
                            required
                            help={getFeeExtra()}
                          >
                            <CheckBoxGroup
                              options={resultTypeConfig.map(code => ({
                                label: (
                                  <span>
                                    {intl.formatMessage({ id: resultMediumTypeDict[code] })}
                                    {/* 電子文件後面增加圖標 */}
                                    {Number(code) === resultTypeEnum.electronic && (
                                      <span onClick={ModalAlert} style={{ marginLeft: 5 }}>
                                        <img src={vDocTipIcon} />
                                      </span>
                                    )}
                                  </span>
                                ),
                                value: Number(code),
                                disabled: handleDisabled(curReceiveType, Number(code)),
                              }))}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>

                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.receiveResult[fieldName].resultType !==
                        currentValues.receiveResult[fieldName].resultType}
                    >
                      {({ getFieldValue }) =>
                        getFieldValue(['receiveResult', fieldName, 'resultType']) ===
                        resultTypeEnum.paper ? (
                          <>
                            <Form.Item
                              initialValue={1}
                              name={[fieldName, 'serviceResultCount']}
                              fieldKey={[fieldKey, 'serviceResultCount']}
                              label={intl.formatMessage({ id: '服务结果份数' })}
                              required
                            >
                              <Stepper min={1} max={99} mobile={mobile} />
                            </Form.Item>
                            <Form.Item
                              name={[fieldName, 'receiveAddress']}
                              label={intl.formatMessage({ id: '领取地点' })}
                              required
                              rules={[{ required: true, message: intl.formatMessage({ id: '請選擇' }) }]}
                            >
                              <Select
                                options={addressOpts.map(item => ({
                                  ...item,
                                  label: intl.formatMessage({ id: item.label }),
                                }))}
                                placeholder={intl.formatMessage({ id: '請選擇' })}
                              />
                            </Form.Item>
                          </>
                        ) : null}
                    </Form.Item>
                    <Modal
                      visible={modalVisible}
                      onClose={() => {
                        setModalVisible(false);
                      }}
                      title={intl.formatMessage({ id: '温馨提示' })}
                      transparent
                      maskClosable={false}
                      footer={[
                        {
                          text: intl.formatMessage({ id: '确认' }),
                          onPress: () => {
                            setModalVisible(false);
                          },
                        },
                      ]}
                    >
                      <div style={{ textAlign: 'left' }}>
                        {intl.formatMessage({
                          id: '电子证明的法律效率及其有效期等同于相同内容的纸本证明',
                        })}
                        <br />
                        {intl.formatMessage({
                          id:
                            '若需提交电子证明予本澳其他政府部门只需提供能连接电子证明的二维码或分享码',
                        })}
                        <br />
                      </div>
                    </Modal>
                  </React.Fragment>
                );
              })}
            </>
          )}
        </Form.List>
      )}
    </div>
  );
}

export default ResultForm;
