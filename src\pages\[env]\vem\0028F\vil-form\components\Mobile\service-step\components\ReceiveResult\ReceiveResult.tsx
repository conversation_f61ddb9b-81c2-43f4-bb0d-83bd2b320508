import React, { useEffect } from 'react';
// import { Form } from 'antd';
import type { receiveResultModelsState } from 'umi';
import type { FormInstance } from 'antd';
import { usePersistFn } from 'ahooks';
// import { Accordion } from 'antd-mobile';
// import AccordionHeader from '@/components/AccordionHeader';
import { LayoutForm as Form } from '@/components/BaseForm';
// import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import type { ReceiveResultType } from '../../types/defs';
import styles from './ReceiveResult.less';
import ResultForm from './components/ResultForm';
import ResultDetail from './components/ResultDetail';
// import { resultTypeEnum } from '../../config';

export interface ReceiveResultProps extends React.HTMLAttributes<HTMLDivElement> {
  mobile?: boolean;
  isDetail?: boolean;
  title?: string;
  onInstance?: (instance: FormInstance<{ receiveResult: ReceiveResultType }> | null) => void;
}

const ReceiveResult = (props: ReceiveResultProps) => {
  const {
    isDetail = false,
    mobile = true,
    className = '',
    onInstance,
    title,
    ...othersProps
  } = props;
  const [form] = Form.useForm<{ receiveResult: ReceiveResultType }>();
  // const intl = useIntl();
  const { receiveResult, isShowOnly, resultReceiveConfig } = useSelector(
    ({ receiveResultModels }: { receiveResultModels: receiveResultModelsState }) =>
      receiveResultModels,
  );

  useEffect(() => {
    if (onInstance) {
      onInstance(form);
      // onInstance({
      //   ...form,
      //   validateFields: () => form.validateFields().then(val => ({
      //     receiveResult: val.receiveResult.map(i => ({
      //       ...i,
      //       receiveType: i.receiveType?.[0]?.value,
      //     })),
      //   })),
      // });
    }
    return () => {
      if (onInstance) {
        onInstance(null);
      }
    };
  }, []);

  const renderTips = usePersistFn((result?: ReceiveResultType) => null);
  //   let tips = '';
  //   let isShowTips: boolean | undefined;
  //   result?.forEach((res, index: number) => {
  //     if (res.resultType === resultTypeEnum.electronic) {
  //       isShowTips = true;
  //       if (resultReceiveConfig[index].tips) {
  //         tips = `${tips}${resultReceiveConfig[index].tips}\n`;
  //       }
  //     }
  //   });
  //   if (!tips) tips = intl.formatMessage({ id: 'default_tips' });
  //   return isShowTips ? (
  //     <Accordion
  //       className={`${styles.warnning} ${isDetail ? styles.detailTips : ''}`}
  //       defaultActiveKey="tips"
  //     >
  //       <Accordion.Panel
  //         className={styles.accordionPanel}
  //         key="tips"
  //         header={(
  //           <AccordionHeader
  //             className={styles.headers}
  //             title={intl.formatMessage({ id: '温馨提示' })}
  //           />
  //         )}
  //       >
  //         <div className={styles.tipContent}>{tips}</div>
  //       </Accordion.Panel>
  //     </Accordion>
  //   ) : null;

  if (!resultReceiveConfig.length) {
    return null;
  }

  if (isShowOnly) {
    return isDetail ? (
      <div className={`${styles.root} ${className}`} {...othersProps}>
        <div className={styles.title}>{title}</div>
        <ResultDetail mobile={mobile}>{renderTips(receiveResult)}</ResultDetail>
      </div>
    ) : null;
  }

  return (
    <div className={`${styles.root} ${className}`} {...othersProps}>
      {/* {isDetail && <div className={styles.title}>{title}</div>} */}
      {isDetail ? (
        <ResultDetail mobile={mobile}>{renderTips(receiveResult)}</ResultDetail>
      ) : (
        <>
          {mobile && <div className={styles.baseTitleContainer} />}
          <Form
            initialValues={receiveResult.length && !isDetail ? { receiveResult } : undefined}
            // initialValues={receiveResult.length && !isDetail ? {
            //   receiveResult: receiveResult.map(i => ({
            //     ...i,
            //     receiveType: Array.isArray(i.receiveType) ? i.receiveType : [i.receiveType],
            //   })),
            // } : undefined}
            // layout="vertical"
            form={form}
          >
            <ResultForm mobile={mobile} form={form} />
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.receiveResult !== currentValues.receiveResult
              }
            >
              {({ getFieldValue }) => {
                const formValue = getFieldValue('receiveResult');
                return renderTips(formValue);
              }}
            </Form.Item>
          </Form>
        </>
      )}
    </div>
  );
};

export default ReceiveResult;
