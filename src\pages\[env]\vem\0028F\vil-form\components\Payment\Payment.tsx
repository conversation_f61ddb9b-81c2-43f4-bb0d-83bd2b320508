import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './Payment.less';

import { useIntl } from 'umi';
import { Alert, Form, Radio, Table } from 'antd';

import { cancelTxnPay, completePayment, getServiceFee } from '@/services/0028F';
import { isMobile } from '@/utils';
import { getLangGroup, getResponseMessage } from '@/locales/lang';

const px = pc('payment-28f');

export interface FormNineProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setNextStepText: (text: string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;

  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;

  txnId: string | undefined;
}

const FormNine = (props: FormNineProps) => {
  const { className = '', ...otherProps } = props;
  const {
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    setDisableNextStep,
    contentNextStep,
    contentForNextStep,
    txnId,
  } = otherProps;

  const [tipMsg, setTipMsg] = useState('');

  const intl = useIntl();
  const [form] = Form.useForm();
  const baseUrl =
    window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const submit = async () => {
    setTipMsg('');
    // 是否是取消支付
    if (cancelPayDto?.isShowCancelPayButton) {
      handleCancelTxnPay().then();
      return;
    }

    const fees: completePaymentFee[] = feeItems!.map((item) => ({
      nameZh: item.zh,
      namePt: item.pt,
      code: item.code,
      price: item.price,
      amount: item.price,
      taxPrice: item.taxPriceTotal,
      taxAmount: item.taxPriceTotal,
      subTotal: item.subtotal,
    }));
    let pageUrl = 'ovsap/web/vem/0028F/vil-form?page=success';
    if (paySelected === 'BOCP' || paySelected === 'BOC' || paySelected === 'BNU') {
      pageUrl = `${baseUrl}/${pageUrl}`;
    }
    const data: completePaymentParams = {
      txnId: txnId!,
      payMethodCodeKey: paySelected,
      lang: 'zh_TW',
      ua: isMobile() ? 'MobileWeb' : 'PCWeb',
      remark: '',
      // allSubTotal: total,
      pageUrl,
      // serviceFeeItems: fees
    };

    const ret = await completePayment(data).catch((e) => {
      setTipMsg(e.message);
    });
    if (!ret) {
      return;
    }
    if (ret.code === '0') {
      const { data } = ret;

      if (data.payUrl) {
        location.href = data.payUrl;
      } else if (data.returnHTML) {
        document.write(data.returnHTML?.replace(/\\/, ''));
      }
      // contentForNextStep(2);
    } else {
      setTipMsg(getResponseMessage(ret));
      setDisableNextStep(false);
    }
  };

  const [payCodes, setPayCodes] = useState<payCodes[]>();
  const [feeItems, setFeeItems] = useState<feeItems[]>();
  const [total, setTotal] = useState(0);
  const [paySelected, setPaySelected] = useState('');
  const [cancelPayDto, setCancelPayDto] = useState<CancelPayDto>();

  const handleCancelTxnPay = async () => {
    if (!txnId) return;
    const res = await cancelTxnPay([txnId]);
    if (res.code !== '0') {
      setTipMsg(getResponseMessage(res));
      return;
    }
    // 刷新页面
    location.reload();
  };

  useEffect(() => {
    if (!txnId) return;

    getServiceFee(txnId)
      .then((ret) => {
        if (ret.code === '0') {
          const { data } = ret;
          // 设置取消支付状态
          setCancelPayDto(data?.cancelPayDto);
          // 如果需要取消支付
          if (data?.cancelPayDto?.isShowCancelPayButton === true) {
            // 禁用支付选择
            data.payCodes.forEach((item) => {
              item.available = false;
            });
            // 设置取消支付按钮文案
            setNextStepText(intl.formatMessage({ id: 'cancel_payment' }));
          }
          setPayCodes(data.payCodes);
          setFeeItems(data.feeItems);
          setTotal(data.total);

          const tableData: {
            key: string;
            title: string;
            amount: string;
            tax: unknown;
            subtotal: unknown;
          }[] = [];
          data.feeItems.forEach((item, index) => {
            tableData.push({
              key: (index + 1).toString(),
              title: getLangGroup(item.zh, item.pt),
              amount: item.priceTotal?.toLocaleString(),
              tax: item.taxPriceTotal?.toLocaleString(),
              subtotal: item.subtotal?.toLocaleString(),
            });
          });
          tableData.push({
            key: (data.feeItems.length + 1).toString(),
            title: intl.formatMessage({ id: 'total' }),
            amount: '',
            tax: <span style={{ color: '#084ab8' }}>MOP</span>,
            subtotal: <span style={{ color: '#084ab8' }}>{data.total?.toLocaleString()}</span>,
          });
          setTableData(tableData);

          // 设置默认选中
          if (data.payCodes && data.payCodes.length) {
            const codeKey =
              data?.cancelPayDto?.isShowCancelPayButton === true
                ? data.cancelPayDto.payCodeKey
                : data.payCodes[0].codeKey;
            setPaySelected(codeKey);
          }
        } else {
          setTipMsg(getResponseMessage(ret));
        }
      })
      .catch((e) => {
        setTipMsg(e.message);
      });
  }, [txnId]);

  useEffect(() => {
    setShowClose(false);
    setShowTempStore(false);
    setShowCloseAndTempStore(true);

    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }));

    contentForNextStep(1);
    return () => {
      setShowClose(true);
      setShowTempStore(true);
      setShowCloseAndTempStore(false);

      setNextStepText(intl.formatMessage({ id: 'next' }));

      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      submit();
    }
  }, [contentNextStep]);

  const columns = [
    {
      title: intl.formatMessage({ id: 'cost_details' }),
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: intl.formatMessage({ id: 'amount' }),
      dataIndex: 'amount',
      key: 'amount',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'tax_payment' }),
      dataIndex: 'tax',
      key: 'tax',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'subtotal' }),
      key: 'subtotal',
      dataIndex: 'subtotal',
      width: '100px',
    },
  ];
  const [tableData, setTableData] = useState<
    { key: string; title: string; amount: string; tax: string; subtotal: string }[]
  >([]);

  return (
    <>
      {tipMsg ? (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      ) : (
        <></>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('sectionTitle')}>
          {intl.formatMessage({ id: 'payment_information' })}
        </div>
        <Table columns={columns} dataSource={tableData} pagination={false} />
        <div className={px('pay-way')}>
          <p className={px('formTitle')} style={{ marginBottom: '10px' }}>
            {intl.formatMessage({ id: 'payment_channels' })}
            {cancelPayDto?.isShowCancelPayButton && (
              // <span style={{ marginLeft: '10px', color: 'red' }}>
              //   (
              //   {getLangGroup(
              //     cancelPayDto?.payMsgCodeDto.codeCname,
              //     cancelPayDto?.payMsgCodeDto.codePname,
              //     cancelPayDto?.payMsgCodeDto.codeEname,
              //   )}
              //   )
              // </span>
              <div style={{ marginLeft: '10px', color: 'red' }} dangerouslySetInnerHTML={{
                __html: getLangGroup(
                  cancelPayDto?.payMsgCodeDto.codeCname,
                  cancelPayDto?.payMsgCodeDto.codePname,
                  cancelPayDto?.payMsgCodeDto.codeEname,
                )
              }}
              />
            )}
          </p>
          {payCodes?.map((item, index) => (
            <div style={{ paddingLeft: '30px', marginTop: '5px' }}>
              <Radio
                disabled={!item.available}
                checked={item.codeKey == paySelected}
                onChange={(e) => {
                  setPaySelected(item.codeKey);
                }}
              >
                {item.codeKey === 'GOV' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/GOVpay.png" alt="" />
                )}
                {item.codeKey === 'BOC' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCEPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BOCP' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCPPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BNU' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/BNU.png" alt="" />
                )}
                {!item.available && (
                  <div style={{ color: 'red', fontSize: '16px' }}>
                    {getLangGroup(
                      item.payMaintenanceCodeDto?.codeCname,
                      item.payMaintenanceCodeDto?.codePname,
                      item.payMaintenanceCodeDto?.codeEname,
                    )}
                  </div>
                )}
              </Radio>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default FormNine;
