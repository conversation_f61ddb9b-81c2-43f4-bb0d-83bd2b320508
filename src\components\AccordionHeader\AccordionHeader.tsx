import React from 'react';
import styles from './AccordionHeader.less';

export interface AccordionHeaderProps {
  title: string;
  className?: string;
}

function AccordionHeader(props: AccordionHeaderProps) {
  const { title, className = '' } = props;
  return (
    <div className={`${styles.titleArea} ${className}`}>
      <span>{title}</span>
      <span className={styles.icon}>
        <svg viewBox="0 0 15 15" width="1em" height="1em">
          <path d="M 0 2.5L 7.5 12.5L 15 2.5Z" />
        </svg>
      </span>
    </div>
  );
}

export default AccordionHeader;
