.checkbox {
  text-align: left;
  color: @brand-primary;

  .childBox {
    display: inline-block;
    line-height: 36px;
  }
  :global {
    .am-checkbox-wrapper {
      display: flex;
      align-items: center;
    }
    .am-checkbox {
      width: 32px;
      height: 32px;
      margin-right: 16px;
    }

    .am-checkbox-inner {
      width: 32px;
      height: 32px;
      border-radius: 6px !important;
    }
    .am-checkbox-checked {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      background-color: #1BAEDF;
    }
    .am-checkbox-inner:after {
      top: 0;
      right: 8px;
      width: 8px;
      height: 20px;
    }
    .am-list-item .am-list-line .am-list-content {
      font-size: @font-size-md;
      padding: 0;
    }

    .am-list-item .am-list-thumb:first-child {
      margin-right: 20px;
    }
    .am-list-item-active {
      background-color: #fff;
    }
    .am-list-item {
      min-height: 46px;
      padding-left: 0;
    }
    .am-list-item.am-list-item-middle .am-list-line {
      align-items: flex-end;
    }
  }
}

