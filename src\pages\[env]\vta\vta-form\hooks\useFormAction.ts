import { useEffect, useMemo, useState } from 'react';
import { useGetRes } from './useGetRes';
import { useGetForm1Param, useGetForm3Param, useGetFormParam } from './useGetFormParam';
import { FetchResponse } from '@/utils/fetch';
import { FormInstance } from 'antd';

export const useFormAction = (
  txnId,
  formIns: FormInstance<any>,
  getFormDataFn: (
    data?: any,
  ) => Promise<
    | FetchResponse<any>
    | {
        error: boolean;
        data: null;
        response: Response;
      }
  >,
  saveFormDataFn: (
    data?: any,
  ) => Promise<
    | FetchResponse<any>
    | {
        error: boolean;
        data: null;
        response: Response;
      }
  >,
  props,
) => {
  let { nextStepTrigger, handleNextStep } = props;
  console.log(props, 'props')
  const [canEditable, setCanEditable] = useState({});
  let { getFormData } = useGetRes();
  let { formData } = useGetFormParam(txnId);
  let { formData: formData1 } = useGetForm1Param(txnId);
  let { formData: formData3 } = useGetForm3Param(txnId);
  let vehType: any = useMemo(() => {
    return formData?.vehType;
  }, [formData]);
  let vtaCategory: any = useMemo(() => {
    return formData1?.vtaCategory || '';
  }, [formData1]);

  let powerSource: any = useMemo(() => {
    return formData3?.powerSource || '';
  }, [formData3]);
  const getRecordData = async () => {
    if (!txnId) return;
    const res = await getFormDataFn({ txnId: txnId });
    let { formData, canEditable } = getFormData(res.data);
    setCanEditable(canEditable);
    formIns.setFieldsValue({ ...formData });
  };
  useEffect(() => {
    getRecordData();
  }, [txnId]);
  const submit = async () => {
    try {
      const data = await formIns.validateFields();
      console.log(data, 'data')
      let res = await saveFormDataFn(data);
      if (res.error) {
        return false;
      }
      return true;
    } catch (error) {
      return false;
    }
  };
  useEffect(() => {
    if (!nextStepTrigger || nextStepTrigger === 0) {
      return;
    }
    handleNextStep(() => {
      return submit();
    });
  }, [nextStepTrigger]);

  return {
    formData,
    formData1,
    vehType,
    vtaCategory,
    powerSource,
    canEditable,
  };
};
