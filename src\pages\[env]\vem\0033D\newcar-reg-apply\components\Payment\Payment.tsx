import React, { useContext, useEffect, useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Payment.less';

import { Alert, Radio, Table } from 'antd';
import { cancelTxnPay, completePayment, getServiceFee } from '@/services/0028F';
import { useIntl } from 'umi';
import { isMobile } from '@/utils';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import SpinContext from '@/components/SpinContext';

const px = pc('payment-33d');

export interface PaymentProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;
  txnId: string | undefined;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (text: string) => void;

  setShowClose: (boolean) => void;
  setShowTempStore: (boolean) => void;
  setShowCloseAndTempStore: (boolean) => void;
  contentForNextStep: (number) => void;
}

export type ServiceFeeType = {
  key: string;
  title: string;
  amount: string;
  tax: any;
  subtotal: any;
};

const Payment = (props: PaymentProps) => {
  const intl = useIntl();
  const { className = '', ...otherProps } = props;
  const {
    setNextStepText,
    setShowClose,
    setShowTempStore,
    setShowCloseAndTempStore,
    txnId,
    contentForNextStep,
    contentNextStep,
    setDisableNextStep,
  } = otherProps;

  const [tipMsg, setTipMsg] = useState<string>('');
  const [serviceFeeData, setServiceFeeData] = useState<getServiceFeeRes>({} as getServiceFeeRes);
  const [paySelected, setPaySelected] = useState<string>('');
  const [cancelPayDto, setCancelPayDto] = useState<CancelPayDto>();

  const { setLoading } = useContext(SpinContext);
  const baseUrl =
    window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const handleCancelTxnPay = async () => {
    if (!txnId) return;
    try {
      const res = await cancelTxnPay([txnId]);
      if (res?.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }
      // 刷新页面
      location.reload();
    } finally {
      setDisableNextStep(false);
    }
  };

  useEffect(() => {
    const fetchServiceFee = async () => {
      try {
        if (txnId) {
          const res = await getServiceFee(txnId);
          setLoading(false);
          if (res && res.code === '0') {
            const { data } = res;
            // 设置取消支付状态
            setCancelPayDto(data?.cancelPayDto);
            // 如果需要取消支付
            if (data?.cancelPayDto?.isShowCancelPayButton === true) {
              // 禁用支付选择
              data.payCodes.forEach((item) => {
                item.available = false;
              });
              // 设置取消支付按钮文案
              setNextStepText(intl.formatMessage({ id: 'cancel_payment' }));
            }

            setServiceFeeData(res.data ?? {});
            if (res.data.payCodes && res.data.payCodes?.length) {
              const codeKey =
                data?.cancelPayDto?.isShowCancelPayButton === true
                  ? data.cancelPayDto.payCodeKey
                  : data.payCodes[0].codeKey;
              setPaySelected(codeKey);
            }
          } else {
            setLoading(false);
            setTipMsg(getResponseMessage(res));
          }
        }
      } catch (e: any) {
        setLoading(false);
        setTipMsg(e.message);
      }
    };
    fetchServiceFee();
  }, [txnId]);

  const tableData = useMemo(() => {
    const dataSource: ServiceFeeType[] = [];
    if (serviceFeeData && serviceFeeData.feeItems) {
      (serviceFeeData.feeItems ?? []).forEach((item, index) => {
        dataSource.push({
          key: (index + 1).toString(),
          title: getLangGroup(item.zh, item.pt),
          amount: item.priceTotal?.toLocaleString(),
          tax: item.taxPriceTotal?.toLocaleString(),
          subtotal: item.subtotal?.toLocaleString(),
        });
      });
      dataSource.push({
        key: (serviceFeeData.feeItems.length + 1).toString(),
        title: intl.formatMessage({ id: 'total' }),
        amount: '',
        tax: <span style={{ color: '#084ab8' }}>MOP</span>,
        subtotal: (
          <span style={{ color: '#084ab8' }}>{serviceFeeData.total?.toLocaleString()}</span>
        ),
      });
    }
    return dataSource;
  }, [serviceFeeData]);

  useEffect(() => {
    setLoading(true);
    setShowClose(false);
    setShowTempStore(false);
    setShowCloseAndTempStore(true);

    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }));
    contentForNextStep(1);
    setDisableNextStep(false);
    return () => {
      setShowClose(true);
      setShowTempStore(true);
      setShowCloseAndTempStore(false);

      setNextStepText(intl.formatMessage({ id: 'next' }));
      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    const submit = async () => {
      setTipMsg('');
      setDisableNextStep(true);
      // 是否是取消支付
      if (cancelPayDto?.isShowCancelPayButton) {
        await handleCancelTxnPay();
        return;
      }

      let pageUrl = 'ovsap/web/vem/0033D/newcar-reg-apply?page=success';
      if (paySelected === 'BOCP' || paySelected === 'BOC' || paySelected === 'BNU') {
        pageUrl = baseUrl + '/' + pageUrl;
      }
      const data: completePaymentParams = {
        txnId: txnId!,
        payMethodCodeKey: paySelected,
        lang: 'zh_TW',
        ua: isMobile() ? 'MobileWeb' : 'PCWeb',
        remark: '',
        pageUrl,
      };

      try {
        const ret = await completePayment(data);
        setDisableNextStep(false);
        if (!ret) return;
        if (ret.code === '0') {
          const { data } = ret;
          if (data.payUrl) {
            location.href = data.payUrl;
          } else if (data.returnHTML) {
            document.write(data.returnHTML?.replace(/\\/, ''));
          }
        } else {
          setTipMsg(getResponseMessage(ret));
        }
      } catch (e: any) {
        setTipMsg(e.message);
        setDisableNextStep(false);
      }
    };

    if (contentNextStep > 0) {
      submit();
    }
  }, [contentNextStep]);

  const columns = [
    {
      title: intl.formatMessage({ id: 'cost_details' }),
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: intl.formatMessage({ id: 'amount' }),
      dataIndex: 'amount',
      key: 'amount',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'tax_payment' }),
      dataIndex: 'tax',
      key: 'tax',
      width: '100px',
    },
    {
      title: intl.formatMessage({ id: 'subtotal' }),
      key: 'subtotal',
      dataIndex: 'subtotal',
      width: '100px',
    },
  ];

  return (
    <>
      {tipMsg ? (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      ) : null}
      <div className={`${px('root')} ${className}`}>
        <Table columns={columns} dataSource={tableData} pagination={false} />
        <div
          className={px('pay-tip')}
          dangerouslySetInnerHTML={{
            __html: getLangGroup(
              serviceFeeData?.codeDTO?.codeCname,
              serviceFeeData?.codeDTO?.codePname,
              serviceFeeData?.codeDTO?.codeEname,
            ),
          }}
        />

        <div className={px('pay-way')}>
          <p className={px('formTitle')}>
            {intl.formatMessage({ id: 'payment_channels' })}
            {cancelPayDto?.isShowCancelPayButton && (
              <div style={{ marginLeft: '10px', color: 'red' }}>
                (
                {getLangGroup(
                  cancelPayDto?.payMsgCodeDto.codeCname,
                  cancelPayDto?.payMsgCodeDto.codePname,
                  cancelPayDto?.payMsgCodeDto.codeEname,
                )}
                )
              </div>
            )}
          </p>
          {(serviceFeeData.payCodes ?? []).map((item) => (
            <div style={{ paddingLeft: '30px', marginTop: '5px' }} key={item.codeKey}>
              <Radio
                disabled={!item.available}
                checked={item.codeKey === paySelected}
                onChange={(e) => {
                  setPaySelected(item.codeKey);
                }}
              >
                {item.codeKey === 'GOV' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/GOVpay.png" alt="" />
                )}
                {item.codeKey === 'BOC' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCEPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BOCP' && (
                  <img style={{ height: '45px' }} src="/ovsap/image/pay/BOCPPAY.jpg" alt="" />
                )}
                {item.codeKey === 'BNU' && (
                  <img style={{ width: '150px' }} src="/ovsap/image/pay/BNU.png" alt="" />
                )}
              </Radio>
              {!item.available && (
                <div style={{ color: 'red', fontSize: '16px' }}>
                  {getLangGroup(
                    item?.payMaintenanceCodeDto?.codeCname,
                    item?.payMaintenanceCodeDto?.codePname,
                    item?.payMaintenanceCodeDto?.codeEname,
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default Payment;
