@import '../../../../styles/listItemMixins.less';
.root {
  padding-bottom: 0;
  :global {
    .cbd-web-base-form-checkbox .ant-checkbox-disabled .ant-checkbox-inner {
      background-color: #d1d1d1;
      border-color: #d1d1d1;
    }
    .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
      border-color: #fff;
    }
  }
}

.detailRoot {
  font-size: 16 * @hd;
  padding: 16 * @hd;
  padding-bottom: 4 * @hd;
  & > div {
    margin-bottom: 16 * @hd;
    display: flex;
    span:first-child {
      .itemKeyMixins(@hd);
    }
    span:first-child::after {
      content: '';
      margin-left: 4 * @hd;
      margin-right: 8 * @hd;
    }
    span:nth-child(2),
    pre {
      .itemValueMixins(@hd);
    }
  }
  div:last-child {
    margin-bottom: 0;
  }
  .prewrapDiv {
    white-space: pre-wrap;
    color: var(--firstTextColor);
  }
}

.webDetailRoot {
  font-size: 16px;
  margin: 16px;
  margin-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #d1d1d6;
  & > div {
    margin-bottom: 16px;
    display: flex;
    flex: none;
    width: 50%;
    span:first-child {
      .itemKeyMixins();
    }
    span:first-child::after {
      // content: ':';
      // margin-left: 2 * @hd;
    }
    span:nth-child(2) {
      .itemValueMixins();
    }
    &.prewrapDiv {
      white-space: pre-wrap;
    }
  }
}

.baseTitleContainer {
  margin: 0 16 * @hd;
  .cardTitleMixins();
}
