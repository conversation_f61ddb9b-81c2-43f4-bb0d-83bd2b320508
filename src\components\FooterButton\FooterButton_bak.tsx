import React from 'react';
import { useIntl, useSelector } from 'umi';
import { Button } from 'antd';
import type { PaymentModelsState } from 'umi';
import { stepType as StepType } from '@/pages/service-step/config';
import useModalMethod from '../Modal/useModalMethod';
import './FooterButton.less';

export interface IFooterButton {
  className?: string;
  step?: number;
  stepType?: string;
  handleNextStep?: () => void;
  handlePrevStep?: () => void;
  handleComplete?: () => void;
  handleClose?: () => void;
}

const FooterButton = (props: IFooterButton) => {
  const intl = useIntl();
  const { handlePrevStep, handleNextStep, className = '', step, stepType, handleComplete } = props;
  const [Modal] = useModalMethod();

  const allModels = useSelector(({ paymentModels }: { paymentModels: PaymentModelsState }) => ({
    paymentModels,
  }));
  const {
    paymentModels: { webPaymentOrder },
  } = allModels;

  const isPayList = stepType === StepType.payment && !webPaymentOrder;
  const isCheckoutCounter = stepType === StepType.payment && webPaymentOrder;
  // const notShowPrevStep = !(step === 1 || isCheckoutCounter);
  const notShowPrevStep = !(step === 1);
  const isDatacheckStep = stepType === StepType.dataCheck;
  let nextStepName = isCheckoutCounter ? '支付' : intl.formatMessage({ id: 'next' });
  if (isPayList) nextStepName = intl.formatMessage({ id: 'immediate_payment' });
  if (isDatacheckStep) nextStepName = '确认';

  // const handleClose = () => {
  //   // 关闭当前或上层窗口
  //   window.top.close();
  // };

  const handleClose = () => {
    props.handleClose ? props.handleClose() : handleComplete ? window.top.close() : Modal.quitConfirm();
  };

  return (
    <div className={className}>
      {!handleComplete ? (
        <>
          <Button type="default" onClick={handleClose}>
            {intl.formatMessage({ id: 'turn_off' })}
          </Button>
          {notShowPrevStep && (
            <Button onClick={handlePrevStep} type="primary">
              {intl.formatMessage({ id: 'previous' })}
            </Button>
          )}
          <Button onClick={handleNextStep} type="primary">
            {intl.formatMessage({ id: nextStepName })}
          </Button>
        </>
      ) : (
        <Button onClick={handleComplete} type="primary">
          {intl.formatMessage({ id: 'complete' })}
        </Button>
      )}
    </div>
  );
};

export default FooterButton;
