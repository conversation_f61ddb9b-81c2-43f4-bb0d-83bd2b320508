import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import { history, Location, useIntl, useLocation } from 'umi';

import FormBase from './components/FormBase';
import FooterButton from '@/components/FooterButton';
import GuideButton from '@/components/GuideButton';

import FormTen from './components/FormTen';

const classPrefix = 'vta-form-page';
const px = pc(classPrefix);
type VilFormLocation = {
  query: {
    step?: number;
  };
};
const VilFormPage = () => {
  const intl = useIntl();
  const location = useLocation() as Location<unknown> & VilFormLocation;

  const urlParams = new URLSearchParams(window.location.search);
  const txnId = urlParams.get('txnId') || '';
  const [step, setStep] = useState(() => {
    if (location.query.step && Number(location.query.step) > 0) {
      return Number(location.query.step);
    }
    return 0;
  });
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));

  const [showPrevStep, setShowPrevStep] = useState(true);
  const [showNextStep, setShowNextStep] = useState(true);
  const [showClose, setShowClose] = useState(true);
  const [showCloseAndTempStore, setShowCloseAndTempStore] = useState(false);
  const [showTempStore, setShowTempStore] = useState(true);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);
  const [nextStepTrigger, setNextStepTrigger] = useState(0);
  const prevStep = () => {
    setStep(step - 1);
  };

  const handleNextStep = async (callback?: () => boolean | Promise<boolean>) => {
    setDisableNextStep(true);
    setNextStepTrigger(0);
    // 如果提供了回調函數，則呼叫它來決定是否繼續
    if (callback) {
      try {
        const shouldProceed = await callback();
        if (!shouldProceed) {
          setDisableNextStep(false);
          return; // 如果回調返回false，則不繼續下一步
        }
      } finally {
        setDisableNextStep(false);
      }
    }
    setDisableNextStep(false);
    setStep(step + 1);
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const nextStep = () => {
    setNextStepTrigger(1);
  };

  const handleClose = () => {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  useEffect(() => {
    setDisablePrevStep(step <= 0);
    history.replace({
      pathname: location.pathname,
      query: { ...location.query, step: `${step}`,txnId},
    });
  }, [step]);

  if (step === 11) {
    return <FormTen />;
  }

  return (
    <>
      <FormBase
        txnId={txnId}
        step={step}
        classPrefix={classPrefix}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setShowPrevStep={(flag) => {
          setShowPrevStep(flag);
        }}
        setShowNextStep={(flag) => {
          setShowNextStep(flag);
        }}
        setShowCloseAndTempStore={(flag) => {
          setShowCloseAndTempStore(flag);
        }}
        setShowClose={(flag) => {
          setShowClose(flag);
        }}
        setShowTempStore={(flag) => {
          setShowTempStore(flag);
        }}
        nextStep={nextStep}
        setNextStepText={(text) => {
          setNextStepText(text);
        }}
        nextStepTrigger={nextStepTrigger}
        handleNextStep={handleNextStep}
      />
      <GuideButton itemId="" />
      <FooterButton
        // handleClose={handleClose}
        className={px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        showCloseAndTempStore={showCloseAndTempStore}
        showTempStore={showTempStore}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
      />
    </>
  );
};

export default VilFormPage;
