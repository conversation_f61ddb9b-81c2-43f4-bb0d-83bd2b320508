@classprefix: entranceInfo-28f;

.@{classprefix} {
  &-entranceInfoRoot {
    margin: 0.16rem 0.16rem 0;
    flex-wrap: wrap;
    font-size: 0.16rem;
    line-height: 0.22rem;
    white-space: pre-wrap;
    .ant-descriptions-item-label {
      width: 50%;
      font-size: 0.16rem;
    }
   
    .ant-descriptions-item-content {
      width: 50%;
      font-size: 0.16rem;
    }
    .ant-descriptions-item-label {
      color: #6c6c6c !important;
      font-size: 0.16rem !important;
    }

    .ant-descriptions-item-label::after {
      content: '';
    }
    .ant-collapse {
      color: white;
      background-color: @brand-primary;
      border: 1px solid #f0f0f0;
      .ant-collapse-header {
        padding: 12px 16px 12px 40px !important;
        color: white;
        font-size: 16px;
        cursor: pointer;
      }
      .ant-collapse-content {
        color: rgba(0, 0, 0, 0.85);
        background-color: #fff;
        border-top: 1px solid #f0f0f0;
      }
      .form-title {
        font-size: 16px;
        color: @brand-primary;
      }
      .form-title2 {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #232323;
        margin: 0 24px;
        padding-bottom: 10px;
      }
      .form-title2:before {
        display: block;
        content: '';
        width: 4px;
        height: 16px;
        background: @brand-primary;
        margin-right: 8px;
      }
    }
  }

  &-formTitle {
    margin-top: -0.12rem;
    display: flex;
    align-items: center;
    font-weight: bold;
    margin-left: 0.24rem;
    font-size: 0.16rem;
    color: #232323;
    padding-bottom: 0.1rem;

    &:before {
      display: block;
      content: '';
      width: 0.04rem;
      height: 0.16rem;
      background: @brand-primary;
      margin-right: 0.08rem;
    }
  }
}

.section-title {
  margin: 0;
  width: 100%;
  padding: 0 20px;
  min-height: 41px;
  font-size: 0.2rem;
  color: #ffffff;
  background: @brand-primary;
  border-radius: 10px 10px 0px 0px;
}


