@baseSize: 12 * @hd;

.cbd-mobile-base-form-checkbox-tips {
  position: relative;
  .ant-checkbox-checked::after {
    top: -1 * @hd;
    left: -1 * @hd;
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 32 * @hd;
    padding-bottom: 10 * @hd;
  }
  &-label {
    color: @font-color-title;
    font-size: @font-size-head-sm;
  }
  &-subtext {
    padding: @baseSize;
    background: #fafafa;
    border-radius: 4 * @hd;
    font-size: @font-size-md;
    color: @mobile-baseColor;
    .ant-checkbox {
      top: 0;
    }
  }
  .ant-checkbox-wrapper {
    position: absolute;
    top: 8 * @hd;
    right: 0;
  }
  .ant-checkbox-wrapper:hover .ant-checkbox-inner,
  .ant-checkbox:hover .ant-checkbox-inner,
  .ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: #c1c1c1;
  }
  .ant-checkbox-inner {
    &::after {
      background-color: @brand-second;
    }
  }

  .ant-checkbox-checked {
    .ant-checkbox-inner {
      background-color: @brand-second;
      border-color: @brand-second!important;
    }
    &::after {
      border-color: @brand-second;
    }
  }
}

.cbd-web-base-form-checkbox-tips {
  padding-top: 8 * @hd;
  &-label {
    color: rgba(0, 0, 0, 0.85);
  }
  &-subtext {
    margin-top: 16 * @hd;
    color: rgba(0, 0, 0, 0.65);
    line-height: 22 * @hd;
  }
  .ant-checkbox-wrapper {
    margin-right: 8 * @hd;
  }
}
