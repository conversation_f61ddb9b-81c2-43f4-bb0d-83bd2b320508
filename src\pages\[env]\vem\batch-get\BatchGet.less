@prefix: page-batch-get;
@prefix-mobile: page-batch-get-mobile;

.@{prefix} {
  &- {
    &root {
      max-width: 1048px;
      margin: 0 auto;
      padding-bottom: 0.68rem;
      .my-card-item {
        .my-card-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .ant-btn.ant-btn-dangerous {
            background: #fff;
            color: #f33b40;
            border: 1px solid #f33b40;
          }
        }
      }
      .batch-get-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0.68rem;
        background-color: #fff;
        display: flex;
        align-items: center;
        box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

        .footer-container {
          width: 1048px;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .ant-btn {
            &.ant-btn-default {
              background: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
            }

            min-width: 128px;
            height: 48px;
            line-height: 48px;
            border-radius: 24px;
            font-size: 16px;
            padding: 0 5px;
          }
        }
      }
      .batch-get-card-form {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: space-between;
        .ant-form-item {
          width: 48%;
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
            }
          }

          .statusBg1 {
            padding: 0 2px;
            color: #f33b40;
          }
          .statusBg2 {
            padding: 0 2px;
            color: @brand-primary;
          }
        }
        .my-card-body {
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
    &title {
      font-size: 0.2rem;
      font-weight: 700;
      padding: 0.2rem 0.24rem 0.2rem 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    }

    &sectionTitle {
      margin-top: 20px;
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
      color: #232323;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }
  }
}

.@{prefix-mobile} {
  &- {
    &root {
      background-color: #eee;
      padding: 40px 20px;
      padding-bottom: 1.8rem;
      .my-card-item {
        .my-card-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .ant-btn.ant-btn-dangerous {
            background: #fff;
            color: #f33b40;
            border: 1px solid #f33b40;
          }
        }
      }
      .batch-get-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        padding:10px 0;
        background-color: #fff;
        display: flex;
        align-items: center;
        box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);

        .footer-container {
          width: 100%;
          padding: 0 20px;
          display: flex;
          justify-content: space-around;
          .ant-btn {
            flex: 1;
            height: 48px;
            line-height: 48px;
            border-radius: 24px;
            font-size: 16px;
            padding: 0 5px;
            &.ant-btn-default {
              background: #fff;
              color: @brand-primary;
              border: 1px solid @brand-primary;
            }
          }
        }
      }
      .batch-get-card-form {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: space-between;
        .ant-form-item {
          width: 100%;
          font-size: 16px;
          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
            }
          }

          .statusBg1 {
            padding: 0 2px;
            color: #f33b40;
          }
          .statusBg2 {
            padding: 0 2px;
            color: @brand-primary;
          }
        }
        .my-card-body {
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
    &title {
      font-size: 0.2rem;
      font-weight: 700;
      padding: 0.2rem 0.24rem 0.2rem 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      margin-bottom: 0.2rem;
    }

    &sectionTitle {
      display: none;
    }

    &sectionTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }
  }
}
