import React, { useRef, useState } from 'react';
import { Form } from 'antd';
import { Modal } from 'antd-mobile';
import type { UploadModelsState } from 'umi';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import Upload from '../Upload/Upload';
import styles from './UploadForm.less';

const getIntlText = (mo?: string, cn?: string, pt?: string, en?: string) => {
  // const lang = 'zh-MO';
  const res = mo ?? '';
  // if (lang === 'zh-CN' && cn) res = cn;
  // if (lang === 'pt' && pt) res = pt;
  // if (lang === 'en' && en) res = en;
  return res;
};

function UploadForm() {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const _this = useRef<{ isFirstClick: boolean }>({ isFirstClick: true }).current;
  const { applicantMaterials, materialConfig } = useSelector(
    ({ uploadModels }: { uploadModels: UploadModelsState }) => uploadModels,
  );
  const intl = useIntl();

  return (
    <div>
      {materialConfig.map((item, index) => {
        const {
          material: { materialId, code, nameEn, namePt, nameZhCn, nameZhMo },
        } = item;
        const { noticeEn, noticePt, noticeZhCn, noticeZhMo } = item;
        const name = getIntlText(nameZhMo, nameZhCn, namePt, nameEn);
        const notice = getIntlText(noticeZhMo, noticeZhCn, noticePt, noticeEn);
        return (
          <React.Fragment key={code}>
            <div className={styles.required}>{name}</div>
            <Form.Item
              name={materialId}
              noStyle
              rules={[{ required: true, message: intl.formatMessage({ id: '必填' }) }]}
            >
              <Upload
                materialsCode={code}
                materialsId={materialId}
                materialsName={name}
                setModalVisible={(v: boolean) => {
                  setModalVisible(v);
                }}
                isFirstClick={_this.isFirstClick && applicantMaterials?.length === 0}
                setIsFirstClick={(v: boolean) => {
                  _this.isFirstClick = v;
                }}
              />
            </Form.Item>
            {notice && (
              <Modal
                visible={modalVisible}
                onClose={() => {
                  setModalVisible(false);
                }}
                title={intl.formatMessage({ id: '提示' })}
                transparent
                maskClosable={false}
                footer={[
                  {
                    text: intl.formatMessage({ id: '知道' }),
                    onPress: () => {
                      setModalVisible(false);
                    },
                  },
                ]}
              >
                <div style={{ whiteSpace: 'pre-wrap', textAlign: 'left' }}>{notice}</div>
              </Modal>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}

export default UploadForm;
