import React from 'react';
import { Picker, List } from 'antd-mobile';
import usePersistFunc from '@/layouts/usePersistFunc';
import { useIntl } from 'react-intl';
import styles from './Select.less';

export interface SelectProps {
  data: {
    value: string | number;
    label: string;
  }[];
  value?: string | number;
  onChange?: (v: string | number) => void;
  disabled?: boolean;
}
function Select(props: SelectProps) {
  const { data, value, onChange, disabled = false } = props;
  const intl = useIntl();

  const handleValueChange = usePersistFunc((v: Array<string | number> | undefined) => {
    if (v && onChange) {
      onChange(v[0]);
    }
  });

  return (
    <div className={`${styles.root} ${disabled ? styles.disabled : ''}`}>
      <Picker
        extra={intl.formatMessage({ id: 'please_select' })}
        disabled={disabled}
        cols={1}
        value={value ? [value] : []}
        onChange={handleValueChange}
        data={data}
      >
        <List.Item extra="cs" arrow={disabled ? '' : 'down'} />
      </Picker>
    </div>
  );
}

export default Select;
