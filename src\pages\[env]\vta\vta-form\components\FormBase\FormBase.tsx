import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './FormBase.less';

import { Steps } from 'antd';
import Form1 from '../Form1';
import Form2 from '../Form2';
import Form3 from '../Form3';
import Form6 from '../Form6';

import Form9 from '../Form9';

import FormSeven1 from '../FormSeven1';
import FormEight1 from '../FormEight1';
import { useIntl } from 'umi';
import Form0 from '../Form0';
import Form4 from '../Form4';
import Form5 from '../Form5';
import ContactDetails from '../ContactDetails';
import { getServiceTitle } from '@/services/vta';
import { getLangGroup } from '@/locales/lang';

export interface FormBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  classPrefix: string;
  step: number;
  txnId?: string;
  setDisablePrevStep: (show: boolean) => void;
  setDisableNextStep: (show: boolean) => void;
  setShowPrevStep: (show: boolean) => void;
  setShowNextStep: (show: boolean) => void;
  setShowCloseAndTempStore: (show: boolean) => void;
  setShowClose: (show: boolean) => void;
  setShowTempStore: (show: boolean) => void;
  nextStep: () => void;
  setNextStepText: (text: string) => void;
  nextStepTrigger?: number;
  handleNextStep: (callback?: () => boolean | Promise<boolean>) => void;
}

const FormBase = (props: FormBaseProps) => {
  const intl = useIntl();

  const { step, className = '',txnId, ...otherProps } = props;
  const {
    setDisablePrevStep,
    setDisableNextStep,
    nextStep,
    setNextStepText,
    nextStepTrigger,
    handleNextStep,
  } = otherProps;

  const px = pc(otherProps.classPrefix);

  useEffect(() => {
    console.log('[FormBase] step', step);
  }, [step]);

  //變速器 gearboxType
  const [gearboxType, setGearboxType] = useState<string>('');

  const [vehType, setVehType] = useState<any>(0);

  const renderRightContent = () => {
    // 支付信息
    if (step === 10) {
      return (
        <Form9
          txnId={txnId}
          setNextStepText={setNextStepText}
          setShowCloseAndTempStore={props.setShowCloseAndTempStore}
          setShowClose={props.setShowClose}
          setShowTempStore={props.setShowTempStore}
          setShowPrevStep={props.setShowPrevStep}
        />
      );
    }

    // 確定資料
    if (step === 9) {
      return (
        <FormEight1 txnId={txnId} setDisableNextStep={setDisableNextStep} setNextStepText={setNextStepText} />
      );
    }

    // 上傳文件
    if (step === 8) {
      return <FormSeven1 txnId={txnId} />;
    }

    // 進口商聯絡資料
    if (step === 7) {
      return (
        <ContactDetails
          txnId={txnId}
          setDisableNextStep={function (boolean: any): void {
            throw new Error('Function not implemented.');
          }}
        />
      );
    }
    // 電池資料
    if (step === 6) {
      return <Form6
          txnId={txnId}
          nextStepTrigger={nextStepTrigger}
          handleNextStep={handleNextStep} />;
    }

    // 尾氣排放
    if (step === 5) {
      return <Form5
          txnId={txnId}
          nextStepTrigger={nextStepTrigger}
          handleNextStep={handleNextStep} />;
    }

    // 車身
    if (step === 4) {
      return <Form4
          txnId={txnId}
          nextStepTrigger={nextStepTrigger}
          handleNextStep={handleNextStep} />;
    }

    // 動力裝置
    if (step === 3) {
      return <Form3
          txnId={txnId}
          nextStepTrigger={nextStepTrigger}
          handleNextStep={handleNextStep}  />;
    }

    // 尺寸及重量
    if (step === 2) {
      return (
        <Form2
          txnId={txnId}
          nextStepTrigger={nextStepTrigger}
          handleNextStep={handleNextStep}
        />
      );
    }
    // 特徵
    if (step === 1) {
      return (
        <Form1
          txnId={txnId}
          step={step}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          nextStepTrigger={nextStepTrigger}
          handleNextStep={handleNextStep}
        />
      );
    }
    // 車輛級別及商標
    if (step === 0) {
      return (
        <Form0
          txnId={txnId}
          step={step}
          setDisablePrevStep={setDisablePrevStep}
          setDisableNextStep={setDisableNextStep}
          nextStep={nextStep}
          nextStepTrigger={nextStepTrigger}
          handleNextStep={handleNextStep}
        />
      );
    }
    return <></>;
  };
  let [serviceTitle,setServiceTitle] = useState<string>('');
useEffect(() => {
  getServiceTitle({spOperationCode:'0025C'}).then((res) => {
    console.log(res)
      setServiceTitle(
        getLangGroup(
          res?.data?.serviceTitleCn,
          res?.data?.serviceTitlePt,
          res?.data?.serviceTitleEn,
        ),
      );
    });
  }, []);

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <h2 className={px('title')}>
        <span
          style={{ flex: 1 }}
        >{serviceTitle}</span>
        <span>{`公司95`}</span>
      </h2>
      <div className={px('stepinfo')}>
        <div className={px('step')}>
          <h3>{intl.formatMessage({ id: 'application_steps' })}</h3>
          <Steps
            direction="vertical"
            size="small"
            current={step == 7 ? 1 : step == 8 ? 2 : step == 9 ? 3 : 4}
          >
            <Steps.Step title="錄入資料" icon={1} />
            <Steps.Step title="提交文件" icon={2} />
            <Steps.Step title="資料確定" icon={3} />
            <Steps.Step title="支付信息" icon={4} />
          </Steps>
        </div>

        <div className={px('form')}>
          {step > 7 ? (
            renderRightContent()
          ) : (
            <>
              <div className={px('body-root')}>
                <div className={px('sectionTitle')}>錄入資料</div>
                <div className={px('sectionBody')}>
                  <div className="tab-wrap">
                    <div className="tab-header">
                      <ul>
                        <li className={step === 0 ? 'active' : ''}>車輛級別及商標</li>
                        <li className={step === 1 ? 'active' : ''}>特徵</li>
                        <li className={step === 2 ? 'active' : ''}>尺寸及重量</li>
                        <li className={step === 3 ? 'active' : ''}>動力裝置</li>
                        <li className={step === 4 ? 'active' : ''}>車身</li>
                        <li className={step === 5 ? 'active' : ''}>尾氣排放</li>
                        <li className={step === 6 ? 'active' : ''}>電池及充電接口</li>
                        {/*<li className={step === 7 ? 'active' : ''}>型號認可其他資料</li>*/}
                        {/*<li className={step === 0 ? 'active' : ''}>機動車輛稅</li>*/}
                        {/*<li className={step === 1 ? 'active' : ''}>*/}
                        {/*  {intl.formatMessage({ id: 'model_approval_documents' })}*/}
                        {/*</li>*/}
                        {/*<li className={step === 2 ? 'active' : ''}>型號認可其他資料</li>*/}
                        {/*<li className={step === 3 ? 'active' : ''}>馬達資料</li>*/}
                        {/* <li className={step === 4 ? 'active' : ''}>前制動系統</li>
                        <li className={step === 5 ? 'active' : ''}>後制動系統</li> */}
                        {/*<li className={step === 4 ? 'active' : ''}>電池資料</li>*/}
                        {/*<li className={step === 5 ? 'active' : ''}>輪胎資料</li>*/}
                        <li className={step === 7 ? 'active' : ''}>申請人聯絡資料</li>
                        {/* <li className={step === 8 ? 'active' : ''}>配件資料</li> */}
                      </ul>
                    </div>
                    <div className="tab-body">{renderRightContent()}</div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FormBase;
