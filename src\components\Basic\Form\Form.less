.form {
  width: 100%;
  margin-bottom: 40px;
  .formLabel {
    height: 40px;
    line-height: 40px;
    font-size: @font-size-md;
    color: @font-color-base;
  }

  .tip {
    height: 33px;
    font-size: @font-size-sm;
    color: @brand-error;
    line-height: 33px;
  }

  .noBottomLine {
    :global {
      .am-list-body::after{
        height: 0;
      }
    }
  }

  :global {
    .am-list-body::before {
      height: 0 !important;
    }

    .am-list-item.am-input-item {
      padding-left: 0;
    }

    .am-list-item {
      padding-left: 0;
    }
    .am-list-item-active {
      background: transparent !important;
    }
    .am-list-line {
      padding-right: 0;
    }

    .am-list-item .am-list-line .am-list-extra {
      flex-basis: unset;
    }

    .am-list-item .am-input-control input {
      height: 50px;
    }

    .am-list-item {
      min-height: 50px;
    }

    .am-list-item .am-list-line .am-list-content {
      padding-top: 16px;
      padding-bottom: 24px;
      min-height: 50px;
    }

    .am-list-line .am-list-line {
      height: 50px;
    }

    .am-list-item.am-input-item {
      height: auto;
    }

    .am-list-line .am-list-line::after {
      height: 0 !important;
    }

    .am-list-body::after {
      transform: scaleY(0.9) !important;
    }

    .am-radio {
      width: 36px;
      height: 36px;
      border: 1px solid @border-color-base;
      border-radius: 50%;
      margin-right: 16px;
    }

    .am-radio-checked {
      border-width: 10px;
      border-color: @brand-primary;
    }

    .am-radio.am-radio-checked .am-radio-inner:after {
      display: none;
    }

    .am-list-content .am-radio-wrapper {
      margin-right: 50px;
      height: 70px;
    }
  }
}
