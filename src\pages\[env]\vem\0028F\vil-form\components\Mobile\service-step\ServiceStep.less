.baseRoot {
  :global {
    .am-navbar-title {
      justify-content: left;
      margin-left: 12px * @hd;
      color: #232323;
    }
    .mo-baseContainer-mobile-title {
      font-weight: 500;
    }
  }
  .bodyContent {
    position: absolute;
    background-color: var(--firstBgColor);
    width: 100%;
  }
  .root {
    display: flex;
    flex-direction: column;
    height: 100%;
    .main {
      z-index: 11;
      flex-grow: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      .form {
        flex-grow: 1;
      }
      .headerStep {
        flex-shrink: 0;
        background: #fff;
        border-radius: @radius-md;
        color: @brand-primary;
        padding: 44px 0;
        margin-bottom: 12px;
        .title {
          text-align: center;
        }
        .progress {
          margin-top: 26px;
        }
      }
    }
    // .serviceIntroduction {
    //   position: absolute;
    //   bottom: 300px;
    //   z-index: 999;
    //   right: 40px;
    //   width: 112px;
    //   height: 112px;
    //   font-size: 20px;
    //   background: #fff;
    //   border-radius: 50%;
    //   box-shadow: 0px 0px 10px 4px rgba(47,47,47,0.05);
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   flex-direction: column;
    //   color: @brand-primary;
    //   img {
    //     width: 33px;
    //     height: 36px;
    //   }
    //   span {
    //     transform: scale(0.9, 0.9);
    //   }
    // }
    :global {
      .cbd-mobile-form-editabletable-drawer {
        top: 152px;
        padding: 24px 32px;
      }
    }
  }
  .headerRightContent {
    display: flex;
    align-items: center;
    .divider {
      width: 1px;
      height: 35px;
      background: rgba(238, 238, 238, 1);
      margin: 0 24px;
      flex-shrink: 0;
    }
    .iconArea {
      flex-shrink: 0;
      height: 48px;
      width: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var(--borderColor);
      border-radius: 30px;
      .icon {
        color: #8a8a8a;
        font-size: 32px;
      }
    }
  }
  :global {
    .mo-baseContainer-mobile-navBar.shadow {
      box-shadow: none;
      background: var(--secondBgColor);
    }
    .am-navbar-title {
      justify-content: left;
      .mo-baseContainer-mobile-title {
        font-weight: 400;
      }
    }
  }
}
