.root {
  padding: 8 * @hd 0 8 * @hd 0;
  background: var(--secondBgColor);
  border-radius: 0 0 @radius-md @radius-md;
  margin-bottom: 24 * @hd;
  box-shadow: 0px 6 * @hd 12 * @hd 0 rgba(0, 0, 0, 0.08);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  .title {
    margin-bottom: 4 * @hd;
    font-size: 16 * @hd;
    color: var(--firstTextColor);
    text-align: center;
  }
  .procressArea {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 104px;
    font-size: 26px;
    .procressItem {
      // flex-grow: 1;
      // flex-basis: 0;
      .procressIndexArea {
        display: flex;
        align-items: center;
        .procressIndex {
          margin: 0 16px;
          width: 72px;
          height: 72px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          // box-shadow: 0 0 0 24px #c4c4c4;
          background: fadeout(#c4c4c4, 75%);
          .progressIndexContent {
            width: 48px;
            height: 48px;
            color: #ffffff;
            border-radius: 50%;
            background: var(--step-number);
            line-height: 48px;
            font-size: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .procressDashLine {
          height: 3px;
          width: 80px;
          background: #c1c1c1;
          &.line2 {
            width: 122px;
          }
          &.line3 {
            width: 202px;
          }
          &.line4 {
            width: 80px;
          }
        }
        .activeLine {
          background-color: var(--primaryColor);
        }
        .active {
          background: fadeout(@brand-success, 75%);
          .progressIndexContent {
            background-color: var(--primaryColor);
          }
        }
        .current {
          background: fadeout(#ffc107, 75%);
          .progressIndexContent {
            background-color: var(--step-focuse);
          }
        }
      }
      // .stepName {
      //   text-align: center;
      //   color: #8c8c8c;
      //   margin-top: 10px;
      //   line-height: 42px;
      //   user-select: none;
      // }
      // .currentStep {
      //   color: #262626;
      //   font-size: 28px;
      //   font-weight: bold;
      // }
    }
  }
  .divider {
    margin: 16px 0 12px 0;
    height: 10px;
    width: 340px;
    position: relative;
    width: 100%;
    svg {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      fill: rgba(0, 0, 0, 0.05);
      width: 340px;
      height: 10px;
    }
  }
}
