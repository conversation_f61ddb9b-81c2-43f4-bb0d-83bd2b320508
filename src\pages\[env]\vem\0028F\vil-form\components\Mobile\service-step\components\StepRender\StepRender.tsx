import React, { useContext, useEffect } from 'react';
import { usePersistFn } from 'ahooks';
import type { FormInstance } from 'antd';
import type { PaymentModelsState } from 'umi';
import { MoPay } from '@gov-mo/components';
import { useSelector, useDispatch } from 'umi';
import StepContext from './StepContext';
import { stepType } from '../../config';
import DataCheck from '../DataCheck';
import FormInfo from '../FormInfo';
import ReceiveResult from '../ReceiveResult';
import PaymentInformation from '../PaymentInformation';
import UploadFile from '../UploadFile';
import IdentityAuth from '../IdentityAuth';

const StepRender = (props) => {
  const dispatch = useDispatch();
  const context = useContext(StepContext);
  const allModels = useSelector(({ paymentModels }: { paymentModels: PaymentModelsState }) => ({
    paymentModels,
  }));
  const { formData, currentStep, itemSecurityLevel, stepInfoList, mobile, handleNext } = props;
  const {
    paymentModels: { webPaymentOrder },
  } = allModels;

  useEffect(() => {
    if (context.moPayRef) {
      dispatch({ type: 'paymentModels/init' });
    }
  }, [context.moPayRef]);

  /**
   * 支付流程成功回调
   * ps:流程成功，不一定是支付成功，有可能会是支付失败
   */
  const handlePaySuccess = (e) => {
    // TODO: if success
    // eslint-disable-next-line no-console
    console.log('支付成功', e);
    if (e?.data?.success && currentStep.stepType === stepType.payment) {
      handleNext();
    }
  };

  /**
   * 支付流程中断回调
   */
  const handlePayCancel = () => {
    // eslint-disable-next-line no-console
    console.log('支付被取消');
  };

  // 渲染對應步驟組件
  const renderStep = usePersistFn(() => {
    let res: React.ReactNode;
    switch (currentStep.stepType) {
      case stepType.form:
        res = (
          <FormInfo
            title={currentStep.stepName}
            mobile={mobile}
            onInstance={(
              instance: FormInstance | null,
              receiveResultInstance: FormInstance | null,
            ) => {
              context.formRenderInstance = instance;
              if (receiveResultInstance) {
                context.receiveResultInstance = receiveResultInstance;
              }
              if (!context.initFormValue && instance) {
                context.initFormValue = JSON.stringify(
                  Object.keys(formData).length ? formData : instance.getFieldsValue(),
                );
              }
            }}
          />
        );
        break;
      case stepType.dataCheck:
        res = <DataCheck mobile={mobile} needShowStepList={stepInfoList} />;
        break;
      case stepType.resultReceive:
        res = <ReceiveResult title={currentStep.stepName} />;
        break;
      case stepType.faceRecognition:
        res = (
          <IdentityAuth
            title={currentStep.stepName}
            itemSecurityLevel={itemSecurityLevel}
            onSuccess={() => {
              if (!context.isAuth) {
                context.isAuth = true;
                handleNext();
              }
            }}
            ref={(ref) => { context.identityRef = ref; }}
          />
        );
        break;
      case stepType.payment:
        if (webPaymentOrder) {
          res = (
            <MoPay
              ref={(ref) => {
                context.moPayRef = ref;
              }}
              payOrder={webPaymentOrder}
              onPaySuccess={() => {
                if (!context.isPayed) {
                  context.isPayed = true;
                  handleNext();
                }
              }}
            />
          );
        } else {
          res = <PaymentInformation onSuccess={handlePaySuccess} onCancel={handlePayCancel} />;
        }
        break;
      case stepType.upload:
        res = (
          <UploadFile
            onInstance={(instance: FormInstance | null) => {
              context.uploadInstance = instance;
              if (!context.initUploadValue && instance) {
                context.initUploadValue = JSON.stringify(instance.getFieldsValue());
              }
            }}
          />
        );
        break;
      default:
        res = null;
    }
    return res;
  });

  return <>{renderStep()}</>;
};

export default StepRender;
