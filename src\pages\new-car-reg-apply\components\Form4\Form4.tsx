import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Form4.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Table } from 'antd';

const px = pc('form4');

export interface Form4Props extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;

  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setNextStepText: (text:string) => void;
}

const Form4 = (props: Form4Props) => {
  const { className = '', ...otherProps } = props;
  const { step, setDisablePrevStep, setDisableNextStep, nextStep, setNextStepText } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  useEffect(() => {
    setDisablePrevStep(false)
    setDisableNextStep(false)

    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }))
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }))
    }
  }, [])

  const columns = [
    {
      title: intl.formatMessage({ id: 'cost_details' }),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: intl.formatMessage({ id: 'amount' }),
      dataIndex: 'age',
      key: 'age',
      width: '100px'
    },
    {
      title: intl.formatMessage({ id: 'tax_payment' }),
      dataIndex: 'address',
      key: 'address',
      width: '100px'
    },
    {
      title: intl.formatMessage({ id: 'subtotal' }),
      key: 'tags',
      dataIndex: 'tags',
      width: '100px'
    },
  ];
  const data = [
    {
      key: '1',
      name: intl.formatMessage({ id: 'light_vehicles_register' }),
      age: '9,600',
      address: 1,
      tags: '9,600'
    },
    {
      key: '2',
      name: intl.formatMessage({ id: 'total' }),
      age: '',
      address: '澳門元',
      tags: '9,600'
    },
  ];

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <Table columns={columns} dataSource={data} pagination={false} />
      <div style={{height: '50px'}}></div>
      <div>
        <p className={px('formTitle')}>{intl.formatMessage({ id: 'payment_channels' })}</p>
        <div style={{paddingLeft: '30px',marginTop:"15px"}}>
          <Radio checked={true}><img style={{width:'150px'}} src="/ovsap/image/pay/GOVpay.png" alt="" /> </Radio>
        </div>
        <div style={{paddingLeft: '30px',marginTop:"5px"}}>
                  <Radio checked={false}><img style={{width:'150px'}} src="/ovsap/image/pay/pay2.gif" alt="" /> </Radio>
                </div>
      </div>
    </div>
  );
};

export default Form4;
