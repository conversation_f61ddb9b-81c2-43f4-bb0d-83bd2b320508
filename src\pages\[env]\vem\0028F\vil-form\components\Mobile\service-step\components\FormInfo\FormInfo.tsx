import React, { useEffect, useRef } from 'react';
import type { FormInstance } from 'antd';
import type { FormModelsState } from 'umi';
import { useSelector, useIntl } from 'umi';
import { MOBILE_CLASS_PREFIX, PC_CLASS_PREFIX } from '@/components/BaseForm/constant';
import { FormContext } from '@/components/BaseForm';
import cn from 'classnames';
import classPrefix from '@ali-whale/class-prefix';
import './FormInfo.less';
import PersonalForm from '../PersonalForm';
import ReceiveResult from '../ReceiveResult';

export interface FormInfoPopps extends React.HTMLAttributes<HTMLDivElement> {
  isDetail?: boolean;
  canGoBack?: boolean;
  setCanGoback?: (v: boolean) => void;
  title?: string;
  onInstance?: (instance: FormInstance | null, receiveResultInstance: FormInstance | null) => void;
  mobile?: boolean;
}

const FormInfo = (props: FormInfoPopps) => {
  const {
    isDetail = false,
    className = '',
    canGoBack,
    setCanGoback,
    mobile = true,
    title,
    onInstance,
    ...otherProps
  } = props;
  const { formData } = useSelector(({ formModels }: { formModels: FormModelsState }) => formModels);

  const intl = useIntl();

  const _this = useRef<{
    formRenderInstance: FormInstance | null;
    receiveResultInstance: FormInstance | null;
  }>({
    formRenderInstance: null,
    receiveResultInstance: null,
  }).current;

  const focusin = () => {
    document.documentElement.scrollBy(0, -1);
    document.body.scrollBy(0, -1);
  };

  const focusout = () => {
    // 软键盘关闭事件
    document.documentElement.scrollBy(0, 1);
    document.body.scrollBy(0, 1);
  };

  // 修复ios滚动时光标停留原地导致错位的问题
  const fixCursor = () => {
    if (document.activeElement) {
      document.activeElement.blur();
    }
  };

  const addEvent = () => {
    const ua = window.navigator.userAgent.toLocaleLowerCase();
    const isIOS = /iphone|ipad|ipod/.test(ua);
    if (isIOS) {
      document.body.addEventListener('focusin', focusin);
      document.body.addEventListener('focusout', focusout);
      document.body.addEventListener('touchmove', fixCursor, { passive: false });
      return () => {
        document.body.removeEventListener('focusin', focusin);
        document.body.removeEventListener('focusout', focusout);
        document.body.removeEventListener('touchmove', fixCursor);
      };
    }
    return null;
  };

  useEffect(() => {
    addEvent();
    // 详情数据回填
    if (_this.formRenderInstance && Object.keys(formData).length > 0) {
      _this.formRenderInstance.setFieldsValue(formData);
    }
  }, []);

  useEffect(() => {
    if (onInstance) {
      onInstance(_this.formRenderInstance, _this.receiveResultInstance);
    }
    return () => {
      if (onInstance) {
        onInstance(null, null);
      }
    };
  }, []);

  const px = mobile
    ? classPrefix(`${MOBILE_CLASS_PREFIX}-wrapper`)
    : classPrefix(`${PC_CLASS_PREFIX}-wrapper`);

  return (
    <div className={cn(px('root'), className, { [px('detailRoot')]: isDetail })} {...otherProps}>
      {/* {title && <div className={styles.title}>{title}</div>} */}
      {isDetail && <div className={px('title')}>{intl.formatMessage({ id: '申请资料' })}</div>}
      <div className={px('content')}>
        <FormContext.Provider value={{ isDetail, mobile }}>
          <PersonalForm
            // canGoBack={canGoBack}
            // onCanGoBackChange={setCanGoback}
            isDetail={isDetail}
            mobile={mobile}
            onInstance={(instance: FormInstance | null) => {
              _this.formRenderInstance = instance;
            }}
            values={Object.keys(formData).length > 0 ? formData : undefined}
            // formUtils={{
            //   addGoBackListener(fn: () => void) {
            //     document.addEventListener('back', fn, false);
            //     return () => {
            //       document.removeEventListener('back', fn);
            //     };
            //   },
            // }}
          />
          {/* <div className={px('divider')} /> */}
          <ReceiveResult
            // title=""
            isDetail={isDetail}
            mobile={mobile}
            onInstance={(instance) => {
              _this.receiveResultInstance = instance;
            }}
          />
        </FormContext.Provider>
      </div>
    </div>
  );
};

export default FormInfo;
