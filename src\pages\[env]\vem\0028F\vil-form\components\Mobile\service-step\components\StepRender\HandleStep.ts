import { useContext } from 'react';
import { Pay } from '@gov-mo/mpaas-js-bridge';
import type { PayConfig } from '@gov-mo/mpaas-js-bridge/es/module/pay';
import { Toast } from 'antd-mobile';
import { usePersistFn } from 'ahooks';
import { useDispatch, useSelector, history, useIntl, getLocale } from 'umi';
import type {
  StepModelsState,
  FormModelsState,
  PaymentModelsState,
  UploadModelsState,
  receiveResultModelsState,
  ApplicantInformationsModelsState,
} from 'umi';
import { ServiceInfo } from '@/conf/ServiceConf';
import * as queryString from 'query-string';
import { isMobile } from '@/utils';
import { transform2HantFormat } from '@/locales/lang';
import { savePersonalCert, isPaySuccess } from '../../services/serviceStepApi';
import type { MaterialsListType, ReceiveResultType } from '../../types/defs';
import { stepType, modelsType } from '../../config';
import StepContext from './StepContext';
import { getParams } from '../../utils';

function useHandleStep(currentStep) {
  const context = useContext(StepContext);
  const dispatch = useDispatch();
  const intl = useIntl();

  const allModels = useSelector(
    ({
      stepModels,
      formModels,
      paymentModels,
      uploadModels,
      receiveResultModels,
      applicantInformationsModels,
    }: {
      stepModels: StepModelsState;
      formModels: FormModelsState;
      paymentModels: PaymentModelsState;
      uploadModels: UploadModelsState;
      receiveResultModels: receiveResultModelsState;
      applicantInformationsModels: ApplicantInformationsModelsState;
    }) => ({
      stepModels,
      formModels,
      paymentModels,
      uploadModels,
      receiveResultModels,
      applicantInformationsModels,
    }),
  );

  const {
    stepModels: { step, workNo, itemName },
    formModels: { formData },
    paymentModels: { commodityList, total, webPaymentOrder },
    receiveResultModels: { receiveResult, isShowOnly },
    applicantInformationsModels: { applicantInformations },
  } = allModels;

  const {
    location: { query = {} },
  } = history;
  const { itemCode = '' } = query as Record<string, string>;

  // 更新/初始化models
  const updataModels = usePersistFn((payload, type) => {
    dispatch({
      type: `${type}/update`,
      payload,
    });
  });

  const initModels = usePersistFn((models: modelsType[]) => {
    models.forEach((type) => {
      dispatch({ type: `${type}/init` });
    });
  });

  const Serivce = ServiceInfo.ServiceList.find((item) => item.itemCode === itemCode);
  const stepInfoList = Serivce?.stepInfo || [];
  const stepInfoToShowList = isShowOnly
    ? stepInfoList.filter((i) => i.stepType !== stepType.resultReceive)
    : [...stepInfoList];

  const handleSaveOrSubmit = usePersistFn<(status: 0 | 1) => Promise<boolean>>(async (status) => {
    if (status === 1) {
      initModels([
        modelsType.applicantInformationsModels,
        modelsType.formModels,
        modelsType.receiveResultModels,
        modelsType.upload,
      ]);
      history.push(
        queryString.stringifyUrl({
          url: '/complete-submission',
          query: {
            workNo,
            itemName,
          },
        }),
      );
      return true;
    }
    Toast.loading(intl.formatMessage({ id: '請稍候' }), 0);
    const curFormData: Record<string, unknown> = context.formRenderInstance
      ? context.formRenderInstance.getFieldsValue()
      : formData;
    const params = {
      ...getParams({ applicantInformations, receiveResult, formData: curFormData }),
      itemCode,
    };
    const res = await savePersonalCert(params).finally(() => {
      Toast.hide();
    });
    if (res.success) {
      // 保存办件编码，清空models以及草稿缓存
      updataModels(
        {
          step: 1,
          workNo: res.data.applyNo,
          forecastTime: res.data.deliveryTime,
        },
        modelsType.stepModels,
      );
    } else {
      Toast.fail(res.message);
    }
    return res.success;
  });

  const handleNext = () => {
    // 最后一步，提交，后续也需根据需求区分是否有提交不在最后一步的情况情况
    if (step === stepInfoToShowList.length) {
      handleSaveOrSubmit(1);
    } else {
      updataModels({ step: step + 1 }, modelsType.stepModels);
    }
  };

  const handleNextStep = usePersistFn(async () => {
    // 步骤为身份识别且未识别成功过，则调起身份识别
    if (currentStep.stepType === stepType.faceRecognition && !context.isAuth) {
      context.identityRef.handleAuth();
      return;
    } else if (
      currentStep.stepType === stepType.form ||
      currentStep.stepType === stepType.resultReceive
    ) {
      // 表单类步骤触发校验并保存
      const formValidateFields =
        context.formRenderInstance?.validateFields ??
        (() =>
          new Promise((resolve) => {
            resolve(false);
          }));
      const resultValidateFields =
        context.receiveResultInstance?.validateFields ??
        (() =>
          new Promise<{ receiveResult?: ReceiveResultType }>((resolve) => {
            resolve({});
          }));
      const [formValue, receiveResultValue] = await Promise.all([
        formValidateFields(),
        resultValidateFields(),
      ]);
      if (formValue) updataModels({ formData: formValue }, modelsType.formModels);
      if (receiveResultValue.receiveResult) {
        updataModels(
          { receiveResult: receiveResultValue.receiveResult },
          modelsType.receiveResultModels,
        );
      }
    } else if (currentStep.stepType === stepType.upload && context.uploadInstance?.validateFields) {
      const uploadValue = await context.uploadInstance.validateFields();
      const uploadList: MaterialsListType = [];
      Object.keys(uploadValue).forEach((key) => {
        uploadValue[key].forEach((file) => {
          uploadList.push(file);
        });
      });
      updataModels({ applicantMaterials: uploadList }, modelsType.upload);
    } else if (currentStep.stepType === stepType.payment && !webPaymentOrder && !context.isPayed) {
      const res = await isPaySuccess(commodityList.billId);
      if (!res.data) {
        const paymentOrder: PayConfig = {
          title: intl.formatMessage({ id: Serivce?.name }) ?? '',
          amount: total.toFixed(2),
          billId: commodityList.billId,
          cashierLanguage: transform2HantFormat(getLocale()),
          currency: commodityList.unit,
          terminalType: 'MOBILE',
          tarUrl: window.location.origin,
          // 各事项根据后端接口是否满足自行定制化修改
          orderServer: `${API_HOST}/payment/order`,
          stateServer: `${API_HOST}/payment/bill/success`,
          payChannels: ['BOCPAY', 'MPGS', 'BOCCARDPAY', 'MPAY'],
          // 部門自定義支付服務參數
          isProxyOrderServer: false,
          mpayRenderUrl: `${location.origin}/${window.routerBase.replace('/', '')}mpay.html`,
          redirectRelUri: `${window.routerBase.replace('/', '')}pay-passed`,
          channelServer: `${API_HOST}/pay-channel`,
        };
        if (isMobile()) {
          await Pay.start(paymentOrder, {
            path: `${location.origin}/${window.routerBase.replace('/', '')}pay-begin`,
          });
        } else {
          updataModels(
            {
              webPaymentOrder: {
                ...paymentOrder,
                terminalType: 'PC',
              },
            },
            modelsType.paymentModels,
          );
        }
      }
      return;
    } else if (currentStep.stepType === stepType.payment && webPaymentOrder && !context.isPayed) {
      context.moPayRef.goPay();
      return;
    } else if (currentStep.stepType === stepType.dataCheck) {
      const res: boolean = await handleSaveOrSubmit(0);
      if (!res) return;
    }
    handleNext();
  });

  return handleNextStep;
}

export default useHandleStep;
