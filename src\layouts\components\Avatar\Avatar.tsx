import React, { useState, useEffect } from 'react';
import { Dropdown, Menu } from 'antd';
import { history, useIntl, useDispatch } from 'umi';
import { CaretDownOutlined } from '@ant-design/icons';
import styles from './Avatar.less';

export interface UserInfoProps {
  username: string;
}

const Avatar = () => {
  const { location } = history;
  const dispatch = useDispatch();
  const [userInfo, setUserInfo] = useState<UserInfoProps>({ username: '' });
  const LoginPage = location.pathname === '/login';
  const intl = useIntl();

  useEffect(() => {
    setUserInfo({ username: '' });
  }, []);

  const logout = () => {
    dispatch({
      type: 'user/logout',
      payload: {
        accountType: 'Personal',
        clientType: 'pc',
        loginName: 'safpuser29',
        password: 'xVt0udWcvOq+BSSCoRYRJg==',
      },
    });
  };

  const menu = (
    <Menu>
      <Menu.Item key="logout" onClick={logout}>
        {intl.formatMessage({ id: '登出' })}
      </Menu.Item>
    </Menu>
  );

  return (
    <div className={styles.root}>
      {!userInfo.username ? (
        <div
          className={styles.login}
          onClick={() => {
            if (!LoginPage) {
              history.push('/login');
            }
          }}
        >
          {intl.formatMessage({ id: '登入' })}
        </div>
      ) : (
        <Dropdown overlay={menu}>
          <div className={`${styles.avatar}`}>
            {userInfo.username}
            <CaretDownOutlined />
          </div>
        </Dropdown>
      )}
    </div>
  );
};

export default Avatar;
