.cbd-mobile-base-form-wrapper {
  &-root {
    border-radius: @radius-md;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.02);
    font-size: 36px;
    color: #262626;
  }
  &-title {
    padding: 32px;
    // font-weight: bold;
    // margin-bottom: 32px;
    .cardTitleMixins(@color2);
    color: var(--firstTextColor);
  }
  &-detailRoot {
    margin: 0 16 * @hd;
    .cbd-mobile-base-form-wrapper-content {
      background: var(--secondBgColor);
    }
  }
  &-divider {
    height: 12 * @hd;
    border-bottom: 1px solid var(--borderColor);
    margin: 0 16 * @hd;
  }
  &-content {
    background: var(--firstBgColor);
    border-bottom-left-radius: @radius-md;
    border-bottom-right-radius: @radius-md;
    :global {
      .cbd-mobile-base-form-root {
        margin: 0;
        .ant-form-item:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.cbd-web-base-form-wrapper {
  &-root {
    border-radius: 0;
    box-shadow: none;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    padding-bottom: 68px;
  }
  // &-title {
  //   border-bottom: 0;
  //   background: transparent;
  //   display: flex;
  //   align-items: center;
  //   font-weight: bold;
  //   margin-left: 24px;
  //   &:before {
  //     display: block;
  //     content: '';
  //     width: 4px;
  //     height: 16px;
  //     background: #008378;
  //     margin-right: 8px;
  //   }
  // }
  &-detailRoot {
    .cbd-web-base-form-wrapper-content {
      border: 1px solid rgba(0, 0, 0, 0.1);
      margin-left: 20px;
      margin-right: 32px;
    }
    .cbd-web-base-form-wrapper-title {
      border: 1px solid rgba(0, 0, 0, 0.1);
      // border-bottom: none;
      background: rgba(239, 243, 245, 0.76);
      padding: 16px;
      font-weight: bold;
      margin-left: 20px;
      margin-right: 32px;
    }
  }
}
