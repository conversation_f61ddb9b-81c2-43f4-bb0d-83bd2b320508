import React, { useEffect, useState, useContext } from 'react';
import { DatePicker } from 'antd-mobile';
import { ClockCircleOutlined } from '@ant-design/icons';
import classPrefix from '@ali-whale/class-prefix';
import cn from 'classnames';
import moment from 'moment';
import FormContext from '../FormContext';
import './TimeRangeMobile.less';

const px = classPrefix('cbd-mobile-base-form-timerange');

const TimeRangeMobile = (props) => {
  const { isDetail } = useContext(FormContext);
  const [startTime, setStartTime] = useState();
  const [endTime, setEndTime] = useState();

  const {
    onChange,
    value,
    placeholder = ['开始时间', '结束时间'],
    format = 'HH:mm',
    minTime = '08:00',
    maxTime = '22:59',
    gapMinutes = 0,
  } = props;

  const getItemContent = (time, defaultText) => time || defaultText;

  const getTimeVal = (time) => {
    const [hour, minute] = time.split(':');
    return { hour, minute };
  };

  useEffect(() => {
    if (value) {
      const [start, end] = value;
      setStartTime(start);
      setEndTime(end);
    }
  }, [value]);

  const minDate = moment(getTimeVal(minTime)).toDate();
  const maxDate = moment(getTimeVal(maxTime)).toDate();

  const TimeInput = ({ onClick, children, value: val }) => (
    <div className={cn(px('item'), { [px('item-placeholder')]: !val })} onClick={onClick}>
      {children}
      <span style={{ float: 'right', color: '#888' }}>
        <ClockCircleOutlined />
      </span>
    </div>
  );

  if (isDetail) {
    if (!Array.isArray(value)) return null;
    const [_startTime = '', _endTime = ''] = value;
    return <div>{`${_startTime}-${_endTime}`}</div>;
  }

  return (
    <div className={px('root')}>
      <div className={px('time')}>
        <div style={{ width: '100%' }}>
          <DatePicker
            mode="time"
            minDate={minDate}
            maxDate={maxDate}
            onChange={(time) => {
              const timeVal = moment(time).format(format);
              setStartTime(timeVal);
              onChange([timeVal, endTime]);
            }}
          >
            <TimeInput value={startTime}>{getItemContent(startTime, placeholder[0])}</TimeInput>
          </DatePicker>
        </div>
        <span className={px('gap')}>-</span>
        <div style={{ width: '100%' }}>
          <DatePicker
            mode="time"
            minDate={moment(getTimeVal(startTime || '08:00'))
              .add(gapMinutes, 'minutes')
              .toDate()}
            maxDate={maxDate}
            onChange={(time) => {
              const timeVal = moment(time).format(format);
              setEndTime(timeVal);
              onChange([startTime, timeVal]);
            }}
          >
            <TimeInput value={endTime}>{getItemContent(endTime, placeholder[1])}</TimeInput>
          </DatePicker>
        </div>
      </div>
    </div>
  );
};

export default TimeRangeMobile;
