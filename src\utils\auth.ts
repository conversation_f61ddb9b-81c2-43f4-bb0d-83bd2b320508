import * as queryString from 'query-string';
import { isMobile } from '.';
import crypto from 'crypto';
import { getStorageObject, removeStorage, setStorageObject } from './storage';

export const ACCOUNT_TYPE = PLATFORM === 'business' ? 'Corp-Entity' : 'Personal'
export const ORIGIN_PLAT_FORM = PLATFORM === 'business' ? 'association-business' : 'one-account'
export const REDIRECT_SERVER = AUTH_SERVER;
export const REDIRECT_CLIENT = CLIENT_ID;

export const AUTH_VERSION_KEY = 'auth_verison_key';
export const AUTHORIZE_TOKEN_KEY = 'authorize_token_key'
export const CODE_VERSION = 'code_version';

let authVersion = getAuthVersion() || {} as AuthVersion;

export const redirectToYiWang = (): boolean => {
  if (isMobile()) return true;
  const url = `${AUTH_SERVER}/#/service/service-authorize?redirectUri=${encodeURIComponent(
    window.location.href,
  )}&state=true`;

  const query = queryString.parse(window.location.search);
  if (query.code && !query.state) {
    window.location.href = url;
    return false;
  }
  return true;
};

export function generateRandomString(length) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let result = '';
  const randomValues = crypto.randomBytes(length);
  for (let i = 0; i < length; i++) {
      result += charset[randomValues[i] % charset.length];
  }
  return result;
}

export function generateCodeVerifier(length: number, version?: string) {
  if (!version) {
    return generateRandomString(length);
  }

  if (authVersion?.version === version) {
    return authVersion?.codeVerifier || '';
  }

  return generateRandomString(length);
}

export function base64UrlEncode(buffer) {
  return buffer.toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');
}

export function generateCodeChallenge(codeVerifier: string, version?: string) {
  if (!version) {
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    return base64UrlEncode(hash);
  }

  if (authVersion?.version === version) {
    return authVersion?.codeChallenge || '';
  }

  const hash = crypto.createHash('sha256').update(codeVerifier).digest();
  return base64UrlEncode(hash);
}

export function setAuthVersion(codeVerifier: string, codeChallenge: string, version: string) {
  authVersion = {
    codeVerifier,
    codeChallenge,
    version: version
  }

  setStorageObject(AUTH_VERSION_KEY, authVersion)
}

export function getAuthVersion() {
  return getStorageObject<AuthVersion>(AUTH_VERSION_KEY);
}

export function removeAuthVersion() {
  removeStorage(AUTH_VERSION_KEY);
}

export function generateRedirectServerUrl(redirect_uri: string, codeVerifier: string, codeChallenge: string, version: string) {
  setAuthVersion(codeVerifier, codeChallenge, version);

  return `${REDIRECT_SERVER}?appId=${APP_ID}&client_id=${REDIRECT_CLIENT}&grant_type=code&redirect_uri=${encodeURIComponent(
    redirect_uri,
  )}&response_type=code&scope=profile&code_challenge=${codeChallenge}&code_challenge_method=S256`
}