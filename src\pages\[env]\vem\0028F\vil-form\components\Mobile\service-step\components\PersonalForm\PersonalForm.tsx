import React, { useEffect } from 'react';
import { LayoutForm as Form, Select, CheckBoxGroup, Text } from '@/components/BaseForm';
import { useIntl, useSelector, useDispatch } from 'umi';
import type { ApplicantInformationsModelsState } from 'umi';
import type { FormInstance } from 'antd';
import type { PersonalFormType } from '../../types/defs';
import { getUsageList } from './services';
import type { PersonalFormModelsState } from './models/personalFormModels';

import styles from './PersonalForm.less';
import Upload from '../Upload';

export interface PersonalFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDetail?: boolean;
  onInstance?: (instance: FormInstance | null) => void;
  mobile?: boolean;
  values?: PersonalFormType;
}

export interface FormValues {
  useCode: { label: string; value: string }[];
  items: number[];
  otherUse?: string;
  // englishTranslation: number;
}

const PersonalForm = (props: PersonalFormProps) => {
  const { onInstance, isDetail = false, values, mobile = true } = props;
  const [form] = Form.useForm();
  // const [usageDict, setUsageDict] = useState<Record<string, string>[]>([]);
  // const [projectDict, setProjectDict] = useState<Record<string, string | boolean>[]>([]);
  const {
    // applicantInformationsModels: { applicantInformations },
    personalFormModels: { projectDict, usageDict },
  } = useSelector(
    ({
      applicantInformationsModels,
      personalFormModels,
    }: {
      applicantInformationsModels: ApplicantInformationsModelsState;
      personalFormModels: PersonalFormModelsState;
    }) => ({ applicantInformationsModels, personalFormModels }),
  );
  const dispatch = useDispatch();

  const getDict = () => {
    if (usageDict.length === 0) {
      getUsageList().then((res) => {
        if (res.success && res.data) {
          const data = res.data as { applyUses: Record<string, string>[] };
          const usageDict = data.applyUses.map(({ useCode, useName }) => ({
            label: useName,
            value: useCode,
          }));
          dispatch({ type: 'personalFormModels/update', payload: { usageDict } });
        }
      });
    }
  };

  useEffect(() => {
    getDict();
    onInstance?.({
      ...form,
      validateFields: () =>
        form.validateFields().then((vals: FormValues) => ({
          ...vals,
          useCode: vals.useCode[0]?.value,
          items: vals.items.join(','),
        })),
      setFieldsValue: (vals: PersonalFormType) => {
        form.setFieldsValue({
          ...vals,
          useCode: vals.useCode ? [vals.useCode] : undefined,
          items: vals.items.split(','),
        });
      },
    });
    form.setFieldsValue({
      ...values,
      useCode: values?.useCode ? [values.useCode] : undefined,
    });
  }, []);
  const intl = useIntl();
  // const renderOptsIntl = (opts: { label: string; value: number | string }[]) =>
  //   opts.map(i => ({ ...i, label: intl.formatMessage({ id: i.label }) }));
  if (isDetail) {
    return (
      <div className={`${mobile ? styles.detailRoot : styles.webDetailRoot}`}>
        <div role="text">
          <span>{intl.formatMessage({ id: '文件上传' })}</span>
          <span>
            <Upload id="com0" value={values?.uploadFiles} isDetail />
          </span>
        </div>
        <div role="text">
          <span>{intl.formatMessage({ id: '用途' })}</span>
          <span>{usageDict.find((i) => i.value === values?.useCode)?.label}</span>
        </div>
        {values?.useCode === '99' && (
          <div role="text">
            <span>{intl.formatMessage({ id: '其他用途' })}</span>
            <span>{values.otherUse}</span>
          </div>
        )}
        <div role="text">
          <span>{intl.formatMessage({ id: '證明書需要登載的項目' })}</span>
          <div className={styles.prewrapDiv}>
            {values?.items
              .split(',')
              .map((item) => projectDict.find((i) => i.value === item)?.label)
              .filter((i) => i)
              .join('\n')}
          </div>
        </div>
      </div>
    );
  }
  return (
    <>
      {mobile && <div className={styles.baseTitleContainer} />}
      <Form form={form} className={styles.root}>
        <Form.Item label={intl.formatMessage({ id: '文件上传' })} name="uploadFiles">
          <Upload id="com0" />
        </Form.Item>
        <Form.Item
          label={intl.formatMessage({ id: '用途' })}
          required
          rules={[{ required: true, message: intl.formatMessage({ id: '請選擇' }) }]}
          name="useCode"
        >
          <Select options={usageDict} placeholder={intl.formatMessage({ id: '請選擇' })} />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev: FormValues, cur: FormValues) => prev.useCode !== cur.useCode}
        >
          {({ getFieldValue }) =>
            getFieldValue('useCode')?.[0]?.value === '99' ? (
              <Form.Item
                name="otherUse"
                label={intl.formatMessage({ id: '其他用途' })}
                required
                rules={[{ required: true, message: intl.formatMessage({ id: '請輸入' }) }]}
              >
                <Text placeholder={intl.formatMessage({ id: '請輸入' })} maxLength={256} />
              </Form.Item>
            ) : null
          }
        </Form.Item>
        <Form.Item
          label={intl.formatMessage({ id: '證明書需要登載的項目（可多選）' })}
          required
          rules={[{ required: true }]}
          name="items"
          initialValue={['1']}
        >
          <CheckBoxGroup multiple options={projectDict} layout="vertical" />
        </Form.Item>
      </Form>
    </>
  );
};

export default PersonalForm;
