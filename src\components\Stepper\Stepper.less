.root {
  width: 252px;
  height: 64px;
  line-height: 64px;
  text-align: center;
  display: flex;
  transform: scale(0.8);
  transform-origin: left;
  align-items: center;
  .left,
  .right {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 64px;
    width: 64px;
    border-radius: 50%;
    border: 2px solid @brand-success;
    background-color: var(--secondBgColor);
    color: @brand-success;
    .minus {
      width: 36px;
      height: 6px;
      background-color: @brand-success;
    }
  }
  .right {
    background-color: @brand-success;
    color: #fff;
    .line,
    .line:after {
      width: 32px;
      height: 6px;
      background-color: #fff;
    }
    .line:after {
      clear: both;
      content: '';
      display: block;
      transform: rotate(90deg);
    }
  }
  .disabled {
    opacity: 0.5;
  }
  .input {
    text-align: center;
    display: inline-block;
    width: 124px;
    // height: 52px;
    border-radius: 6px;
    // border: 2px solid rgba(240, 240, 240, 1);
    background-color: transparent;
    color: var(--firstTextColor);
    font-size: 28 * @hd;
  }
}

.webRoot {
  transform: scale(1);
  height: 24px;
  line-height: 24px;
  .left,
  .right {
    height: 24px;
    width: 24px;
    border: 1px solid @brand-success;
    .minus {
      width: 14px;
      height: 2px;
    }
  }
  .right {
    .line,
    .line:after {
      width: 14px;
      height: 2px;
    }
    .line:after {
      clear: both;
      content: '';
      display: block;
      transform: rotate(90deg);
    }
  }
  .input {
    font-size: 18px;
    width: 40px;
  }
}
