import React from 'react';
import { Popover } from 'antd-mobile';
import { useIntl } from 'react-intl';
// import { useDispatch } from 'react-redux';
import styles from './StepsPopover.less';

export interface StepsPopoverProps {
  current: number;
  stepList: string[];
  maxStep: number;
}

function StepsPopover(props: StepsPopoverProps) {
  const { current, stepList /* maxStep */ } = props;
  const intl = useIntl();
  // const dispatch = useDispatch();

  // const handleStepChange = (target: number) => {
  //   if (target > maxStep) {
  //     Toast.info(intl.formatMessage({ id: '步骤未完成' }, { step: maxStep }));
  //     return;
  //   }
  //   dispatch({
  //     type: 'stepModels/update',
  //     payload: { step: target },
  //   });
  // };
  const getAriaLabel = (index, val) => {
    if (index + 1 < current) {
      return `步骤${index + 1},${val && intl.formatMessage({ id: val })}, 已完成`;
    } else if (index + 1 === current) {
      return `步骤${index + 1},${val && intl.formatMessage({ id: val })}, 进行中`;
    }
    return `步骤${index + 1},${val && intl.formatMessage({ id: val })}, 未完成`;
  };

  return (
    <Popover
      overlay={(
        <div className={styles.root}>
          {stepList.map((item, index) => (
            <>
              <div
                role="text"
                aria-label={getAriaLabel(index, item)}
                className={`${styles.stepItem} ${
                  index + 1 < current ? styles.stepItemActive : ''
                } ${index + 1 === current ? styles.stepItemFocus : ''}`}
                // onClick={() => {
                //   handleStepChange(index + 1);
                // }}
              >
                <div className={styles.stepNumber}>{index + 1}</div>
                <div
                  className={`${styles.content} ${index + 1 < current ? styles.before : ''} ${
                    index + 1 === current ? styles.active : ''
                  }`}
                >
                  <div className={styles.twoRowEllipsis}>
                    {item && intl.formatMessage({ id: item })}
                  </div>
                </div>
              </div>
              {index + 1 < stepList.length && (
                <div className={`${styles.tail} ${index + 1 < current ? styles.tailActive : ''}`} />
              )}
            </>
          ))}
        </div>
      )}
    >
      <div role="button" className={styles.popoverTitle}>
        {intl.formatMessage({ id: '申请步骤' })}
      </div>
    </Popover>
  );
}

export default StepsPopover;
