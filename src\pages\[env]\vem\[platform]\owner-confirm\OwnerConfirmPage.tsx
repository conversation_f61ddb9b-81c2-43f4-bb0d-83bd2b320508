import pc from 'prefix-classnames';
import React, { useEffect, useState } from 'react';
import { getLocale, history, Location, useIntl, useLocation } from 'umi';

import FooterButton from '@/components/FooterButton';
import GuideButton from '@/components/GuideButton';

import { getEXTxnData } from '@/services/0028F';
import { checkServiceAvailable, getServiceTitle } from '@/services/publicApi';
import { getLoginUserInfo, identityAuthorize } from '@/services/UserApi';
import { FetchResponse, post } from '@/utils/fetch';
import { Modal } from 'antd';
import FormBase from './components/FormBase';
import FormBaseMobile from './components/Mobile/FormBase';
import SuccessMobile from './components/Mobile/Success';
import Success from './components/Success';

import { getLangGroup, getResponseMessage, GovLang, Lang } from '@/locales/lang';
import { isMobile } from '@/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Page } from '@gov-mo/mpaas-js-bridge';

const classPrefix = isMobile() ? 'owner-confirm-page-mobile' : 'owner-confirm-page';
const px = pc(classPrefix);

type OwnerConfirmQuery = {
  txnId: string;
  faceAuth?: string;
  code?: string;
};

type Props = {} & BaseProps;

const OwnerConfirmPage = (props: Props) => {
  const ComFormBase = isMobile() ? FormBaseMobile : FormBase;

  const intl = useIntl();
  const isPT = intl.locale === 'pt-PT';
  const [txnId, setTxnId] = useState('');
  const [ownerId, setOwnerId] = useState('');
  const [serviceTitle, setServiceTitle] = useState('');
  const [detail, setDetail] = useState<getEXTxnDataRes>({} as getEXTxnDataRes);
  const [userProfile, setUserProfile] = useState<UserProfile>({} as UserProfile);

  // 有多個車主資料，用這個狀態顯示目前車主臉辨識狀態，這個狀態也會在人臉辨識之後用 setVerficationStatus 刷新，所以要使用這個狀態
  const [verficationStatus, setVerficationStatus] = useState<string>('');
  const [currentOwner, setCurrentOwner] = useState<saveTxnOwnersParams>();
  const [tipMsg, setTipMsg] = useState('');

  const location = useLocation() as Location & OwnerConfirmQuery;

  const [step, setStep] = useState(3);
  const [nextStepText, setNextStepText] = useState(intl.formatMessage({ id: 'next' }));

  const [showPrevStep, setShowPrevStep] = useState(false);
  const [showNextStep, setShowNextStep] = useState(false);
  const [showClose, setShowClose] = useState(true);
  const [showTempStore, setShowTempStore] = useState(false);
  const [showConfirmApply, setShowConfirmApply] = useState(false);

  const [disablePrevStep, setDisablePrevStep] = useState(true);
  const [disableNextStep, setDisableNextStep] = useState(false);
  const [disableClose, setDisableClose] = useState(false);
  const [disableConfirmApply, setDisableConfirmApply] = useState(true);
  const [disableTempStore, setDisableTempStore] = useState(false);

  const [checkServiceModal, checkServiceContextHolder] = Modal.useModal();

  const baseUrl =
    window.location.hostname === 'localhost'
      ? 'https://appdev.dsat.gov.mo'
      : window.location.origin;

  const prevStep = () => {
    if (step <= 1) {
      isMobile() && Page.close();
      return;
    }

    setStep(step - 1);
  };

  const nextStep = () => {
    setStep(step + 1);
  };

  const handleClose = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const handleTempStore = () => {};

  useEffect(() => {
    setDisablePrevStep(step <= 1);
  }, [step]);

  function getData() {
    getEXTxnData(location.query?.txnId as string).then(async (res) => {
      // console.log('getEXTxnData', res);
      if (res?.code === '401') {
        getData();
        return;
      }

      if (res?.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }

      setTipMsg('');

      // 獲取用戶數據
      const user = await getLoginUserInfo();
      const currentOwner = (res.data.ownerDataList || []).find(
        (item) => item.ownerIdentNo === user.identityNo,
      );

      setCurrentOwner(currentOwner);

      // 設置當前車主人臉識別狀態
      setVerficationStatus(currentOwner?.verficationStatus || '');

      setDetail(res.data || {});
      setUserProfile(user);

      !res?.data?.dsiMsgCodeDTO && setDisableConfirmApply(false);
    });
  }

  function isSuccessPage() {
    return location.search?.includes('page=success');
  }

  useEffect(() => {
    checkServiceAvailable('VEM').then((res) => {
      console.log('checkServiceAvailable', res);

      if (!res || !res.data) {
        init();
        return;
      }

      if (res?.data?.available === false) {
        history.push('/web/error');
        return;
      }

      if (isSuccessPage()) {
        init();
        return;
      }

      init();
      if (isMobile()) {
        Modal.warning({
          icon: null,
          centered: true,
          className: 'system-maintenance-mobile',
          title: intl.formatMessage({ id: 'warm_reminder' }),
          content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
          okText: intl.formatMessage({ id: 'confirm' }),
        });
        return;
      }
      checkServiceModal.warning({
        title: intl.formatMessage({ id: 'warm_reminder' }),
        icon: <ExclamationCircleOutlined />,
        content: getLangGroup(res?.data?.msCn, res?.data?.msPt, res?.data?.msEn),
        okText: intl.formatMessage({ id: 'confirm' }),
      });
    });
  }, []);

  function init() {
    const txnId = location.query?.txnId as string;
    const ownerId = location.query?.ownerId as string;

    getServiceTitle('0028F').then((res) => {
      setServiceTitle(
        getLangGroup(
          res?.data?.serviceTitleCn,
          res?.data?.serviceTitlePt,
          res?.data?.serviceTitleEn,
        ),
      );
    });

    if (txnId) {
      setTxnId(txnId);
    }
    if (ownerId) {
      setOwnerId(ownerId);
    }

    if (!!location.query?.faceAuth && !!location.query.code) {
      checkFaceAuth(txnId, ownerId);
      return;
    }

    // 獲取詳情數據
    getData();
  }

  async function handleFaceAuth(auth = false) {
    if (!auth) {
      return;
    }

    const currentOwner = getCurrentOwner();
    if (!currentOwner) {
      return;
    }

    const language = getLocale() === Lang.葡语 ? GovLang.葡语 : GovLang.繁体中文;

    // ${window.location.origin}${window.location.pathname}
    const res = await identityAuthorize({
      txnId,
      isShowHeader: true,
      ownerId: currentOwner.txnOwnerId,
      language: getLocale() || 'zh-MO',
      reason: '0028F',
      redirectUri: decodeURIComponent(
        `${baseUrl}/ovsap/web/vem/personal/owner-confirm?page=success&txnId=${txnId}&ownerId=${currentOwner.txnOwnerId}&language=${language}&faceAuth=1`,
      ),
      lastModifyDate: detail?.lastModifyDate,
    });

    res?.data && (window.location.href = res.data);
  }

  async function checkFaceAuth(txnId: string, ownerId: string) {
    const language = getLocale() === Lang.葡语 ? GovLang.葡语 : GovLang.繁体中文;

    const res = await post<FetchResponse<string>>('/oa/face/checkFaceRecognition', {
      code: location.query?.code as string,
      ownerId,
      verficationStatus: 'Y',
      redirectUri: decodeURIComponent(
        `${baseUrl}/ovsap/web/vem/personal/owner-confirm?page=success&txnId=${txnId}&ownerId=${ownerId}&language=${language}&faceAuth=1`,
      ),
    });

    if (res?.code === '401') {
      checkFaceAuth(txnId, ownerId);
      return;
    }

    if (res?.code !== '0') {
      setTipMsg(getResponseMessage(res));
      return;
    }

    setTipMsg('');
    if (res.code === '0') {
      setVerficationStatus('Y');
    }

    getData();

    return res;
  }

  // 通過這個方法可以獲得當前車主資料
  function getCurrentOwner() {
    const currentUser = (detail.ownerDataList || []).find(
      (item) => item.ownerIdentNo === userProfile.identityNo,
    );
    if (!currentUser) {
      return undefined;
    }

    return currentUser;
  }

  if (isSuccessPage()) {
    return isMobile() ? (
      <SuccessMobile
        txnId={txnId}
        detail={detail}
        serviceTitle={serviceTitle}
        currentOwner={currentOwner}
      />
    ) : (
      <Success
        txnId={txnId}
        detail={detail}
        serviceTitle={serviceTitle}
        currentOwner={currentOwner}
      />
    );
  }

  return (
    <>
      <ComFormBase
        step={step}
        classPrefix={classPrefix}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
          handleFaceAuth(true);
        }}
        setDisableConfirmApply={(flag) => {
          setDisableConfirmApply(flag);
        }}
        nextStep={nextStep}
        setNextStepText={(text) => {
          setNextStepText(text);
        }}
        setShowTempStore={setShowTempStore}
        setShowConfirmApply={setShowConfirmApply}
        serviceTitle={serviceTitle}
        ownerId={ownerId}
        detail={detail}
        txnId={txnId}
        verficationStatus={verficationStatus}
        currentOwner={currentOwner}
        tipMsg={tipMsg}
        handlePrevStep={() => {
          prevStep();
        }}
      />
      <GuideButton itemId="" />
      <FooterButton
        // handleClose={handleClose}
        className={isPT ? `${px('footer-button')} ${px('pt-footer-button')}` : px('footer-button')}
        step={step}
        stepType="progress"
        nextStepText={nextStepText}
        showPrevStep={showPrevStep}
        showNextStep={showNextStep}
        showClose={showClose}
        showTempStore={showTempStore}
        showConfirmApply={showConfirmApply}
        setShowPrevStep={() => {
          setShowPrevStep;
        }}
        setShowNextStep={() => {
          setShowNextStep;
        }}
        setShowClose={() => {
          setShowClose;
        }}
        setShowTempStore={() => {
          setShowTempStore;
        }}
        disablePrevStep={disablePrevStep}
        disableNextStep={disableNextStep}
        disableClose={disableClose}
        disableConfirmApply={disableConfirmApply}
        disableTempStore={disableTempStore}
        setDisablePrevStep={(flag) => {
          setDisablePrevStep(flag);
        }}
        setDisableNextStep={(flag) => {
          setDisableNextStep(flag);
        }}
        setDisableClose={() => {
          setDisableClose;
        }}
        setDisableConfirmApply={(flag) => {
          setDisableConfirmApply(flag);
        }}
        setDisableTempStore={() => {
          setDisableTempStore;
        }}
        handlePrevStep={() => {
          prevStep();
        }}
        handleNextStep={() => {
          nextStep();
        }}
        handleClose={() => {
          handleClose();
        }}
        handleTempStore={() => {
          handleTempStore();
        }}
        handleConfirmApply={() => {
          setDisableNextStep(true);
          handleFaceAuth(true);
        }}
      />

      {!isMobile() && checkServiceContextHolder}
    </>
  );
};

export default OwnerConfirmPage;
