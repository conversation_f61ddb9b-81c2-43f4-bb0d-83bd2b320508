import React, { useState, useEffect } from 'react';
import { history, Location, useIntl, useLocation } from 'umi';
import styles from './VemPage.module.less';
import { NavBar } from 'antd-mobile';
import { LeftOutlined } from '@ant-design/icons';
import ListTwo from './components/ListTwo';
import ListThree from './components/ListThree';
// import { PROPS_MATCH_PARAMS_APP } from '@/utils/const';
import { Page } from '@gov-mo/mpaas-js-bridge';
import { isMobile } from '@/utils';
import { getStatusBarHeight } from '@/utils/hooks';

declare type Props = {} & BaseProps;

type VemLocation = {
  query: {};
} & Location;

const VemPage = (props: Props) => {
  const [step, setStep] = useState(2);
  const location = useLocation() as VemLocation;
  const intl = useIntl();

  useEffect(() => {
    let query = location?.query;
    if (isMobile()) {
      query = { ...query, hideNavigationBar: true } as any;
    }

    history.replace({
      pathname: location?.pathname,
      query: query,
    });
  }, []);

  function handleNavTo(url: string | number) {
    process.env.NODE_ENV === 'production' && !isMobile()
      ? window.open(`/ovsap${url}`, '_blank')
      : history.push(url as string);
  }
  useEffect(() => {
    if (window.location.search && window.location.search == '?tabIndex=2') {
      setStep(2);
    } else if (window.location.search && window.location.search == '?tabIndex=3') {
      setStep(3);
    }
  });

  const handleClose = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }
    process.env.NODE_ENV === 'production' ? window.close() : history.goBack();
  };

  return (
    <div className={`${styles.root}`}>
      {isMobile() ? (
        <NavBar
          style={{ paddingTop: getStatusBarHeight() }}
          mode="light"
          leftContent={<LeftOutlined onClick={handleClose} style={{ fontSize: '0.16rem' }} />}
          rightContent={
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}></div>
          }
        >
          {/* title */}
          <span style={{ color: '#333', fontSize: '0.16rem' }}>
            {intl.formatMessage({ id: 'new_car_registration' })}
          </span>
        </NavBar>
      ) : null}
      {
        {
          2: <ListTwo onGoto={handleNavTo} />,
          3: <ListThree onGoto={handleNavTo} />,
        }[step]
      }
    </div>
  );
};

export default VemPage;
