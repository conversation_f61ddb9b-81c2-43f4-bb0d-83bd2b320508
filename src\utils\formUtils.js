import scrollIntoView from 'scroll-into-view-if-needed';

/**
 * docs:
 * https://scroll-into-view-if-needed.netlify.app/
 * https://github.com/ant-design/ant-design/blob/47dfa1f2ca81df1d1791b4f58ff4690800493be8/components/form/hooks/useForm.ts#L42
 */
function getFieldId(namePath, formName) {
  if (!namePath.length) return undefined;

  const mergedId = Array.isArray(namePath) ? namePath.join('_') : namePath;
  return formName ? `${formName}_${mergedId}` : mergedId;
}

function scrollToField(namePath, formInstance, options) {
  const name = getFieldId(namePath, formInstance?.__INTERNAL__?.name);
  const node = name ? document.getElementById(name) : null;

  if (node) {
    scrollIntoView(node, {
      scrollMode: 'if-needed',
      block: 'center',
      //   inline: 'center',
      //   boundary: document.getElementById('example-boundary'),
      ...options,
    });
  } else {
    window.console.log(`[${name}] 找不到表单组件的id`);
  }
}

function scrollToFirstError(validFn, formInstance, options) {
  return new Promise((resolve, reject) => {
    validFn()
      .then((res) => {
        resolve(res);
      })
      .catch((errs) => {
        const namePath = errs?.errorFields?.[0]?.name;
        window.console.log('namePath', namePath);

        if (namePath) {
          scrollToField(namePath, formInstance, options);
        }
        reject(errs);
      });
  });
}

export default scrollToFirstError;
export { scrollToField, getFieldId, scrollToFirstError };
