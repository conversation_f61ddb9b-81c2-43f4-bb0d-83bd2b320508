body {
  --primaryColor: #084ab8;
  --firstBgColor: #ffffff;
  --secondBgColor: #ffffff;
  --thirdBgColor: #d1d1d640;
  --firstTextColor: #232323;
  --secondTextColor: #6c6c6c;
  --thirdTextColor: #8a8a8a;
  --borderColor: #c1c1c1;
  --loopColor1: #d9ebe7;
  --loopColor1-dark7: #c2dfd8;
  --loopColor2: #dfeaf5;
  --loopColor2-dark7: #c4d8ed;
  --loopColor3: #faedd9;
  --loopColor3-dark7: #f6deb9;
  --loopColor4: #f7e4da;
  --loopColor4-dark7: #f1cfbd;
  --picker-mask-first: hsla(0, 0%, 100%, 0.95);
  --picker-mask-second: hsla(0, 0%, 100%, 0.6);
  --item-border: #d4d4d4;
  --step-focuse: #ffc107;
  --step-number: #c1c1c1;
  --error: #f33b40;
  --adm-color-primary: #084ab8;
}
body.theme-dark {
  --primaryColor: #084ab8;
  --firstBgColor: #000000;
  --secondBgColor: #1f1f1f;
  --thirdBgColor: #373737;
  --firstTextColor: #fffffff5;
  --secondTextColor: #ffffffc2;
  --thirdTextColor: #ffffff99;
  --borderColor: #3e3e3e;
  --loopColor1: #3e3e3e;
  --loopColor1-dark7: #373737;
  --loopColor2: #3e3e3e;
  --loopColor2-dark7: #373737;
  --loopColor3: #3e3e3e;
  --loopColor3-dark7: #373737;
  --loopColor4: #3e3e3e;
  --loopColor4-dark7: #373737;
  --picker-mask-first: hsla(0, 0%, 12%, 0.95);
  --picker-mask-second: hsla(0, 0%, 12%, 0.6);
  --item-border: #979797;
  --step-focuse: #ffd351;
  --step-number: #868686;
  --error: #ff5358;
}
.am-picker-popup {
  background: var(--secondBgColor);
}
.am-picker-popup .am-picker-col-mask {
  background-image: linear-gradient(180deg, var(--picker-mask-first), var(--picker-mask-second)), linear-gradient(0deg, var(--picker-mask-first), var(--picker-mask-second));
}
.am-button.am-button-primary {
  background-color: var(--primaryColor);
}
.ant-divider {
  border-top-color: var(--borderColor);
}
.adm-popup .adm-popup-body {
  background-color: var(--secondBgColor);
}
.adm-popup .adm-picker-header-button {
  color: var(--thirdTextColor);
}
.adm-popup .adm-picker-view-column-item {
  color: var(--firstTextColor);
}
.adm-popup .adm-picker-view-mask-top {
  background-image: linear-gradient(180deg, var(--picker-mask-first), var(--picker-mask-second));
}
.adm-popup .adm-picker-view-mask-bottom {
  background-image: linear-gradient(0deg, var(--picker-mask-first), var(--picker-mask-second));
}
.ant-modal-root .ant-modal-footer .ant-btn.ant-btn-default {
  background: #fff;
  color: #084ab8;
  border: 1px solid #084ab8;
}
