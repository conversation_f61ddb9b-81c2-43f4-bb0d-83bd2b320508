type CustomEventParams = {
  [key: string]: string
}

type CustomEventCallback = (params: CustomEventParams) => void

const events = {}

export function emit(event: string, params: CustomEventParams = {}) {
  if(!events[event]) {
    return
  }
  events[event].forEach((fn: CustomEventCallback) => {
    fn(params)
  })
}

export function subscribe(event: string, callback: CustomEventCallback) {
  if(!events[event]) {
    events[event] = []
  }
  
  events[event].push(callback)
}
