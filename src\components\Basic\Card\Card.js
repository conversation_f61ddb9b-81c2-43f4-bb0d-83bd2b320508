/**
 * <AUTHOR>
 * @description 通用卡片
 */
import React from 'react';
import classNames from 'classnames';
import { Card } from 'antd-mobile';
import styles from './Card.less';

export default class CommonCard extends React.Component {
  static defaultProps = {
    headerLine: true, // Header下划线
    footerLine: true, // Footer上划线
  };

  static Header = Card.Header;

  static Body = Card.Body;

  static Footer = Card.Footer;

  render() {
    const { headerLine, footerLine, className, ...restProps } = this.props;
    return (
      <div
        className={classNames(
          styles.commonCard,
          {
            [styles.hasHeaderLine]: headerLine,
            [styles.hasFooterLine]: footerLine,
          },
          className,
        )}
      >
        <Card {...restProps} />
      </div>
    );
  }
}
