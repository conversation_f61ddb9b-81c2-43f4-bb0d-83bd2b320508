import type { ApplicantInformationsModelsState, receiveResultModelsState } from 'umi';

export const getParams = ({ applicantInformations, receiveResult, formData }: {
  applicantInformations: ApplicantInformationsModelsState['applicantInformations'];
  receiveResult: receiveResultModelsState['receiveResult'];
  formData: Record<string, unknown>;
}) => {
  const resultReceive = receiveResult[0] || {};
  const receiveType = resultReceive.receiveType[0] instanceof Object ? resultReceive.receiveType[0].value : '';
  const {
    applicantCertNo: idNo,
    applicantCertType: idType,
    applicantLastnameZh: lastNameZh,
    applicantLastnameFl: lastNameEn,
    applicantNameZh: firstNameZh,
    applicantNameFl: firstNameEn,
    mobilePhone: mobile,
    euid,
  } = applicantInformations;
  return {
    idNo,
    idType,
    lastNameZh,
    lastNameEn,
    firstNameZh,
    firstNameEn,
    mobile,
    euid,
    ...formData,
    deliveryCount: resultReceive.serviceResultCount ?? 1,
    deliveryLocation: resultReceive.receiveAddress?.[0].value ?? '',
    deliveryProfile: resultReceive.resultType,
    deliveryType: receiveType,
  };
};
