import React, { useMemo, useState } from 'react';
import pc from 'prefix-classnames';
import './Form7.less';

import { useIntl } from 'umi';
import { Form, Input, Space, Button, message, Modal, Radio, Row, Col } from 'antd';

const px = pc('vta-form-form-7');

const props = {
  name: 'file',
  action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
  headers: {
    authorization: 'authorization-text',
  },
  onChange(info) {
    if (info.file.status !== 'uploading') {
      console.log(info.file, info.fileList);
    }
    if (info.file.status === 'done') {
      message.success(`${info.file.name} file uploaded successfully`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed.`);
    }
  },
};

export interface FormSevenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {}

const FormSeven = (props: FormSevenProps) => {
  const { className = '', ...otherProps } = props;

  const intl = useIntl();
  const [form] = Form.useForm();

  const [show, setShow] = useState(false);

  const [isModalOpen, setModalOpen] = useState(false);

  function handleOk() {
    setModalOpen(false);
    setShow(true);
  }

  function handleCancel() {
    setModalOpen(false);
  }
  const [typeUnit,setTypeUnit] = useState(0)
  let Dom=useMemo(() => {
    return   typeUnit === 0 ? (
              <Row >
                <Col span={11}>
                  <Input value="" placeholder="輪胎寛度" />
                </Col>
                <Col span={2} style={{background:'#eee',display:'flex',alignItems:'center',justifyContent:'center'}}>-</Col>
                <Col span={11}>
                  <Input value="" placeholder="輪胎直徑" />
                </Col>
              </Row>
            ) : (
              <Row>
                <Col span={7}>
                  <Input value="" placeholder="輪胎寛度" />
                </Col>
                <Col span={2} style={{background:'#eee',display:'flex',alignItems:'center',justifyContent:'center'}}>/</Col>
               
                <Col span={7}>
                  <Input value="" placeholder="輪胎扁平率" />
                </Col>

                <Col span={2} style={{background:'#eee',display:'flex',alignItems:'center',justifyContent:'center'}}>-</Col>
               
                <Col span={6}>
                  <Input value="" placeholder="輪胎直徑" />
                </Col>
              </Row>
            )
  },[typeUnit])
  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      {show && (
        <div className="multi-card-container">
          <div className="multi-card-item">
            <div className="multi-card-title">
              <span>輪胎規格1</span>
              <div className="multi-card-title-action">
                <Space>
                  <Button>更新</Button>
                  <Button danger>{intl.formatMessage({ id: 'delete' })}</Button>
                </Space>
              </div>
            </div>
            <div className="multi-card-body">
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label="英制/公制" style={{ width: '100%' }}>
                  <div>英制</div>
                </Form.Item>
                <Form.Item label="前置輪胎規格">
                  <div>195/55-15</div>
                </Form.Item>
                <Form.Item label="後置輪胎規格">
                  <div>195/55-15</div>
                </Form.Item>
                <Form.Item label="旁置輪胎規格">
                  <div></div>
                </Form.Item>
                {/* <Form.Item label={intl.formatMessage({ id: 'notes' })} style={{ width: '100%' }}>
                  <div></div>
                </Form.Item> */}
              </Form>
            </div>
          </div>
          <div className="multi-card-item">
            <div className="multi-card-title">
              <span>輪胎規格2</span>
              <div className="multi-card-title-action">
                <Space>
                  <Button>更新</Button>
                  <Button danger>{intl.formatMessage({ id: 'delete' })}</Button>
                </Space>
              </div>
            </div>
            <div className="multi-card-body">
              <Form form={form} className={px('form')} layout="vertical">
                <Form.Item label="英制/公制" style={{ width: '100%' }}>
                  <div>英制</div>
                </Form.Item>
                <Form.Item label="前置輪胎規格">
                  <div>195/55-15</div>
                </Form.Item>
                <Form.Item label="後置輪胎規格">
                  <div>195/55-15</div>
                </Form.Item>
                <Form.Item label="旁置輪胎規格">
                  <div></div>
                </Form.Item>
                {/* <Form.Item label={intl.formatMessage({ id: 'notes' })} style={{ width: '100%' }}>
                  <div></div>
                </Form.Item> */}
              </Form>
            </div>
          </div>
        </div>
      )}
      <div className={px('add-btn')}>
        <Button
          type="primary"
          onClick={() => {
            setModalOpen(true);
          }}
        >
          新增輪胎資料
        </Button>
      </div>

      <Modal
        title="錄入輪胎資料"
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="確 認"
        width={600}
        cancelButtonProps={{ type: 'default' }}
      >
        <Form layout="vertical" >
          <Form.Item label="英制/公制" required >
            <Radio.Group
            value={typeUnit}
              options={['英制', '公制'].map((item, index) => ({
                label: item,
                value: Number(index),
              }))}
              onChange={(e) => {
                setTypeUnit(e.target.value)
              }}
            />
          </Form.Item>
          <Form.Item label="前置輪胎規格" required>
          {Dom}
          </Form.Item>
          <Form.Item label="後置輪胎規格" required>
              {Dom}
          </Form.Item>
          <Form.Item label="旁置輪胎規格">
             {Dom}
          </Form.Item>
          {/* <Form.Item label={intl.formatMessage({ id: 'notes' })}>
            <Input.TextArea placeholder={intl.formatMessage({ id: 'please_enter' })} />
          </Form.Item> */}
        </Form>
      </Modal>
    </div>
  );
};

export default FormSeven;
