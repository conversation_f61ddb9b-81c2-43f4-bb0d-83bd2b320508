import React, { useState, useEffect } from 'react';
import pc from 'prefix-classnames';
import './Success.less';

import { history, useIntl } from 'umi';
import { Col, Button, Alert } from 'antd';
import dayjs from 'dayjs';

import {
  getExTxnCompleteData,
  completeTxnForPay,
  getTxnPendingApproval,
  getReceiptFile,
  checkTxnJumpOrder,
} from '@/services/0028F';
import { FetchResponse } from '@/utils/fetch';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { getLangGroup, getResponseMessage } from '@/locales/lang';

const px = pc('success-28f');

export interface SuccessProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId: string;
  requestId: string;
  ID: string;
}

const Success = (props: SuccessProps) => {
  const intl = useIntl();
  const { className = '', txnId, requestId, ID, ...otherProps } = props;
  const [transId, setTransId] = useState(ID);

  const [tipMsg, setTipMsg] = useState('');
  const [data, setData] = useState<getExTxnCompleteDataRes>();
  const [pendingApprovalCodeDto, setPendingApprovalCodeDto] = useState<sysCodesByCodeTypeOpt | undefined>(undefined);

  const [download, setDownload] = useState<boolean>(false);

  let times: number = 0;

  function gotoIndex() {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  }

  const receiptFile = async () => {
    setTipMsg('');

    if (!txnId && !requestId) {
      return;
    }

    const ret = await getReceiptFile(txnId || requestId).catch((e) => setTipMsg(e.message));
    if (!ret) {
      return;
    }

    if ((ret as FetchResponse<string>)?.code) {
      setTipMsg(getResponseMessage(ret as FetchResponse<string>));
      return;
    }

    const blob = new Blob([ret as BlobPart], { type: 'application/pdf' });
    blob && window.open(URL.createObjectURL(blob), '_blank');
  };

  const hander = (res: FetchResponse<getExTxnCompleteDataRes>) => {
    if (times >= 21) {
      setTipMsg(getResponseMessage(res));
      setDownload(false);
      return;
    }

    times++;

    timeout(1000);
  };

  const timeout = (ms: number) => {
    setTimeout(() => {
      getData();
    }, ms);
  };

  const func = () => {
    getExTxnCompleteData(txnId || requestId)
      .then((ret) => {
        if (!ret) return;
        if (ret.code === 'W-8888') {
          timeout(1000);
          return;
        }

        if (ret.code !== '0') {
          hander(ret);
          return;
        }

        setData(ret.data);
        setDownload(true);
        times = 0;
      })
      .catch((e) => {
        setDownload(false);
        setTipMsg(e.message);
      });
  };

  const handleBNU = async (transId: string) => {
    // BNU
    const res = await getTxnPendingApproval(transId);
    if (!res) return;
    if (res.code === '0') {
      setData(res.dataArr[0]);
      setDownload(true);
      setPendingApprovalCodeDto(res.dataArr[0]?.pendingApprovalCodeDto);
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const handleBOC = async () => {
    // BOC
    const res = await completeTxnForPay(requestId);
    if (!res) return;
    if (res.code === '0') {
      func();
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const getData = async () => {
    if (txnId) {
      func();
      return;
    }
    if (requestId) {
      const checkRes = await checkTxnJumpOrder(requestId);
      if (!checkRes) return;
      if (checkRes.code !== '0') {
        setTipMsg(getResponseMessage(checkRes));
        return;
      }
      if (checkRes.data.isShowCompletePage) {
        await handleBOC();
      }
      if (checkRes.data.isShowPendingPage) {
        setTransId(checkRes.data.transId);
        await handleBNU(checkRes.data.transId);
      }
      return;
    }

    if (transId) {
      // BNU
      await handleBNU(transId);
    }
  };

  useEffect(() => {
    getData();
  }, [txnId, requestId, transId]);

  return (
    <>
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div style={{ maxWidth: '1048px', margin: 'auto' }}>
          {tipMsg && (
            <div style={{ paddingBottom: '0.24rem' }}>
              <Alert message={tipMsg} type="error" showIcon />
            </div>
          )}
        </div>
        <div className={px('body')}>
          <div className={px('successTitle')}>{getLangGroup(data?.serviceTitleRespDTO?.serviceTitleCn, data?.serviceTitleRespDTO?.serviceTitlePt, data?.serviceTitleRespDTO?.serviceTitleEn)}</div>
          <div className={px('sectionBody')}>
            <div className={px('sectionTitle')}>
              {data?.txnStatus === 'E' && (
                <>
                  <CloseCircleFilled style={{ color: '#084ab8' }} />
                  <span>{intl.formatMessage({ id: 'failed_submitted_application' })}</span>
                </>
              )}
              {['A', 'F'].includes(data?.txnStatus || '') && (
                <>
                  <CheckCircleFilled style={{ color: '#084ab8' }} />
                  <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
                </>
              )}
              {data?.txnStatus === 'S' && (
                <>
                  <CheckCircleFilled style={{ color: '#084ab8' }} />
                  <span>{intl.formatMessage({ id: 'bnu_pay_submitted_application' })}</span>
                </>
              )}
            </div>
            {/* BNU支付显示信息 */}
            {transId ? (
              <>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'import_license_number' })}</div>
                  <div className="value">{data?.importNoFull}</div>
                </Col>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                  <div className="value">
                    {getLangGroup(data?.vehTypeDescCn, data?.vehTypeDescPt, data?.vehTypeDescEn)}
                  </div>
                </Col>
              </>
            ) : (
              <>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'query_number' })}</div>
                  <div className="value">{data?.spNo?.split('-')?.pop()}</div>
                </Col>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'establishment_time' })}</div>
                  <div className="value">
                    {data?.txnStatusDate
                      ? dayjs(data?.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                      : ''}
                  </div>
                </Col>
              </>
            )}

            <div className={px('successBottomTitle')} style={{textAlign:'left'}}>
              {
                pendingApprovalCodeDto ? getLangGroup(pendingApprovalCodeDto.codeCname, pendingApprovalCodeDto.codePname, pendingApprovalCodeDto.codeEname) : 
                intl.formatMessage(
                  { id: 'success_bottom_title_sst' },
                  {
                    platform: (
                      <span className={px('successBottomTitleSpan')}>
                        {intl.formatMessage({ id: 'platform_sst' })}
                      </span>
                    ),
                  },
                )
              }
            </div>
          </div>
        </div>
      </div>
      <div className={px('footer')}>
        <div className="footer-container">
          {download && (
            <Button
              type="default"
              onClick={() => {
                gotoIndex();
              }}
            >
              {intl.formatMessage({ id: 'complete' })}
            </Button>
          )}
          {download && data?.txnStatus === 'F' && !transId && (
            <Button
              type="primary"
              onClick={() => {
                receiptFile();
              }}
            >
              {intl.formatMessage({ id: 'download_receipt' })}
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

export default Success;
