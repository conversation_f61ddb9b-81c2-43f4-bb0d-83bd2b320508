{"home": "Página inicial", "view_details": "Consultar detalhes", "certificate_type": "Tipo do documento de identificação", "next": "Próximo passo", "previous": "Passo anterior", "turn_off": "<PERSON><PERSON><PERSON>", "close_save": "Guardar e fechar", "application_steps": "Etapas de pedido", "phone": "Telefone de contacto", "other": "Outros", "other_phone": "Outro telefone", "yes": "<PERSON>m", "no": "Não", "successfully_submitted_application": "Submissão do pedido com sucesso", "failed_submitted_application": "Transacção em andamento, consulte mais tarde o “Registo de pedido” para verificar o estado.", "bnu_pay_submitted_application": "Pagamento por aprovar", "complete": "Concluir", "prompt": "<PERSON>a", "confirm": "Confirmar", "ok": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "warm_reminder": "Le<PERSON><PERSON>", "confirm_logout": "Confirmar o cancelamento da operação actual e sair", "immediate_payment": "Pagar agora", "please_select": "<PERSON><PERSON><PERSON> de sele<PERSON>", "application_materials": "Dados do pedido", "total": "Total", "new_car_registration": "Matrícula de novo veículo", "model_approval": "Aprovação de modelo", "application_record": "Registo de pedido", "determine_information": "Confirmar dados", "payment": "<PERSON><PERSON>", "payment_information": "Dados do pagamento", "data_confirmation": "Confirmar dados", "identity_recognition": "Identificação", "owner_data_confirmation": "Confirmar os dados do proprietário", "car_owner_information": "Dados do proprietário ", "id_number": "N.º do documento de identificação", "contact_address": "Endereço", "language": "Língua", "vehicle_information": "Dados do veículo", "vehicle_register_information": "Dados da matrícula de veículo", "update_vehicle_information": "Alterar os dados do veículo", "enter_import_license_number": "Inserir o n.º da licença de importação", "format": "Exemplar de formato", "model_approval_number": "N.º de aprovação de modelo", "vehicle_level": "Classe", "vehicle_usage": "Serviço", "brand": "<PERSON><PERSON>", "vehicle_number": "N.º do quadro (VIN)", "style": "<PERSON><PERSON>", "style_year": "<PERSON>o de <PERSON>", "color": "Cor", "vehicle_build_year": "Ano de fabrico", "vehicle_cate_gory_desc": "Tipo ", "vin": "N.º do quadro (VIN)", "vin_4": "Os últimos 4 dígitos do n.º do quadro (VIN)", "enter_vin_4": "Insira os últimos 4 dígitos do n.º do quadro (VIN)", "enter_max_n": "<PERSON><PERSON> pode inserir, no máximo, {n} d<PERSON><PERSON><PERSON>", "enter_min_n": "Insira os últimos 4 dígitos do n.º do quadro ", "enter_year_min": "O ano da licença de importação não pode ser inferior a 1900", "enter_only_char": "Insira apenas caracteres", "enter_only_number": "Insira apenas números", "enter_only_number_char": "Insira apenas letras, números", "enter_only_pt_char": "Insira apenas letras e números do nome português", "engine_no": "N.º do motor", "engine_no_before": "Prefixo do n.º do motor", "engine_no_position": "Posição do n.º do motor", "tyre_f_r_s": "Medida dos pneus (dianteiro - traseiro - carro lateral)", "frontTyreQty": "N.º dos pneus dianteiros", "rearTyreQty": "N.º dos pneus traseiros", "frontAxleQty": "N.º de eixos dianteiros", "rearAxleQty": "N.º de eixos traseiros", "vehWeight": "Tara", "vehGrossWeight": "Peso bruto", "vehLoadWeight": "Carga útil", "car_size": "Dimensões da caixa", "length": "Comprimento", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "cabinTypeDescCn": "Tipo de caixa", "vehicle_size": "Dimensões do veículo", "vehSourceCtryDescCn": "País ou local de procedência", "maxPowerWithUnit": "Potência", "cylinderQty": "N.º de cilindros", "cylinderVol": "Cilindrada em c.c.", "engineInjection": "Modo de fornecimento de combustível", "fuelTypeDescCn": "Tipo de combustível", "contact_information": "Dados de contacto", "vehicle_contact_information": "Dados de contacto para recepção do serviço de notificação via SMS", "local_mobile_phone": "N.º de telemóvel de Macau", "seating_apacity": "Lotação", "license_plate_information": "Dados de matrícula ", "license_plate_number": "N.º de matrícula", "ex_license_plate_number": "Chapa de experiência (EX)", "ex_license_plate_no": "Chapa de experiência", "ex_license_plate_validity": "Validade da chapa de experiência (EX)", "apply_date_last": "Última data para requerer a matrícula", "license_plate_lottery_status": "Estado de sorteio", "lottery_result": "Resultado de sorteio", "payment_after_buy": "Após pagamento, usar o n.º de matrícula adquirido", "service_processing_date": "Data de apresentação do pedido", "query_number": "N.º de processo", "service_application_status": "Estado de pedido", "service_procedure_name": "Nome de formalidade", "vehicle_contact_phone_number": "Telefone de contacto do veículo", "please_enter": "Favor de ins<PERSON>r", "vehicle_owner_id_type": "Tipo de documento de identificação do proprietário de veículo", "vehicle_owner_id_number": "N.º do documento de identificação do proprietário de veículo", "search": "Consultar", "reset": "Redefinir", "in_progress": "Em redacção", "pending_payment": "Taxa por pagar", "pending_payment_approval": "Pagamento por aprovar", "application_completed": "Pedido concluído", "waiting_ex_cards": "Chapa de experiência (EX) por levantar", "got_ex_cards": "Chapa de experiência (EX) levantada", "pending_vehicle_registration": "Mat<PERSON><PERSON><PERSON> de veículo por finalizar", "my_create_record": "Registo de pedido criado", "batch_payment": "Pagamento em lote", "unknown_type": "<PERSON><PERSON><PERSON> desconhecido", "complete_vehicle_registration": "Próximo passo", "download_receipt": "Recibo (Pedido)", "download_complete_vehicle_registration_receipt": "Recibo (Concluir a matrícula)", "appointment_vehicle_inspection": "Marcar/ Alterar a inspecção ", "download_ex_card": "Licença provisória da chapa de experiência (EX)", "service_completed": "Formalidade concluída", "download_booklet": "Guia substitutiva do livrete", "qr_code": "Código QR", "service_remarks": "Observações", "invoice_number": "N.º de factura", "trading_time": "Hora de conclusão do pagamento", "transaction_channel": "Mei<PERSON>mento", "payment_method": "Forma de pagamento", "total_transaction_amount": "Valor total de pagamento", "payment_status": "Estado de pagamento", "submitted_documents": "Documentos entregues", "application_status": "Estado de pedido", "return": "Voltar", "import_license_information": "Dados sobre a licença de importação", "car_license_information": "Dados sobre a matrícula de veículo", "vehicle_tax_information": "Dados sobre Veículos Motorizados", "apply_information": "Dados do pedido", "dsf": "Estado do imposto", "dsf_ref_no": "N.º de referência da DSF", "m_4_date": "Data de declaração do Modelo M/4", "exemption_regulations": "Legislação de isenção", "tax_exemption_start_date": "Data de início da isenção de imposto", "tax_exemption_end_date": "Data de termo da isenção de imposto", "model_approval_documents": "Dados de aprovação de modelos de veículos", "vehicle_origin": "Local de origem do veículo", "import_license_year": "Ano de licença de importação", "exit": "<PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "application_notice": "Chamadas de atenção no requerimento", "must_1": "A soma das quotas-partes deve ser 1", "cp_must_enter": "Preenchimento obrigatório de apelido e nome (Chinês ou língua estrangeira)", "cp_save": "Guardar", "cp_save_success": "Guardar em sucesso", "enter_address": "Preencha completamente o endereço", "each_same_share": "Igual quota-parte detida por cada comproprietário", "area": "Região", "region": "Zona", "building_street": "Edifício/ Rua", "door_number": "Numeração policial", "floor": "<PERSON><PERSON>", "room_unit": "Fracção", "notes": "Observações", "address_content": "Teor do endereço", "importer_information": "Dados do importador", "importer_chinese_name": "Nome chinês do importador", "importer_portuguese_name": "Nome português do importador", "agent_information": "Dados da agência", "agent_type": "Tipo de agência", "agent_no": "N.º de agência", "agent_chinese_name": "Nome chinês da agência", "agent_portuguese_name": "Nome português da agência", "agent_phone": "Telefone de contacto da agência", "phone_tips": "O n.º de telefone tem de ser 8 dígitos e começa com 6", "car_owner": "Pessoa de contacto (No caso de o veículo ser detido por mais de um proprietário, é favor fornecer um deles)", "car_owner_view": "Pessoa de contacto", "agree": "Concordo", "no_agree": "Não concordo", "usage_license_plates": "Obtenção de matrículas através de", "draw_lots": "<PERSON><PERSON><PERSON>", "use_license_plates": "Utilizar a matrícula adquirida", "cancel_license_plates": "Cancelar a utilização da matrícula adquirida", "confirm_cancel_license_plates": "Quer cancelar a utilização da matrícula adquirida?", "size_weight": "Dimensões e Peso", "car_body": "Quadro", "motor": "Motor", "contact_chinese_name": "<PERSON><PERSON><PERSON> (Nome chinês)", "contact_portuguese_name": "Nome estrangeiro da pessoa de contacto", "confirm_payment": "Confirmar e pagar", "cost_details": "Detalhe de pagamento", "amount": "Valor", "tax_payment": "Imposto", "subtotal": "Subtotal", "payment_channels": "Meios de pagamento", "upload_files": "<PERSON><PERSON><PERSON>", "import_license_number": "N.º de licença de importação", "inspection_results": "Sujeito ao resultado da inspecção de novos veículos", "import_license_type": "Tipo de licença de importação", "is_accelerating": "Quer solicitar um pedido urgente?", "chinese_surname": "Apelido (em chinês)", "chinese_name": "No<PERSON> chin<PERSON>s", "chinese_sur_name": "Nome (em chinês)", "portuguese_surname": "Apelido (em língua estrangeira)", "portuguese_name": "<PERSON><PERSON>", "portuguese_sur_name": "Nome (em língua estrangeira)", "share_held": "Quota-parte detida", "registered_residence": "Residência/sede constante do registo de automóveis", "new_car_owner": "Acrescentar proprietário", "address": "Endereço", "fill_in_type": "Preencher o tipo", "select_region": "Seleccionar a zona", "share": "Quota-parte", "submit_application_materials": "Dados do pedido", "33Dii—submit_application_materials": "Dados do pedido (Matrícula de veículo concluída)", "ex_test_vehicle": "Chapa de experiência (EX)", "temporary_license_plates": "N.º de matrícula ", "accelerate_registration_documents": "Acelerar o tratamento do livrete de veículo", "notice": "Observações", "application_record_query": "Consultar o registo de pedido", "all": "Todos", "terminate_record": "Quer terminar este registo de pedido?", "termination": "En<PERSON><PERSON>", "update_information": "Actualizar os dados ", "ex_test_license_plate_application": "Chapa de Experiência - 1.° pedido", "vehicle_registration_application": "Matrícula de veículo - 1.° pedido", "establishment_time": "Hora de submissão", "newcar_owner_confirmation_time": "Hora de confirmação do novo proprietário", "collection_ex_time": "Hora de levantamento da chapa de experiência (EX)", "flow_creation_time": "Hora de criação da formalidade", "confirm_application": "Confirmar e efectuar a identificação", "error": "Manutenção do sistema em curso", "no_data": "Não há temporariamente dados disponíveis", "confirm_receipt": "Confirmar o levantamento", "batch_get": "Levantamento em lote", "batch_no": "<PERSON><PERSON><PERSON><PERSON>e", "update": "<PERSON><PERSON><PERSON><PERSON>", "get_car_list": "Lista para o levantamento das chapas de experiência (EX)", "status": "Estado", "saved": "Guardar", "current_car_owner": "Propriet<PERSON><PERSON> veículo <PERSON>", "other_car_owner": "Outro proprietário de veículo", "id_number_check": "O número do BIR deve conter 8 dígitos", "get_address": "Local para levantamento", "license_plate": "Obs.: O número da chapa de experiência (EX) é determinado no momento em que a chapa física seja levantada. A chapa de experiência (EX) terá uma validade de 15 dias consecutivos, contados a partir do dia seguinte ao do levantamento da chapa física.", "delete": "Eliminar", "complete_submit": "Submetido", "qrcode_car_count": "N.º  de chapas de matrícula: {count}", "download": "<PERSON><PERSON><PERSON><PERSON>", "payment_results": "Resultado de pagamento", "old_motorcycle_replace_subsidy": "Plano de concessão de apoio financeiro ao abate de motociclos obsoletos e à sua substituição por motociclos eléctricos novos", "fp_no": "N.º  do registo de entrada", "fp_no_tip": "O n.º  do registo de entrada consta da notificação sobre a concessão de apoio financeiro emitida pelo FPACE", "application_deadline": "Prazo limite para apresentação de pedido ", "old_plate_no": "N.º  de matrícula do veículo obsoleto", "submit_application": "Submeter o pedido", "cancel_payment": "Cancelar o pagamento", "success_bottom_title_sst": "V. Exa. também pode aceder à 【{platform}】 para consultar o andamento deste pedido.", "platform_sst": "Plataforma para Empresas e Associações", "success_bottom_title_yht": "V. Exa. também pode aceder à Conta Única de Macau para consultar o andamento deste pedido, após o pagamento efectuado pela agência de veículos", "fee_item": "<PERSON><PERSON>aga<PERSON>", "car_use_type": "Obtenção de matrículas através de", "car_submit_time": "Hora de submissão por proprietário de veículo", "lose_efficacy": "Invalidar", "collection_time": "<PERSON><PERSON>", "please_enter_the_address": "Favor de inserir o endereço", "licAddrCode": "Favor seleccionar o local para levantamento", "dsaj_addr_ticked": "Usar o endereço como a residência / sede do registo de automóveis", "batch_complete_vehicle_registration": "Conclusão da matrícula de veículos em lote", "batch_complete_vehicle_payment_title": "Veículos sujeitos ao pagamento do imposto de circulação", "batch_complete_vehicle_not_payment_title": "Veículos isentos do pagamento do imposto de circulação", "batch_complete_vehicle_modal_tip": "Após a conclusão do serviço, a(s) seguin<PERSON>(s) chapa(s) de experiência (EX) ficará(ão) inválida(s).", "add_style": "新增型號", "light_vehicles": "輕型汽車", "emergency_exit": "緊急出口", "vehicle_feature": "車輛特徵", "ma_tyre_f_r_s": "馬輪胎規格(前 - 後 - 旁)", "jks_phone": "進口商聯絡電話", "service_completed_invalid": "服務完成後紅牌會失效?", "vehType": "車輛級別", "vehBrandCode": "商標", "vehModel": "型號", "vehModelYear": "型號年份", "customModelCode": "型號編碼", "steeringWheelPosition": "方向盤位置", "vehBuildCtryCode": "出產國或地區 ", "vehBuildYear": "出廠年份", "vehSourceCtryCode": "來源國或地區", "vtaCategory": "車輛種類", "vtaUsageCode": "重型客車類別", "passCapacity": "載客量", "seatQty": "座位數量", "standQty": "站立位數量", "disablilitySeatQty": "傷殘位數量", "touristGuideQty": "導遊位數量", "doorQty": "車門數量", "emergencyExitQty": "緊急出口數量", "passFootholdType": "乘客腳踏型式", "passHandrailType": "扶手位置", "vehLength": "長", "vehWidth": "寬", "vehHeight": "高", "axleDistance": "軸距", "frontWheelWidth": "前輪闊度", "rearWheelWidth": "後輪寬度", "cabinType": "車廂種類", "cabinCode": "車廂特徵", "cabinLength": "長", "cabinWidth": "寬", "cabinHeight": "高", "sidecarLength": "長", "sidecarWidth": "寬", "sidecarHeight": "高", "vehMaxGrossWeight": "最大技術總重量 (kg) *", "fuelType": "能源種類", "fuelSupplyType": "動力來源", "valveQty": "氣閥數量", "bore": "口徑(mm)", "stroke": "衝程(mm)", "twoStrokeCycle": "四衝程", "aspiration": "進氣方式", "maxPowerPowerUnitType": "額定功率(kw)及轉速(rpm) - 1", "maxPowerRpm": "最大功率(kw)及轉速(rpm) - 1", "maxTorqueTorqueUnitCode": "額定扭力(Nm)及轉速(rpm) - 1", "maxTorqueRpm": "最大扭力(Nm)及轉速(rpm) - 1", "obd": "車載自我診斷系統", "txType": "傳動方式", "gearboxType": "變速器", "varSpeedQty": "檔數", "leafSpringSuspension": "鋼板彈簧式懸掛 - 後", "frontSuspension": "前*", "frontSuspensionQty": "數量 - 前", "rearSuspension": "後*", "rearSuspensionQty": "數量 - 後", "frontBrakeType": "前", "rearBrakeType": "後", "hasAbsBrake": "防鎖死煞車系統 ", "otherBrakeFunc": "其他性能", "handBrake": "種類", "parkingAxle": "駐車軸", "dualFrontAxle": "軸荷 - 牽引軸 (kg)", "axleLoad1": "軸荷 - 第一軸 (kg)", "axleLoad2": "軸荷 - 第二軸~第十六軸 (kg) *", "vehHeadlamp": "車輛前照燈 - 遠光燈光束 (W)", "fuelTankVol": "燃料箱容量", "exhaustStd": "尾氣排放報告所載標準", "exhaustTestProg": "排放測試程序", "fuelEffTestProg": "燃料效率測試程序", "fuelEfficiency": "燃料效率 (km/L)", "fuelEffTestVehWeight": "車重 (kg)", "fuelEffTestTopSpeed": "最高車速 (km/h)", "efStatus": "環保車申請", "seatType": "座椅尺寸 (mm)", "driverSeatLength": "座位長(mm)", "driverSeatWidth": "座位寬(mm)", "passSeatLength": "座位長(mm)", "passSeatWidth": "座位寬(mm)", "sideSeatLength": "座位長(mm)", "sideSeatWidth": "座位寬(mm)", "submit_size_type_tips_size": "支持png, jpeg, jpg, pdf格式上傳，文件大小不超過{size}", "strOther": "若無想選擇的商標，請聯絡交通事務局", "features": "特徵", "sidecard_size": "Dimensão do Sidecar", "weight": "Peso", "max_load": "A carga total e o peso em ordem de marcha do veículo não devem exceder o valor especificado pelo fabricante", "payload_formula": "Carga útil = Peso Bruto - Peso em Ordem de Marcha", "identifier": "Identificador", "serial_number": "Número de Série", "max_power": "Potência Máxima", "unit": "Unidade", "rpm": "Velocidade (rpm)", "max_torque": "<PERSON><PERSON>", "power_unit": "Unidade de Potência", "energy_type": "Tipo de Energia", "internal_engine": "Motor de Combustão Interna", "fuel_tank": "Capacidade do Tanque de Combustível (L)", "input_motor_data": "Inserir Dados do Motor", "power_unit_total": "Unidade de Potência - Total", "add_new": "<PERSON><PERSON><PERSON><PERSON>", "cylinder_volume_calc": "O volume do cilindro calculado com base nos dados inseridos", "vin_number": "Número de Identificação do Veículo (VIN)", "suspension_system": "Sistema de Suspensão", "front_suspension": "Suspensão Dianteira", "front_suspension_qty": "Quantidade de Suspensão Dianteira", "independent_suspension": "Suspensão Independente", "rear_suspension": "Suspensão Traseira", "rear_suspension_qty": "Quantidade de Suspensão Traseira", "brake_system": "Sistema de Travagem", "abs_system": "Sistema de Travagem Antibloqueio (ABS)", "parking_brake": "Travão de Estacionamento", "type": "Tipo", "parking_axle": "Eixo de Estacionamento", "axle": "Eixo", "qty_front": "Quantidade - Frente", "qty_rear": "Quantidade - Traseira", "steering_dual_axle": "Sistema de Direção - Eixo Duplo", "axle_load_1": "<PERSON>ga por Eixo - <PERSON>iro <PERSON> (kg)", "axle_load_2": "Carga por Eixo - <PERSON><PERSON><PERSON> (kg)", "axle_load_3": "Carga por Eixo - <PERSON><PERSON><PERSON> (kg)", "axle_load_4": "Carga por Eixo - Quarto Eixo (kg)", "axle_load_5": "Carga por Eixo - <PERSON><PERSON><PERSON> (kg)", "axle_load_other": "Carga por Eixo - Outro Eixo (kg)", "axle_load_drive": "Carga por Eixo - Eixo de Tração (kg)", "fuel_tank_capacity": "Capacidade do Tanque de Combustível", "wheel": "Roda", "wheel_qty_front": "Quantidade de Rodas - Frente", "wheel_qty_rear": "Quantidade de Rodas - Traseira", "tire_data": "Dados do Pneu", "add_tire_data": "Adicionar Dados do Pneu", "tire_spec": "Especificação do Pneu", "imperial_metric": "Imperial / Métrico", "imperial": "Imperial", "tire_spec_front": "Especificação do Pneu Dianteiro", "tire_spec_rear": "Especificação do Pneu Traseiro", "tire_spec_side": "Especificação do Pneu Lateral", "input_tire_data": "Inserir Dados do Pneu", "tire_width": "Largura do Pneu", "tire_aspect_ratio": "Perfil do Pneu", "tire_diameter": "Diâmetro do Pneu"}