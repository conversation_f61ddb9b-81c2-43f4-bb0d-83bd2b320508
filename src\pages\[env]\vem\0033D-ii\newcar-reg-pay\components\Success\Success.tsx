import React, { useEffect, useRef, useState } from 'react';
import pc from 'prefix-classnames';
import dayjs from 'dayjs';
import './Success.less';

import { history, useIntl } from 'umi';
import { <PERSON><PERSON>, <PERSON><PERSON>, Col } from 'antd';
import {
  checkTxnJumpOrder,
  completeTxnForPay,
  getExTxnCompleteData,
  getReceiptFile,
  getTxnPendingApproval,
} from '@/services/0028F';
import { printSubRegReport } from '@/services/0033D-ii';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { FetchResponse } from '@/utils/fetch';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import { isMobile } from '@/utils';
import { Page } from '@gov-mo/mpaas-js-bridge';

const px = pc('success-33d-ii');

export interface FormTenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId: string;
  requestId: string;
  ID: string;
}

const FormTen = (props: FormTenProps) => {
  const intl = useIntl();
  const { className = '', txnId, requestId, ID } = props;
  const [transId, setTransId] = useState(ID);
  const [tipMsg, setTipMsg] = useState('');
  const [completeData, setCompleteData] = useState<getExTxnCompleteDataRes>(
    {} as getExTxnCompleteDataRes,
  );
  const [download, setDownload] = useState<boolean>(false);
  const [pendingApprovalCodeDto, setPendingApprovalCodeDto] = useState<
    sysCodesByCodeTypeOpt | undefined
  >(undefined);
  const requestTimes = useRef<number>(0);

  const handleBNU = async (transId: string) => {
    // BNU
    const res = await getTxnPendingApproval(transId);
    if (!res) return;
    if (res.code === '0') {
      setCompleteData(res.dataArr[0]);
      setDownload(true);
      setPendingApprovalCodeDto(res.dataArr[0]?.pendingApprovalCodeDto);
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const handleBOC = async () => {
    // BOC
    const res = await completeTxnForPay(requestId);
    if (!res) return;
    if (res.code === '0') {
      await func();
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const fetchExTxnCompleteData = async () => {
    try {
      if (txnId) {
        await func();
        return;
      }
      if (requestId) {
        const checkRes = await checkTxnJumpOrder(requestId);
        if (!checkRes) return;
        if (checkRes.code !== '0') {
          setTipMsg(getResponseMessage(checkRes));
          return;
        }
        if (checkRes.data.isShowCompletePage) {
          await handleBOC();
        }
        if (checkRes.data.isShowPendingPage) {
          setTransId(checkRes.data.transId);
          await handleBNU(checkRes.data.transId);
        }
        return;
      }

      if (transId) {
        // BNU
        await handleBNU(transId);
      }
    } catch (e: any) {
      setDownload(false);
      setTipMsg(e.message);
    }
  };

  const func = async () => {
    const res = await getExTxnCompleteData(txnId || requestId);
    if (!res) return;
    if (res?.code === 'W-8888') {
      setTimeout(() => {
        fetchExTxnCompleteData();
      }, 1000);
      return;
    }

    if (res && res.code === '0') {
      setCompleteData(res?.data ?? {});
      requestTimes.current = 0;
      setDownload(true);
    } else if (requestTimes.current < 21) {
      requestTimes.current += 1;

      setTimeout(() => {
        fetchExTxnCompleteData();
      }, 1000);
    } else if (requestTimes.current >= 21) {
      setTipMsg(getResponseMessage(res));
      setDownload(false);
    }
  };

  useEffect(() => {
    fetchExTxnCompleteData();
  }, [txnId, requestId, transId]);

  function gotoIndex() {
    if (isMobile()) {
      process.env.NODE_ENV === 'production' ? Page.close() : history.push('/web/vem');
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  }

  async function receiptFile() {
    setTipMsg('');

    if (!txnId && !requestId) {
      return;
    }

    const ret = await getReceiptFile(txnId || requestId).catch((e) => {
      setTipMsg(e.message);
    });
    if (!ret) {
      return;
    }

    if ((ret as FetchResponse<string>).code) {
      setTipMsg(getResponseMessage(ret as FetchResponse<string>));
      return;
    }

    const blob = new Blob([ret as BlobPart], { type: 'application/pdf' });
    blob && window.open(URL.createObjectURL(blob), '_blank');
  }

  const handlePrintSubRegReport = async () => {
    setTipMsg('');

    if (!txnId && !requestId) {
      return;
    }

    try {
      const ret = await printSubRegReport(txnId || requestId);
      if (!ret) {
        return;
      }

      if ((ret as FetchResponse<string>).code) {
        setTipMsg(getResponseMessage(ret as FetchResponse<string>));
        return;
      }

      const blob = new Blob([ret as BlobPart], { type: 'application/pdf' });
      blob && window.open(URL.createObjectURL(blob), '_blank');
    } catch (e: any) {
      setTipMsg(e.message);
    }
  };

  return (
    <div className={`${px('root')} ${className}`}>
      <div style={{ maxWidth: '1048px', margin: 'auto' }}>
        {tipMsg && (
          <div style={{ paddingBottom: '0.24rem' }}>
            <Alert message={tipMsg} type="error" showIcon />
          </div>
        )}
      </div>
      <div className={px('body')}>
        <div className={px('successTitle')}>
          {getLangGroup(
            completeData.serviceTitleRespDTO?.serviceTitleCn,
            completeData.serviceTitleRespDTO?.serviceTitlePt,
            completeData.serviceTitleRespDTO?.serviceTitleEn,
          )}
        </div>
        <div className={px('sectionBody')}>
          <div className={px('sectionTitle')}>
            {completeData?.txnStatus === 'E' && (
              <>
                <CloseCircleFilled style={{ color: '#084ab8' }} />
                <span>{intl.formatMessage({ id: 'failed_submitted_application' })}</span>
              </>
            )}
            {['A', 'F'].includes(completeData?.txnStatus || '') && (
              <>
                <CheckCircleFilled style={{ color: '#084ab8' }} />
                <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
              </>
            )}
            {completeData?.txnStatus === 'S' && (
              <>
                <CheckCircleFilled style={{ color: '#084ab8' }} />
                <span>{intl.formatMessage({ id: 'bnu_pay_submitted_application' })}</span>
              </>
            )}
          </div>
          {transId ? (
            <>
              <Col>
                <div className="label">{intl.formatMessage({ id: 'import_license_number' })}</div>
                <div className="value">{completeData?.importNoFull}</div>
              </Col>
              <Col>
                <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                <div className="value">
                  {getLangGroup(
                    completeData?.vehTypeDescCn,
                    completeData?.vehTypeDescPt,
                    completeData?.vehTypeDescEn,
                  )}
                </div>
              </Col>
            </>
          ) : (
            <>
              <Col>
                <div className="label">{intl.formatMessage({ id: 'query_number' })}</div>
                <div className="value">{completeData.spNo}</div>
              </Col>
              <Col>
                <div className="label">{intl.formatMessage({ id: 'establishment_time' })}</div>
                <div className="value">
                  {completeData.txnStatusDate
                    ? dayjs(completeData.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                    : ''}
                </div>
              </Col>
            </>
          )}
          <div className={px('successBottomTitle')} style={{ textAlign: 'left' }}>
            {pendingApprovalCodeDto
              ? getLangGroup(
                  pendingApprovalCodeDto.codeCname,
                  pendingApprovalCodeDto.codePname,
                  pendingApprovalCodeDto.codeEname,
                )
              : intl.formatMessage(
                  { id: 'success_bottom_title_sst' },
                  {
                    platform: (
                      <span className={px('successBottomTitleSpan')}>
                        {intl.formatMessage({ id: 'platform_sst' })}
                      </span>
                    ),
                  },
                )}
          </div>
        </div>
      </div>
      <div className={px('footer')}>
        <div className="footer-container">
          {download && (
            <Button
              type="default"
              onClick={() => {
                gotoIndex();
              }}
            >
              {intl.formatMessage({ id: 'complete' })}
            </Button>
          )}
          {download && completeData?.txnStatus === 'F' && !transId && (
            <>
              <Button type="primary" onClick={receiptFile}>
                {intl.formatMessage({ id: 'download_complete_vehicle_registration_receipt' })}
              </Button>
              <Button style={{ width: 200 }} type="primary" onClick={handlePrintSubRegReport}>
                {intl.formatMessage({ id: 'download_booklet' })}
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FormTen;
