import React, {useMemo} from 'react';
import {openPageRelativePath} from '@/utils';
import DraggableFloat from '../DraggableFloat';
import {useIntl} from 'umi';

interface IGuideButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  itemId: string;
}

const GuideButton = ({itemId}: IGuideButtonProps) => {
  const intl = useIntl();

  const itemList = useMemo(
    () => [
      {
        title: intl.formatMessage({id: 'application_notice'}),
        onClick: () => {
          openPageRelativePath(`/service-introduction?itemId=${itemId}`);
        },
      },
    ],
    [intl, itemId],
  );

  return <DraggableFloat itemList={itemList}/>;
};

export default GuideButton;
