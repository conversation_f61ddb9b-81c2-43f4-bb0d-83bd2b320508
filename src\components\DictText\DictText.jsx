import { useDictionary } from '@/utils/hooks';
import * as React from 'react';
import { connect } from 'react-redux';

const DictText = (props) => {
  const {
    dispatch,
    className,
    forceUpdate = false,
    prefix = 'root',
    name = '',
    code = '',
    ...otherProps
  } = props;
  const [dictionary] = useDictionary([name], prefix, forceUpdate);

  return (
    <span className={className} {...otherProps}>
      {dictionary[code]}
    </span>
  );
};

export default connect()(DictText);
