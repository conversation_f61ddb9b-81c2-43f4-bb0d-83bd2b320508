import React, { useEffect, useState } from 'react';
import type { IRouteComponentProps } from 'umi';
import { getLocale } from 'umi';
import {
  getLanguageFromUrl,
  getLocale2ComponentLocaleMap,
  transform2HantFormat,
} from '@/locales/lang';
import { LocaleProvider } from 'antd-mobile';
import { zhHant } from '@gov-mo/components/esm/locales/mobile';
import { updateLocale } from '@/utils/languageUtils';
import { subscribe } from '@/utils/customEvent';
import { Toast } from "antd-mobile-v5";
import { EMIT_KEY_MESSAGE_GLOBLE_ERROR } from '@/utils/const';

export default function Layout({
  children,
  location,
  route,
  history,
  match,
}: IRouteComponentProps) {
  const [locale, setLocale] = useState(() => transform2HantFormat(getLocale()));
  const [showLoading, setShowLoading] = useState<boolean>(false);

  useEffect(() => {
    const language = getLanguageFromUrl();
    if (language) {
      updateLocale(language);
      if (language !== transform2HantFormat(getLocale())) {
        setLocale(language);
      }
    }
  }, []);

  const gesturestart = (e) => {
    e.preventDefault();
  };

  useEffect(() => {
    document.addEventListener('gesturestart', gesturestart);
    return () => {
      document.removeEventListener('gesturestart', gesturestart);
    };
  }, []);

  const defaultAntdLocale = {
    ...(require('@gov-mo/components/esm/locales/mobile')[
      getLocale2ComponentLocaleMap[getLocale()]
    ] ?? zhHant),
  };

  const requestStart = () => {
    if (showLoading) {
      return;
    }

    setShowLoading(true);
    try {
      Toast.show({
        content: 'Loading...',
        icon: 'loading',
        duration: 0,
        maskClickable: false
      });
    } catch (error) {}
  };

  const requestEnd = () => {
    setShowLoading(false);
    Toast.clear();
  };

  const onGlobleError = (msg: string) => {
    Toast.show({
      content: msg,
      icon: 'fail',
    });
  }

  useEffect(() => {
    subscribe('request-start', () => {
      requestStart();
    });
    subscribe('request-end', () => {
      requestEnd();
    });
    subscribe(EMIT_KEY_MESSAGE_GLOBLE_ERROR, (params) => {
      onGlobleError(params?.content)
    });
  }, []);

  return (
    <LocaleProvider key={locale} locale={defaultAntdLocale}>
      {children}
    </LocaleProvider>
  );
}
