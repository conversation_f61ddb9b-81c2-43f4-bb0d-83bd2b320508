import React, { useEffect } from 'react';
import pc from 'prefix-classnames';
import './FormNine.less';

import { useIntl } from 'umi';
import { Form, Select, Input, Radio, Space, Button, Table } from 'antd';

const px = pc('form-nine');

export interface FormNineProps extends
  Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setNextStepText: (text:string) => void;
}

const FormNine = (props: FormNineProps) => {
  const { className = '', ...otherProps } = props;
  const { setNextStepText } = otherProps;

  const intl = useIntl();
  const [form] = Form.useForm();

  useEffect(() => {
    setNextStepText(intl.formatMessage({ id: 'confirm_payment' }))
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }))
    }
  }, [])

  const columns = [
    {
      title: intl.formatMessage({ id: 'cost_details' }),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: intl.formatMessage({ id: 'amount' }),
      dataIndex: 'age',
      key: 'age',
      width: '100px'
    },
    {
      title: intl.formatMessage({ id: 'tax_payment' }),
      dataIndex: 'address',
      key: 'address',
      width: '100px'
    },
    {
      title: intl.formatMessage({ id: 'subtotal' }),
      key: 'tags',
      dataIndex: 'tags',
      width: '100px'
    },
  ];
  const data = [
    {
      key: '1',
      name: intl.formatMessage({ id: 'light_vehicles_28' }),
      age: 900,
      address: 1,
      tags: 900
    },
    {
      key: '2',
      name: intl.formatMessage({ id: 'total' }),
      age: '',
      address: 'MOP',
      tags: 900
    },
  ];

  return (
    <div className={`${px('root')} ${className}`} {...otherProps}>
      <Table columns={columns} dataSource={data} pagination={false} />
      <div style={{height: '50px'}}></div>
      <div>
        <p className={px('formTitle')}>{intl.formatMessage({ id: 'payment_channels' })}</p>
        <div style={{paddingLeft: '30px',marginTop:"15px"}}>
          <Radio checked={true}><img style={{width:'150px'}} src="/ovsap/image/pay/GOVpay.png" alt="" /> </Radio>
        </div>

        <div style={{paddingLeft: '30px',marginTop:"5px"}}>
                  <Radio checked={false}><img style={{width:'150px'}} src="/ovsap/image/pay/pay2.gif" alt="" /> </Radio>
                </div>
      </div>
    </div>
  );
};

export default FormNine;
