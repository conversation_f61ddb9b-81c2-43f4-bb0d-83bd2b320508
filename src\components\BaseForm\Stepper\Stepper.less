.commomStyle(@height,@width) {
  height: @height;
  line-height: @height;

  &.am-stepper.showNumber {
    width: @width;
  }
  .am-stepper-input {
    color: @font-color-title;
  }
  .am-stepper-input-wrap {
    height: @height;
    display: flex !important;
    align-items: center;
    justify-content: center;
  }
  .am-stepper-handler {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border-radius: 50%;
    border: @brand-success 2px solid;
    color: @brand-success;
    height: @height;
    width: @height;
    line-height: @height;
    .am-icon {
      stroke: currentColor;
      stroke-width: 4;
      stroke-linecap: round;
    }
    &-up {
      background-color: @brand-success;
      color: #fff;
    }
  }
}

.cbd-web-base-form-stepper {
  .commomStyle(24px,110px);
}
.cbd-mobile-base-form-stepper {
  .commomStyle(64px,252px);
}
