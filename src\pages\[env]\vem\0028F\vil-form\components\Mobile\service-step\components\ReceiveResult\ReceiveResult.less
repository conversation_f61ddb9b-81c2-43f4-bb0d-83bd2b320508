.root {
  // background: #fff;
  // margin-bottom: 24px;
  // border-radius: @radius-md;
  .title {
    padding: 32px 24px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 32px;
    font-weight: bold;
  }
  :global {
    .am-accordion .am-accordion-item .am-accordion-header {
      padding: 0 24px;
      background: rgba(255, 91, 96, 0.08);
    }
    // .cbd-mobile-base-form-root {
    //   border-top-left-radius: 0;
    //   border-top-right-radius: 0;
    // }
  }
  :global {
    .am-stepper {
      height: 52px;
      padding: 0;
      border: 2px solid #d9d9d9;
      border-radius: @radius-sm;
      overflow: visible;
      width: 180px;
      line-height: 52px;
    }
    .am-stepper-handler {
      width: 52px;
      height: 52px;
      line-height: 48px;
      border: 2px solid #d9d9d9;
      border-radius: @radius-sm;
      top: -2px;
      svg {
        fill: @brand-primary;
        width: 24px;
        height: 24px;
        stroke: @brand-primary;
        stroke-width: 1px;
      }
    }
    .am-stepper-handler-down {
      left: -2px;
    }
    .am-stepper-handler-up {
      right: -2px;
    }
    .am-stepper-input-wrap {
      height: 52px;
      line-height: 52px;
    }
    .am-stepper-handler-down-disabled,
    .am-stepper-handler-up-disabled {
      opacity: 1;
      svg {
        opacity: 0.3;
      }
    }
  }
  .warnning {
    // padding: 24px 0;
    .accordionPanel {
      border-radius: @radius-md;
      overflow: hidden;
      .headers {
        color: #ff5b60;
        font-weight: bold;
        box-shadow: none;
        svg {
          fill: #ff5b60;
        }
      }
      .tipContent {
        background: rgba(255, 91, 96, 0.08);
        padding: 0 24px;
        color: #ff5b60;
        white-space: pre-line;
      }
    }
  }
  // .detailTips {
  //   padding: 24px 0;
  // }

  .baseTitleContainer {
    margin: 0 16 * @hd;
    .cardTitleMixins(@color2);
  }
}
