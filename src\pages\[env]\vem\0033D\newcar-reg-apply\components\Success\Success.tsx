import React, { useEffect, useState, useRef } from 'react';
import pc from 'prefix-classnames';
import './Success.less';
import { FetchResponse } from '@/utils/fetch';

import { getLocale, history, useIntl } from 'umi';
import dayjs from 'dayjs';
import { Col, Button, Alert } from 'antd';
import {
  completeTxnForPay,
  getTxnPendingApproval,
  getExTxnCompleteData,
  getReceiptFile,
  checkTxnJumpOrder,
} from '@/services/0028F';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { getLangGroup, getLocaleMap, getResponseMessage, GovLang } from '@/locales/lang';
import { getBookingInfo } from '@/services/publicApi';

const px = pc('success-33d');

export interface SuccessProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  txnId: string;
  requestId: string;
  ID: string;
}

const Success = (props: SuccessProps) => {
  const intl = useIntl();
  const { className = '', txnId, requestId, ID, ...otherProps } = props;
  const [transId, setTransId] = useState(ID);
  const [tipMsg, setTipMsg] = useState('');
  const [completeData, setCompleteData] = useState<getExTxnCompleteDataRes>(
    {} as getExTxnCompleteDataRes,
  );
  const [download, setDownload] = useState<boolean>(false);
  const [pendingApprovalCodeDto, setPendingApprovalCodeDto] = useState<sysCodesByCodeTypeOpt | undefined>(undefined);
  const requestTimes = useRef<number>(0);

  const func = async () => {
    const res = await getExTxnCompleteData(txnId || requestId);
    if (!res) return;

    if (res?.code === 'W-8888') {
      setTimeout(() => {
        fetchExTxnCompleteData();
      }, 1000);
      return;
    }

    if (res && res.code === '0') {
      setCompleteData(res?.data ?? {});
      requestTimes.current = 0;
      setDownload(true);
    } else if (requestTimes.current < 21) {
      requestTimes.current += 1;

      setTimeout(() => {
        fetchExTxnCompleteData();
      }, 1000);
    } else if (requestTimes.current >= 21) {
      setTipMsg(getResponseMessage(res));
      setDownload(false);
    }
  };

  const handleBNU = async (transId: string) => {
    // BNU
    const res = await getTxnPendingApproval(transId);
    if (!res) return;
    if (res.code === '0') {
      setCompleteData(res.dataArr[0]);
      setDownload(true);
      setPendingApprovalCodeDto(res.dataArr[0]?.pendingApprovalCodeDto);
      return;
    }

    setTipMsg(getResponseMessage(res));
  };

  const handleBOC = async () => {
    // BOC
    const res = await completeTxnForPay(requestId);
    if (!res) return;
    if (res.code === '0') {
      await func();
      return;
    }

    setTipMsg(getResponseMessage(res));
  };
  const fetchExTxnCompleteData = async () => {
    try {
      if (txnId) {
        await func();
        return;
      }
      if (requestId) {
        const checkRes = await checkTxnJumpOrder(requestId);
        if (!checkRes) return;
        if (checkRes.code !== '0') {
          setTipMsg(getResponseMessage(checkRes));
          return;
        }
        if (checkRes.data.isShowCompletePage) {
          await handleBOC();
        }
        if (checkRes.data.isShowPendingPage) {
          setTransId(checkRes.data.transId);
          await handleBNU(checkRes.data.transId);
        }
        return;
      }

      if (transId) {
        // BNU
        await handleBNU(transId);
      }
    } catch (e: any) {
      setDownload(false);
      setTipMsg(e.message);
    }
  };

  useEffect(() => {
    fetchExTxnCompleteData();
  }, [txnId, requestId, transId]);

  function gotoIndex() {
    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  }

  async function receiptFile() {
    setTipMsg('');

    if (!txnId && !requestId) {
      return;
    }

    const ret = await getReceiptFile(txnId || requestId).catch((e) => {
      setTipMsg(e.message);
    });
    if (!ret) {
      return;
    }

    if ((ret as FetchResponse<string>).code) {
      setTipMsg(getResponseMessage(ret as FetchResponse<string>));
      return;
    }

    const blob = new Blob([ret as BlobPart], { type: 'application/pdf' });
    blob && window.open(URL.createObjectURL(blob), '_blank');
  }

  function bookingInfo(spNo: string, vehId: string) {
    getBookingInfo(spNo, vehId, getLocaleMap[getLocale()] || GovLang.繁体中文).then((res) => {
      if (!res) return;
      if (res?.code !== '0') {
        setTipMsg(getResponseMessage(res));
        return;
      }

      window.open(res?.data, '_blank');
    });
  }

  return (
    <>
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div style={{ maxWidth: '1048px', margin: 'auto' }}>
          {tipMsg ? (
            <div style={{ paddingBottom: '0.24rem' }}>
              <Alert message={tipMsg} type="error" showIcon />
            </div>
          ) : null}
        </div>
        <div className={px('body')}>
          <div className={px('successTitle')}>
            {getLangGroup(completeData.serviceTitleRespDTO?.serviceTitleCn, completeData.serviceTitleRespDTO?.serviceTitlePt, completeData.serviceTitleRespDTO?.serviceTitleEn)}
          </div>

          <div className={px('sectionBody')}>
            <div className={px('sectionTitle')}>
              {completeData.txnStatus === 'E' && (
                <>
                  <CloseCircleFilled style={{ color: '#084ab8' }} />
                  <span>{intl.formatMessage({ id: 'failed_submitted_application' })}</span>
                </>
              )}
              {['A', 'F'].includes(completeData.txnStatus || '') && (
                <>
                  <CheckCircleFilled style={{ color: '#084ab8' }} />
                  <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
                </>
              )}
              {completeData.txnStatus === 'S' && (
                <>
                  <CheckCircleFilled style={{ color: '#084ab8' }} />
                  <span>{intl.formatMessage({ id: 'bnu_pay_submitted_application' })}</span>
                </>
              )}
            </div>
            {transId ? (
              <>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'import_license_number' })}</div>
                  <div className="value">{completeData.importNoFull}</div>
                </Col>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'vehicle_level' })}</div>
                  <div className="value">
                    {getLangGroup(
                      completeData.vehTypeDescCn,
                      completeData.vehTypeDescPt,
                      completeData.vehTypeDescEn,
                    )}
                  </div>
                </Col>
              </>
            ) : (
              <>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'query_number' })}</div>
                  <div className="value">{completeData.spNo}</div>
                </Col>
                <Col>
                  <div className="label">{intl.formatMessage({ id: 'establishment_time' })}</div>
                  <div className="value">
                    {completeData.txnStatusDate
                      ? dayjs(completeData.txnStatusDate).format('YYYY-MM-DD HH:mm:ss')
                      : ''}
                  </div>
                </Col>
              </>
            )}
            <div className="success-33d-successBottomTitle" style={{textAlign:'left'}}>
              {
                pendingApprovalCodeDto ? getLangGroup(pendingApprovalCodeDto.codeCname, pendingApprovalCodeDto.codePname, pendingApprovalCodeDto.codeEname) : 
                intl.formatMessage(
                  { id: 'success_bottom_title_sst' },
                  {
                    platform: (
                      <span className={px('successBottomTitleSpan')}>
                        {intl.formatMessage({ id: 'platform_sst' })}
                      </span>
                    ),
                  },
                )
              }
            </div>
          </div>
        </div>
      </div>
      <div className={px('footer')}>
        <div className="footer-container">
          {download && (
            <Button
              type="default"
              onClick={() => {
                gotoIndex();
              }}
            >
              {intl.formatMessage({ id: 'complete' })}
            </Button>
          )}
          {download && completeData.txnStatus === 'F' && !transId && (
            <>
              <Button type="primary" onClick={receiptFile}>
                {intl.formatMessage({ id: 'download_receipt' })}
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  bookingInfo(completeData.spNo, completeData.vehId);
                }}
              >
                {intl.formatMessage({ id: 'appointment_vehicle_inspection' })}
              </Button>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Success;
