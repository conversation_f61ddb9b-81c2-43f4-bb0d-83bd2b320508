import React, { useState, useEffect, useContext } from 'react';
import pc from 'prefix-classnames';
import './Form1.less';
import { history, Location, useIntl, useLocation } from 'umi';
import { Input, Row, Col, Form, Modal, Alert } from 'antd';
import { ValidateStatus } from 'node_modules/antd/lib/form/FormItem/index';

import { getRivmByDSFM4, createEXTxn, getRivmByDSFM4WithTxnId } from '@/services/0028F';
import { getResponseMessage } from '@/locales/lang';
import EntranceInfo from '../EntranceInfo';
import SpinContext from '../../../../../../../components/SpinContext';

const px = pc('form1-28f');

type LocationQuery = {
  query: {
    txnId?: string;
    step?: number;
    importNo?: string;
    importType?: string;
    importYear?: string;
    vinPart?: string;
  };
} & Location;

export interface FormOneProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;
  setDisablePrevStep: (boolean) => void;
  setDisableNextStep: (boolean) => void;

  nextStep: () => void;

  setShowTempStore: (boolean) => void;
  setShowPrevStep: (boolean) => void;

  contentForNextStep: (number) => void;

  setEntranceInfo: (data) => void;
  entranceInfo?: getRivmByDSFM4Res;

  setCreateEXTxnParams: (data: createEXTxnParams) => void;
  createEXTxnParams: createEXTxnParams | undefined;
  setTxnId: (data) => void;
  txnId: string;
}

const FormOne = (props: FormOneProps) => {
  const intl = useIntl();
  const { query } = useLocation() as LocationQuery;
  const { className = '', ...otherProps } = props;
  const {
    step,
    contentNextStep,
    setDisableNextStep,
    setShowTempStore,
    setShowPrevStep,
    contentForNextStep,
    setEntranceInfo,
    entranceInfo,
    setCreateEXTxnParams,
    createEXTxnParams,
    setTxnId,
  } = otherProps;

  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    window.location.href = '/web/index?tabIndex=2';
    setIsModalOpen(false);
  };

  const [tipMsg, setTipMsg] = useState('');

  const [form] = Form.useForm();

  const [isSearch, setIsSearch] = useState(false);

  // 表单数据
  const [importType, setImportType] = useState('I');
  const [importNo, setImportNo] = useState('');
  const [importYear, setImportYear] = useState('');
  const [vin, setVin] = useState('');

  const [validateVinStatus, setValidateVinStatus] = useState<ValidateStatus>('');
  const [vinHelp, setVinHelp] = useState<string>('');

  const [validateYearStatus, setValidateYearStatus] = useState<ValidateStatus>('');
  const [yearHelp, setYearHelp] = useState<string>('');

  const [validateImportType, setValidateImportType] = useState<ValidateStatus>('');
  const [importTypeHelp, setImportTypeHelp] = useState<string>('');
  const { setLoading } = useContext(SpinContext);
  useEffect(() => {
    if (
      importNo.length &&
      importYear.length &&
      parseInt(importYear) >= 1900 &&
      vin.length &&
      vin.length === 4
    ) {
      setDisableNextStep(false);
    } else {
      setDisableNextStep(true);
    }
  }, [importNo, importYear, vin]);

  useEffect(() => {
    if (isSearch) {
      setDisableNextStep(false);
    } else {
      setDisableNextStep(true);
    }
  }, [isSearch]);

  const setEntranceInfoData = async () => {
    if (query && step == 2) {
      const postData = {
        importNo: query.importNo!,
        importType: query.importType!,
        importYear: query.importYear!,
        vin: query.vinPart!,
      };
      const ret = await getRivmByDSFM4(postData).catch((e) => {
        setTipMsg(e.message);
      });
      if (!ret) {
        return;
      }
      setEntranceInfo(ret.data);
      setDisableNextStep(false);
      setCreateEXTxnParams({
        importNo: query.importNo!,
        importType: query.importType!,
        importYear: query.importYear!,
        vinPart: query.vinPart!,
      });
    }
  };

  useEffect(() => {
    // showModal()
    if (history.location.query?.detail === 'true') {
      setIsSearch(true);
    }
    contentForNextStep(1);
    setShowPrevStep(false);
    setShowTempStore(false);
    return () => {
      contentForNextStep(0);
      setShowPrevStep(true);
      setShowTempStore(true);
    };
  }, []);

  useEffect(() => {
    if (props.txnId) {
      setLoading(true)
      getRivmByDSFM4WithTxnId(props.txnId)
        .then((ret) => {
          if (!ret) {
            return;
          }
          if (ret.code === '0') {
            setEntranceInfo(ret.data);
            setDisableNextStep(false);
          } else {
            setTipMsg(getResponseMessage(ret));
          }
        })
        .catch((e) => {
          setTipMsg(e.message);
        }).finally(()=>{
          setLoading(false)
        });
    }
    !props.txnId && !query.txnId && setEntranceInfoData();
  }, [props.txnId]);

  useEffect(() => {
    if (step === 1 && contentNextStep > 0) {
      const fn = async () => {
        setTipMsg('');

        const ret = await getRivmByDSFM4({
          importType,
          importNo,
          importYear,
          vin,
        }).catch((e) => {
          setTipMsg(e.message);
        }).finally(()=>{
          setDisableNextStep(false)
          setLoading(false)
        });
        if (!ret) {
          return;
        }
        if (ret.code === '0') {
          setCreateEXTxnParams({
            importType,
            importNo,
            importYear,
            vinPart: vin,
          });
          setEntranceInfo(ret.data);
          contentForNextStep(2);
        } else {
          setTipMsg(getResponseMessage(ret));
        }
      };
      fn();
    } else if (step === 2) {
      if (contentNextStep > 0) {
        const fn = async () => {
          setTipMsg('');

          if (props.txnId) {
            contentForNextStep(2);
            return;
          }

          if (!createEXTxnParams) {
            return;
          }

          const ret = await createEXTxn(createEXTxnParams).catch((e) => {
            setTipMsg(e.message);
          });
          if (!ret) {
            return;
          }
          if (ret.code === '0') {
            setTxnId(ret.data.txnId);
            contentForNextStep(2);
          } else {
            setTipMsg(getResponseMessage(ret));
            if (ret.code === 'E-0004') {
              setDisableNextStep(true);
            }
          }
        };
        fn();
      } else {
        contentForNextStep(1);
      }
    }
  }, [step, contentNextStep]);

  useEffect(() => {
    setShowPrevStep(step !== 1);
  }, [step]);

  return (
    <>
      {tipMsg && (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        {step == 1 ? (
          <>
            <div className={`${px('sectionTitle')}`}>
              {intl.formatMessage({ id: 'apply_information' })}
            </div>
            <div className="step-root">
              <div className="my-card-container">
                <Form form={form} className={px('form')} layout="vertical">
                  <div className="my-card-item">
                    <div className="my-card-title">
                      {intl.formatMessage({ id: 'import_license_information' })}
                    </div>
                    <div className="my-card-body">
                      <Form.Item
                        label={`${intl.formatMessage({
                          id: 'enter_import_license_number',
                        })}（${intl.formatMessage({ id: 'format' })}：I/123456/2025）`}
                        required
                        rules={[{ required: true, message: 'please_enter' }]}
                      >
                        <Row>
                          <Col span={6}>
                            <Form.Item
                              validateStatus={validateImportType}
                              help={importTypeHelp}
                              hasFeedback={!!vinHelp}
                            >
                              <Input
                                placeholder="I"
                                value={importType}
                                readOnly
                                onChange={(e) => {
                                  const { value } = e.target;
                                  if (value.length > 2 || !/^[A-Z]*$/.test(value)) {
                                    setValidateImportType('error');
                                    setImportTypeHelp(
                                      value.length > 2
                                        ? intl.formatMessage({ id: 'enter_max_n' }, { n: 2 })
                                        : intl.formatMessage({ id: 'enter_only_char' }),
                                    );
                                  } else {
                                    setValidateImportType('');
                                    setImportTypeHelp('');
                                  }
                                  setImportType(e.target.value);
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={1}>
                            <span className="span-t">/</span>
                          </Col>
                          <Col span={8}>
                            <Input
                              placeholder={intl.formatMessage({ id: 'import_license_number' })}
                              maxLength={6}
                              value={importNo}
                              onChange={(e) => {
                                setImportNo(e.target.value.replace(/[^\d.-]/g, ''));
                              }}
                            />
                          </Col>
                          <Col span={1}>
                            <span className="span-t">/</span>
                          </Col>
                          <Col span={8}>
                            <Form.Item
                              validateStatus={validateYearStatus}
                              help={yearHelp}
                              hasFeedback={!!yearHelp}
                            >
                              <Input
                                placeholder={intl.formatMessage({ id: 'import_license_year' })}
                                maxLength={4}
                                value={importYear}
                                onChange={(e) => {
                                  const value = e.target.value.replace(/[^\d.-]/g, '');

                                  setImportYear(value);
                                  if (value.length < 4) {
                                    setValidateYearStatus('');
                                    setYearHelp('');
                                    return;
                                  }

                                  const year = parseInt(value);
                                  if (year < 1900) {
                                    setValidateYearStatus('error');
                                    setYearHelp(intl.formatMessage({ id: 'enter_year_min' }));
                                    return;
                                  }

                                  setValidateYearStatus('');
                                  setYearHelp('');
                                }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Form.Item>

                      <Form.Item
                        label={intl.formatMessage({ id: 'vin_4' })}
                        required
                        rules={[
                          { required: true, message: intl.formatMessage({ id: 'enter_vin_4' }) },
                          {
                            pattern: /^[a-zA-Z0-9]*$/,
                            message: intl.formatMessage({ id: 'enter_only_number_char' }),
                          },
                        ]}
                        validateStatus={validateVinStatus}
                        help={vinHelp}
                        hasFeedback={!!vinHelp}
                      >
                        <Input
                          style={{ width: '100%' }}
                          value={vin}
                          maxLength={4}
                          onChange={(e) => {
                            const { value } = e.target;
                            if (value.length < 4) {
                              setValidateVinStatus('error');
                              setVinHelp(intl.formatMessage({ id: 'enter_min_n' }));
                              setVin(value);
                              return;
                            }

                            if (value.length > 4 || !/^[a-zA-Z0-9]*$/g.test(value)) {
                              setValidateVinStatus('error');
                              setVinHelp(
                                value.length > 4
                                  ? intl.formatMessage({ id: 'enter_max_n' }, { n: 4 })
                                  : intl.formatMessage({ id: 'enter_only_number_char' }),
                              );
                            } else {
                              setValidateVinStatus('');
                              setVinHelp('');
                            }
                            setVin(value);
                          }}
                        />
                      </Form.Item>
                    </div>
                  </div>
                </Form>
              </div>
            </div>

            <Modal
              title={intl.formatMessage({ id: 'warm_reminder' })}
              cancelText={intl.formatMessage({ id: 'exit' })}
              okText={intl.formatMessage({ id: 'continue' })}
              visible={isModalOpen}
              onOk={handleOk}
              onCancel={handleCancel}
            >
              <p>
                {intl.formatMessage({ id: 'application_notice' })}
                ...
              </p>
            </Modal>
          </>
        ) : (
          <EntranceInfo data={entranceInfo} />
        )}
      </div>
    </>
  );
};

export default FormOne;
