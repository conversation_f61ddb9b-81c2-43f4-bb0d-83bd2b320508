import React, { useState, useEffect, useContext } from 'react';
import pc from 'prefix-classnames';
import './UploadFile.less';
import { File } from '@gov-mo/mpaas-js-bridge';
import { Form, Upload, message, Divider, Alert } from 'antd';
import type { UploadFile, FormProps } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  getServiceDocs,
  serviceItemFileUpload,
  checkServiceItemFileUpload,
  deleteServiceItemFile,
} from '@/services/0028F';
import { getLangGroup, getResponseMessage } from '@/locales/lang';
import SpinContext from '../../../../../../../../components/SpinContext';

const px = pc('uploadFile-28f-mobile');

export interface FormSevenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  step: number;
  contentNextStep: number;

  nextStep: () => void;
  contentForNextStep: (number) => void;
  setDisableNextStep: (boolean) => void;
  setShowTempStore: (value: boolean) => void;

  txnId: string | undefined;
}

const FormSeven = (props: FormSevenProps) => {
  const { className = '', setDisableNextStep, ...otherProps } = props;
  const { contentNextStep, contentForNextStep, setShowTempStore, txnId } = otherProps;

  const [tipMsg, setTipMsg] = useState('');
  const [titleInfo, setTitleInfo] = useState<string>('');

  const [form] = Form.useForm();

  const [list, setList] = useState<getServiceDocsRes[]>([]);
  const [uploadfileList, setUploadfileList] = useState<Record<string, UploadFile[]>>();
  const [fileList, setFileList] = useState<{ [key: string]: UploadFile }>();
  const { setLoading } = useContext(SpinContext);
  const getUploadfileList = () => {
    return uploadfileList;
  };

  const getFileList = () => {
    return fileList;
  };

  const onFinish: FormProps['onFinish'] = async (values) => {
    setTipMsg('');

    for (const key in list) {
      const item = (list || [])[key];
      const file = (uploadfileList || [])[`${item?.serviceDocumentID}`];

      console.log('item', item, file);

      if (item?.category === 0 && (!file || file.length === 0)) {
        setTipMsg(`${item.nameZh}`);
        return;
      }
    }

    if (!txnId) {
      return;
    }

    const ret = await checkServiceItemFileUpload(txnId).catch((e) => setTipMsg(e.message));
    if (!ret) {
      return;
    }
    if (ret.code === '0') {
      contentForNextStep(2);
    } else {
      setTipMsg(getResponseMessage(ret));
    }
  };

  const onFinishFailed: FormProps['onFinishFailed'] = (errorInfo) => {
    console.log('onFinishFailed', errorInfo);
  };

  const handleServiceDocs = async () => {
    if (!txnId) {
      setDisableNextStep(false);
      setLoading(false);
      return;
    }
    try {
      const ret = await getServiceDocs(txnId);
      if (ret?.code === '0') {
        const { dataArr, data } = ret;
        setList(dataArr);
        setTitleInfo(getLangGroup(data?.docTitleCn, data?.docTitlePt, data?.docTitleEn));

        const uploadList = (dataArr || []).reduce((acc: any, current: any) => {
          const key = `${current.serviceDocumentID}`;
          if (!acc[key]) {
            acc[key] = [];
          }

          current?.originalFileName &&
            acc[key].push({
              name: current?.originalFileName,
              category: current?.category,
              uid: `${current?.serviceDocumentID}`,
              url: current?.uploadFileUrl,
            });
          return acc;
        }, {});

        console.log('uploadList', uploadList);
        setUploadfileList(uploadList);
        return;
      }
      setTipMsg(getResponseMessage(ret));
    } catch (e: any) {
      setTipMsg(e.message);
    } finally {
      setDisableNextStep(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    handleServiceDocs().then();
  }, [txnId]);

  useEffect(() => {
    contentForNextStep(1);
    setShowTempStore(false);
    setDisableNextStep(true);
    setLoading(true);
    return () => {
      contentForNextStep(0);
    };
  }, []);

  useEffect(() => {
    if (contentNextStep > 0) {
      form.submit();
    }
  }, [contentNextStep]);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url) {
      return;
    }
    // 开发环境使用 https://appdev.dsat.gov.mo/ 域名拼接，其他环境使用当前域名。
    const currentDomain =
      process.env.NODE_ENV === 'development'
        ? 'https://appdev.dsat.gov.mo/'
        : `${window.location.origin}/`;
    const url = `${currentDomain}${file.url}`;
    const res = await File.downloadFileByType({ downloadType: 'url', downloadParam: url });
    const { success, data } = res;
    if (success) {
      console.log(data);
    }
  };

  return (
    <>
      {tipMsg && (
        <div style={{ padding: '0.2rem 0.24rem 0' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <Form
          form={form}
          className={px('form')}
          layout="vertical"
          preserve={false}
          scrollToFirstError={true}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
        >
          <div className={px('sectionTitle')}>{titleInfo}</div>
          <div className={px('sectionBody')}>
            {list.map((item, index) => (
              <Form.Item
                label={`${index + 1}. ${item.nameZh}`}
                name={`${item.serviceDocumentID}`}
                required={item?.category === 0}
              >
                <Upload
                  showUploadList={{
                    showPreviewIcon: true,
                    showRemoveIcon: true,
                  }}
                  onDownload={(file) => {
                    console.log('onDownload', file);
                  }}
                  onPreview={handlePreview}
                  // defaultFileList={((item?.originalFileName && [{name: item?.originalFileName }]) || []) as UploadFile[]}
                  fileList={(uploadfileList || [])[`${item.serviceDocumentID}`]}
                  beforeUpload={(file) => {
                    const uploadfileList = getUploadfileList();
                    const fileList = getFileList();
                    let data = { ...uploadfileList };
                    data[String(item.serviceDocumentID)] = [
                      {
                        uid: String(item.serviceDocumentID),
                        name: file.name,
                        status: 'uploading',
                      },
                    ];
                    setUploadfileList(data);
                    let files = { ...fileList };
                    files[String(item.serviceDocumentID)] = file;
                    setFileList(files);
                    return false;
                  }}
                  onRemove={(file) => {
                    deleteServiceItemFile(txnId as string, item?.serviceDocumentID);
                  }}
                  onChange={(e) => {
                    const uploadfileList = getUploadfileList();
                    const fileList = getFileList();
                    if ('removed' === e.file.status) {
                      let data = { ...uploadfileList };
                      data[e.file.uid] = [];
                      setUploadfileList(data);
                      let files = { ...fileList };
                      delete files[e.file.uid];
                      setFileList(files);
                      return;
                    }
                    if (!txnId) {
                      return;
                    }
                    const file = e.fileList[0];
                    console.log('e', e);
                    console.log('e.fileList[1]', e.fileList[1]);
                    console.log('e.fileList[1] originFileObj', e.fileList[1]?.originFileObj);
                    console.log('e.file', e.file);
                    console.log('fileList', fileList);
                    if (!(fileList && fileList[file.uid])) {
                      return;
                    }
                    const formData = new FormData();
                    formData.append('txnId', txnId);
                    formData.append('code', item.code ?? '');
                    formData.append('spServiceDocId', `${item.serviceDocumentID}`);
                    formData.append('nameZh', item.nameZh);
                    formData.append('namePt', item.namePt);
                    formData.append('file', fileList[file.uid] as any);
                    serviceItemFileUpload(
                      {
                        txnId,
                        code: item.code,
                        spServiceDocId: item.serviceDocumentID,
                        nameZh: item.nameZh,
                        namePt: item.namePt,
                      },
                      formData,
                    )
                      .then((ret) => {
                        const uid = String(item.serviceDocumentID);
                        const uploadfileList = getUploadfileList();
                        let data = { ...uploadfileList };
                        if (!(data[uid] && data[uid].length)) {
                          return;
                        }
                        let f = data[uid][0];
                        console.log('ret===', ret);
                        if (ret.code !== '0') {
                          f.status = 'error';
                          setTipMsg(getResponseMessage(ret));
                        } else {
                          f.status = 'done';
                          //f.url = ret.data.url
                        }
                        data[uid] = [f];
                        setUploadfileList(data);
                        handleServiceDocs();
                      })
                      .catch((e) => {
                        message.error(e.message);
                        const uid = String(item.serviceDocumentID);
                        const uploadfileList = getUploadfileList();
                        let data = { ...uploadfileList };
                        if (!(data[uid] && data[uid].length)) {
                          return;
                        }
                        let f = data[uid][0];
                        f.status = 'error';
                        data[uid] = [f];
                        setUploadfileList(data);
                      });
                  }}
                >
                  <div className="upload-box">
                    <PlusOutlined style={{ fontSize: 28, color: '#bfbfbf' }} />
                  </div>
                </Upload>
                <Divider />
              </Form.Item>
            ))}
          </div>
        </Form>
      </div>
    </>
  );
};

export default FormSeven;
