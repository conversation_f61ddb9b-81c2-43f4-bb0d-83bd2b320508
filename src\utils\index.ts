import { Page } from '@gov-mo/mpaas-js-bridge';

/**
 * 身份证校验
 * @param {*} str
 */

export const valideIDCard = (str) => {
  // 澳门&香港 例：H1234567890
  const Macao = /^([A-Z]\d{6,10}(\(\w{1}\))?)$/;
  // 台湾 例： 1234567890B
  const Taiwan = /^\d{8}|^[a-zA-Z0-9]{10}|^\d{18}$/;
  // 1.大陆，可含X
  // eslint-disable-next-line max-len
  const Dalu = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/;

  return Macao.test(str) || Taiwan.test(str) || Dalu.test(str);
};

/**
 * 手機號校驗
 * @param {*} str
 */
export const validePhone = (str) => /^1[3|4|5|7|8][0-9]{9}$/.test(str);

/**
 * 密碼校驗 6-16 數字或字母
 * @param {*} str
 */
export const validePassword = (str) => /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/.test(str);

/**
 * 邮箱地址校验
 * @param {*} str
 */
export const valideEmail = (str) =>
  /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/.test(str);

/**
 * 获取tree深度
 * @param {array} root 节点
 * @param {number} deep 当前深度
 * @param {string} field children字段
 */
export const getTreeDeep = (root, deep = 0, field = 'children') => {
  if (!root?.length) {
    return deep;
  }
  return Math.max.apply(
    null,
    root.map((child) => getTreeDeep(child?.[field], deep + 1, field)),
  );
};

export const isIPad = () => {
  return /iPad/i.test(navigator.userAgent);
};

/**
 * 获取设备类型
 */
function getDeviceType() {
  const ua = navigator.userAgent.toLowerCase();

  // 判断是否为平板
  const isiPad = ua.includes('ipad');
  const isAndroidTablet = ua.includes('android') && !ua.includes('mobile');

  if (isiPad || isAndroidTablet) {
    return 'tablet';
  }

  // 判断是否为手机
  const isMobile = ua.includes('mobi') || ua.includes('iph') || ua.includes('480'); // 涵盖 'mobile' 或类似标识
  if (isMobile) {
    return 'phone';
  }

  // 默认视为桌面
  return 'desktop';
}

export const isMobile = () => {
  // 判断是否为平板
  if (getDeviceType() === 'tablet') {
    // 如果是平板判断是否为app,如果是app返回true,不是返回false,app可能注入的ua: env=uat type=Business mPaaSClient
    return /(env=|mPaaSClient)/i.test(navigator.userAgent);
  }
  return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i.test(
    navigator.userAgent,
  );
};

export const isIOS = () => {
  const ua = window.navigator.userAgent.toLocaleLowerCase();
  return /iphone|ipad|ipod/.test(ua);
};

/**
 * 相对路径转绝对路径
 * @param {string} url 相对路径
 */
const relative2absolute = function (url: string) {
  if (url.startsWith('/')) {
    return `${window.location.origin}${window.routerBase}${url.substring(1)}`;
  }
  return new URL(url, window.location.href).href;
};

/**
 * 经封装的Page.openPage方法
 * @param {string} router 相对路径
 */
export const openPageRelativePath = (route, options = {}) => {
  Page.openPage({
    path: relative2absolute(route),
    options: { hideNavigationBar: false, ...options },
  });
};

export const formatName = (name) => {
  if (!name) return ['', ''];
  if (name.includes(',')) {
    const [lastName = '', firstName = ''] = name.split(',');
    return [lastName, firstName];
  }
  return [name.slice(0, 1), name.slice(1)];
};

// 外文名
export const formatForeignName = (name) => {
  if (!name) return ['', ''];
  // 优先逗号分隔
  if (name.includes(',')) {
    const [lastName = '', firstName = ''] = name.split(',');
    return [lastName, firstName];
  }
  // 空格分隔
  const index = name.indexOf(' ');
  if (index !== -1) {
    return [name.slice(index + 1)?.trim(), name.slice(0, index)];
  }
  // 仅单一单词
  return [name, ''];
};

export const delUrlParams = (paramKey) => {
  let url = window.location.href;
  const urlParam = window.location.search.substr(1); // 页面参数
  const beforeUrl = url.substr(0, url.indexOf('?')); // 页面主地址（参数之前地址）
  let nextUrl = '';
  const arr: string[] = [];
  if (urlParam !== '') {
    const urlParamArr = urlParam.split('&'); // 将参数按照&符分成数组
    for (let i = 0; i < urlParamArr.length; i++) {
      const paramArr = urlParamArr[i].split('='); // 将参数键，值拆开
      // 如果键与要删除的不一致，则加入到参数中
      if (!paramKey.includes(paramArr[0])) {
        arr.push(urlParamArr[i]);
      }
    }
  }
  if (arr.length > 0) {
    nextUrl = `?${arr.join('&')}`;
  }
  url = beforeUrl + nextUrl;
  return url;
};

export const handleContactName = (data: getTxnContactRes, split: string = ' ') => {
  if (!data) {
    return '';
  }

  let name = `${data.contactLastNameCn || ''}${data.contactFirstNameCn || ''}`;
  let namePt = `${data.contactLastNamePt || ''} ${data.contactFirstNamePt || ''}`;

  if (name && !namePt) {
    return name;
  }
  if (!name && namePt) {
    return namePt;
  }

  return `${name}${split}${namePt}`;
};
export function hexToRgba(hex, alpha = 1) {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}