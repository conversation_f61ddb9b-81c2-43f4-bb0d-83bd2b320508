import React, { useState, useContext } from 'react';
import { InputItem } from 'antd-mobile';
import { Input } from 'antd';
import classPrefix from '@ali-whale/class-prefix';
import FormContext from '../FormContext';
import './DoubleText.less';

const DoubleText = (props) => {
  const {
    id,
    placeholder = '',
    description,
    value = [undefined, undefined],
    onChange,
    ...otherProps
  } = props;
  const [value1, value2] = value;
  const [_value, setValue] = useState(value);
  const { mobile, isDetail } = useContext(FormContext);
  const px = mobile ? classPrefix('cbd-mobile-base-form') : classPrefix('cbd-web-base-form');
  const Text = mobile ? InputItem : Input;

  if (isDetail) {
    const [_value1 = '', _value2 = ''] = value;
    return <div>{`${_value1}/${_value2}`}</div>;
  }

  return (
    <div className={px('text')}>
      <div className={px('double-text-wrapper')}>
        <Text
          id={`${id}_1`}
          className={px('text-value')}
          placeholder={placeholder}
          {...otherProps}
          value={value1}
          onChange={(e) => {
            const newValue = [..._value];
            newValue[0] = mobile ? e : e.target.value;
            setValue(newValue);
            onChange(newValue);
          }}
        />
        <div className={px('double-text-Symbol')}>/</div>
        <Text
          id={`${id}_2`}
          className={px('text-value')}
          placeholder={placeholder}
          {...otherProps}
          value={value2}
          onChange={(e) => {
            const newValue = [..._value];
            newValue[1] = mobile ? e : e.target.value;
            setValue(newValue);
            onChange(newValue);
          }}
        />
      </div>
      {description && <span className={px('text-describe')}>{description}</span>}
    </div>
  );
};

export default DoubleText;
