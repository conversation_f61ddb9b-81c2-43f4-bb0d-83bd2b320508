import { Login, Infos, Storage } from '@gov-mo/mpaas-js-bridge';

export default {
  namespace: 'user',
  state: {
    logined: undefined,
    profile: undefined,
    gxToken: '', // 国信token
    osType: 0,
  },
  effects: {
    *isLogin({ forceUpdate = false }, { put, select }) {
      const { logined } = yield select(x => x.user);
      if (logined !== undefined && !forceUpdate) return logined;
      const { success, data } = yield Login.isLogin();
      if (success && logined !== data) {
        yield put({ type: 'update', logined: data });
      }
      return data;
    },
    *requestProfile({ forceUpdate = false }, { put, select }) {
      const { profile } = yield select(x => x.user);
      if (profile !== undefined && !forceUpdate) return profile;
      const { success, data } = yield Infos.requestProfile();
      if (success) {
        yield put({ type: 'update', profile: data });
      }
      return data;
    },
    *logout(_, { put }) {
      yield put({ type: 'update', profile: undefined, logined: undefined });
      yield Storage.save('gxToken', '');
      yield Storage.save('userType', 'Personal');
    },
  },
  reducers: {
    update: (state, { type, ...newState }) => ({
      ...state,
      ...newState,
    }),
  },
};
