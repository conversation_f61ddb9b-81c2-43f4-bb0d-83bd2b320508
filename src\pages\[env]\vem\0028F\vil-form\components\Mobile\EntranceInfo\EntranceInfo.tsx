import React from 'react';
import classPrefix from '@ali-whale/class-prefix';
import { Descriptions } from 'antd';

import './EntranceInfo.less';
import { useIntl } from 'umi';
import { getLangGroup } from '@/locales/lang';

const px = classPrefix('batchSuccess-28f-mobile');

export interface EntranceInfoProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  data?: getRivmByDSFM4Res;
}

const EntranceInfo = (props: EntranceInfoProps) => {
  const intl = useIntl();
  const { data } = props;

  return (
    <div className={px('entranceInfoRoot')}>
      {/* <div className={`${px('formTitle')}`} style={{ marginLeft: '-16px', paddingBottom: '26px' }}>
        {intl.formatMessage({ id: 'apply_information' })}
      </div> */}
      <div className="my-card-container">
        <div className="my-card-item">
          <div className="my-card-title">
            {intl.formatMessage({ id: 'import_license_information' })}
          </div>
          <div className="my-card-body">
            <Descriptions column={1}>
              <Descriptions.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                {data?.importNoFull}
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'vin' })}>
                {data?.vin}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </div>

        <div className="my-card-item">
          <div className="my-card-title">
            {intl.formatMessage({ id: 'vehicle_tax_information' })}
          </div>
          <div className="my-card-body">
            <Descriptions column={1}>
              <Descriptions.Item label={intl.formatMessage({ id: 'dsf_ref_no' })}>
                {data?.dsfRefNo}
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'dsf' })}>
                <span style={{ fontWeight: 700, color: '#13a07b' }}>
                  {getLangGroup(data?.dataPagaCn, data?.dataPagaPt, data?.dataPagaEn)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'm_4_date' })}>
                {data?.dataEnt}
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'exemption_regulations' })}>
                {data?.isencaoLei}
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'tax_exemption_start_date' })}>
                {data?.isencaoDateInit}
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'tax_exemption_end_date' })}>
                {data?.isencaoDateFim}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </div>

        <div className="my-card-item">
          <div className="my-card-title">
            {intl.formatMessage({ id: 'model_approval_documents' })}
          </div>
          <div className="my-card-body">
            <Descriptions column={1}>
              <Descriptions.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                <span className="textColor">{data?.vtaNoFull}</span>
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                <span className="textColor">{getLangGroup(data?.vehTypeDescCN, data?.vehTypeDescPT, data?.vehTypeDescEN)}</span>
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                <span className="textColor">{getLangGroup(data?.vehCategoryCname, data?.vehCategoryPname, data?.vehCategoryEname)}</span>
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'brand' })}>
                <span className="textColor">{getLangGroup(data?.vehBrandCodeCname, data?.vehBrandCodePname, data?.vehBrandCodeEname)}</span>
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'style' })}>
                <span className="textColor">{data?.vehModel}</span>
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'style_year' })}>
                <span className="textColor">{data?.vehModelYear}</span>
              </Descriptions.Item>
              <Descriptions.Item label={intl.formatMessage({ id: 'vehicle_origin' })}>
                <span className="textColor">{getLangGroup(data?.vehBuildCtryCodeCname, data?.vehBuildCtryCodePname, data?.vehBuildCtryCodeEname)}</span>
              </Descriptions.Item>
            </Descriptions>
          </div>
        </div>
      </div>
    </div>
  );
};
export default EntranceInfo;
