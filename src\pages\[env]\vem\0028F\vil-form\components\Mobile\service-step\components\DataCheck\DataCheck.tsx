import usePersistFunc from '@/layouts/usePersistFunc';
import React from 'react';
import { useSelector } from 'react-redux';
import type { receiveResultModelsState, ApplicantInformationsModelsState } from 'umi';
import { stepType } from '../../config';
import type { StepInfoType } from '../../types/defs';
import ApplicantInformations from '../ApplicantInformations';
import FormInfo from '../FormInfo';
import ReceiveResult from '../ReceiveResult';
import UploadFile from '../UploadFile';
import styles from './DataCheck.less';

interface DataCheckProps {
  needShowStepList: StepInfoType[];
  mobile: boolean;
}

const DataCheck = (props: DataCheckProps) => {
  const { needShowStepList, mobile } = props;
  const {
    receiveResultModels: { hasForm },
    applicantInformationsModels: { hasApplicantInformations },
  } = useSelector(
    ({
      receiveResultModels,
      applicantInformationsModels,
    }: {
      receiveResultModels: receiveResultModelsState;
      applicantInformationsModels: ApplicantInformationsModelsState;
    }) => ({ receiveResultModels, applicantInformationsModels }),
  );
  // 渲染對應步驟組件
  const renderStep: (currentStep: StepInfoType) => React.ReactNode = usePersistFunc(
    (currentStep: StepInfoType) => {
      let res: React.ReactNode;
      switch (currentStep.stepType) {
        case stepType.form:
          res = <FormInfo isDetail mobile={mobile} />;
          break;
        case stepType.resultReceive:
          res = <ReceiveResult hasForm={hasForm} title={currentStep.stepName} isDetail />;
          break;
        case stepType.upload:
          res = <UploadFile title={currentStep.stepName} isDetail />;
          break;
        default:
          res = null;
      }
      return res;
    },
  );

  return (
    <div className={styles.root}>
      {hasApplicantInformations && <ApplicantInformations mobile={mobile} />}
      {needShowStepList.map(item => renderStep(item))}
    </div>
  );
};

export default DataCheck;
