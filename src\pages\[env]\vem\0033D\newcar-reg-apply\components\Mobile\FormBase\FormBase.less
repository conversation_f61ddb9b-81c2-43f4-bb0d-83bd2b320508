@prefix: page-33d-mobile;

.@{prefix} {
  &- {
    &root {
      display: flex;
      flex-direction: column;
      padding-top: 45px;
      border-bottom: 100px solid #eeeeee;
      overflow-y: auto;
      background-color: #ffffff;

      font-size: 16px;
      .ant-form {
        font-size: 16px;
      }
      .ant-form-item-label > label {
        font-size: 16px;
      }
      .ant-select {
        font-size: 16px;
      }
      .ant-input {
        font-size: 16px;
      }
    }

    &title {
      color: #363636;
      line-height: 21px;
      text-align: center;
      font-weight: 700;
      padding: 0 0.24rem 0.2rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      font-size: 16px;
      font-weight: 400;
    }

    &stepinfo {
      flex: 1;
      flex-direction: column;
      display: flex;
    }
    &step {
      min-width: -webkit-fit-content;
      min-width: -moz-fit-content;
      min-width: fit-content;
    }

    &step h3 {
      font-size: 0.16rem;
      line-height: 0.22rem;
      font-weight: 700;
      padding: 0 0.02rem 0.16rem;
    }

    &step .ant-steps-small {
      font-size: 0.16rem;
    }

    &step .ant-steps-small .ant-steps-item {
      height: 0.5rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon {
      background-color: rgba(8, 74, 184, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-icon {
      background-color: @brand-primary;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-finish .ant-steps-item-tail:after {
      background-color: @brand-primary;
      height: 0.2rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-icon {
      background-color: rgba(255, 193, 7, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-icon {
      background-color: #ffc107;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-process .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 0.3rem;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-icon {
      background-color: hsla(0, 0%, 76.9%, 0.25);
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-icon {
      background-color: #c4c4c4;
    }

    &step .ant-steps-small .ant-steps-item.ant-steps-item-wait .ant-steps-item-tail:after {
      background-color: #c4c4c4;
      height: 0.3rem;
    }

    &step
      .ant-steps-small
      .ant-steps-item.ant-steps-item-wait:last-child
      .ant-steps-item-tail:after {
      display: none;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container {
      height: 0.4rem;
      min-height: 0.4rem;
      display: flex;
      align-items: center;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-tail {
      bottom: 0;
      top: 0.35rem;
      left: 0.14rem;
      padding: 0;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-icon {
      width: 0.28rem;
      height: 0.28rem;
      border-radius: 50%;
      margin-right: 0.06rem;
      cursor: pointer;
      flex-shrink: 0;
    }

    &step
      .ant-steps-small
      .ant-steps-item
      .ant-steps-item-container
      .ant-steps-item-icon
      .ant-steps-icon {
      height: 0.18rem;
      min-height: 0.18rem;
      display: flex;
      align-items: center;
      width: 0.18rem;
      line-height: 0.18rem;
      left: 0.05rem;
      top: 0.05rem;
      font-size: 0.14rem;
      justify-content: center;
      color: #fff;
      border-radius: 50%;
    }

    &step .ant-steps-small .ant-steps-item .ant-steps-item-container .ant-steps-item-content {
      height: 0.4rem;
      min-height: 0.4rem;
      display: flex;
      align-items: center;
      line-height: 0.16rem;
    }

    &step
      .ant-steps-small
      .ant-steps-item
      .ant-steps-item-container
      .ant-steps-item-content
      .ant-steps-item-title {
      font-size: 0.16rem;
      color: #333;
    }

    &mobile-step {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 25px;
      .step-item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 33px;
        height: 33px;
        font-size: 14px;
        color: #ffffff;
        background-color: rgba(193, 193, 193, 0.25);
        border-radius: 50%;
        span {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 25px;
          height: 25px;
          background-color: #c1c1c1;
          border-radius: 50%;
        }
        &.finish {
          background-color: rgba(8, 74, 184, 0.25);
          span {
            background-color: @brand-primary;
          }
        }
        &.progress {
          background-color: rgba(255, 193, 7, 0.25);
          span {
            background-color: #ffc107;
          }
        }
      }
      .line {
        flex: 1;
        margin: 0 16px;
        min-width: 0.1rem;
        max-width: 1rem;
        height: 2px;
        background-color: #c1c1c1;
      }
    }

    &form {
      display: flex;
      flex-direction: column;
      margin-top: 0;
      background-color: #eeeeee;
      flex: 1;
    }

    &form h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 700;
      margin-left: 24px;
    }

    &form > .pc-pay-ui-root {
      margin-left: 24px;
    }

    &form .pc-pay-ui-top-amount {
      line-height: 65px;
    }

    &form .pc-pay-ui-top-amount span {
      position: relative;
    }

    &form .pc-pay-ui-top-amount span:first-child {
      font-size: 16px;
      color: #666;
      top: -2px;
    }

    &form .pc-pay-ui-top-amount span:nth-child(2) {
      font-size: 24px;
      top: -2px;
    }

    &formTitle {
      display: flex;
      align-items: center;
      font-weight: 700;
      margin-left: 24px;
      font-size: 18px;
      color: #232323;
      padding-bottom: 10px;
    }

    &formTitle:before {
      display: block;
      content: '';
      width: 4px;
      height: 16px;
      background: @brand-primary;
      margin-right: 8px;
    }

    &spin {
      width: 100%;
      height: 100%;
      padding: 45%;
    }

    &footer-button {
      position: fixed;
      z-index: 1;
      bottom: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 20px;
      background: #fff;
      box-shadow: 0 -0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
      height: 100px;
      gap: 4%;
      .ant-btn {
        flex: 1;
        height: 52px;
        line-height: 52px;
        border-radius: 26px;
        font-size: 16px;
        padding: 0 5px;
        &.ant-btn-default {
          background: #fff;
          color: @brand-primary;
          border: 1px solid @brand-primary;
        }
      }
    }
  }
}
.am-navbar {
  position: fixed;
  top: 0;
  width: 100%;
  color: #000000;
  padding-bottom: 10px;
  height: auto;
  .select {
    position: relative;
    margin-right: 10px;
    padding: 4px 8px;
    line-height: 1em;
    border: 1px solid #ccc;
    border-radius: 30px;
    color: #666;
    font-size: 14px;
    white-space: nowrap;
    box-shadow: 0px 0 5px 0 rgba(0, 0, 0, 0.06);
    .opt-wrap {
      position: absolute;
      z-index: 1;
      left: 50%;
      top: 100%;
      margin-top: 5px;
      padding: 15px;
      border-radius: 5px;
      background-color: #ffffff;
      box-shadow: 0px 0 5px 0 rgba(0, 0, 0, 0.1);
      transform: translate(-50%, 0);

      font-size: 16px;
      .item {
        display: flex;
        align-items: center;
        white-space: nowrap;
        .serial {
          margin-right: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 25px;
          height: 25px;
          background-color: #c1c1c1;
          border-radius: 50%;
          color: #ffffff;
        }
        &.progress {
          color: #ffc107;
          .serial {
            background-color: #ffc107;
          }
        }
        &.finish {
          color: @brand-primary;
          .serial {
            background-color: @brand-primary;
          }
        }
      }
      .line {
        margin: 3px 11px;
        height: 20px;
        width: 2px;
        background-color: #c1c1c1;
      }
    }
  }
}

// mobile card style

.my-card-container-mobile {
  .my-card-item-mobile {
    border-radius: 0.12rem;
    margin-bottom: 0.2rem;
    &:nth-of-type(4n + 1) {
      background-color: @brand-primary;
    }
    &:nth-of-type(4n + 2) {
      background-color: #e9b745;
    }
    &:nth-of-type(4n + 3) {
      background-color: #13a07b;
    }
    &:nth-of-type(4n + 4) {
      background-color: #f33b40;
    }
    .my-card-title-mobile {
      height: 0.44rem;
      padding: 0 0.2rem;
      color: #fff;
      font-size: 0.16rem;
      display: flex;
      align-items: center;
    }
    .my-card-body-mobile {
      background: #fff;
      border: 1px solid #f0f0f0;
      padding: 0.2rem;
      border-radius: 0 0 0.12rem 0.12rem;
    }
  }
}
.step-h {
  padding: 0 12px;
  .ant-steps-small .ant-steps-item-title:after {
    top: 0;
  }
  .ant-steps-item-process
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-title:after {
    background-color: #c4c4c4;
  }
  .ant-steps-item-wait
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-title:after {
    background-color: #c4c4c4;
  }
  .ant-steps-item-finish
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-title:after {
    background-color: @brand-primary;
  }
}
