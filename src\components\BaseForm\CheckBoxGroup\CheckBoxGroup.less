@PC_CLASS_PREFIX: 'cbd-web-base-form';

@MOBILE_CLASS_PREFIX: 'cbd-mobile-base-form';

.cbd-web-base-form-checkbox {
  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }
  // 多选项 - 必填（即使是disabled也显示正常颜色）
  .mo-checkbox-wrapper-required {
    .requiredAfterMixins(1px);
    &::after {
      width: auto;
    }
    .ant-checkbox-disabled + span {
      color: var(--firstTextColor);
    }
    .ant-checkbox-disabled .ant-checkbox-inner {
      background-color: var(--primaryColor) !important;
      border-color: var(--primaryColor) !important;
    }
  }
  &-vertical {
    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
      width: 100%;
    }
  }
  // 单选
  .ant-radio-input:focus + .ant-radio-inner,
  .ant-radio-wrapper:hover .ant-radio,
  .ant-radio:hover .ant-radio-inner {
    border-color: @brand-success;
  }

  .ant-radio-input:focus + .ant-radio-inner {
    box-shadow: none;
  }

  .ant-radio {
    &-wrapper {
      margin-right: 32px;
    }
    &-inner {
      border-color: #c1c1c1;
      &::after {
        background-color: transparent;
      }
    }
    &-checked {
      &::after {
        border-color: @brand-success;
      }
      .ant-radio-inner {
        border-color: @brand-success;
        background-color: @brand-success;
        &::after {
          top: 50%;
          width: 5px;
          height: 10px;
          border: 2px solid #fff;
          border-radius: 0;
          border-left: 0;
          border-top: 0;
          transform: rotate(45deg) scale(1) translate(-50%, -50%);
        }
      }
    }
    &-disabled {
      &.ant-radio-checked {
        .ant-radio-inner {
          background-color: #8a8a8a;
          border-color: #8a8a8a !important;
        }
      }
    }
  }

  // 多选
  .ant-checkbox {
    &-checked {
      .ant-checkbox-inner {
        background-color: @brand-success;
        border-color: @brand-success;
      }
    }
  }

  .ant-checkbox-wrapper:hover .ant-checkbox-inner,
  .ant-checkbox:hover .ant-checkbox-inner,
  .ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: @brand-success;
  }
}

.cbd-mobile-base-form-checkbox {
  &-vertical {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
  &-horizontal {
    display: flex;
    flex-wrap: wrap;
    .am-checkbox-wrapper {
      margin-right: 50 * @hd;
      margin-top: 3 * @hd;
      margin-bottom: 3 * @hd;
    }
  }
  &-multiple {
    .am-checkbox-inner {
      border-radius: 3 * @hd;
    }
    .disabledMixins(@color) {
      .am-checkbox-disabled .am-checkbox-inner {
        border-color: @color;
      }
      .am-checkbox-checked.am-checkbox-disabled {
        .am-checkbox-inner {
          background: @color;
          border-color: @color;
        }
      }
    }
    .disabledMixins(#888);
    .cbd-mobile-base-form-checkbox-required {
      .disabledMixins(#00785d);
      .am-checkbox-checked.am-checkbox-disabled {
        opacity: 1;
      }
    }
  }
  &-disabled {
    color: #bfbfbf;
    // .am-checkbox-disabled:not(.am-checkbox-checked) {
    //   .am-checkbox-inner {
    //     background: #bfbfbf;
    //     border: 1px solid #999;
    //   }
    // }
    .am-checkbox-disabled {
      &.am-checkbox.am-checkbox-checked .am-checkbox-inner {
        &:after {
          border-color: #fff;
        }
      }
    }
  }
  &-radio {
    .am-checkbox-inner {
      width: 16 * @hd;
      height: 16 * @hd;
      top: 2 * @hd;
    }
    // .am-checkbox-inner:after {
    //   position: absolute;
    //   top: 4 * @hd;
    //   left: 4 * @hd;
    //   width: 50%;
    //   height: 50%;
    //   box-sizing: border-box;
    //   color: #fff;
    //   border-radius: 50%;
    //   content: '';
    //   background-color: #fff;
    // }
    .am-checkbox-inner:after {
      top: 2.7 * @hd;
      right: 5.8 * @hd;
      width: 5 * @hd;
      height: 9 * @hd;
      border-width: 0 2.4 * @hd 2.4 * @hd 0;
    }
    .am-checkbox-checked {
      .am-checkbox-inner {
        border: unset;
        // background: @color-primary;
        // background-color: #ffc107;
      }
    }

    // 禁用-未選中
    .am-checkbox-disabled .am-checkbox-inner {
      border-color: #8a8a8a !important;
      background: #d1d1d6 !important;
    }

    // 禁用-選中
    .am-checkbox-checked.am-checkbox-disabled {
      opacity: 1;
      .am-checkbox-inner {
        background: #8a8a8a !important;
        &::after {
          border-color: #fff;
        }
      }
    }
    span {
      display: flex;
      align-items: center;
    }
  }

  &-border {
    border: 1 * @hd solid rgba(217, 217, 217, 1);
    color: #595959;
    border-radius: 3 * @hd;
    &-checked {
      border: 1 * @hd solid rgba(21, 170, 218, 1);
    }
  }

  .am-checkbox {
    flex-shrink: 0;
    margin-right: 8 * @hd;
    margin-top: 2 * @hd;
  }
  .am-checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-top: 6 * @hd;
    margin-bottom: 12 * @hd;
    font-size: 16 * @hd;
    line-height: 26px;
  }
}
