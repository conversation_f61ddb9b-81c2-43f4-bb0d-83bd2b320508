.container{
  width: 100%;
  .inputItem{
    padding: 16px;
    width: 100%;
    border-radius: @radius-sm;
    background-color: #fff;
    border: 1px solid @border-color-base;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
  }
  .placeholder{
    color: @font-color-disable;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }
  .value{
    padding-right: 40px;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }
  .close{
    color: @font-color-disable;
    position: absolute;
    right: 16px;
    font-size: 24px;
  }
}

.modal{
  .topBtn{
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid @border-color-base;
    .btnText{
      padding: 18px 30px;
      color: @brand-primary;
    }
  }
}