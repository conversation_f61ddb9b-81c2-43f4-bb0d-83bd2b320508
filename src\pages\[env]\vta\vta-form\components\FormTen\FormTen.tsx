import React from 'react';
import pc from 'prefix-classnames';
import './FormTen.less';

import { history, useIntl } from 'umi';
import { Col, Button } from 'antd';
import { CheckCircleFilled } from '@ant-design/icons';

const px = pc('form-ten');

export interface FormTenProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {}

const FormTen = (props: FormTenProps) => {
  const intl = useIntl();
  const { className = '', ...otherProps } = props;

  function gotoIndex() {
    history.push('/web/vem');
  }

  return (
    <>
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('body')}>
          <div className={px('successTitle')}>機動車輛商標及型號核准 – 申請</div>
          <div className={px('sectionBody')}>
            <div className={px('sectionTitle')}>
              <CheckCircleFilled style={{ color: '#084ab8' }} />
              <span>{intl.formatMessage({ id: 'successfully_submitted_application' })}</span>
            </div>
            <Col>
              <div className="label">{intl.formatMessage({ id: 'query_number' })}</div>
              <div className="value">250000424</div>
            </Col>
            <Col>
              <div className="label">{intl.formatMessage({ id: 'establishment_time' })}</div>
              <div className="value">2025-02-19 11:10:18</div>
            </Col>
            <div className={px('successBottomTitle')}>
              {intl.formatMessage(
                { id: 'success_bottom_title_sst' },
                {
                  platform: (
                    <span className={px('successBottomTitleSpan')}>
                      {intl.formatMessage({ id: 'platform_sst' })}
                    </span>
                  ),
                },
              )}
            </div>
          </div>
        </div>
      </div>
      <div className={px('footer')}>
        <div className="footer-container">
          <Button
            type="default"
            onClick={() => {
              gotoIndex();
            }}
          >
            {intl.formatMessage({ id: 'return' })}
          </Button>
          <Button type="primary">{intl.formatMessage({ id: 'download_receipt' })}</Button>
        </div>
      </div>
    </>
  );
};

export default FormTen;
