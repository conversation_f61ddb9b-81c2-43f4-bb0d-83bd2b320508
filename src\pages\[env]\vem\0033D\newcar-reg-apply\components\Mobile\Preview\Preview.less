@prefix: preview-33d-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 20px 12px;
      padding-bottom: 20px;
      font-size: 0.16rem;
      border-radius: 12px 12px 0px 0px;

      .my-card-container-mobile {
        .my-card-item-mobile:nth-child(2) {
          background-color: #eee;

          .my-card-title-mobile {
            border-radius: 12px 12px 0 0;
            background-color: #e9b745;
          }
        }
      }
    }

    &sectionTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 700;
      padding: 5px 20px;
      min-height: 41px;
      font-size: 0.18rem;
      color: #ffffff;
      line-height: 21px;
      background: @brand-primary;
      border-radius: 10px 10px 0px 0px;
    }

    &sectionBody {
      .ant-collapse {
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 20px;
        color: white;
        border: 1px solid #f0f0f0;

        &:nth-of-type(4n + 1) {
          background-color: @brand-primary;
        }

        &:nth-of-type(4n + 2) {
          background-color: #e9b745;
        }

        &:nth-of-type(4n + 3) {
          background-color: #13a07b;
        }

        &:nth-of-type(4n + 4) {
          background-color: #f33b40;
        }

        .ant-collapse-header {
          padding: 12px 16px 12px 40px !important;
          color: white;
          font-size: 16px;
          cursor: pointer;
        }

        .ant-collapse-content {
          color: rgba(0, 0, 0, 0.85);
          background-color: #fff;
        }

        .form-title {
          font-size: 16px;
          color: @brand-primary;
        }

        .form-title2 {
          display: flex;
          align-items: center;
          font-weight: 700;
          font-size: 16px;
          color: #232323;
          margin: 0 24px;
          padding-bottom: 10px;
        }

        .form-title2:before {
          display: block;
          content: '';
          width: 4px;
          height: 16px;
          background: @brand-primary;
          margin-right: 8px;
        }

        .disAgree {
          padding-top: 20px;
          padding-bottom: 20px;
        }
      }

      .ant-form {
        padding: 0 24px;
        background-color: #fff;

        .ant-form-item {
          font-size: 16px;

          .ant-form-item-label {
            label {
              font-size: 16px;
              color: #6c6c6c;
              // color: @brand-primary;
            }
          }

          .statusBg1 {
            padding: 0 2px;
            color: #fff;
            background-color: #ffc107;
          }

          .statusBg2 {
            padding: 0 2px;
            color: #fff;
            background-color: #13a07b;

            // background-color: @brand-primary;
          }

          .statusBg3 {
            padding: 0 2px;
            color: #fff;
            background-color: #ffc107;
          }
        }
      }
    }
  }
}
.btn-mobile-height {
  display: flex;
  height: auto !important;
  white-space: pre-wrap !important;
}
.owner-card-form-mobile {
  background-color: #fff;

  .ant-form-item {
    font-size: 16px;

    .ant-form-item-label {
      label {
        font-size: 16px;
        color: #6c6c6c;
      }
    }

    .statusBg1 {
      padding: 0 2px;
      color: #f33b40;
    }

    .statusBg2 {
      padding: 0 2px;
      color: @brand-primary;
    }
  }
}

.owner-card-container-33d-mobile {
  background-color: #eee;
  padding-top: 20px;

  .owner-card-2 {
    background: #fdf8f0;
  }

  .owner-card-3 {
    background: #E5F1EF;
  }

  .owner-card-item-33d-mobile {
    margin-bottom: 20px;
    border-radius: 12px;
    border: 1px solid #E5F1EF;
    overflow: hidden;
    // background: #E5F1EF;

    // &:nth-child(4n + 1) {
    //   background: #d9ebe7;
    // }
    // &:nth-child(4n + 2) {
    //   background: #dfeaf5;
    // }
    // &:nth-child(4n + 3) {
    //   background: #faedd9;
    // }
    // &:nth-child(4n + 4) {
    //   background: #f7e4da;
    // }

    .owner-card-title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }

    .owner-card-form-mobile {
      background-color: #fff;
      padding: 20px;
    }
  }
}

.disAgree {
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 16px;
  color: #f33b40;
}
