import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import './Preview.less';

import { Alert, Checkbox, Collapse, Form } from 'antd';
import { getFormUrlByKey } from '@/services/UserApi';
import { useIntl } from 'umi';
import { getLangGroup } from '@/locales/lang';
import { handleContactName } from '@/utils';
import Watermark from '@/components/Watermark';
import dayjs from 'dayjs';
import { useToggle } from 'ahooks';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

const px = pc('vil-form-preview');

export interface FormEightProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'defaultValue' | 'onChange'> {
  setDisableNextStep: (boolean) => void;
  setNextStepText: (string) => void;
  setShowConfirmApply: (boolean) => void;
  setDisableConfirmApply: (boolean) => void;

  detail: getEXTxnDataRes;
  txnId: string | undefined;
  verficationStatus?: string;
  currentOwner?: saveTxnOwnersParams;
  tipMsg?: string;
}

const FormEight = (props: FormEightProps) => {
  const { className = '', ...otherProps } = props;
  const { setNextStepText, setShowConfirmApply, setDisableConfirmApply, detail } = otherProps;

  const [tipMsg, setTipMsg] = useState(props.tipMsg);

  const intl = useIntl();
  const [form] = Form.useForm();

  const [posted, setPosted] = useState(false);
  const [isChecked, setIsChecked] = useState(false);

  const [ownerDataList, setOwnerDataList] = useState<saveTxnOwnersParams[]>([]);
  const [currentOwnerList, setCurrentOwnerList] = useState<saveTxnOwnersParams[]>([]);
  const [otherOwnerList, setOtherOwnerList] = useState<saveTxnOwnersParams[]>([]);
  const [ovsapVehDataDTO, setOvsapVehDataDTO] = useState<ovsapVehDataDTO>();
  // const [ovsapAgentCompData, setOvsapAgentCompData] = useState<saveAgentCompParams>();
  const [ovsapTxnContactDTO, setOvsapTxnContactDTO] = useState<getTxnContactRes>();
  // const [noticeStatus, setNoticeStatus] = useState<{ownerId: string;status: number}[]>();
  // const [verficationStatus, setVerficationStatus] = useState<string>(props.verficationStatus || '');
  const [currentOwner, setCurrentOwner] = useState<saveTxnOwnersParams | undefined>();
  const [dspaFpDataDto, setDspaFpDataDto] = useState<dspaFpDataDTO>();
  useEffect(() => {
    // setVerficationStatus(props.verficationStatus || '');
    setCurrentOwner(props.currentOwner);
    if (props.currentOwner?.verficationStatus === 'Y') {
      setIsChecked(true);
    }

    setTipMsg(props.tipMsg);
  }, [props.verficationStatus, props.currentOwner, props.tipMsg]);

  useEffect(() => {
    if (detail) {
      console.log('detail===', detail);
      const data = detail;

      const ownerDataList: saveTxnOwnersParams[] = [];
      (data.ownerDataList ?? []).forEach((item) => {
        if (currentOwner?.ownerIdentNo === item.ownerIdentNo) {
          ownerDataList.unshift(item);
        } else {
          ownerDataList.push(item);
        }
      });

      setDspaFpDataDto(data?.dspaFpDataDTO || {});
      setOwnerDataList(ownerDataList);
      setCurrentOwnerList(
        ownerDataList?.filter((item) => item.ownerIdentNo === currentOwner?.ownerIdentNo),
      );
      setOtherOwnerList(
        ownerDataList?.filter((item) => item.ownerIdentNo !== currentOwner?.ownerIdentNo),
      );
      setOvsapVehDataDTO(data.ovsapVehDataDTO);
      // setOvsapAgentCompData(data.ovsapAgentCompData);
      setOvsapTxnContactDTO(data.ovsapTxnContactRequDTO);

      const status: { ownerId: string; status: number }[] = [];
      let flag = true;
      (data.ownerDataList ?? []).forEach((item) => {
        const obj = {
          ownerId: item.ownerId,
          status: item.verficationStatus != 'W' ? 1 : 0,
        };
        status.push(obj);

        if (obj.status == 0) {
          flag = false;
        }
      });
      if (flag) {
        // setPosted(true)
      }
      // setNoticeStatus(status);
    }
  }, [detail, currentOwner]);

  useEffect(() => {
    const faceAuth = getFormUrlByKey('faceAuth');

    if (faceAuth) {
      setPosted(true);
    }

    setNextStepText(intl.formatMessage({ id: 'payment' }));
    return () => {
      setNextStepText(intl.formatMessage({ id: 'next' }));
    };
  }, []);

  useEffect(() => {
    const res = ownerDataList.some(
      (item) => item.ownerFacialStatus && currentOwner?.ownerIdentNo === item.ownerIdentNo,
    );
    setShowConfirmApply(res);
  }, [ownerDataList, currentOwner]);

  const [state, { toggle }] = useToggle();
  return (
    <Watermark text={dayjs().format('YYYY-MM-DD HH:mm:ss')}>
      {tipMsg && (
        <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
          <Alert message={tipMsg} type="error" showIcon />
        </div>
      )}
    
      <div className={`${px('root')} ${className}`} {...otherProps}>
        <div className={px('sectionTitle')}>
          {posted
            ? intl.formatMessage({ id: 'data_confirmation' })
            : intl.formatMessage({ id: 'data_confirmation' })}
        </div>
        {detail?.dsiMsgCodeDTO && (
          <div className="owner-checkbox">
            <Checkbox
            style={{fontSize:'16px'}}
              checked={isChecked}
              onChange={(e) => {
                setIsChecked(e.target.checked);
                setDisableConfirmApply(!e.target.checked);
              }}
            >
              {getLangGroup(
                detail?.dsiMsgCodeDTO?.codeCname,
                detail?.dsiMsgCodeDTO?.codePname,
                detail?.dsiMsgCodeDTO?.codeEname,
              )}
            </Checkbox>
          </div>
        )}

        <div className={px('sectionBody')}>
          <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
            <Collapse.Panel
              showArrow={false}
              header={<>{intl.formatMessage({ id: 'usage_license_plates' })}</>}
              key={1}
            >
              <div>
                <Form form={form} className={px('form')} layout="vertical">
                  <Form.Item style={{ width: '100%' }}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.plateNoUserCn,
                        ovsapVehDataDTO?.plateNoUserPt,
                        ovsapVehDataDTO?.plateNoUserEn,
                      )}
                    </div>
                    <div>{ovsapVehDataDTO?.relatedPlateNo || ''}</div>
                  </Form.Item>
                </Form>
              </div>
            </Collapse.Panel>
          </Collapse>

          {dspaFpDataDto?.dspaFpNo ? (
            <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
              <Collapse.Panel
                showArrow={false}
                header={<>{intl.formatMessage({ id: 'old_motorcycle_replace_subsidy' })}</>}
                key={1}
              >
                <div>
                  <Form form={form} className={px('form')} layout="vertical" colon={false}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'fp_no' })}
                      style={{ width: '100%' }}
                    >
                      <div>{dspaFpDataDto?.dspaFpNo}</div>
                      <div style={{ color: '#084ab8' }}>
                        {`${getLangGroup(
                          dspaFpDataDto?.dspaFpFeeCodeDto.codeCname,
                          dspaFpDataDto?.dspaFpFeeCodeDto.codePname,
                          dspaFpDataDto?.dspaFpFeeCodeDto.codeEname,
                        )}`}
                      </div>
                    </Form.Item>
                  </Form>
                </div>
              </Collapse.Panel>
            </Collapse>
          ) : (
            ''
          )}
          {/* <div className="owner-title">{intl.formatMessage({ id: 'car_owner_information' })}</div> */}
          {/* 当前车主 */}
          {currentOwnerList?.length > 0 && (
            <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
              <Collapse.Panel
                showArrow={false}
                header={<>{intl.formatMessage({ id: 'current_car_owner' })} </>}
                key={1}
              >
                {currentOwnerList?.map((item, index, arr) => (
                  <Form form={form} className={px('form')} layout="vertical">
                    <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                      <div>
                        {item.ownerLastNameCn}
                        {item.ownerFirstNameCn}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                      <div>
                        {item.ownerLastNamePt} {item.ownerFirstNamePt}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'certificate_type' })}>
                      <div>
                        {getLangGroup(
                          item.ownerIdentTypeCn,
                          item.ownerIdentTypePt,
                          item.ownerIdentTypeEn,
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'id_number' })}>
                      <div>{item.ownerIdentNo}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'contact_address' })}>
                      <div>{item.ownerAddr}</div>
                    </Form.Item>

                    {(ownerDataList || []).length > 1 &&
                      ['L', 'P'].includes(ovsapVehDataDTO?.vehType || '') && (
                        <Form.Item label={intl.formatMessage({ id: 'registered_residence' })}>
                          <div>{item.dsajAddr}</div>
                        </Form.Item>
                      )}
                    {(ownerDataList || []).length > 1 &&
                      ['L', 'P'].includes(ovsapVehDataDTO?.vehType || '') && (
                        <Form.Item label={intl.formatMessage({ id: 'share' })}>
                          <div>
                            {item.quotaNumerator} / {item.quotaDenominator}
                          </div>
                        </Form.Item>
                      )}
                    {item.isShowFacialLabel ? (
                      <Form.Item label="">
                        <span
                          className={
                            item.verficationStatus == 'P'
                              ? 'statusBg1'
                              : item.verficationStatus == 'Y'
                              ? 'statusBg2'
                              : 'statusBg1'
                          }
                        >
                          {getLangGroup(
                            item?.verficationStatusCn,
                            item?.verficationStatusPt,
                            item?.verficationStatusEn,
                          )}
                        </span>
                      </Form.Item>
                    ) : null}
                  </Form>
                ))}
              </Collapse.Panel>
            </Collapse>
          )}

          {/* 其他车主 */}
          {otherOwnerList?.length > 0 && (
            <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
              <Collapse.Panel
                showArrow={false}
                header={<>{intl.formatMessage({ id: 'other_car_owner' })} </>}
                key={1}
              >
                {otherOwnerList?.map((item, index, arr) => (
                  <div>
                    <p className="form-title">
                      {intl.formatMessage({ id: 'car_owner_information' })}
                      {arr.length > 1 ? index + 1 : ''}：
                    </p>
                    <Form form={form} className={px('form')} layout="vertical">
                      <Form.Item label={intl.formatMessage({ id: 'chinese_name' })}>
                        <div>
                          {item.ownerLastNameCn}
                          {item.ownerFirstNameCn}
                        </div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'portuguese_name' })}>
                        <div>
                          {item.ownerLastNamePt} {item.ownerFirstNamePt}
                        </div>
                      </Form.Item>
                      {(ownerDataList || []).length > 1 &&
                        ['L', 'P'].includes(ovsapVehDataDTO?.vehType || '') && (
                          <Form.Item label={intl.formatMessage({ id: 'share' })}>
                            <div>
                              {item.quotaNumerator} / {item.quotaDenominator}
                            </div>
                          </Form.Item>
                        )}
                      {item.isShowFacialLabel ? (
                        <Form.Item label="">
                          <span
                            className={
                              item.verficationStatus == 'P'
                                ? 'statusBg1'
                                : item.verficationStatus == 'Y'
                                ? 'statusBg2'
                                : 'statusBg1'
                            }
                          >
                            {getLangGroup(
                              item?.verficationStatusCn,
                              item?.verficationStatusPt,
                              item?.verficationStatusEn,
                            )}
                          </span>
                        </Form.Item>
                      ) : null}
                    </Form>
                  </div>
                ))}
              </Collapse.Panel>
            </Collapse>
          )}

          <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
            <Collapse.Panel
              showArrow={false}
              header={
                <div className="flex-c-s" style={{ alignItems: 'center' }}>
                  <div>{intl.formatMessage({ id: 'vehicle_information' })} </div>
                  {!state ? (
                    <CaretDownOutlined onClick={() => toggle()} />
                  ) : (
                    <CaretUpOutlined onClick={() => toggle()} />
                  )}
                </div>
              }
              key={1}
            >
              <div>
                <Form form={form} className={px('form')} layout="vertical">
                  {false && (
                    <>
                      <Form.Item label={intl.formatMessage({ id: 'import_license_number' })}>
                        <div>{ovsapVehDataDTO?.importNoFull}</div>
                      </Form.Item>
                      <Form.Item label={intl.formatMessage({ id: 'model_approval_number' })}>
                        <div>{ovsapVehDataDTO?.vtaNoFull}</div>
                      </Form.Item>{' '}
                    </>
                  )}

                  <Form.Item>
                    <div style={{ color: '#f00' }}>
                      {getLangGroup(
                        ovsapVehDataDTO?.newCheckResultCodeDto.codeCname,
                        ovsapVehDataDTO?.newCheckResultCodeDto.codeEname,
                        ovsapVehDataDTO?.newCheckResultCodeDto.codePname,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item></Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'brand' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehBrandDescCn,
                        ovsapVehDataDTO?.vehBrandDescPt,
                        ovsapVehDataDTO?.vehBrandDescEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'style' })}>
                    <div>{ovsapVehDataDTO?.vehModel}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_build_year' })}>
                    <div>{ovsapVehDataDTO?.vehBuildYear}</div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'color' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.colorDescCn,
                        ovsapVehDataDTO?.colorDescPt,
                        ovsapVehDataDTO?.colorDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehicle_level' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehTypeDescCn,
                        ovsapVehDataDTO?.vehTypeDescPt,
                        ovsapVehDataDTO?.vehTypeDescEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vehicle_usage' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehUsageDescCn,
                        ovsapVehDataDTO?.vehUsageDescPt,
                        ovsapVehDataDTO?.vehUsageDescEn,
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label={intl.formatMessage({ id: 'vin' })}>
                    <div>{ovsapVehDataDTO?.vin}</div>
                  </Form.Item>
                  <Form.Item label={intl.formatMessage({ id: 'vehSourceCtryDescCn' })}>
                    <div>
                      {getLangGroup(
                        ovsapVehDataDTO?.vehSourceCtryDescCn,
                        ovsapVehDataDTO?.vehSourceCtryDescPt,
                        ovsapVehDataDTO?.vehSourceCtryDescEn,
                      )}
                    </div>
                  </Form.Item>
                  <div
                    style={{ height: state ? 'auto' : '0px', overflow: 'hidden' }}
                    className="flex-c-s"
                  >
                    <Form.Item label={intl.formatMessage({ id: 'vehicle_cate_gory_desc' })}>
                      <div>
                        {getLangGroup(
                          ovsapVehDataDTO?.vehCateGoryDescCn,
                          ovsapVehDataDTO?.vehCateGoryDescPt,
                          ovsapVehDataDTO?.vehCateGoryDescEn,
                        )}
                      </div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' })}>
                      <div>{ovsapVehDataDTO?.engineNo}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '2'}>
                      <div>{ovsapVehDataDTO?.engineNo2}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '3'}>
                      <div>{ovsapVehDataDTO?.engineNo3}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'engine_no' }) + '4'}>
                      <div>{ovsapVehDataDTO?.engineNo4}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'cylinderQty' })}>
                      <div>{ovsapVehDataDTO?.cylinderQty}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'cylinderVol' })}>
                      <div>{`${ovsapVehDataDTO?.cylinderVol || 0} c.c`}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'maxPowerWithUnit' })}>
                      <div>{ovsapVehDataDTO?.maxPowerWithUnit}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'fuelTypeDescCn' })}>
                      <div>
                        {getLangGroup(
                          ovsapVehDataDTO?.fuelTypeDescCn,
                          ovsapVehDataDTO?.fuelTypeDescPt,
                          ovsapVehDataDTO?.fuelTypeDescEn,
                        )}
                      </div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'vehicle_size' })}>
                      {(ovsapVehDataDTO?.vehLength ||
                        ovsapVehDataDTO?.vehWidth ||
                        ovsapVehDataDTO?.vehHeight) && (
                        <div>
                          ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.vehLength} mm x
                          ({intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.vehWidth} mm x (
                          {intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.vehHeight} mm
                        </div>
                      )}
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'cabinTypeDescCn' })}>
                      <div>
                        {getLangGroup(
                          ovsapVehDataDTO?.cabinTypeDescCn,
                          ovsapVehDataDTO?.cabinTypeDescPt,
                          ovsapVehDataDTO?.cabinTypeDescEn,
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'car_size' })}>
                      {(ovsapVehDataDTO?.cabinLength ||
                        ovsapVehDataDTO?.cabinWidth ||
                        ovsapVehDataDTO?.cabinHeight) && (
                        <div>
                          ({intl.formatMessage({ id: 'length' })}){ovsapVehDataDTO?.cabinLength} mm
                          x ({intl.formatMessage({ id: 'width' })}){ovsapVehDataDTO?.cabinWidth} mm
                          x ({intl.formatMessage({ id: 'height' })}){ovsapVehDataDTO?.cabinHeight}{' '}
                          mm
                        </div>
                      )}
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'seating_apacity' })}>
                      <div>{ovsapVehDataDTO?.seatQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'tyre_f_r_s' })}>
                      <div>{ovsapVehDataDTO?.tyreDesc}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'frontTyreQty' })}>
                      <div>{ovsapVehDataDTO?.frontTyreQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'rearTyreQty' })}>
                      <div>{ovsapVehDataDTO?.rearTyreQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'frontAxleQty' })}>
                      <div>{ovsapVehDataDTO?.frontAxleQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'rearAxleQty' })}>
                      <div>{ovsapVehDataDTO?.rearAxleQty}</div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'vehWeight' })}>
                      <div>
                        {ovsapVehDataDTO?.vehWeight} {ovsapVehDataDTO?.vehWeight && `kg`}
                      </div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'vehLoadWeight' })}>
                      <div>
                        {ovsapVehDataDTO?.vehLoadWeight} {ovsapVehDataDTO?.vehLoadWeight && `kg`}
                      </div>
                    </Form.Item>

                    <Form.Item label={intl.formatMessage({ id: 'vehGrossWeight' })}>
                      <div>
                        {ovsapVehDataDTO?.vehGrossWeight} {ovsapVehDataDTO?.vehGrossWeight && `kg`}
                      </div>
                    </Form.Item>
                  </div>
                </Form>
              </div>
            </Collapse.Panel>
          </Collapse>

          <Collapse ghost collapsible="disabled" defaultActiveKey={[1]}>
            <Collapse.Panel
              showArrow={false}
              header={<>{intl.formatMessage({ id: 'contact_information' })}</>}
              key={1}
            >
              {ovsapTxnContactDTO?.contactAgreest === 'Y' ? (
                <div>
                  <Form form={form} className={px('form')} layout="vertical">
                    <Form.Item label={intl.formatMessage({ id: 'car_owner_view' })}>
                      <div>{handleContactName(ovsapTxnContactDTO)}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'local_mobile_phone' })}>
                      <div>{ovsapTxnContactDTO.contactMobilePhone}</div>
                    </Form.Item>
                    <Form.Item label={intl.formatMessage({ id: 'language' })}>
                      {getLangGroup(
                        ovsapTxnContactDTO?.contactLanguageCn,
                        ovsapTxnContactDTO?.contactLanguagePt,
                        ovsapTxnContactDTO?.contactLanguageEn,
                      )}
                    </Form.Item>
                    <Form.Item style={{ width: '100%' }}>
                      {getLangGroup(
                        ovsapTxnContactDTO?.agreeCodeDto?.codeCname,
                        ovsapTxnContactDTO?.agreeCodeDto?.codePname,
                        ovsapTxnContactDTO?.agreeCodeDto?.codeEname,
                      )}
                    </Form.Item>
                  </Form>
                </div>
              ) : (
                <div className="disAgree">{getLangGroup(
                  ovsapTxnContactDTO?.disAgreeCodeDto?.codeCname,
                  ovsapTxnContactDTO?.disAgreeCodeDto?.codePname,
                  ovsapTxnContactDTO?.disAgreeCodeDto?.codeEname,
                )}</div>
              )}
            </Collapse.Panel>
          </Collapse>
        </div>
      </div>
    </Watermark>
  );
};

export default FormEight;
