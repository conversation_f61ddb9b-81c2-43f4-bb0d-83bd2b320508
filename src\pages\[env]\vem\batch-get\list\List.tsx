import React, { useEffect, useState } from 'react';
import pc from 'prefix-classnames';
import { history, useIntl } from 'umi';
import './List.less';
import { Alert, Button, Empty, Pagination, Spin, Tag } from 'antd';
import QRCode from 'qrcode';
import { isMobile } from '@/utils';

import { cancelBatchData, getBatchExDataPage } from '@/services/0028F';

import { NavBar } from 'antd-mobile';
import { getStatusBarHeight } from '@/utils/hooks';
import { LeftOutlined } from '@ant-design/icons';
import { getLangGroup, getResponseMessage, Lang } from '@/locales/lang';
import { Page } from '@gov-mo/mpaas-js-bridge';
import { TweenOneGroup } from 'rc-tween-one';
import { chain } from 'lodash';
import { downQRCode } from '@/hook';
import dayjs from 'dayjs';
import { getLocale } from '@@/plugin-locale/localeExports';

const BatchGetListPage = () => {
  const px = pc(isMobile() ? 'page-batch-get-list-mobile' : 'page-batch-get-list');
  const intl = useIntl();
  const [tipMsg, setTipMsg] = useState('');

  // const [qrcodeStatus, setQrcodeStatus] = useState(false);
  // const [qrcodeUrl, setQrcodeUrl] = useState('/img/QR_code.svg');
  // const [tableItem, setTableItem] = useState<getBatchExDataPageDataResItem>();

  const [list, setList] = useState<getBatchExDataPageDataResItem[]>([]);
  const [loading, setLoading] = useState(false);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  const getList = async (pageParams?: number) => {
    try {
      setLoading(true);
      const res = await getBatchExDataPage({ length: 10, start: (page - 1) * 10 });
      if (res?.code === '0') {

        let list: any = await Promise.all(
          (res.data?.data || []).map(async (item) => {
            const objectUrl = await QRCode.toDataURL(item.batchCode.toString(), {
              errorCorrectionLevel: 'H',
            });
            return {
              ...item,
              imgUrl: objectUrl,
            };
          }),
        );

        setList(list);
        setPageSize(res.data?.pageSize);
        setTotal(res.data?.total);
        setLoading(false);
        return;
      }

      setTipMsg(getResponseMessage(res));
      setLoading(false);
    } catch (error: any) {
      setLoading(false);
      setTipMsg(error?.message);
    }
  };

  useEffect(() => {
    getList(page);
  }, [page]);

  const handleClose = () => {
    if (process.env.NODE_ENV === 'production' && isMobile()) {
      Page.close();
      return;
    }

    process.env.NODE_ENV === 'production' ? window.close() : history.push('/web/vem');
  };

  const handlePrevious = () => {
    history.push('/web/vem');
  };
  // const openQRCode = async (item: getBatchExDataPageDataResItem) => {
  //   // 弹出二维码
  //   const url = await QRCode.toDataURL(item.batchCode.toString(), { errorCorrectionLevel: 'H' });
  //   setQrcodeUrl(url);
  //   setTableItem(item);
  //   setQrcodeStatus(true);
  // };

  const handleCancelBatchData = async (item) => {
    const res = await cancelBatchData(item.batchId);
    if (res?.code === '0') {
      getList(0);
    }
  };

  // const downloadQRCode = (base64Image, item) => {
  //   let byteString = atob(base64Image.split(',')[1]);
  //   let mimeString = base64Image.split(',')[0].split(':')[1].split(';')[0];
  //   let ab = new ArrayBuffer(byteString.length);
  //   let ia = new Uint8Array(ab);
  //   for (let i = 0; i < byteString.length; i++) {
  //     ia[i] = byteString.charCodeAt(i);
  //   }
  //   let blob = new Blob([ab], { type: mimeString });

  //   let url = URL.createObjectURL(blob);
  //   let a = document.createElement('a');
  //   a.href = url;
  //   a.download = item.batchCode + '.png';
  //   document.body.appendChild(a);
  //   a.click();

  //   // 释放URL对象
  //   URL.revokeObjectURL(url);
  // };

  let getDom = (info) => {
    const groupKey = getLocale() === Lang.葡语 ? 'codePname' : 'codeCname';
    let data = chain(info?.vemGroup)
      .groupBy(groupKey) // 先按 type 分组
      .map((list, label) => ({ label, list })) // 转换结构
      .value();
    return data.map((item) => {
      return (
        <div>
          <div>{item.label}</div>
          <div className="flex-1">
            <TweenOneGroup
              enter={{
                scale: 0.8,
                opacity: 0,
                type: 'from',
                duration: 100,
              }}
              onEnd={(e) => {
                if (e.type === 'appear' || e.type === 'enter') {
                  (e.target as any).style = 'display: inline-block';
                }
              }}
              leave={{ opacity: 0, width: 0, scale: 0, duration: 200 }}
              appear={false}
            >
              {item?.list.map((ite) => {
                return <Tag key={ite.vtaNoFull}>{ite.plateNo || '-'}</Tag>;
              })}
            </TweenOneGroup>
          </div>
        </div>
      );
    });
  };
  return (
    <>
      {isMobile() ? (
        <NavBar
          style={{
            paddingTop: getStatusBarHeight(),
          }}
          mode="light"
          leftContent={<LeftOutlined onClick={handlePrevious} />}
        >
          <span style={{ color: '#333' }}>{intl.formatMessage({ id: 'get_car_list' })}</span>
        </NavBar>
      ) : null}

      <div
        className={`${px('root')}`}
        style={{ paddingTop: isMobile() ? 55 + getStatusBarHeight() : 0 }}
      >
        {tipMsg ? (
          <div style={{ paddingLeft: '0.24rem', paddingBottom: '0.24rem' }}>
            <Alert message={tipMsg} type="error" showIcon />
          </div>
        ) : null}
        {!isMobile() && (
          <h2 className={px('title')}>{intl.formatMessage({ id: 'get_car_list' })}</h2>
        )}
        <div className="my-card-container">
          <div className="batch-get-card-form">
            <Spin spinning={loading}>
              {list && list.length > 0 ? (
                list.map((item, index) => {
                  return (
                    <div style={{ position: 'relative', fontSize: '16px' }}>
                      <div
                        className="my-card-item"
                        style={{
                          background: item.batchStatus === 'F' ? '#13a07b' : '#084ab8',
                        }}
                      >
                        <div className="my-card-title">
                          <span>
                            {getLangGroup(item.companyCname, item.companyEname, item.companyPname)}
                          </span>
                          <div className="status">
                            {getLangGroup(
                              item.batchStatusCn,
                              item.batchStatusPt,
                              item.batchStatusEn,
                            )}
                          </div>
                        </div>
                        <div className="my-card-body">
                          <div className="flex-r-s" style={{ alignItems: 'flex-start' }}>
                            <div className="flex-c-c" style={{ alignItems: 'flex-start' }}>
                              {getDom(item)}
                            </div>
                            <div
                              className="flex-c-c"
                              style={{
                                width: '200px',
                                flex: 1,
                                alignItems: isMobile() ? 'center' : 'end',
                              }}
                            >
                              <img src={item.imgUrl} style={{ width: 200, height: 200 }} />
                              <div
                                style={{ textAlign: 'center', fontSize: '16px', width: '200px' }}
                              >
                                {intl.formatMessage(
                                  { id: 'qrcode_car_count' },
                                  { count: item?.batchDetailSize || 0 },
                                )}
                              </div>
                            </div>
                          </div>
                          <div style={{ color: '#f00', paddingBottom: '10px', marginTop: '10px' }}>
                            {intl.formatMessage({ id: 'license_plate' })}
                          </div>
                          <div>
                            <span style={{ color: '#6c6c6c' }}>
                              {intl.formatMessage({ id: 'get_address' })}
                              &nbsp;&nbsp;
                              {getLangGroup(
                                item.licAddrCodeDescCn,
                                item.licAddrCodeDescPt,
                                item.licAddrCodeDescEn,
                              )}
                            </span>
                          </div>
                          <div
                            className="my-card-item-btn"
                            style={{
                              gap: '10px',
                              flexDirection: isMobile() ? 'column' : 'row',
                            }}
                          >
                            <Button
                              onClick={() =>
                                downQRCode('capture-area-' + index, `${item?.batchId}`)
                              }
                              type="default"
                            >
                              {intl.formatMessage({ id: 'qr_code' })}
                            </Button>
                            {item.batchStatus !== 'F' && (
                              <Button
                                onClick={() => {
                                  history.push(`/web/vem/batch-get?batchId=${item.batchId}`);
                                }}
                                type="default"
                              >
                                {intl.formatMessage({ id: 'update_information' })}
                              </Button>
                            )}
                            {item.batchStatus == 'A' ? (
                              <Button onClick={() => handleCancelBatchData(item)} type="default">
                                {intl.formatMessage({ id: 'lose_efficacy' })}
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      <div
                        className="my-card-item"
                        id={`capture-area-` + index}
                        style={{
                          background: item.batchStatus === 'F' ? '#13a07b' : '#084ab8',
                          position: 'absolute',
                          left: '-9999px',
                          width: '100%',
                        }}
                      >
                        <div className="my-card-title">
                          <span style={{ fontSize: '16px' }}>
                            {getLangGroup(item.companyCname, item.companyEname, item.companyPname)}
                          </span>
                        </div>
                        <div className="my-card-body">
                          <div className="flex-r-s" style={{ alignItems: 'flex-start' }}>
                            <div>
                              <div className="flex-c-c" style={{ alignItems: 'flex-start' }}>
                                {getDom(item)}
                              </div>
                            </div>
                            <div
                              className="flex-c-c"
                              style={{
                                width: '200px',
                                flex: 1,
                                alignItems: isMobile() ? 'center' : 'end',
                              }}
                            >
                              <img src={item.imgUrl} style={{ width: '200px', height: '200px' }} />
                              <div
                                style={{ textAlign: 'center', fontSize: '16px', width: '200px' }}
                              >
                                {intl.formatMessage(
                                  { id: 'qrcode_car_count' },
                                  { count: item?.batchDetailSize || 0 },
                                )}
                              </div>
                            </div>
                          </div>
                          <div style={{ marginTop: isMobile() ? '0px' : '-120px' }}>
                            <div style={{ fontSize: '16px', marginTop: '34px' }}>
                              <span style={{ color: 'rgb(108, 108, 108)' }}>
                                {intl.formatMessage({ id: 'collection_time' })} &nbsp;&nbsp;
                                {dayjs().format('YYYY-MM-DD HH:mm:ss')}
                              </span>
                            </div>
                            <div style={{ fontSize: '16px', marginBottom: '16px' }}>
                              <span style={{ color: '#6c6c6c' }}>
                                {intl.formatMessage({ id: 'get_address' })}
                                &nbsp;&nbsp;
                                {getLangGroup(
                                  item.licAddrCodeDescCn,
                                  item.licAddrCodeDescPt,
                                  item.licAddrCodeDescEn,
                                )}
                              </span>
                            </div>
                          </div>
                          <div
                            dangerouslySetInnerHTML={{
                              __html: getLangGroup(
                                item?.batchMsgCodeDto.codeCname,
                                item?.batchMsgCodeDto.codePname,
                                item?.batchMsgCodeDto.codeEname,
                              ),
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <Empty description={intl.formatMessage({ id: 'no_data' })} />
              )}
            </Spin>
          </div>
        </div>
        <div className={px('pagination-box')}>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={total}
            onChange={(e) => {
              console.log(e);
              setPage(e);
            }}
          />
        </div>
        <div className="batch-get-footer">
          <div className="footer-container">
            <Button onClick={handleClose} type="default">
              {intl.formatMessage({ id: 'turn_off' })}
            </Button>
          </div>
        </div>
      </div>

      {/* <Modal
        title={intl.formatMessage({ id: 'qr_code' })}
        width="300px"
        cancelText={intl.formatMessage({ id: 'turn_off' })}
        visible={qrcodeStatus}
        onCancel={() => setQrcodeStatus(false)}
        footer={null}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <img src={qrcodeUrl} style={{ width: 200, height: 200 }} />
          <div style={{ fontSize: 16 }}>
            {getLangGroup(
              tableItem?.companyCname,
              tableItem?.companyPname,
              tableItem?.companyEname,
            )}
          </div>
          <div style={{ fontSize: 16, marginBottom: 10 }}>
            {intl.formatMessage(
              { id: 'qrcode_car_count' },
              { count: tableItem?.batchDetailSize || 0 },
            )}
          </div>
          <Button type="primary" onClick={() => downloadQRCode(qrcodeUrl, tableItem)}>
            {intl.formatMessage({ id: 'download' })}
          </Button>
        </div>
      </Modal> */}
    </>
  );
};

export default BatchGetListPage;
