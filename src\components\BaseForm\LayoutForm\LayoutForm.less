* {
  box-sizing: border-box;
}

.cbd-web-base-form-root {
  padding: 0 24px;
  .ant-form-item {
    margin-bottom: 20px;
  }
  .ant-form-item-label {
    & > label {
      font-size: 16px;
      line-height: 22px;
      white-space: pre-wrap;
      align-items: baseline;
      height: auto;
    }
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      color: var(--error);
    }
  }
  .ant-form-item-control {
    max-width: 345px;
  }
  .ant-radio-disabled .ant-radio-inner,
  .ant-checkbox-disabled .ant-checkbox-inner {
    border-color: #ccc !important;
  }
  .ant-picker,
  .ant-picker-range {
    width: 345px;
  }
  .ant-radio-wrapper,
  .ant-checkbox-wrapper {
    line-height: 32px;
    font-size: 16px;
  }
  .ant-select {
    font-size: 16px;
  }
  .ant-input {
    font-size: 16px;
  }
}

.cbd-mobile-base-form-root {
  padding: 16 * @hd 16 * @hd;
  margin: 0 16 * @hd;
  background: var(--secondBgColor);
  border-bottom-left-radius: 10 * @hd;
  border-bottom-right-radius: 10 * @hd;
  .ant-form-item-explain {
    color: var(--error);
    margin-top: 16px;
  }
  .ant-form-item-label {
    padding-bottom: 10 * @hd;
    line-height: 21 * @hd;
    label {
      height: auto;
      min-height: 16 * @hd;
      font-size: 18 * @hd;
      color: var(--firstTextColor);
    }
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
      display: inline-block;
      margin-left: 4 * @hd;
      color: var(--error);
      font-size: 16 * @hd;
      line-height: 1;
      content: '*';
    }
  }
  .am-list-body {
    background-color: rgba(255, 255, 255, 0);
    border-bottom: none;
    border-top: none;
  }
  .am-list-body::before {
    display: none !important;
  }
  .am-list-body::after {
    display: none !important;
  }
  .ant-form-item {
    margin: 0 0 16 * @hd;
    font-size: 16 * @hd;
  }
  .ant-col:last-child {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
  .cbd-mobile-base-form-divider {
    border-top: 1 * @hd #f7f7f7 solid;
    margin: 12 * @hd 0;
  }
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #ccc;
  }
  .am-list-item {
    border: 1 * @hd solid var(--borderColor);
    border-radius: 6 * @hd;

    .am-input-control input,
    .am-textarea-control textarea {
      font-size: 16 * @hd;
      color: @selected-value-color;
    }
  }
  // 校验有误
  .ant-form-item-has-error {
    .am-list-item {
      border-color: var(--error);
    }
  }
  .am-input-control,
  .am-textarea-control {
    input:-moz-placeholder,
    input::-moz-placeholder,
    input:-ms-input-placeholder,
    textarea:-moz-placeholder,
    textarea::-moz-placeholder,
    textarea:-ms-input-placeholder {
      font-size: 16 * @hd;
    }
  }
  .am-list-item .am-list-line .am-list-extra {
    font-size: 16 * @hd;
  }
  label.am-checkbox-wrapper {
    font-size: 16 * @hd;
    color: var(--firstTextColor);
    &.cbd-mobile-base-form-checkbox-disabled {
      color: #bfbfbf;
    }
    &.cbd-mobile-base-form-checkbox-required {
      color: var(--firstTextColor);
      .requiredAfterMixins();
    }
  }
}
.mo-address-drawer {
  position: fixed !important;
  z-index: 1;
}
.mo-address-root .am-list-line {
  // position: fixed !important;
  z-index: 1;
  padding-left: 0;
}

.address-mo-area-main .am-list-item.am-input-item {
  box-sizing: content-box;
}

.mo-address-root,
.cbd-mobile-base-form-text,
.cbd-mobile-base-form-address-mobileText,
.cbd-mobile-base-form-cascader,
.cbd-mobile-base-form-single-date,
.cbd-mobile-base-form-select,
.cbd-mobile-base-form-timerange-root,
.cbd-mobile-base-form-checkbox {
  .am-list-item:last-child {
    // border: 1px solid rgba(217, 217, 217, 1) !important;
  }
  .am-list-item {
    // border: 1px solid rgba(217, 217, 217, 1);
    padding: 0;
    min-height: 56 * @hd;
    box-sizing: border-box;
    background: var(--secondBgColor);
  }
  .am-list-line::after {
    display: none !important;
  }
}

.am-list-item.am-input-item {
  padding: 10 * @hd 0 10 * @hd 12 * @hd;
  height: 56 * @hd;
}

.am-picker-col-item {
  font-size: 16 * @hd !important;
}

.am-picker-col-item-selected,
.am-picker-popup-item {
  font-size: 17 * @hd !important;
}

.cbd-mobile-base-form-detail {
  &.cbd-mobile-base-form-root {
    .ant-form-item-control-input {
      padding: 5 * @hd 0;
      min-height: auto;
    }
    .ant-form-item {
      margin: 0;
      display: flex;
      // align-items: center;
      align-items: flex-start;
      flex-wrap: wrap;
      .ant-form-item-label {
        // max-width: 40%;
        max-width: 100%;
        flex: none;
        overflow-wrap: anywhere;
        padding-bottom: 0;
        padding-right: 8 * @hd;
        > label {
          display: inline-block;
          padding: 5 * @hd 0;
        }
        > label:not(.ant-form-item-required-mark-optional)::after {
          margin: 0;
          color: #595959;
          content: ':';
        }
      }
      .ant-form-item-control {
        // flex: 1 1 60%;
        flex: auto;
        // padding-left: 8 * @hd;
        color: #8c8c8c;
      }
    }
  }
}
