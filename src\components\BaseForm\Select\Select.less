.cbd-mobile-base-form-select {
  .am-list-item {
    .am-list-line .am-list-extra {
      -webkit-flex-basis: 100%;
      flex-basis: 100%;
      text-align: left;
      padding-left: 12 * @hd;
    }
  }
}
/* 下拉框样式：值 */
.cbd-mobile-base-form-select-isSelected {
  .am-list-item .am-list-line .am-list-extra {
    color: @selected-value-color;
  }
}
.cbd-mobile-base-form-select-disabled {
  .am-list-item .am-list-line {
    background: #f5f5f5;
    .am-list-extra {
      color: #8c8c8c;
    }
  }
}
.cbd-web-base-form-select {
  .ant-select-arrow {
    font-size: 14px;
    margin-top: 0;
    top: 10px;
  }
}

.am-picker-col-item {
  margin: 0 16px;
  display: block;
  overflow: hidden;
}

.cbd-selector-selected-item-scroll {
  animation: marquee 3s 0.1s linear 1;
  -webkit-animation: marquee 3s 0.1s linear 1;
  text-overflow: unset;
  overflow: visible;
  display: inline-block;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(calc(100vw - 100% - 16px));
  }
  100% {
    transform: translateX(calc(100vw - 100% - 16px));
  }
}
