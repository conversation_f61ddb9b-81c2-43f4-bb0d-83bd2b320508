@prefix: payment-33d-mobile;

.@{prefix} {
  &- {
    &root {
      margin: 20px 12px 40px;
      padding-bottom: 20px;
      font-size: 0.16rem;

      .ant-table {
        font-size: 12px !important;
        border-radius: 12px;
        border: 1px solid #f0f0f0;
        overflow: hidden;
      }

      .ant-table-thead > tr > th {
        background: @brand-primary;
        color: #fff;
      }
    }

    &formTitle {
      padding: 16px;
      background: #e9b745;
      color: #fff;
      font-size: 12px;
    }

    &cost-title {
      padding: 0.16rem;
      background: #084ab8;
      color: #fff;
      font-size: 0.12rem;
    }

    &cost-container {
      padding: 0.16rem 0.16rem 0 0.16rem;
    }

    &cost-content {
      background: #f1f8fe;
      border-radius: 0.12rem;
      padding: 0.12rem 0.12rem 0 0.12rem;

      &-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.14rem;
        padding-bottom: 0.10rem;

        &-label {
          flex-shrink: 0;
          font-size: 0.14rem;
          color: #ACACAC;
          padding-right: 0.10rem;
        }

        &-text-black {
          color: black;
        }

        &-text-primary {
          color: #084ab8;
        }
      }
    }

    &cost-divide {
      border-top: 1px solid #ACACAC;
      padding-top: 0.10rem;
    }

    &pay-tip {
      color: red;
      font-size: 0.12rem;
      margin: 0.16rem;
    }
  }
}

.pay-way-mobile {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  padding-bottom: 20px;
  margin-top: 20px;
  background-color: #ffffff;

  .ant-radio-checked {
    &:after {
      border-color: #084ab8;
    }

    .ant-radio-inner {
      border-color: #084ab8;

      &::after {
        background-color: #084ab8;
      }
    }
  }
}
