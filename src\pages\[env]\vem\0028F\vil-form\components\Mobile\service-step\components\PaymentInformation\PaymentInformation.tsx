import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import type { StepModelsState } from 'umi';
import { useSelector, useDispatch } from 'umi';
import { useMount } from 'ahooks';
import { Toast } from 'antd-mobile';
import { Pay } from '@gov-mo/mpaas-js-bridge';
import { isMobile } from '@/utils';
import { FetchResponse } from '@/utils/fetch';
import { ServiceInfo } from '@/conf/ServiceConf';
import { getPersonalCertPay } from '../../services/serviceStepApi';
import styles from './PaymentInformation.less';
import WebPaymentInformation from './WebPaymentInformation';
import { PayItemType } from '../../types/defs';

const EMPTY_FUNCTION = () => undefined;

interface PaymentInformationProps {
  onSuccess?: (e: unknown) => void;
  onCancel?: (e: unknown) => void;
  getSecondPayList?: () => Promise<FetchResponse<PayItemType>>;
}

function PaymentInformation(props: PaymentInformationProps) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { onSuccess, onCancel, getSecondPayList } = props;
  const intl = useIntl();
  const dispatch = useDispatch();
  const {
    stepModels: { workNo },
  } = useSelector(({ stepModels }: { stepModels: StepModelsState }) => ({ stepModels }));
  const initData = [
    {
      name: intl.formatMessage({ id: ServiceInfo.ServiceList[0].name }),
      price: 0,
      quantity: 0,
    },
  ];
  const [data, setData] = useState(initData);
  const [total, setTotal] = useState(0);
  const [unit, setUnit] = useState('MOP');
  useMount(() => {
    Toast.loading(intl.formatMessage({ id: '請稍候' }), 0);
    const params = { payCode: 1, applyNo: workNo };
    const getPayList = (params: unknown) =>
      getSecondPayList ? getSecondPayList() : getPersonalCertPay(params);
    // 监听支付结果返回
    Pay.listenPayResult((e) => {
      onSuccess?.(e);
    });
    // 监听是否取消了支付
    Pay.listenPayLater((e) => {
      onCancel?.(e);
    });
    getPayList(params)
      .then((res) => {
        if (res.success) {
          const {
            data: { payItemName, price, quantity, code, unit: curUnit, total: curTotal, billId },
          } = res;
          setData([
            {
              name: payItemName,
              price,
              quantity,
            },
          ]);
          setUnit(curUnit);
          setTotal(curTotal);
          dispatch({
            type: 'paymentModels/update',
            payload: {
              commodityList: {
                payItemId: code,
                price,
                quantity,
                unit: curUnit,
                money: price * quantity,
                isExemption: 0,
                billId,
              },
              total: curTotal,
            },
          });
        } else {
          Toast.fail(res.message);
        }
      })
      .finally(() => {
        Toast.hide();
      });
  });
  if (!isMobile()) {
    return <WebPaymentInformation data={data} total={total} unit={unit} />;
  }
  return (
    <div className={styles.root}>
      <div className={styles.nameArea}>
        <span className={styles.paymentName}>{intl.formatMessage({ id: 'Demo事项' })}</span>
      </div>

      {data.map((item) => {
        const { name, price, quantity } = item;
        return (
          <div key={name} className={styles.paymentArea}>
            <div className={styles.itemArea}>
              <span className={styles.itemName}>{`${intl.formatMessage({ id: '单价' })}`}</span>
              <span className={styles.itemValue}>
                <span className={styles.priceUnit}>
                  {`${unit}`}
                  &nbsp;
                </span>
                <span className={styles.priceValue}>{price.toFixed(2)}</span>
              </span>
            </div>
            <div className={styles.itemArea}>
              <span className={styles.itemName}>{`${intl.formatMessage({ id: '数量' })}`}</span>
              <span className={styles.itemValue}>{quantity}</span>
            </div>
          </div>
        );
      })}

      <div className={styles.totalContainer}>
        <div className={styles.totalArea}>
          <span>{`${intl.formatMessage({ id: '合计' })}`}</span>
          <span className={styles.total}>
            <span className={styles.totalUnit}>
              {`${unit}`}
              &nbsp;
            </span>
            <span className={styles.totalValue}>{total.toFixed(2)}</span>
          </span>
        </div>
      </div>
    </div>
  );
}

PaymentInformation.defaultProps = {
  onSuccess: EMPTY_FUNCTION,
  onCancel: EMPTY_FUNCTION,
  getSecondPayList: null,
};

export default PaymentInformation;
