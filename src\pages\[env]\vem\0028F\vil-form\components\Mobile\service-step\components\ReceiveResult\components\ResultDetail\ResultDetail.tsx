import React from 'react';
import type { receiveResultModelsState } from 'umi';
import { useIntl } from 'umi';
import { useSelector } from 'react-redux';
// import { useDictionary } from '@/utils/hooks';
import { resultTypeEnum } from '@/pages/service-step/config';
import { resultLanguageMap, resultMediumTypeDict } from '../../config';
import styles from './ResultDetail.less';
// import AddAddress from '../AddAddress';

function ResultDetail(props: { children: React.ReactNode; mobile: boolean }) {
  const { children, mobile } = props;
  const { receiveResult } = useSelector(
    ({ receiveResultModels }: { receiveResultModels: receiveResultModelsState }) =>
      receiveResultModels,
  );
  // const [resultMediumTypeDict, resultReceiveTypeDict] = useDictionary([
  //   'service-result-type',
  //   'receive-type',
  // ]);
  const intl = useIntl();

  return (
    <div className={mobile ? styles.root : styles.webRoot}>
      {receiveResult.map((item, index) => {
        const {
          // premiumAble,
          // receiveType,
          // resultNameZhMO,
          resultType,
          serviceResultCount,
          // person,
          // send,
          resultLanguage,
          receiveAddress,
        } = item;
        // const address = receiveType === receiveTypeEnum.personal ? person : send;
        return (
          <div key={index} className={styles.content}>
            {/* {premiumAble > 0 && (
              <div>
                <span>{`${intl.formatMessage({ id: '办理类别' })}：`}</span>
                <span>{intl.formatMessage({ id: premiumAble === 2 ? '普通' : '加急' })}</span>
              </div>
            )} */}
            {/* {resultType === resultTypeEnum.electronic && (
              <div>
                <span>{`${intl.formatMessage({ id: '服务结果名称' })}：`}</span>
                <span>{intl.formatMessage({ id: resultNameZhMO })}</span>
              </div>
            )} */}
            <div role="text">
              <span>{`${intl.formatMessage({ id: '服务结果类型' })}`}</span>
              <span>{intl.formatMessage({ id: resultMediumTypeDict[resultType] })}</span>
            </div>
            {resultLanguage && (
              <div role="text">
                <span>{`${intl.formatMessage({ id: '结果物语种' })}`}</span>
                <span>{resultLanguageMap[resultLanguage]}</span>
              </div>
            )}
            <div role="text">
              <span>{intl.formatMessage({ id: '费用' })}</span>
              <span>{`MOP ${((serviceResultCount ?? 1) * 15).toFixed(2)}`}</span>
            </div>
            {serviceResultCount && resultType !== resultTypeEnum.electronic && (
              <div role="text">
                <span>{intl.formatMessage({ id: '服务结果份数' })}</span>
                <span>{serviceResultCount}</span>
              </div>
            )}
            {resultType === resultTypeEnum.paper && (
              <>
                <div role="text">
                  <span>{`${intl.formatMessage({ id: '领取方式' })}`}</span>
                  <span>{intl.formatMessage({ id: '亲临领取' })}</span>
                </div>
                <div role="text">
                  <span>{`${intl.formatMessage({ id: '领取地点' })}`}</span>
                  <span>{receiveAddress?.[0]?.label}</span>
                </div>
              </>
            )}
            {/* {address &&
              (receiveType === receiveTypeEnum.personal || receiveType === receiveTypeEnum.send) && (
              <AddAddress className={styles.address} type={receiveType} value={address} detail />
            )} */}
          </div>
        );
      })}
      {children}
    </div>
  );
}

export default ResultDetail;
